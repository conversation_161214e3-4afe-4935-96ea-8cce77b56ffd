<?php

/**
 * The template for displaying 404 pages (not found)
 *
 * @link https://codex.wordpress.org/Creating_an_Error_404_Page
 *
 * @package frontend
 */

 /**
  * Get translated strings
  * @return string
  */
$title              = __('Page not found', 'FORBES');
$description        = __('Sorry, the page you are looking for doesn’t exist. Take a run around the block or hit that search button.', 'FORBES');
$search_button_text = __('Try search on Forbes', 'FORBES');
$home_button_text   = __('Go to Forbes', 'FORBES');

get_header(); ?>

<main class="page-404">

	<div class="container">

		<div class="page-404__wrapper">

			<?php $pageTitle = new Heading($title, 'h1', null, '800', 'archivo'); echo $pageTitle->render(); ?>

			<p class="page-404__description"><?php echo $description; ?></p>

			<div class="page-404__button-wrapper">
				<?php
					$button = new Button($search_button_text, null, 'medium', 'primary', false, false, 'button--search-opener', '');
					echo $button->render();
				?>

				<?php
					$button = new Button($home_button_text, home_url(), 'medium', 'secondary', false, false, '', '');
					echo $button->render();
				?>

			</div>

		</div>

	</div>

</main>

<?php get_footer(); ?>
