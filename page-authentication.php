<?php
/**
 * Template Name: Authentication Page
 */

/**
 * The image ID for the authentication image (light mode)
 * @var string
 */
$image_id_light = get_field('auth_image_light', 'option');

/**
 * The image ID for the authentication image (dark mode)
 * @var string
 */
$image_id_dark = get_field('auth_image_dark', 'option');

/**
 * The slug for the saved articles
 * @var string
 */
$savedArticlesSlug = frontend_translate('saved-articles', 'route') ?? 'saved-articles';

get_header();

?>

<script>
    // If I have window.crm_user then redirect me to /my-account/#/saved-articles
    if(window.crm_user) {
        window.location.href = '/' + window.myAccountPageSlug + '/#/<?php echo $savedArticlesSlug; ?>';
    }
</script>


<main class="page-authentication">

	<div class="container">

		<div class="row">
			<div id="reactAuthenticationForm" class="col-12 col-lg-5 page-authentication__form"></div>

			<div class="col-12 col-lg-7 page-authentication__image">
				<div class="authentication__image-wrapper">
					<?php if ($image_id_light) : ?>
						<?php echo wp_get_attachment_image($image_id_light, 'large', false, array('class' => 'authentication__image authentication__image--light')); ?>
					<?php endif; ?>
					<?php if ($image_id_dark) : ?>
						<?php echo wp_get_attachment_image($image_id_dark, 'large', false, array('class' => 'authentication__image authentication__image--dark')); ?>
					<?php endif; ?>
				</div>
			</div>

		</div>

	</div>

</main>

<?php get_footer(); ?>