@font-face {
  font-family: 'Archivo';
  src:
    url('assets/fonts/archivo/Archivo-SemiBoldItalic.woff2') format('woff2'),
    url('assets/fonts/archivo/Archivo-SemiBoldItalic.woff') format('woff');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Archivo';
  src:
    url('assets/fonts/archivo/Archivo-MediumItalic.woff2') format('woff2'),
    url('assets/fonts/archivo/Archivo-MediumItalic.woff') format('woff');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Archivo';
  src:
    url('assets/fonts/archivo/Archivo-SemiBold.woff2') format('woff2'),
    url('assets/fonts/archivo/Archivo-SemiBold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Archivo';
  src:
    url('assets/fonts/archivo/Archivo-Bold.woff2') format('woff2'),
    url('assets/fonts/archivo/Archivo-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Archivo';
  src:
    url('assets/fonts/archivo/Archivo-Regular.woff2') format('woff2'),
    url('assets/fonts/archivo/Archivo-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Archivo';
  src:
    url('assets/fonts/archivo/Archivo-Italic.woff2') format('woff2'),
    url('assets/fonts/archivo/Archivo-Italic.woff') format('woff');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Archivo';
  src:
    url('assets/fonts/archivo/Archivo-BoldItalic.woff2') format('woff2'),
    url('assets/fonts/archivo/Archivo-BoldItalic.woff') format('woff');
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Archivo';
  src:
    url('assets/fonts/archivo/Archivo-Medium.woff2') format('woff2'),
    url('assets/fonts/archivo/Archivo-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
:root {
  --ds-color-text-primary: #020617;
  --ds-color-text-secondary: #475569;
  --ds-error: #be123c;
  --ds-success: #047857;
  --ds-brand: #b45309;
  --ds-surface-primary: #ffffff;
  --ds-surface-secondary: #f8fafc;
  --ds-surface-icon: #f1f5f9;
  --ds-surface-invert: #1e293b;
  --ds-surface-brand-light: #fde68a;
  --ds-surface-brand-dark: #b45309;
  --ds-surface-pro-standard: #f1f5f9;
  --ds-surface-pro-premium: #fef3c7;
  --ds-surface-accent-blue: #eff6ff;
  --ds-button-primary: #020617;
  --ds-button-primary-pressed: #1e293b;
  --ds-button-primary-disabled: #475569;
  --ds-button-secondary: #ffffff;
  --ds-button-secondary-pressed: #f1f5f9;
  --ds-button-secondary-disabled: #94a3b8;
  --ds-icon-primary: #020617;
  --ds-icon-secondary: #475569;
  --ds-icon-onLight: #020617;
  --ds-icon-onDark: #ffffff;
  --ds-divider: #e2e8f0;
  --ds-link-default: #020617;
  --ds-link-hover: #b45309;
  --ds-green: #16a34a;
  --ds-tag-default: #b45309;
  --ds-tag-hover: #d97706;
  --ds-primary-font: 'noto-serif', 'serif';
  --ds-secondary-font: 'Archivo';
}
:root.dark-mode {
  --ds-color-text-primary: #ffffff;
  --ds-color-text-secondary: #cbd5e1;
  --ds-error: #fda4af;
  --ds-success: #6ee7b7;
  --ds-brand: #fcd34d;
  --ds-surface-primary: #020617;
  --ds-surface-secondary: #1e293b;
  --ds-surface-icon: #0f172a;
  --ds-surface-invert: #e2e8f0;
  --ds-surface-brand-light: #92400e;
  --ds-surface-brand-dark: #fde68a;
  --ds-surface-pro-standard: #0f172a;
  --ds-surface-pro-premium: #78350f;
  --ds-surface-accent-blue: #0f172a;
  --ds-button-primary: #f8fafc;
  --ds-button-primary-pressed: #e2e8f0;
  --ds-button-primary-disabled: #94a3b8;
  --ds-button-secondary: #020617;
  --ds-button-secondary-pressed: #1e293b;
  --ds-button-secondary-disabled: #475569;
  --ds-icon-primary: #f8fafc;
  --ds-icon-secondary: #cbd5e1;
  --ds-icon-onLight: #020617;
  --ds-icon-onDark: #ffffff;
  --ds-divider: #334155;
  --ds-link-default: #ffffff;
  --ds-link-hover: #fcd34d;
  --ds-green: #16a34a;
  --ds-tag-default: #fcd34d;
  --ds-tag-hover: #fde68a;
}
:root {
  --amber200: #fde68a;
  --amber300: #fcd34d;
  --amber400: #fbbf24;
  --amber500: #f59e0b;
  --amber600: #d97706;
  --amber700: #b45309;
  --amber800: #b45309;
  --amber900: #b45309;
  --top: #121212;
  --high: #19181f;
  --mid: #363c4a;
  --low: #4b5563;
  --separator: #e5e7eb;
  --separator-dark: #d9d9d9;
  --background-1: #f3f4f6;
  --background-1-rgb: 243, 244, 246;
  --background-0: #ffffff;
  --background-0-rgb: 255, 255, 255;
  --success: #04956c;
  --warning: #c75305;
  --error: #c70528;
  --life: #db2777;
  --background-article-1: #fffbeb;
  --podcast: #475569;
  --podcast-cz: #e11d48;
  --brown: #b89b6b;
  --green: #4ade80;
  --light: #fcfcfdb8;
  --background-1-opacity: #f3f4f6b1;
  --box-shadow-color: #00000040;
  --dark-mid: #cdcdce;
  --dark-top: #f3f3f4;
  --black: #000;
  --white: #fff;
  --navigation-bar-background-color: #fff;
  --post-background-color: #fff;
  --primary-font: 'noto-serif', 'serif';
  --secondary-font: 'Ubuntu';
  --condensed-font: 'noto-serif-condensed', 'serif';
  --semicondensed-font: 'noto-serif-semicondensed', 'serif';
  --inter-font: 'Inter', sans-serif;
  --highlight-font: 'Noto Serif Display', serif;
  --flag-border: #dfe2e5;
  --ds-color-text-secondary: #475569;
}
:root.dark-mode {
  --amber200: #78350f7a;
  --amber600: #fcd34d;
  --amber800: #fde68a;
  --dark-top: #121212;
  --top: #f3f3f4;
  --high: #e0e0e1;
  --mid: #cdcdce;
  --low: #bababc;
  --separator: #3d4048;
  --separator-dark: #1f1f1f;
  --background-1: #2e3138;
  --background-1-rgb: 46, 49, 56;
  --background-0: #121212;
  --background-0-rgb: 18, 18, 18;
  --success: #20dfa9;
  --warning: #e58a4d;
  --error: #e54d69;
  --life: #db2777;
  --background-article-1: #211007;
  --podcast: #d2e0f3;
  --podcast-cz: #e11d48;
  --brown: #b89b6b;
  --green: #4ade80;
  --light: #fcfcfdb8;
  --background-1-opacity: #f3f4f6b1;
  --navigation-bar-background-color: #121212;
  --post-background-color: #121212;
  --light-mid: #363c4a;
  --light-top: #121212;
  --flag-border: #000;
  --ds-color-text-secondary: #cbd5e1;
}
main h1,
header h1,
footer h1 {
  color: var(--ds-color-text-primary);
  font-feature-settings:
    'clig' off,
    'liga' off;
  font-family: var(--ds-primary-font) !important;
  font-size: 4.618rem;
  font-weight: 400;
  line-height: 130%;
  transition: color 0.3s ease;
}
main h1.archivo,
header h1.archivo,
footer h1.archivo {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-secondary-font) !important;
  font-size: 5.6rem;
  font-weight: 800;
  line-height: normal;
}
main h2,
header h2,
footer h2 {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-primary-font) !important;
  font-size: 4.105rem;
  font-weight: 400;
  line-height: 127%;
  transition: color 0.3s ease;
}
main h2.archivo,
header h2.archivo,
footer h2.archivo {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-secondary-font) !important;
  font-size: 4rem;
  font-weight: 600;
  line-height: normal;
  letter-spacing: -0.024rem;
}
main h3,
header h3,
footer h3 {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-primary-font) !important;
  font-size: 3.244rem;
  font-weight: 500;
  line-height: 135%;
  letter-spacing: 0.0081rem;
  transition: color 0.3s ease;
}
main h3.archivo,
header h3.archivo,
footer h3.archivo {
  color: var(--ds-color-text-primary);
  font-family: Archivo !important;
  font-size: 3.2rem;
  font-weight: 600;
  line-height: 124%;
}
main h4,
header h4,
footer h4 {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-primary-font) !important;
  font-size: 2.4rem;
  font-weight: 400;
  line-height: 120%;
  letter-spacing: 0.024rem;
  transition: color 0.3s ease;
}
main h4.archivo,
header h4.archivo,
footer h4.archivo {
  color: var(--ds-color-text-primary);
  font-feature-settings:
    'clig' off,
    'liga' off;
  font-family: var(--ds-secondary-font) !important;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 120%;
  letter-spacing: -0.024rem;
}
main h5,
header h5,
footer h5 {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-primary-font) !important;
  font-size: 1.8rem;
  font-weight: 500;
  line-height: 155%;
  transition: color 0.3s ease;
}
main h5.archivo,
header h5.archivo,
footer h5.archivo {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-secondary-font) !important;
  font-size: 2rem;
  font-weight: 600;
  line-height: 140%;
}
main h6,
header h6,
footer h6 {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-primary-font) !important;
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 132%;
  transition: color 0.3s ease;
}
main p,
main .basic,
header p,
header .basic,
footer p,
footer .basic {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-secondary-font) !important;
  font-size: 1.4rem;
  font-style: normal;
  font-weight: 500;
  line-height: 120%;
  transition: color 0.3s ease;
}
main p.basic-16,
main .basic.basic-16,
header p.basic-16,
header .basic.basic-16,
footer p.basic-16,
footer .basic.basic-16 {
  font-size: 1.6rem;
  font-weight: 500;
  line-height: 120%;
}
main p.basic-18,
main .basic.basic-18,
header p.basic-18,
header .basic.basic-18,
footer p.basic-18,
footer .basic.basic-18 {
  font-size: 1.8rem;
  font-weight: 400;
  line-height: 160%;
}
main p.basic-underline,
main .basic.basic-underline,
header p.basic-underline,
header .basic.basic-underline,
footer p.basic-underline,
footer .basic.basic-underline {
  text-decoration: underline;
}
main a,
main .link,
header a,
header .link,
footer a,
footer .link {
  color: var(--ds-color-text-primary);
  font-variant-numeric: lining-nums tabular-nums;
  text-decoration: none;
  font-family: var(--ds-secondary-font) !important;
  font-size: 1.4rem;
  font-style: normal;
  font-weight: 600;
  line-height: 120%;
  cursor: pointer;
  transition: color 0.3s ease;
}
@media (hover: hover) {
  main a:hover,
  main .link:hover,
  header a:hover,
  header .link:hover,
  footer a:hover,
  footer .link:hover {
    color: var(--ds-brand);
  }
}
main a.link-16,
main .link.link-16,
header a.link-16,
header .link.link-16,
footer a.link-16,
footer .link.link-16 {
  font-size: 1.6rem;
}
main a.link-18,
main .link.link-18,
header a.link-18,
header .link.link-18,
footer a.link-18,
footer .link.link-18 {
  font-size: 1.8rem;
}
main a.link-20,
main .link.link-20,
header a.link-20,
header .link.link-20,
footer a.link-20,
footer .link.link-20 {
  font-size: 2rem;
  line-height: 2.8rem;
  font-weight: 600;
}
main a.link-underline,
main .link.link-underline,
header a.link-underline,
header .link.link-underline,
footer a.link-underline,
footer .link.link-underline {
  text-decoration: underline;
}
main a.disabled,
main .link.disabled,
header a.disabled,
header .link.disabled,
footer a.disabled,
footer .link.disabled {
  opacity: 0.5;
  pointer-events: none;
}
main .tag,
header .tag,
footer .tag {
  color: var(--ds-color-text-secondary);
  font-family: var(--ds-secondary-font) !important;
  font-size: 1.4rem;
  font-weight: 500;
  line-height: 120%;
}
main .tag,
header .tag,
footer .tag {
  font-family: var(--ds-secondary-font);
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1.4rem;
  letter-spacing: 0em;
  color: var(--ds-tag-default);
  display: flex;
  align-items: center;
  margin-bottom: 0.8rem;
  transition: color 0.3s ease;
}
main .tag:before,
header .tag:before,
footer .tag:before {
  content: '';
  display: inline-block;
  width: 1.6rem;
  height: 0.1rem;
  border-bottom: 0.15rem solid var(--ds-brand);
  margin-right: 0.4rem;
  margin-top: 0.15rem;
  transition: border-color 0.3s ease;
}
@media (hover: hover) {
  main .tag:hover,
  header .tag:hover,
  footer .tag:hover {
    color: var(--ds-tag-hover);
  }
  main .tag:hover:before,
  header .tag:hover:before,
  footer .tag:hover:before {
    border-bottom-color: var(--ds-tag-hover);
  }
}
main .button,
header .button,
footer .button {
  color: var(--ds-color-text-primary);
  font-family: var(--ds-secondary-font) !important;
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 120%;
  letter-spacing: 0.028rem;
  text-transform: none;
  text-decoration: none;
}
main .button-primary,
header .button-primary,
footer .button-primary {
  color: var(--ds-surface-primary);
}
main .button.disabled,
header .button.disabled,
footer .button.disabled {
  opacity: 0.5;
  pointer-events: none;
}
.wp-block-button,
.button {
  cursor: pointer;
  display: flex;
  font-family: var(--ds-secondary-font) !important;
  font-size: 1.4rem;
  justify-content: center;
  letter-spacing: 0.028rem;
  line-height: 1.68rem;
  font-weight: 600;
  margin: 0;
  text-decoration: none;
  text-transform: none;
  border: 0.2rem solid var(--ds-color-text-primary);
  transition: all 0.3s ease;
}
.wp-block-button--large,
.button--large {
  padding: 1.6rem;
}
.wp-block-button--medium,
.button--medium {
  padding: 1.2rem 1.6rem;
}
.wp-block-button--medium.button--icon-before,
.button--medium.button--icon-before {
  display: flex;
  align-items: center;
}
.wp-block-button--medium.button--icon-before:before,
.button--medium.button--icon-before:before {
  content: '';
  display: inline-block;
  width: 1.6rem;
  height: 1.6rem;
  margin-right: 0.4rem;
  background-color: var(--ds-color-text-primary);
  mask-position: center;
  mask-repeat: no-repeat;
  mask-size: contain;
  -webkit-mask-size: contain;
  -webkit-mask-position: center;
  -webkit-mask-repeat: no-repeat;
}
.wp-block-button--small,
.button--small {
  padding: 0.8rem 1.6rem;
}
.is-style-button-primary a,
.button-primary {
  background-color: var(--ds-button-primary);
  border-color: transparent;
  color: var(--ds-surface-primary);
}
@media (hover: hover) {
  .is-style-button-primary a:hover,
  .button-primary:hover {
    box-shadow:
      0 2rem 2.5rem -0.5rem rgba(0, 0, 0, 0.1),
      0 1rem 1rem -0.5rem rgba(0, 0, 0, 0.04);
    background-color: var(--ds-button-primary);
    color: var(--ds-surface-primary);
  }
  .is-style-button-primary a:hover > *,
  .button-primary:hover > * {
    color: initial !important;
  }
}
.is-style-button-primary a:disabled,
.button-primary:disabled {
  background-color: var(--ds-button-primary-disabled);
  pointer-events: none;
}
.is-style-button-secondary a,
.button-secondary {
  background-color: var(--ds-surface-primary);
  border-radius: 0;
  color: var(--ds-color-text-primary);
}
@media (hover: hover) {
  .is-style-button-secondary a:hover,
  .button-secondary:hover {
    background-color: var(--ds-surface-primary);
    box-shadow:
      0 2rem 2.5rem -0.5rem rgba(0, 0, 0, 0.1),
      0 1rem 1rem -0.5rem rgba(0, 0, 0, 0.04);
    color: var(--ds-color-text-primary);
  }
  .is-style-button-secondary a:hover > *,
  .button-secondary:hover > * {
    color: initial !important;
  }
}
.is-style-button-secondary a:disabled,
.button-secondary:disabled {
  opacity: 0.64;
  background-color: var(--ds-button-secondary);
  pointer-events: none;
}
.navigation--new-ds {
  background-color: var(--ds-surface-primary);
}
.navigation--new-ds .navigation__top-row {
  display: flex;
  align-items: center !important;
  gap: 1.6rem;
}
.navigation--new-ds .navigation__icons {
  display: flex;
}
.navigation--new-ds .navigation__subscription-button {
  color: var(--ds-surface-primary) !important;
  margin-right: 2.4rem;
}
.navigation--new-ds .navigation__dropdown-wrapper {
  position: relative;
}
.navigation--new-ds .navigation__user-button {
  mask-image: unset;
  -webkit-mask-image: unset;
  margin-right: 2.4rem;
  width: 2.6rem;
  height: 2.6rem;
  border-radius: 50%;
  overflow: hidden;
  border: 0.2rem solid transparent;
  transition: all 0.3s ease;
}
@media (hover: hover) {
  .navigation--new-ds .navigation__user-button:hover {
    border-color: var(--ds-brand);
  }
}
.navigation--new-ds .navigation__user-button--no-display-name {
  border-color: var(--ds-brand);
}
.navigation--new-ds .navigation__top-row {
  padding-bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.navigation--new-ds .navigation__bottom-row-wrapper {
  margin-top: 2.3rem;
}
.navigation--new-ds .navigation__bottom-row-inner {
  margin-bottom: 2.4rem;
  gap: 2.4rem;
}
.navigation--new-ds .navigation__menu-item-link {
  color: var(--ds-color-text-secondary);
}
.dark-mode .navigation--new-ds .drawer-handheld-menu-wrapper.open .handheld-overlay {
  background-color: rgba(226, 232, 240, 0.3);
}
@media screen and (max-width: 767px) {
  .navigation__top-row {
    padding-top: 2.4rem !important;
    padding-bottom: 2.4rem !important;
  }
  .navigation__top-row .main-logo {
    max-width: 10.7rem;
    height: auto;
    flex-grow: 1;
  }
  .navigation__subscription-button {
    margin-right: 0.8rem !important;
  }
  .navigation__dropdown-wrapper {
    width: auto;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .navigation__user-button {
    min-width: 4rem !important;
    height: 4rem !important;
    margin-right: 0.8rem !important;
  }
  .navigation .search-opener-btn {
    margin-right: 0.8rem;
  }
}
.new-ds-footer {
  background-color: var(--ds-surface-primary);
}
.new-ds-footer__wrapper {
  padding-block: 4rem;
  border-top: 0.1rem solid var(--ds-divider);
  border-bottom: 0.1rem solid var(--ds-divider);
}
.new-ds-footer__section-title {
  font-weight: 600;
  margin-bottom: 2.4rem;
}
.new-ds-footer__links-wrapper {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 4rem;
}
.new-ds-footer__links-list {
  display: grid;
  grid-template-rows: repeat(7, 1fr);
  grid-auto-flow: column;
  grid-column-gap: 2rem;
  grid-row-gap: 1.6rem;
}
.new-ds-footer__link {
  color: var(--ds-color-text-secondary) !important;
}
@media (hover: hover) {
  .new-ds-footer__link:hover {
    color: var(--ds-brand) !important;
  }
}
.new-ds-footer__bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-block: 4rem;
}
.new-ds-footer__copyright-text {
  color: var(--ds-color-text-secondary);
}
.new-ds-footer__social-media-wrapper {
  display: flex;
  gap: 2.2rem;
}
.new-ds-footer__social-media {
  color: var(--ds-color-text-secondary);
}
@media screen and (max-width: 767px) {
  .new-ds-footer__logo-wrapper {
    margin-bottom: 3.2rem;
  }
  .new-ds-footer .main-logo {
    width: 10.7rem;
    max-width: 10.7rem;
    height: 3.2rem;
  }
  .new-ds-footer__links-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 3rem;
  }
  .new-ds-footer__section {
    flex-grow: 1;
  }
  .new-ds-footer__bottom {
    flex-direction: column;
    align-items: flex-start;
  }
  .new-ds-footer__copyright-text {
    margin-bottom: 2.4rem;
  }
  .new-ds-footer__social-media-wrapper {
    justify-content: space-between;
    width: 100%;
    gap: unset;
  }
  .new-ds-footer__social-media {
    font-weight: 500 !important;
  }
}
.input-wrapper {
  display: flex;
  flex: 1;
  flex-direction: column;
}
.input-wrapper:has(.input.error) .input__error {
  display: block;
}
.input {
  width: 100%;
  background-color: transparent !important;
  color: var(--ds-color-text-secondary) !important;
  outline: none;
  padding: 1.2rem 2.4rem !important;
  font-size: 1.6rem;
  font-weight: 500;
  font-family: var(--ds-secondary-font) !important;
  line-height: 1.92rem;
  border: var(--ds-divider) 0.2rem solid;
  transition: all 0.3s ease;
}
.input::-webkit-outer-spin-button,
.input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.input[type='number'] {
  -moz-appearance: textfield;
}
.input:hover,
.input:focus {
  border-color: var(--ds-color-text-secondary) !important;
  background-color: transparent !important;
}
.input:focus {
  box-shadow: none;
  color: var(--ds-color-text-primary) !important;
  border-color: var(--ds-color-text-primary) !important;
}
.input.mce_inline_error {
  border-color: var(--ds-error) !important;
  background-color: transparent !important;
  color: var(--ds-error) !important;
}
.input.valid {
  border-color: var(--ds-success);
  color: var(--ds-success);
  background-color: var(--ds-surface-primary);
}
.input.error {
  color: var(--ds-error);
  border-color: var(--ds-error);
}
.input:disabled {
  opacity: 0.64;
}
.input__error {
  display: none;
  color: var(--ds-error) !important;
  margin-top: 0.8rem;
  margin-bottom: 0;
  text-align: left;
}
.input-label {
  font-size: 1.6rem;
  line-height: 1.92rem;
  font-family: var(--ds-secondary-font) !important;
  color: var(--ds-color-text-secondary);
  transition: all 0.3s ease;
  width: 100%;
}
.input-label:has(.input:disabled) {
  opacity: 0.64;
}
.input-label .input {
  margin-top: 0.4rem;
}
.input-label:focus-within,
.input-label:hover {
  color: var(--ds-color-text-primary);
}
.ds-custom-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.8rem;
  cursor: pointer;
}
@media (hover: hover) {
  .ds-custom-toggle:hover .ds-custom-toggle__label {
    color: var(--ds-brand);
  }
  .ds-custom-toggle:hover .ds-custom-toggle__inner {
    background-color: var(--ds-color-text-secondary);
  }
}
.ds-custom-toggle:has(.ds-custom-toggle__input:checked) .ds-custom-toggle__label--on {
  display: block;
}
.ds-custom-toggle:has(.ds-custom-toggle__input:checked) .ds-custom-toggle__label--off {
  display: none;
}
.ds-custom-toggle.disabled {
  pointer-events: none;
}
.ds-custom-toggle.disabled .ds-custom-toggle__inner {
  background-color: var(--ds-button-secondary-disabled) !important;
}
.ds-custom-toggle.disabled .ds-custom-toggle__label {
  color: var(--ds-button-secondary-disabled);
}
.ds-custom-toggle__inner {
  display: block;
  background-color: var(--ds-button-secondary-disabled);
  transition: background-color 0.3s ease;
  border-radius: 3rem;
  cursor: pointer;
  position: relative;
}
.ds-custom-toggle__inner:has(.ds-custom-toggle__input:checked) {
  background-color: var(--ds-green);
}
.ds-custom-toggle__inner:has(.ds-custom-toggle__input:disabled) {
  pointer-events: none;
  opacity: 0.6;
}
.ds-custom-toggle__inner--small {
  width: 3.2rem;
  height: 2rem;
}
.ds-custom-toggle__inner--medium {
  width: 4.8rem;
  height: 2.4rem;
  padding: 0.3rem;
}
.ds-custom-toggle__inner--large {
  padding: 0.4rem;
  width: 6.4rem;
  height: 3.2rem;
}
.ds-custom-toggle__input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.ds-custom-toggle__input:checked ~ .ds-custom-toggle__indicator {
  left: calc(50% - 0.2rem);
  transform: translateY(-50%);
}
.ds-custom-toggle__indicator {
  position: absolute;
  top: 50%;
  left: 0.2rem;
  display: block;
  height: calc(100% - 0.4rem);
  transform: translateY(-50%);
  width: auto;
  aspect-ratio: 1/1;
  border-radius: 50%;
  background-color: var(--ds-surface-primary);
  transition: all 0.3s ease;
}
.ds-custom-toggle__label--on {
  display: none;
}
.custom-radio {
  position: relative;
  border: 0.1rem solid var(--ds-color-text-primary);
  margin-right: 0.8rem;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.custom-radio:has(.custom-radio__input:checked) {
  border-color: transparent;
  background-color: var(--ds-brand);
}
.custom-radio::after {
  content: attr(data-label);
  display: inline-block;
  margin-left: calc(100% + 1.6rem);
  font-size: 1.6rem;
  line-height: 1.92rem;
  color: var(--ds-color-text-primary);
  font-family: var(--ds-secondary-font) !important;
  font-weight: 600 !important;
  white-space: nowrap;
}
.custom-radio__input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.custom-radio__input:checked ~ .custom-radio__indicator {
  opacity: 1;
}
.custom-radio__indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--white);
  opacity: 0;
  border-radius: 50%;
  transition: opacity 0.3s ease;
  width: 0.8rem;
  height: 0.8rem;
}
.custom-radio--small {
  width: 1.6rem;
  height: 1.6rem;
}
.custom-radio--medium {
  width: 2.4rem;
  height: 2.4rem;
}
.custom-radio--medium .custom-radio__indicator {
  width: 1.2rem;
  height: 1.2rem;
}
.custom-radio--large {
  width: 3.2rem;
  height: 3.2rem;
}
.custom-radio--large .custom-radio__indicator {
  width: 1.6rem;
  height: 1.6rem;
}
.custom-radio:has(.custom-radio__input:disabled) {
  pointer-events: none;
  opacity: 0.6;
}
.ds-custom-checkbox {
  display: flex;
  align-items: center;
}
.ds-custom-checkbox__input {
  display: none;
}
.ds-custom-checkbox__inner {
  width: 2.4rem;
  height: 2.4rem;
  border: 0.15rem solid var(--ds-icon-secondary);
  margin-right: 0.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}
.ds-custom-checkbox__indicator {
  display: block;
  width: 1.6rem;
  height: 1.6rem;
  mask-image: url('assets/icons/icon-checkmark.svg');
  -webkit-mask-image: url('assets/icons/icon-checkmark.svg');
  mask-position: center;
  -webkit-mask-position: center;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-size: contain;
  -webkit-mask-size: contain;
  background-color: var(--ds-surface-primary);
  transition: all 0.3s ease;
}
@media (hover: hover) {
  .ds-custom-checkbox:hover .ds-custom-checkbox__indicator {
    background-color: var(--ds-icon-secondary);
  }
}
.ds-custom-checkbox:has(.ds-custom-checkbox__input:checked) .ds-custom-checkbox__inner {
  background-color: var(--ds-brand);
  border-color: var(--ds-brand);
}
.ds-custom-checkbox:has(.ds-custom-checkbox__input:checked) .ds-custom-checkbox__indicator {
  background-color: var(--ds-surface-primary);
}
.ds-custom-checkbox.disabled {
  pointer-events: none;
}
.ds-custom-checkbox.disabled .ds-custom-checkbox:has(.ds-custom-checkbox__input:checked) .ds-custom-checkbox__inner {
  border-color: var(--ds-icon-secondary);
}
.ds-custom-checkbox.disabled .ds-custom-checkbox__indicator {
  background-color: var(--ds-icon-secondary);
}
.drawer-handheld-menu {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 100vw;
  max-width: 48rem;
  overflow: hidden;
  background-color: var(--ds-surface-primary);
  z-index: 4;
  transition: transform 0.3s ease-in;
  transform: translateX(100%);
  padding-top: 0;
}
.drawer-handheld-menu .main-menu {
  max-width: 50rem;
  overflow-y: auto;
}
.drawer-handheld-menu .main-menu::-webkit-scrollbar {
  display: none;
}
.drawer-handheld-menu__menu {
  margin-bottom: 4rem;
}
.drawer-handheld-menu .submenus {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: translate(100%);
}
.drawer-handheld-menu .submenus .submenu {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: var(--ds-surface-primary);
  transition: transform 0.3s ease-in;
}
.drawer-handheld-menu .submenus .submenu.open {
  transform: translateX(-100%);
}
.drawer-handheld-menu .submenus .submenu.open .drawer-handheld-menu__header {
  justify-content: space-between;
}
.drawer-handheld-menu .submenus .submenu .drawer-handheld-menu__main {
  padding-inline: 4rem;
}
.drawer-handheld-menu .submenus .submenu .drawer-handheld-menu__header {
  margin-bottom: 4rem;
}
.drawer-handheld-menu .submenus .submenu .drawer-handheld-menu__title {
  margin-bottom: 0;
}
.drawer-handheld-menu__header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 2.4rem;
  padding: 4rem 4rem 0 4rem;
}
.drawer-handheld-menu__login-wrapper {
  margin-inline: 4rem;
  padding-bottom: 4rem;
  margin-bottom: 4rem;
  border-bottom: 0.1rem solid var(--ds-divider);
  display: flex;
  justify-content: center;
}
.drawer-handheld-menu__login {
  text-decoration: none;
  display: flex;
}
.drawer-handheld-menu__login-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.drawer-handheld-menu__user-wrapper {
  margin-inline: 4rem;
  padding-bottom: 4rem;
  margin-bottom: 4rem;
  border-bottom: 0.1rem solid var(--ds-divider);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (hover: hover) {
  .drawer-handheld-menu__user-wrapper:hover .drawer-handheld-menu__user-name {
    color: var(--ds-brand);
  }
}
.drawer-handheld-menu__user-info {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  max-width: calc(100% - 2.8rem);
  overflow: hidden;
}
.drawer-handheld-menu__user-avatar {
  width: 2.4rem;
  height: 2.4rem;
  border-radius: 50%;
}
.drawer-handheld-menu__user-name {
  margin-bottom: 0;
  overflow: hidden;
  max-width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.3s ease;
}
.drawer-handheld-menu__user-saved-articles {
  width: 2.4rem;
  height: 2.4rem;
  border-radius: 50%;
  background-color: var(--ds-icon-primary);
  color: var(--ds-surface-primary);
  line-height: 2.4rem;
  text-align: center;
  font-size: 1rem;
  font-weight: 700;
  font-family: var(--ds-secondary-font);
}
.drawer-handheld-menu__footer {
  padding: 0 4rem 4rem;
}
.drawer-handheld-menu__main {
  flex: 1;
}
.drawer-handheld-menu__night-label {
  transition: color 0.3s ease;
}
.drawer-handheld-menu__favicon {
  margin-right: 0.8rem;
  width: 2.4rem;
  height: 2.4rem;
  object-fit: contain;
}
.drawer-handheld-menu__favicon--dark {
  display: none;
}
.drawer-handheld-menu__menu-item-list {
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 3.2rem;
  padding-bottom: 4rem;
}
.drawer-handheld-menu__menu-item-list--middle {
  margin-inline: 4rem;
  padding-bottom: 0;
  margin-bottom: 4rem;
}
.drawer-handheld-menu__menu-item-list--simple {
  margin-inline: 4rem;
  margin-bottom: 4rem;
  border-bottom: 0.1rem solid var(--ds-divider);
}
.drawer-handheld-menu__menu-item-list--highlight {
  background-color: var(--ds-surface-brand-dark);
  padding: 4rem;
}
.drawer-handheld-menu__menu-item-list--highlight .link {
  color: var(--ds-surface-primary) !important;
}
.drawer-handheld-menu__menu-item-list--highlight .link.submenu-opener:after {
  background-color: var(--ds-surface-primary) !important;
}
@media (hover: hover) {
  .drawer-handheld-menu__menu-item-list--highlight .link:hover {
    color: var(--ds-divider) !important;
  }
  .drawer-handheld-menu__menu-item-list--highlight .link:hover.submenu-opener:after {
    background-color: var(--ds-divider) !important;
  }
}
.drawer-handheld-menu__menu-item {
  display: flex;
  position: relative;
}
.drawer-handheld-menu__menu-item .link {
  width: 100%;
}
.drawer-handheld-menu__menu-item .submenu-opener:after {
  content: '';
  position: absolute;
  right: 0;
  width: 2.4rem;
  height: 2.4rem;
  display: inline-block;
  mask-image: url('assets/icons/icon-plus.svg');
  -webkit-mask-image: url('assets/icons/icon-plus.svg');
  background-color: var(--low);
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-position: 50% 50%;
  -webkit-mask-position: 50% 50%;
  mask-size: contain;
  -webkit-mask-size: contain;
}
@media (hover: hover) {
  .drawer-handheld-menu__menu-item .submenu-opener:hover:after {
    background-color: var(--ds-brand);
  }
}
.drawer-handheld-menu__menu-item .highlight::after {
  content: 'TIP';
  color: var(--ds-surface-primary);
  border-radius: 3rem;
  background-color: var(--ds-surface-brand-dark);
  margin-left: 0.8rem;
  padding: 0.2rem 0.6rem;
  font-size: 1.2rem;
}
.drawer-handheld-menu__menu-item--dark-mode {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.drawer-handheld-menu__menu-item--country-link {
  display: flex;
  margin: 0 0.8rem;
}
.drawer-handheld-menu__menu-item--country-link .drawer-handheld-menu__country-link-flag {
  width: 2rem;
  height: 1.5rem;
  margin-right: 0.8rem;
  border-radius: 0.3rem;
  border: 0.1rem solid var(--flag-border);
}
.drawer-handheld-menu__menu-item--country-link .drawer-handheld-menu__country-link {
  transition: color 0.3s ease;
}
.drawer-handheld-menu__menu-item--country-link .drawer-handheld-menu__menu-item-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (hover: hover) {
  .drawer-handheld-menu__menu-item--country-link
    .drawer-handheld-menu__menu-item-container:hover
    .drawer-handheld-menu__country-link-icon {
    background-color: var(--ds-brand);
  }
}
.drawer-handheld-menu__menu-item--country-link .drawer-handheld-menu__menu-item-image-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}
.drawer-handheld-menu__menu-item--country-link .drawer-handheld-menu__country-link-icon {
  width: 1.2rem;
  height: 1.2rem;
  mask-image: url('assets/icons/icon-link-opener.svg');
  -webkit-mask-image: url('assets/icons/icon-link-opener.svg');
  mask-position: center;
  -webkit-mask-position: center;
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  background-color: var(--ds-color-text-secondary);
  transition: background-color 0.3s ease;
}
.drawer-handheld-menu__menu-item--country-link:first-child {
  pointer-events: none;
  position: relative;
}
.drawer-handheld-menu__menu-item--country-link:first-child:before {
  content: '';
  position: absolute;
  top: -1rem;
  left: -4.8rem;
  width: calc(100% + 9.6rem);
  height: 4.8rem;
  background-color: var(--ds-surface-primary);
  z-index: -1;
}
.drawer-handheld-menu__menu-item--country-link:first-child .drawer-handheld-menu__country-link-icon {
  display: none;
}
@media (hover: hover) {
  .drawer-handheld-menu__menu-item--country-link:hover .drawer-handheld-menu__country-link {
    color: var(--ds-brand);
  }
}
.drawer-handheld-menu__menu-item-icon {
  margin-right: 0.8rem;
  width: 1.8rem;
  height: 1.8rem;
  mask-image: url('assets/icons/icon-globe.svg');
  -webkit-mask-image: url('assets/icons/icon-globe.svg');
  mask-position: center;
  -webkit-mask-position: center;
  mask-size: contain;
  -webkit-mask-size: contain;
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  background-color: var(--ds-color-text-secondary);
  transition: background-color 0.3s ease;
}
.drawer-handheld-menu .magazine__wrapper {
  display: inline-block;
}
.drawer-handheld-menu .magazine__image-wrapper {
  width: 100%;
  margin-bottom: 2.4rem;
  box-shadow: 0 2.5rem 5rem -1.2rem var(--box-shadow-color);
}
.drawer-handheld-menu .magazine__image-wrapper img {
  width: 100%;
  height: auto;
}
.drawer-handheld-menu .magazine__label {
  margin-bottom: 2.4rem;
  color: var(--ds-color-text-secondary);
}
.drawer-handheld-menu .magazine__excerpt {
  margin-bottom: 2.4rem;
}
.drawer-handheld-menu .magazine__excerpt p {
  display: -webkit-box;
  -webkit-line-clamp: 9;
  -webkit-box-orient: vertical;
  overflow: hidden;
  visibility: visible;
}
.drawer-handheld-menu .magazine .button:after {
  background-color: var(--ds-surface-primary);
}
@media screen and (max-width: 767px) {
  .drawer-handheld-menu {
    padding-top: 2.4rem !important;
  }
  .drawer-handheld-menu__header {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    padding-left: 2.4rem !important;
    padding-right: 2.4rem !important;
    margin-bottom: 3.2rem;
  }
  .drawer-handheld-menu__main {
    padding-left: 2.4rem !important;
    padding-right: 2.4rem !important;
  }
  .drawer-handheld-menu__login-wrapper,
  .drawer-handheld-menu__menu-item-list {
    margin-inline: 0;
  }
  .drawer-handheld-menu__login-wrapper--highlight,
  .drawer-handheld-menu__menu-item-list--highlight {
    margin-inline: -2.4rem;
    padding-inline: 2.4rem;
  }
  .drawer-handheld-menu__user-wrapper {
    margin-inline: 0;
  }
  .drawer-handheld-menu__footer {
    padding-bottom: 10rem;
  }
}
.empty-message {
  padding: 2.4rem 4rem;
  background-color: var(--ds-surface-secondary);
  font-size: 1.6rem;
  font-weight: 500;
  line-height: 120%;
  border: 0.1rem solid var(--ds-divider);
}
body {
  background-color: var(--ds-surface-primary);
}
body.branding-active main,
body.gam-branding-active main {
  background-color: var(--ds-surface-primary);
}
body[data-locale='cz'] .googlead#leaderboard-ad {
  background-color: var(--ds-surface-primary);
}
