<?php

/**
 * Raw date from the issue_date field
 * @var string
 */
$raw_date = get_field('issue_date');

/**
 * The date of the issuance of the magazine
 * @var string
 */
$issue_date = get_field('issue_date') ?? 'hu' !== COUNTRY ? frontend_translate_date(get_field('issue_date'), 'M Y') : null;


/**
 * Referrer of the site
 * @var string
 */
$referrer = $_SERVER['HTTP_REFERER'] ?? null;

/**
 * The URL to direct to when pressing on the back button
 * @var string
 */
$back_button_url = get_post_type_archive_link(get_post_type(get_the_ID()));

if ($referrer && str_contains($referrer, 'magazine') || str_contains($referrer, 'premium')) {
	$back_button_url = $referrer;
}

/**
 * The URL that leads to the subscription page
 * @var string
 */
$url_sub = get_field('url_sub');

/**
 * The URL that leads to the shop page of the magazine
 * @var string
 */
$url_issue = get_field('url_issue');

/**
 * The related tag
 * @var array|string
 */
$related_tags = get_field('related_posts');

/**
 * Reviews of the magazine
 * @var array
 */
$reviews = get_field('reviews');

/**
 * The year the post was published in
 * @var string|null
 */
$publish_year = $raw_date ? intval(date('Y', strtotime($raw_date))) : null;

/**
 * The month the post was published in
 * @var string|null
 */
$publish_month = $raw_date ? intval(date('m', strtotime($raw_date))) : null;

/**
 * The ID of the premium tag
 * @var string
 */
$premium_tag = get_field('premium_tag', 'option') ?? '';

/**
 * The label to override the default publish month label with
 * @var string
 */
$label = get_field('label') ?? null;

get_header();
?>

<main class="magazine-single">

	<div class="container">

		<div class="row">
			<div class="col-12">
				<?php if(COUNTRY != 'sk') : ?>
					<a href="<?= $back_button_url; ?>" class="magazine-single__back-button button button--medium button--primary after-icon after-icon--back-arrow"><?= esc_html__('Back', 'FORBES'); ?></a>
				<?php endif; ?>
			</div>
		</div>

		<div class="row h-flex--sm--column-reverse">

			<div class="col-12 col-md-6 col-lg-7 h-d--lg--flex h-flex--lg--column h-justify-content--lg--center">

				<?php if ($issue_date) : ?>

					<h4 class="magazine-single__publish-date"><?= $label ?? ucfirst($issue_date); ?></h4>

				<?php endif; ?>

				<?php $magazineTitle = new Heading(get_the_title(), 'h1', null, '700', 'noto-serif'); echo $magazineTitle->render(); ?>

				<?php if ($url_issue) : ?>

					<a class="magazine-single__button button button--large button--primary cta-link-tag" href="<?= $url_issue; ?>" rel="noopener" target="_blank"><?= strtoupper(esc_html_e('Buy this issue', 'FORBES') ?? ''); ?></a>

				<?php endif; ?>

				<?php if ($url_sub) : ?>

					<a class="magazine-single__button magazine-single__button--issue button button--large button--primary cta-link-tag" href="<?= $url_sub; ?>" rel="noopener" target="_blank"><?= strtoupper(esc_html_e('Buy subscription', 'FORBES') ?? ''); ?></a>

				<?php endif; ?>

				<div class="magazine-single__content justDesktop">

					<?= get_the_content(); ?>

				</div>

			</div>

			<div class="col-12 col-md-6 col-lg-5 magazine-single__container">

				<div class="magazine-single__wrapper h-d--flex h-justify-content--center h-align-items--center" style="background-color: <?= get_field('background_color') ? get_field('background_color') : ''; ?>">

					<div class="magazine-single__image-wrapper">

						<img src="<?= get_the_post_thumbnail_url(null, 'medium'); ?>" alt="alt-text" class="h-width--full">

					</div>

				</div>

			</div>

		</div>

		<div class="justPhone magazine-single__content-mobile">

			<div class="row">

				<div class="h-col--12">

					<?= get_the_content(); ?>

				</div>

			</div>

		</div>

		<?php if (!empty($reviews)) : ?>

			<div class="row">

				<div class="col-12">

					<div class="magazine-single__reviews">

						<h4 class="magazine-single__reviews-title"><?= esc_html__('Reviews', 'FORBES'); ?></h4>

						<div class="magazine-single__reviews-wrapper">

							<?php foreach ($reviews as $key => $review) : ?>

								<?php get_template_part('template-parts/magazine-review/index', null, ['review' => $review]) ?>

								<?php if (count($reviews) > 1 && $key !== count($reviews)) : ?>

									<div class="magazine-single__separator"></div>

								<?php endif; ?>

							<?php endforeach; ?>

						</div>

					</div>

				</div>

			</div>

		<?php endif; ?>

		<div class="row">

			<div class="col-12">

				<div class="magazine-single__related-posts">

					<?php
					$related_posts_text = esc_html__('Articles from this issue', 'FORBES');
					$related_post_text_fields = get_field('magazine_single', 'option');
					if ($related_post_text_fields) {
						$related_posts_text = $related_post_text_fields['related_posts_text'];
						$related_web_exclusives_text = $related_post_text_fields['related_web_posts_text'];
					}
					?>

					<?php if ($related_tags && $raw_date) : ?>

						<?php
						$paged1 = isset($_GET['paged1']) ? (int) $_GET['paged1'] : 1;

						$args = array(
							'posts_per_page'	=> 4,
							'orderby'			=> 'date',
							'order'				=> 'DESC',
							'paged'				=> $paged1,
							'tax_query'			=> array(
								'relation'		=> 'AND',
								array(
									'taxonomy'	=> CATEGORY_TYPE,
									'field'		=> 'term_id',
									'terms'		=> $related_tags
								),
								array(
									'field'		=> 'term_id',
									'terms'		=> $premium_tag,
									'taxonomy'	=> CATEGORY_TYPE
								)
							),
						);

						$related_posts_query = new WP_Query($args);
						?>

						<h4 class="magazine-single__related-title"><?= $related_posts_text; ?></h4>

						<div class="magazine-single__related-posts-grid">

							<?php if ($related_posts_query->have_posts()) : ?>

								<?php while ($related_posts_query->have_posts()) : $related_posts_query->the_post(); ?>

									<?php get_template_part('template-parts/article-card/index', null, array('post_id' => get_the_ID())); ?>

								<?php endwhile; ?>

							<?php endif; ?>

						</div>


						<?php if ($related_posts_query->found_posts > 4) : ?>

							<?php get_template_part('template-parts/pagination/index', null, ['posts_found' => $related_posts_query->found_posts, 'paged' => $paged1, 'max_num_pages' => $related_posts_query->max_num_pages, 'is_single_magazine' => true, 'pagination_string' => 'paged1']) ?>

						<?php endif; ?>

					<?php endif; ?>

				</div>

			</div>

		</div>

		<?php if ($raw_date) : ?>

			<div class="row">

				<div class="col-12">

					<div class="magazine-single__related-web-exclusives">

						<?php
						$paged2 = isset($_GET['paged2']) ? (int) $_GET['paged2'] : 1;
						$args = array(
							'posts_per_page'	=> 4,
							'paged'				=> $paged2,
							'post_type'			=> 'post',
							'post_status'		=> 'publish',
							'orderby'			=> 'date',
							'order'				=> 'DESC',
							'tax_query'			=> array(
								array(
									'field'		=> 'term_id',
									'terms'		=> $premium_tag,
									'taxonomy'	=> CATEGORY_TYPE
								)
							),
							'meta_query'		=> array(
								array(
									'key'		=> 'premium_type',
									'value'		=> 'online'
								)
							),
							'date_query'	=> array(
								array(
									'year'  => $publish_year,
									'month' => $publish_month
								)
							),
						);


						$related_web_exclusives = new WP_Query($args);
						?>

						<?php if ($related_web_exclusives->have_posts()) : ?>

							<h4 class="magazine-single__related-web-title"><?= $related_web_exclusives_text; ?></h4>

							<div class="magazine-single__related-web-posts-grid">

								<?php while ($related_web_exclusives->have_posts()) : $related_web_exclusives->the_post(); ?>

									<?php get_template_part('template-parts/article-card/index', null, array('post_id' => get_the_ID())); ?>

								<?php endwhile; ?>


							</div>


							<?php if ($related_web_exclusives->found_posts > 4) : ?>

								<?php get_template_part('template-parts/pagination/index', null, ['posts_found' => $related_web_exclusives->found_posts, 'paged' => $paged2, 'max_num_pages' => $related_web_exclusives->max_num_pages, 'is_single_magazine' => true, 'pagination_string' => 'paged2']) ?>

							<?php endif; ?>

						<?php endif; ?>

					</div>

				</div>

			</div>

		<?php endif; ?>

	</div>

</main>

<?php get_footer(); ?>
