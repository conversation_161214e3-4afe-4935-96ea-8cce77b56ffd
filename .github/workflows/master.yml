name: Build & Transfer to S3

on:
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    container:
      image: node:20

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install dependencies
        run: npm install

      - name: Build project
        run: |
          npm run build
          npx gulp build

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            style.css
            editor.css
            blocks.css
            lists.css
            single.css
            my-account.css
            editor-blocks.css
            blocks-non-critical.css
            style-non-critical.css
            minified-js/
          retention-days: 1

      - name: Clone React App Repository
        uses: actions/checkout@v4
        with:
          repository: 'MediaRey/forbes-react'
          token: ${{ secrets.REPO_B_ACCESS_TOKEN }}
          ref: 'main'
          path: 'forbes-react'

      - name: Install React App Dependencies
        run: npm install
        working-directory: ./forbes-react

      - name: Build React App
        run: CI=false npm run build
        working-directory: ./forbes-react
        env:
          REACT_APP_WP_BASE_URI: 'https://stage2.forbes.cz'
          REACT_APP_CRM_BASE_URI: 'https://stage-predplatne.forbes.cz'
          REACT_APP_WP_ADMIN_URI: 'wp-admin/admin-ajax.php'
          REACT_APP_WP_JSON_URI: 'wp-json/wp/v2'
          REACT_APP_AI_BASE_URI: 'https://forbes-ai-gynmz.ondigitalocean.app/api/v1'
          REACT_APP_COUNTRY: cz

      - name: Move React Build Output
        run: mv ./forbes-react/build ./react-app

      - name: Extract Build Hash
        run: |
          # Extract the build hash from the main.js filename in asset-manifest.json
          BUILD_HASH=$(grep -oP 'main\.\K[0-9a-f]+(?=\.js)' asset-manifest.json | head -1)
          echo "Extracted BUILD_HASH: $BUILD_HASH"

          # Create build-hash.php with the extracted hash
          echo "<?php" > build-hash.php
          echo "\$BUILD_HASH = \"$BUILD_HASH\";" >> build-hash.php

        working-directory: ./react-app

      - name: Upload React App Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: react-app-build
          path: react-app

  cache-to-aws:
    runs-on: ubuntu-latest
    needs: build

    steps:
      - uses: actions/checkout@v4

      - name: Download WP Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: .

      - name: Download React App Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: react-app-build
          path: react-app

      - name: Prepare AWS CLI
        run: |
          sudo apt-get update -qq
          sudo apt-get install -qq awscli
          aws configure set aws_access_key_id ${{ secrets.S3_UPLOADS_KEY }}
          aws configure set aws_secret_access_key ${{ secrets.S3_UPLOADS_SECRET }}
          aws configure set default.region eu-central-1

      - name: Cache to S3
        run: |
          FILE_NAME=frontend-${{ github.ref_name }}.tar.gz
          tar --exclude-vcs -czf /tmp/$FILE_NAME . react-app
          aws s3 cp /tmp/$FILE_NAME s3://forbes-modules/splendex/forbes/$FILE_NAME
