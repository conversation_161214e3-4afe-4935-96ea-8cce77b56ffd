name: Production

on:
  push:
    branches:
      - production

jobs:
  Build:
    runs-on: ubuntu-22.04
    container:
      image: sleavely/node-awscli:20.x

    steps:
      - uses: actions/checkout@v4

      - name: Init cache for npm
        uses: actions/cache@v4
        with:
          path: .npm
          key: npm-cache-${{ github.ref_name }}-${{ hashFiles('./package-lock.json') }}

      - name: Install dependencies
        run: npm ci --cache .npm --silent --no-progress --no-audit --no-fund --prefer-offline

      - name: Build project
        run: |
          npm run build
          npx gulp build

      - name: Fetch POEditor Translations
        run: |
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773270 -d language=cs -d type=mo )" )" > languages/cs_CZ.mo
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773270 -d language=sk -d type=mo )" )" > languages/sk_SK.mo
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773270 -d language=hu -d type=mo )" )" > languages/hu_HU.mo
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773272 -d language=hu -d type=key_value_json )" )" > react/src/localization/translations-hu.json
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773272 -d language=cs -d type=key_value_json )" )" > react/src/localization/translations-cz.json
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773272 -d language=sk -d type=key_value_json )" )" > react/src/localization/translations-sk.json
          cp react/src/localization/translations-hu.json languages/hu.json
          cp react/src/localization/translations-cz.json languages/cz.json
          cp react/src/localization/translations-sk.json languages/sk.json
          ls -lah languages
          ls -lah react/src/localization

      - name: Init cache for React App
        id: react-cache
        uses: actions/cache@v4
        with:
          path: react-app
          key: react-cache-${{ github.ref_name }}-${{ hashFiles('./react/**') }}

      - name: Install React App dependencies
        if: steps.react-cache.outputs.cache-hit != 'true'
        working-directory: ./react
        run: npm ci --silent --no-progress --no-audit --no-fund --prefer-offline

      - name: Build React App
        if: steps.react-cache.outputs.cache-hit != 'true'
        working-directory: ./react
        run: npm run build
        env:
          REACT_APP_WP_ADMIN_URI: 'wp-admin/admin-ajax.php'
          REACT_APP_WP_JSON_URI: 'wp-json/wp/v2'
          REACT_APP_AI_BASE_URI: 'https://stage-ai.forbesmedia.cz/api/v1'

      - name: Move React App to main project
        if: steps.react-cache.outputs.cache-hit != 'true'
        run: mv ./react/build ./react-app

      - name: Extract Build Hash
        working-directory: ./react-app
        run: |
          # Extract the build hash from the main.js filename in asset-manifest.json
          BUILD_HASH=$(grep -oP 'main\.\K[0-9a-f]+(?=\.js)' asset-manifest.json | head -1)
          echo "Extracted BUILD_HASH: $BUILD_HASH"

          # Create build-hash.php with the extracted hash
          echo "<?php" > build-hash.php
          echo "\$BUILD_HASH = \"$BUILD_HASH\";" >> build-hash.php

          # Copy main.$BUILD_HASH.js to main.js
          cp "./static/js/main.$BUILD_HASH.js" "./static/js/main.js"

      - name: Prepare AWS CLI
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.S3_UPLOADS_KEY }}
          aws-secret-access-key: ${{ secrets.S3_UPLOADS_SECRET }}
          aws-region: eu-central-1

      - name: Cache to S3
        run: |
          FILE_NAME=frontend-${{ github.ref_name }}.tar.gz
          tar --exclude=./node_modules --exclude=./react --exclude=./.npm --exclude=./plugins --exclude-vcs -czf /tmp/$FILE_NAME . react-app
          aws s3 cp /tmp/$FILE_NAME s3://forbes-modules/splendex/forbes/$FILE_NAME

      - name: Trigger GitLab Pipeline
        run: |
          curl --silent --request POST --form token=${{ secrets.CZ_DEPLOY_TOKEN }} --form ref=production "https://gitlab.com/api/v4/projects/35928644/trigger/pipeline"
          curl --silent --request POST --form token=${{ secrets.SK_DEPLOY_TOKEN }} --form ref=production "https://gitlab.com/api/v4/projects/35928619/trigger/pipeline"
          curl --silent --request POST --form token=${{ secrets.HU_DEPLOY_TOKEN }} --form ref=production "https://gitlab.com/api/v4/projects/31115250/trigger/pipeline"

  SonarQube:
    name: SonarQube Analysis
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis
      - uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
