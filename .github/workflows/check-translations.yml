name: Check Translations

on:
  schedule:
    # Every 30 minutes
    - cron: '*/30 * * * *'

jobs:
  check-translations:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    # For private repos
    permissions:
      actions: write

    steps:
      - name: Fetch POEditor Translations
        run: |
          mkdir -p lang
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773270 -d language=cs -d type=mo )" )" > ./lang/cs_CZ.mo
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773270 -d language=sk -d type=mo )" )" > ./lang/sk_SK.mo
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773270 -d language=hu -d type=mo )" )" > ./lang/hu_HU.mo
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773272 -d language=hu -d type=key_value_json )" )" > ./lang/translations-hu.json
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773272 -d language=cs -d type=key_value_json )" )" > ./lang/translations-cz.json
          curl -s "$( node -e 'console.log(JSON.parse(process.argv[1]).result.url)' "$( curl -s -X POST https://api.poeditor.com/v2/projects/export -d api_token="${{ secrets.POEDITOR_API_TOKEN }}" -d id=773272 -d language=sk -d type=key_value_json )" )" > ./lang/translations-sk.json

      - name: Init Cache
        id: cache
        uses: actions/cache@v4
        with:
          path: lang
          key: cache-${{ runner.os }}-${{ hashFiles('lang/**') }}'

      - name: Trigger Workflow
        if: steps.cache.outputs.cache-hit != 'true'
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: develop.yml
          ref: develop
          token: ${{ secrets.GITHUB_TOKEN }}
