<?php

/**
 * Template Name: Newsletter (Legacy)
 */

/**
 * The description of the page
 * @var string
 */
$page_description = get_field('page_description') ?? '';

/**
 * The notice below the signup form
 * @var string
 */
$notice = get_field('notice') ?? '';

/**
 * The title of the signup form
 */
$title = get_field('title') ?? '';

/**
 * The block's description
 * @var string
 */
$form_description = get_field('description') ?? '';

/**
 * The consent text
 * @var string
 */
$consent = get_field('consent') ?? '';

get_header(); ?>

<main class="page-newsletter">

	<div class="container">

		<div class="row">

			<div class="col-12 col-lg-8">

				<?php if (!get_field('hide_title')) : ?>
					<h1 class="page-newsletter__title"><?php the_title(); ?></h1>
				<?php endif; ?>

				<?php if ($page_description) : ?>

					<div class="page-newsletter__description"><?= $page_description; ?></div>

				<?php endif; ?>

				<div class="page-newsletter__signup-wrapper">

					<?php switch (COUNTRY) {
						case 'hu':
						case 'sk':
							get_template_part('template-parts/newsletter/index', null, ['title' => $title, 'description' => $form_description, 'consent' => $consent]);
							break;

						case 'cz':
							/**
							 * The types of newsletters
							 * @var array(string)
							 */
							$newsletters = ['espresso', 'cocktail', 'cryptoshot'];

							/**
							 * Random number to get a random newsletter
							 * @var int
							 */
							$random = rand(0, 2);

							/**
							 * The randonly chosen newsletter to render
							 * @var string
							 */
							$current_newsletter = $newsletters[$random];
							get_template_part('blocks/acf-blocks/home-page-blocks/newsletter/CZ/index');
							break;
					} ?>

				</div>

				<?php if ($notice) : ?>

					<span class="page-newsletter__notice caption"><?= strip_tags($notice); ?></span>

				<?php endif; ?>

			</div>

			<div class="col-hidden col-lg-4">

				<div class="page-newsletter__ad-container googlead-container">
					<div id="newsletter-ad-desktop" class="googlead"></div>
				</div>

			</div>

		</div>

	</div>

</main>

<?php get_footer(); ?>
