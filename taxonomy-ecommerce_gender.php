<?php

$genderSlug = get_query_var('term');
$gender = ForbesEcommerceSync\Gender::getBySlug($genderSlug);

if (!$gender) {
	// TODO: Redirect to brands page?
	wp_redirect(get_home_url());
	exit;
}

// Získanie page parametra z URL
$currentPage = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$itemsPerPage = 20;

// Výpočet celkového počtu produktov na načítanie (page * itemsPerPage)
$totalItemsToLoad = $currentPage * $itemsPerPage;

$productsQuery = (new ForbesEcommerceSync\ProductQuery())
	->setGender($gender)
	->setPerPage($totalItemsToLoad)
	->setSortBy($_GET['sortBy'] ?? null)
	->setSortOrder($_GET['sortOrder'] ?? null);

if (!empty($_GET['category'])) {
	$category = ForbesEcommerceSync\Category::getBySlug($_GET['category']);
	$productsQuery->setCategory($category);
}

$products = $productsQuery->fetchAll();

if ( ! empty($products)) {
    foreach ($products as $product) {
        $product->content = wp_kses_post(html_entity_decode($product->content, ENT_QUOTES));
    }
}

$categories = array_values(array_filter(
	ForbesEcommerceSync\Category::getTopLevel(),
	fn($category): bool => (new ForbesEcommerceSync\ProductQuery())
		->setCategory($category)
		->setGender($gender)
		->exists()
));

$tabs = [
	[
		'label' => __('All products', 'FORBES'),
		'link' => 'category=',
	],
];

$tabs += array_map(
	fn($gender): array => [
		'label' => $gender->name,
		'link' => "category={$gender->slug}",
	],
	$categories
);

$filters = [];

?>

<?php get_header(); ?>

<main class="product-gender-single">
	<div class="container">
		<div class="row">
			<div class="col-12">
				<div
					class="reactEcommerceGenderPage"
                    data-products="<?php echo esc_attr(wp_json_encode($products)); ?> "
                    data-gender-details="<?php echo esc_attr(wp_json_encode($gender)); ?>"
                    data-gender-filters="<?php echo esc_attr(wp_json_encode($filters)); ?>"
                    data-tabs="<?php echo esc_attr(wp_json_encode($tabs)); ?>"
                    data-current-page="<?php echo esc_attr($currentPage); ?>"
				></div>
			</div>
		</div>
	</div>
</main>

<?php get_footer(); ?>
