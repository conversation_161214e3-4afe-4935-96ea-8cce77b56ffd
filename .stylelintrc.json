{"rules": {"block-no-empty": null, "color-no-invalid-hex": true, "comment-empty-line-before": ["always", {"ignore": ["stylelint-commands", "after-comment"]}], "declaration-colon-space-after": "always", "declaration-colon-newline-after": null, "indentation": 2, "font-family-no-missing-generic-family-keyword": null, "max-empty-lines": 2, "unit-allowed-list": ["rem", "%", "s", "vh", "vw", "deg", "px", "fr"], "color-named": "never", "color-hex-length": "short", "max-nesting-depth": [5, {"ignore": ["blockless-at-rules", "pseudo-classes"]}], "declaration-block-no-redundant-longhand-properties": true, "property-case": "lower", "declaration-block-no-duplicate-properties": true, "at-rule-no-unknown": null, "length-zero-no-unit": true, "selector-pseudo-element-colon-notation": "single"}}