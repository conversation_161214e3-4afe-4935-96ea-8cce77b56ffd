import '../style.css';
import '../tailwind.css';
import '../react.css';
import '../style-non-critical.css';
import '../blocks.css';
import '../my-account.css';

/** @type { import('@storybook/react').Preview } */
const preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    darkMode: {
      stylePreview: true,
      darkClass: 'dark-mode',
      classTarget: 'html',
    },
  },
};

export const decorators = [(Story) => <div id="custom-storybook-root">{Story()}</div>];

export default preview;
