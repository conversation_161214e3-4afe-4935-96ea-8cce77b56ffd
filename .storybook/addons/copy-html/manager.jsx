import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { addons, types } from '@storybook/manager-api';
import { IconButton, WithTooltip } from '@storybook/components';
import { CopyIcon } from '@storybook/icons';

const ADDON_ID = 'copy-html-addon';
const TOOL_ID = `${ADDON_ID}/tool`;

const CopyTool = memo(function CopyTool() {
  const [message, setMessage] = useState('');
  const tooltip = useMemo(() => <div style={{ padding: message ? '4px 8px' : '' }}>{message}</div>, [message]);

  const copyHTML = useCallback(async () => {
    try {
      const iframe = document.getElementById('storybook-preview-iframe');
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      const root = iframeDoc.getElementById('custom-storybook-root');
      await navigator.clipboard.writeText(root.innerHTML.replace(/\s+/g, ' ').trim());
      setMessage('Copied :)');
    } catch (e) {
      setMessage('Error copying :(');
    }
  }, []);

  useEffect(() => {
    let timeout;

    if (message) {
      timeout = setTimeout(() => setMessage(''), 2000);
    }

    return () => clearTimeout(timeout);
  }, [message]);

  return (
    <WithTooltip key={TOOL_ID} tooltip={tooltip}>
      <IconButton title="Copy rendered HTML" onClick={copyHTML}>
        <CopyIcon />
      </IconButton>
    </WithTooltip>
  );
});

addons.register(ADDON_ID, () => {
  addons.add(TOOL_ID, {
    type: types.TOOLEXTRA,
    title: 'Copy HTML',
    render: CopyTool,
  });
});
