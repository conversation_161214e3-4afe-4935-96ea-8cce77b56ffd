/** @type { import('@storybook/react-vite').StorybookConfig } */
const config = {
  stories: ['../stories/**/*.mdx', '../stories/**/*.stories.@(js|jsx|mjs|ts|tsx)'],
  addons: [
    '@storybook/addon-essentials',
    '@storybook/addon-onboarding',
    '@storybook/addon-interactions',
    '@storybook/addon-styling-webpack',
    'storybook-dark-mode',
    'storybook-addon-deep-controls',
    './addons/copy-html',
  ],
  framework: {
    name: '@storybook/react-vite',
    options: {},
  },
  viteFinal: async (config) => {
    config.esbuild = {
      ...config.esbuild,
      jsx: 'automatic',
    };

    config.define = {
      ...config.define,
      'process.env.REACT_APP_WP_ADMIN_URI': JSON.stringify('/'),
      'process.env.REACT_APP_WP_JSON_URI': JSON.stringify('/'),
      'process.env.REACT_APP_AI_BASE_URI': JSON.stringify('/'),
    };

    return config;
  },
};

export default config;
