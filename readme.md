### Required VS Code Extensions

- [EditorConfig for VS Code](https://marketplace.visualstudio.com/items?itemName=EditorConfig.EditorConfig)

### Bundled Plugins

- ACF Extended (0.8.6.3)

### Optional Plugins in Repository

- Advanced Custom Fields Pro (5.8.12)
- WP Migrate DB Pro 1.9.13
- WP Migrate DB Pro Media Files 1.4.15
- WP Migrate DB Pro Theme Plugin Files 1.0.5
- WP Migrate DB Pro CLI 1.3.5
- Lazy Loading Feature Plugin 1.1
- WP Mail SMTP by WPForms 2.5.1

### Plugins

- 301 Redirects 2.73
- Advanced Custom Fields PRO 6.0.3
- Advanced Custom Fields: Extended 0.8.8.10
- Clean Image Filenames 1.4
- DN REMP CRM Auth 1.1.0
- DN REMP Paywall 1.1.0
- mango-gutenberg-widget 1.1 (custom)
- oEmbed Plus 1.6
- Post Type Switcher 3.2.1
- Public Post Preview 2.10.0
- Resize Image After Upload 1.8.6
- S3 Uploads 2.0.0
- SearchWP 4.1.21
- SVG Support 2.5.5
- WordPress Importer 0.8
- Yoast SEO 20.4
- Zalomení 1.4.7

### Install

- Create a local project in your Local by Flywheel
- Clone the repository to the theme folder
- In the project theme folder run `make start`
- Open site shell from Local by Flywheel instance and navigate to project theme folder and run `make setup_shell`

### Environment variables

- To run the project oyu will need to define variables in your `wp-config.php`
- AI metric/recommendation `define( 'AI_API_URL', 'https://test.forbes-ai.splendex.dev/' );`
- If you are running redis in docker `define( 'REDIS_HOST', 'localhost' );`
- Declare the country for locale translations and locale function usage `define( 'COUNTRY', 'cz' );`
- The main type of taxonomy used in your environment `define( 'CATEGORY_TYPE', 'stitek' );`

### Development

- Every content is to be developed into custom Gutenberg blocks
- Style follows BEM naming conventions
- Colors and fonts are to be stored in variable to keep to the design system
- All code is to be commented for further development

### Fonts

- Noto Serif
- Archivo
- Fontawesome 4.7.0

### Post types

- Posts -> basic articles throughout the website, differentiated by categories and tags
- Specials (obscolete) -> used for redirections from the website to external articles, now handled by basic articles
- Guides -> Used for promotion pages
- Products -> post type for promotion pages, leading to external link (there is no webshop on the site)
- Quotes -> used for custom gutenberg selection, post is requested by Forbes for data structure
- Magazines -> Used for physical magazine display on the website
- Self Promo -> Used for gutenberg block display only
- Events -> Post types for Forbes event display
- Lists -> Used for rankings throughout and excel service

### Lists

It uses google docs API to get necessary data throughout a google service account

### Redis connection

To configure a Redis connection for the excluded and most read posts you need to add a new configuration entry to the _wp-config.php_ file. (note: the host.docker.internal value is just an example, it may vary on a non development environment)

```
define( 'REDIS_HOST', 'host.docker.internal' );
```

To start a Redis instacne with a Redis commander just simply run the `docker compose up` command on the root of the template directory and it will start the two services.

Redis commander UI: localhost:8081

### Merge Request Template

Másold be a JIRA issue linkjét, vagy foglald össze röviden mit tartalmaz ez az MR, ha nem volt hozzá! 🤘

-

Teszteltem a változtatásokat Chrome, Safari és Firefox böngészőn asztali felbontásokon. ￼🕺

-

Teszteltem a változtatásokat Mobil és Tablet nézetben is (böngésző responsive view). 📱

-

Csatoltam egy videót a blokk működéséről asztali verzión és egy videót a kinézetéről mobilon, vagy veszek az Andrénak 4 sört. 🍺

-

Átnéztem a Commits és Changes tabot és megbizonyosodtam róla, hogy minden változtatásom megjelenik és nincs benne más. 🐐

-

Amennyiben blokkot fejlesztettem, mindenben követtem az [itt leírt] fejlesztési elvárásokat. 📚

-

Amennyiben blokkot fejlesztettem, megbizonyosodtam róla, hogy a Layout ID egyedi, és minden blokkom működik. 🦄

-

JIRA-n elvégeztem minden szükséges adminisztrációt (idő logolás, státusz) 📝

-

Ide bemásolom a tesztelési forgatókönyv pontjait amin végigmentem a feladatom tesztelése során 👀

-
