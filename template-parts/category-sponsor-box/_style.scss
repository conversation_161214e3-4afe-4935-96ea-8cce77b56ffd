.category-sponsor {
  margin-top: $spacing-07-32;
  margin-bottom: -1.6rem;

  &__sponsor-wrapper {
    display: flex !important;
    align-items: center;
    margin-top: $spacing-02;
    text-decoration: none;
  }

  &__logo-wrapper {
    width: 6.4rem;
    height: 6.4rem;
    margin-right: $spacing-03-12;
  }

  &__logo {
    object-fit: contain;
    height: 100%;
  }

  &__sponsor-name {
    color: $color-text-primary;
    margin-bottom: $spacing-00;
  }

  &--mobile {
    display: none;
    justify-content: center;
    margin-bottom: $spacing-00;

    .category-sponsor {
      &__sponsor-wrapper {
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      &__logo-wrapper {
        margin-right: $spacing-00;
        margin-bottom: $spacing-02;
      }

      &__sponsor-name {
        margin-bottom: $spacing-00;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    display: none;

    &--mobile {
      display: flex;
    }
  }
}
