<?php
$sponsor = $args['sponsor'];
$text = $args['sponsor_text'];
$link = $args['sponsor_link'];
$placement = $args['placement'];

$logo = get_field('company_logo', 'user_' . $sponsor) ? get_field('company_logo', 'user_' . $sponsor) : get_field('profile_picture', 'user_' . $sponsor);

?>

<?php if ($sponsor && $text) : ?>

	<div class="category-sponsor <?php if ('bottom' === $placement) : echo 'category-sponsor--mobile';
									endif; ?>">

		<div class="category-sponsor__wrapper">

			<span class="category-sponsor__text callout">
				<?= strip_tags($text); ?>
			</span>


			<?php if ($link) : ?>
				<a href="<?= $link; ?>" target="_blank" rel="noopener" class="category-sponsor__sponsor-wrapper">
				<?php else : ?>
					<div class="category-sponsor__sponsor-wrapper">
					<?php endif; ?>

					<div class="category-sponsor__logo-wrapper">
						<?= wp_get_attachment_image($logo, 'thumbnail', false, ['class' => 'category-sponsor__logo', 'alt' => get_user_by('id', $sponsor)->display_name]); ?>
					</div>

					<span class="category-sponsor__sponsor-name subtitle">
						<?= get_user_by('id', $sponsor)->display_name; ?>
					</span>

					<?php if ($link) : ?>
				</a>
			<?php else : ?>
		</div>
	<?php endif; ?>

	</div>

	</div>

<?php endif; ?>
