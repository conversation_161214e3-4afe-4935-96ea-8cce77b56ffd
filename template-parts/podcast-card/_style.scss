.podcast-card {
  text-decoration: none;
  color: initial;
  display: flex;
  align-items: flex-start !important;

  &__image-wrapper {
    margin-right: $spacing-04-16;
    min-width: 7.9rem;
    max-width: 7.9rem;
    height: 7.8rem;
  }

  &__image {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }

  &__content-wrapper {
    display: flex;
    flex-direction: column;
  }

  &__category {
    display: block;
    color: $color-link-visited;
    margin-bottom: $spacing-02;
    transition: color 0.3s ease;
    font-weight: 700 !important;
    font-size: 1.2rem !important;
    line-height: 1.4rem !important;

    &:before {
      content: '';
      width: 2rem;
      display: inline-block;
      margin-right: $spacing-02;
      border-bottom: 0.1rem solid $color-link-visited;
      transform: translateY(-0.4rem);
    }

    @media (hover: hover) {
      &:hover {
        color: $color-text-brand;
      }
    }
  }

  &__title {
    margin-bottom: $spacing-02;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
    font-size: 1.6rem;
    font-weight: 500;
    font-family: $font-archivo !important;
    line-height: 2.4rem;
    color: $color-text-primary;
    transition: color 0.3s ease;

    @media (hover: hover) {
      &:hover {
        color: $color-text-brand;
      }
    }
  }

  &__title-link {
    text-decoration: none;
  }

  &__details {
    display: flex;
    align-items: center;
  }

  &__publish-date,
  &__length {
    color: $color-text-secondary;
    margin-bottom: $spacing-00;
    font-family: $font-archivo !important;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__image-wrapper {
      min-width: 3rem;
      max-width: 3rem;
      margin-right: $spacing-02;
      height: auto;
    }

    &__category {
      margin-bottom: $spacing-01;
    }

    &__title {
      margin-bottom: $spacing-01;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__image-wrapper {
      min-width: 4.8rem;
      max-width: 4.8rem;
      margin-right: $spacing-04-16;
      height: auto;
    }

    &__image {
      min-width: 100%;
      max-width: 100%;
      height: auto;
    }

    &__category {
      margin-bottom: $spacing-02;
    }
  }
}
