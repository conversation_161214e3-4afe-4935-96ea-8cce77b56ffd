<?php

/**
 * The ID for the podcast
 * @var int
 */
$id = $args['id'] ?? null;

/**
 * The ID for the podcast's thumbnail
 * @var int
 */
$image = 'cz' === COUNTRY ? ('0' !== get_field('podcastImage', $id) ? get_field('podcastImage', $id) : get_field('primaryImage', $id)) : get_field('podcast_image', $id);

/**
 * The length of the podcast (from ACF)
 * @var string
 */
$podcast_length = 'cz' === COUNTRY ? get_field('reading_time', $id) : get_field('podcast_length', $id);

/**
 * Either the category or if CZ the tag
 * @var string
 */
$term = get_the_category($id) ? get_the_category($id)[0] : null;

if ('cz' === COUNTRY) {
	$term = frontend_get_primary_tag($id);
}
?>

<a href="<?= get_permalink($id); ?>" class="podcast-card">

	<?php if ($image) : ?>

		<div class="podcast-card__image-wrapper">
			<?= wp_get_attachment_image( $image, 'home_podcasts', false,
				array(
					'class'  => 'podcast-card__image',
					'alt'    => get_the_title( $id ),
					'srcset' => wp_get_attachment_image_url( $image, 'home_podcasts2x' ) . ' 2x'
				) ); ?>
		</div>

	<?php endif; ?>

	<div class="podcast-card__content-wrapper">
		<div class="podcast-card__tag-wrapper">
			<?php
				$tag = new Tag(
					text: $term?->name,
				);
				echo $tag->render();
			?>
		</div>

		<h5 class="podcast-card__title"><?= get_the_title($id); ?></h5>

		<div class="podcast-card__details">

			<?php
			$date;
			switch (COUNTRY) {
				case 'hu':
					$date = get_the_date('M d.');
					break;
				case 'sk':
					$date = get_the_date('d. M');
					break;
				case 'cz':
					$date = frontend_get_publish_time($id, false, true);
			}
			?>



			<?php if ($podcast_length) : ?>

				<?php

					if(COUNTRY == 'sk') {
						$podcast_length .= ' ';
						$podcast_length .= esc_html__(' minutes', 'FORBES');
					}
				?>

				<span class="podcast-card__length caption"><?= $podcast_length; ?></span>

			<?php endif; ?>

		</div>

	</div>

</a>
