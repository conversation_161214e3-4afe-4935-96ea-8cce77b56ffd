<?php

// TODO: Refactor with new author components

/**
 * The author object
 * @var object
 */
$author = $args['author'] ?? null;

/**
 * The id of the author
 * @var int
 */
$author_id = $author->term_id ?? null;

/**
 * The image of the author
 * @var string
 */
$author_image = frontend_get_user_profile_picture($author_id, true);

/**
 * The name of the author
 * @var string
 */
$author_name = $author?->name ?? null;

/**
 * The url to the author's archive page
 * @var string
 */
$author_link = get_term_link($author->term_id, 'coauthor');

/**
 * Check if the author has a BrandVoice account
 */
$is_brandvoice = 'cz' === COUNTRY ? get_field('isBrandvoice', 'coauthor_' . $author->term_id) : false;
?>

<div class="author-result h-d--md--flex h-justify-content--md--center">

	<a class="author h-text-decoration--none" href="<?= $author_link; ?>">

		<div class="author__wrapper h-d--flex h-justify-content--start h-align-items--center h-flex--row h-flex--md--column h-justify-content--md--between">

			<div class="author__avatar-wrapper <?= $is_brandvoice ? 'brandvoice-author' : '' ?> h-d--flex h-align-items--baseline h-justify-content--md--center">

				<img class="author__avatar" src="<?= $author_image; ?>" alt="<?= $author_name; ?>" loading="lazy">

			</div>

			<div class="author__content-wrapper">

				<span class="author__name link link--medium"><?= $author_name; ?></span>

			</div>

		</div>

	</a>

</div>
