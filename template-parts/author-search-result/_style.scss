.author-result {
  width: 45%;
  margin-bottom: $spacing-05-20;
  margin-right: $spacing-02;

  .author {
    color: $color-text-primary;

    &__avatar-wrapper {
      height: 5rem;
      width: auto;
      border-radius: 50%;
      overflow: hidden;
      margin-right: $spacing-05-20;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    margin-right: $spacing-01;
    margin-left: $spacing-01;

    .author {
      &__content-wrapper {
        .subtitle {
          font-size: 1.6rem;
        }
      }

      &__avatar-wrapper {
        margin-right: $spacing-00;
      }
    }
  }
}
