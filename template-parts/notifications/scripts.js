document.addEventListener('DOMContentLoaded', function () {
  (function ($) {
    class ForbesNotifications {
      constructor() {
        this.notificationContainer = $('.notifications');

        this.init();
      }

      init() {
        if (!this.notificationContainer || !this.notificationContainer.length > 0) {
          $('body div').last().after('<div id="notifications"></div>');

          this.notificationContainer = $('.notifications');
        }
      }

      showNotifications(messages) {
        if (!messages || !messages.length) {
          return;
        }

        // Clear previous notifications
        this.notificationContainer.empty();

        // Generate snackbars
        messages.forEach((message) => {
          this.notificationContainer.append(`<div class="snackbar">${message}</div>`);
        });

        // Show
        this.notificationContainer.addClass('show');

        // Hide after 3 seconds
        setTimeout(() => {
          this.notificationContainer.removeClass('show');
        }, 3000);
      }
    }

    window.ForbesNotifications = window.ForbesNotifications || new ForbesNotifications();
  })(jQuery);
});
