/* The snackbar - position it at the top and in the middle of the screen */
.notifications {
  visibility: hidden;
  position: fixed;
  z-index: 999999;
  left: 50%;
  transform: translateX(-50%);
  top: 3rem;

  .snackbar {
    padding: $spacing-03-12 $spacing-04-16;
    background-color: $color-surface-invert;
    color: $color-surface-primary;
    font-family: $font-archivo;
    font-size: 1.6rem;
    font-weight: 500;
    line-height: 1.6rem;
    margin-bottom: $spacing-02;
    display: flex;
    align-items: center;

    &:last-child {
      margin-bottom: $spacing-00;
    }
  }
}

.notifications.show {
  visibility: visible;
  -webkit-animation:
    fadein 0.5s,
    fadeout 0.5s 2.5s;
  animation:
    fadein 0.5s,
    fadeout 0.5s 2.5s;
}

/* Animations to fade the snackbar in and out */
@-webkit-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@-webkit-keyframes fadeout {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeout {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
