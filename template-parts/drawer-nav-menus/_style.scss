.drawer-handheld-menu {
  .react-toggle {
    margin: $spacing-00;
  }

  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 90vw;
  max-width: 50rem;
  overflow: hidden;
  background-color: $color-surface-primary;
  z-index: 5;
  transition: transform 0.3s ease-in;
  transform: translateX(100%);
  padding-top: $spacing-00;

  .link {
    font-size: 2.2rem;
    line-height: 2.904rem;
    font-variation-settings: 'wdth' 88;

    @media (hover: hover) {
      &:hover {
        text-decoration: underline;
        color: $color-text-primary !important;
      }
    }
  }

  .main-menu {
    height: 100%;
    max-width: 50rem;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__menu {
    margin-bottom: $spacing-08-40;

    &.logged-in {
      .drawer-handheld-menu__login-wrapper {
        justify-content: center;
      }
    }
  }

  .submenu {
    overflow: hidden;
    box-sizing: content-box;
    max-height: 0;
    padding-left: $spacing-06-24;
    display: flex;
    flex-direction: column;
    gap: $spacing-06-24;

    .drawer-handheld-menu {
      &__menu-item {
        display: flex;

        .link {
          font-size: 1.6rem;
          line-height: 2.24rem;
          font-variation-settings: 'wdth' 100;
        }
      }
    }

    @include submenu-animation;

    &.open {
      max-height: 100rem;
      margin-top: $spacing-07-32;
    }
  }

  &__header {
    display: flex;
    justify-content: flex-end;
    padding: $spacing-08-40;
  }

  &__login-wrapper {
    margin: $spacing-00 $spacing-08-40 $spacing-06-24;
    display: flex;
    justify-content: flex-start;
  }

  &__search-wrapper {
    margin: $spacing-00 $spacing-08-40 $spacing-08-40;
    padding-bottom: $spacing-08-40;
    display: flex;
    justify-content: flex-start;
    border-bottom: 0.1rem solid $color-divider;
    cursor: pointer;
  }

  &__search-label {
    display: flex;
    margin-left: $spacing-01;
  }

  &__login-wrapper,
  &__search-wrapper {
    @media (hover: hover) {
      &:hover {
        .link {
          text-decoration: underline;
          color: $color-text-primary !important;
        }
      }
    }
  }

  &__login {
    display: flex;
  }

  &__login-icon-wrapper {
    @include flexbox-properties;
  }

  &__user-wrapper {
    margin-inline: $spacing-08-40;
    margin-bottom: $spacing-06-24;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-decoration: none;

    @media (hover: hover) {
      &:hover {
        .link {
          text-decoration: underline;
        }
      }
    }
  }

  &__user-info {
    display: flex;
    align-items: center;
    gap: $spacing-01;
    max-width: calc(100% - 2.8rem);
    overflow: hidden;
  }

  &__user-avatar {
    width: 2.4rem;
    height: 2.4rem;
    border-radius: 50%;
    object-fit: cover;
  }

  &__user-name {
    margin-bottom: $spacing-00;
    overflow: hidden;
    max-width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__footer {
    padding: $spacing-00 $spacing-08-40 $spacing-08-40;
  }

  &__main {
    flex: 1;
  }

  &__night-label {
    transition: color 0.3s ease;
  }

  &__favicon {
    margin-right: $spacing-01;
    width: 2.4rem;
    height: 2.4rem;
    object-fit: contain;

    &--dark {
      display: none;
    }
  }

  &__menu-item-list {
    display: flex;
    flex-direction: column;
    gap: $spacing-06-24;
    padding-bottom: $spacing-08-40;

    &--middle {
      margin: $spacing-00 $spacing-08-40 $spacing-08-40;
      padding-bottom: $spacing-00;
    }

    &--simple {
      margin: $spacing-00 $spacing-08-40 $spacing-08-40;
      border-bottom: 0.1rem solid $color-divider;
    }

    &--highlight {
      background-color: $color-text-primary;
      padding: $spacing-08-40;

      .link {
        color: $color-white !important;
        font-size: 2.2rem;
        line-height: 2.904rem;
        font-weight: 600;
        text-decoration: none;

        @media (hover: hover) {
          &:hover {
            color: $color-white !important;
          }
        }
      }

      .submenu-indicator {
        &:after,
        &:before {
          background-color: $color-white !important;
        }
      }
    }
  }

  &__menu-item {
    position: relative;

    .link {
      width: 100%;
    }

    .submenu-opener {
      display: inline-flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .submenu-indicator {
        display: inline-block;
      }

      &.open {
        .submenu-indicator {
          &:after {
            visibility: hidden;
          }

          &:before {
            width: 0.15rem;
            height: 1.6rem;
            transform: translate(-50%, -50%) rotate(90deg);
          }
        }
      }
    }

    .submenu-indicator {
      position: relative;
      display: none;
      width: 2.4rem;
      height: 2.4rem;

      &:after,
      &:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        background-color: $color-text-primary;
        transform: translate(-50%, -50%);
        transition: transform 0.3s ease;
      }

      &:before {
        width: 1.6rem;
        height: 0.15rem;
      }

      &:after {
        width: 0.15rem;
        height: 1.6rem;
      }

      &.open {
        &:after {
          visibility: hidden;
        }

        &:before {
          width: 0.15rem;
          height: 1.6rem;
          transform: translate(-50%, -50%) rotate(90deg);
        }
      }
    }

    &--dark-mode {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
    }

    &--country-link {
      .drawer-handheld-menu {
        &__country-link-flag {
          width: 2rem;
          height: 1.5rem;
          margin-right: $spacing-02;
        }

        &__menu-item-container {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        &__menu-item-image-wrapper {
          @include flexbox-properties;
        }

        &__country-link-icon {
          width: 1.6rem;
          height: 1.6rem;
          @include mask-properties;
          mask-image: url('assets/icons/ds2024/icon-external-link.svg');
          -webkit-mask-image: url('assets/icons/ds2024/icon-external-link.svg');
          background-color: $color-text-primary;
          transition: background-color 0.3s ease;
        }
      }

      &:first-child {
        pointer-events: none;
        position: relative;

        &:before {
          content: '';
          position: absolute;
          top: -1rem;
          left: -4.8rem;
          width: calc(100% + 9.6rem);
          height: 4.8rem;
          background-color: $color-surface-primary;
          z-index: -1;
        }

        .drawer-handheld-menu {
          &__country-link-icon {
            display: none;
          }
        }
      }
    }
  }

  .magazine {
    &__wrapper {
      display: inline-block;
    }

    &__title {
      margin-top: $spacing-06-24;
    }

    &__image-wrapper {
      width: 100%;

      img {
        width: 100%;
        height: auto;
        box-shadow: $box-shadow-level-4;
      }

      @media (hover: hover) {
        &:hover {
          img {
            transform: none !important;
          }
        }
      }
    }

    &__label {
      margin-bottom: $spacing-06-24;
      color: $color-text-secondary;
    }

    &__excerpt {
      margin-bottom: $spacing-06-24;

      p {
        display: -webkit-box;
        -webkit-line-clamp: 13;
        -webkit-box-orient: vertical;
        overflow: hidden;
        visibility: visible;
        line-height: 2.52rem;
      }
    }

    &__button-wrapper {
      display: flex;
      flex-direction: column;
      gap: $spacing-02;
    }

    .button {
      &:after {
        background-color: $color-surface-primary;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &__header {
      padding: $spacing-06-24;
    }

    &__main {
      padding: $spacing-00 $spacing-06-24;
    }

    &__search-wrapper,
    &__menu-item-list--simple {
      margin-bottom: $spacing-06-24;
      padding-bottom: $spacing-06-24;
    }

    &__menu-item-list {
      &--middle {
        margin-bottom: $spacing-06-24;
      }
    }

    .submenu {
      &.open {
        margin-top: $spacing-06-24;
      }
    }

    &__menu {
      margin-bottom: $spacing-06-24;
    }

    &__menu-item-list,
    &__login-wrapper,
    &__search-wrapper {
      margin-inline: $spacing-00;

      &--highlight {
        margin-inline: -2.4rem;
        padding: $spacing-06-24;
      }
    }

    .search-opener-btn,
    .search-opener-btn i {
      margin-right: 0 !important;
    }

    &__user-wrapper {
      margin-inline: $spacing-00;
    }

    &__footer {
      padding: $spacing-00 $spacing-06-24 10rem;
    }
  }
}
