<?php
//@TODO: Delete file and add this to the main nav-menus index if use the new drawer menu in all pages

/**
 * The array of "menu_name" => "menu_id" pairs to determine which menu should we use in according to the $menu_name's value
 * @var int[]
 * */
$locations = get_nav_menu_locations();

/**
 * The handheld menu object. Displayed on the right side of the page.
 * @var object
 */

/**
 * We deicde which menu should be loaded based on configuration of content type.
 * */
if (defined('CONTENT_TYPE') && CONTENT_TYPE === 'life') {
	$handheld_menu = isset($locations['life-drawer-handheld-menu']) ? wp_get_nav_menu_object($locations['life-drawer-handheld-menu']) : null;
} else {
	$handheld_menu = isset($locations['drawer-handheld-menu']) ? wp_get_nav_menu_object($locations['drawer-handheld-menu']) : null;
}




/**
 * Tree like structure of the handheld menu items.
 * If a menu item has submenu items, these submenu items can be accessed through the "menu_item_children" property.
 * @var array(object)
 */
$handheld_menu_tree = $handheld_menu ? Navigation\frontend_nav_menu_2_tree($handheld_menu->term_id) : [];

/**
 * Check if the handheld menu has a highlight menu link
 * @var bool
 */
$has_highlight = false;

foreach ($handheld_menu_tree as $item) :
    $is_highlight_menu_link = get_field('is_highlight_menu_link', $item->ID);

    if ($is_highlight_menu_link) :
        $has_highlight = true;
    endif;
endforeach;

/**
 * Filter all handheld menu items to leave only those that have a submenu.
 * @var array(object)
 */
$menu_items_with_submenu = is_array($handheld_menu_tree) ? array_filter($handheld_menu_tree, function ($item) {
	return $item->menu_item_children;
}) : null;

// Night mode section -------------------------------------------------------------------------
/**
 * Night mode Label
 * @var string
 */
$night_mode_label = get_field('navigation', 'option')['mode']['label'] ?? null;

// Magazine section ---------------------------------------------------------------------------
$navigation = get_field('navigation', 'option');

/**
 * ID of the latest magazin release.
 * @var int
 */
$magazine_id = ! empty($navigation['magazin']) ? $navigation['magazin'] : Navigation\frontend_get_latest_magazine_id();

/**
 * Represents the unique identifier for a magazine life entity.
 *
 * @var int|string $magazine_life_id The unique identifier for the magazine life entity.
 */
$magazine_life_id = $navigation['magazin_life'] ?? null;

if ($magazine_life_id && defined('CONTENT_TYPE') && CONTENT_TYPE === 'life') {
	$magazine_id = $magazine_life_id;
}

/**
 * Excerpt of the magazine.
 */
$magazine_excerpt = get_the_excerpt($magazine_id);

/**
 * Title of the magazine.
 * @var string
 */
$magazine_title = get_the_title($magazine_id);

/**
 * Publish month of the magazine.
 * @var string
 */
$magazine_label = get_field('label', $magazine_id);

/**
 * Url of the magazines thumbnail image.
 * @var string
 */
$magazine_image_url = get_the_post_thumbnail_url($magazine_id, 'drawer_magazine');

/**
 * Link to the page of the magazine. (Outer link.)
 * @var string
 */
$magazine_url = get_field('url_issue', $magazine_id);

/**
 * The ACF fields for navigation (from an options menu).
 * @var array
 * */
$navigation = get_field('navigation', 'option');

/**
 * Favicon of the site.
 * @var int
 */
$favicon = $navigation['favicon'] ?? null;

/**
 * Favicon of the site in dark mode.
 * @var int
 */
$dark_favicon = $navigation['dark_favicon'] ?? null;

/**
 * The label of the second button in the magazine section.
 * @var string
 */
$magazine_second_button_label = $navigation['magazine_second_button_label'] ?? null;

/**
 * The link of the second button in the magazine section.
 * @var string
 */
$magazine_second_button_link = $navigation['magazine_second_button_link'] ?? null;
?>

<script>
	window.nightModeLabel = '<?= $night_mode_label; ?>';
</script>

<div class="drawer-handheld-menu-wrapper">

	<div class="drawer-handheld-menu<?= is_admin_bar_showing() ? ' admin-bar' : ''; ?>">

		<div class="main-menu h-d--flex h-flex--column h-justify-content--between">

			<div class="drawer-handheld-menu__container drawer-handheld-menu__header">
				<button class="drawer-handheld-menu-closer-btn button-icon" aria-label="Close Menu">
					<i class="icon icon--cross"></i>
				</button>

			</div>

			<div class="drawer-handheld-menu__container drawer-handheld-menu__main">

				<section class="drawer-handheld-menu__menu">

					<?php
					$menuHeader = new DrawerMenuHeader($favicon, $dark_favicon);
					$menuHeader->render();
					?>

					<ul class="drawer-handheld-menu__menu-item-list drawer-handheld-menu__menu-item-list--simple">

						<?php foreach ($handheld_menu_tree as $item) : ?>

							<?php
							$is_country_link = get_field('is_country_link', $item->ID);
							$is_highlight_menu_link = get_field('is_highlight_menu_link', $item->ID);

							if (!$is_country_link && !$is_highlight_menu_link) :
							?>
								<li class="drawer-handheld-menu__menu-item">

									<a href="<?= $item->url; ?>" id="<?= $item->ID; ?>" class="link link--large link--nodecoration<?= $item->menu_item_children ? ' submenu-opener' : ''; ?><?= in_array('highlight', $item->classes) ? ' highlight' : ''; ?>" <?= $item->menu_item_children ? 'data-toggle="collapse" data-target="#submenu' . $item->ID . '" aria-expanded="false" aria-controls="submenu' . $item->ID . '"' : ''; ?>>
										<?= $item->title; ?>
										<span class="submenu-indicator"></span>
									</a>

									<?php if ($item->menu_item_children) : ?>
										<ul class="submenu collapse" id="submenu<?= $item->ID; ?>">
											<?php foreach ($item->menu_item_children as $subitem) : ?>
												<li class="drawer-handheld-menu__menu-item">
													<a href="<?= $subitem->url; ?>" class="link link--large link--nodecoration"><?= $subitem->title; ?></a>
												</li>
											<?php endforeach; ?>
										</ul>
									<?php endif; ?>
								</li>
							<?php endif; ?>

						<?php endforeach; ?>

					</ul>


					<ul class="drawer-handheld-menu__menu-item-list drawer-handheld-menu__menu-item-list--middle">
						<?php if ($night_mode_label) : ?>

							<li>
								<div id="react-theme-switcher"></div>
							</li>

						<?php endif; ?>

						<?php foreach ($handheld_menu_tree as $item) : ?>

							<?php
							$is_country_link = get_field('is_country_link', $item->ID);

							if ($is_country_link) :
							?>
								<li class="drawer-handheld-menu__menu-item">

									<a href="<?= $item->url; ?>" id="<?= $item->ID; ?>" class="link link--large link--nodecoration<?= $item->menu_item_children ? ' submenu-opener' : ''; ?> <?= in_array('highlight', $item->classes) ? 'highlight' : ''; ?>" data-toggle="collapse" data-target="#submenu<?= $item->ID; ?>" aria-expanded="false" aria-controls="submenu<?= $item->ID; ?>">
										<?= $item->title; ?>
										<span class="submenu-indicator"></span>
									</a>

									<ul class="submenu collapse" id="submenu<?= $item->ID; ?>">

										<?php foreach ($item->menu_item_children as $subitem) : ?>

											<?php
											/**
											 * This variable stores a bool value depending if the submenu item is a country link or not
											 * @var bool $is_country_link
											 */
											$is_country_link = get_field('is_country_link', $subitem->ID);

											/**
											 * This variable stores an integer value representing the ID of a flag image to be displayed if the submenu item is a country link
											 * @var int|bool $flag_image_id
											 */
											$flag_image_id = $is_country_link ? get_field('icon', $subitem->ID) : false;
											?>

											<li class="drawer-handheld-menu__menu-item drawer-handheld-menu__menu-item--country-link">

												<a class="drawer-handheld-menu__menu-item-container link link--large link--nodecoration" href="<?= $subitem->url; ?>" target="_blank" rel="noopener">

													<div class="drawer-handheld-menu__menu-item-image-wrapper">

														<?php if ($flag_image_id) : ?>

															<?= wp_get_attachment_image( $flag_image_id, 'thumbnail', true, [
																'class'  => 'drawer-handheld-menu__country-link-flag',
																'alt'    => esc_html__( 'Flag', 'FORBES' ),
																'srcset' => ''
															] ); ?>

														<?php endif; ?>

														<span class="drawer-handheld-menu__country-link link link--large link--nodecoration">
															<?= $subitem->title; ?>
														</span>

													</div>

													<div class="drawer-handheld-menu__country-link-icon"></div>

												</a>

											</li>

										<?php endforeach; ?>

									</ul>

								</li>
							<?php endif; ?>

						<?php endforeach; ?>

						<?php if(get_field('print_page_link', 'option') && COUNTRY == 'sk') : ?>

							<li class="drawer-handheld-menu__menu-item">
								<a href="<?php echo get_field('print_page_link', 'option'); ?>" class="link link--large link--nodecoration">
                                    <?php echo __('Print Landing', 'FORBES'); ?>
								</a>
							</li>

						<?php endif; ?>

					</ul>

				<?php if(!empty($has_highlight)) : ?>
					<ul class="drawer-handheld-menu__menu-item-list drawer-handheld-menu__menu-item-list--highlight">
						<?php foreach ($handheld_menu_tree as $item) : ?>

							<?php
							$is_highlight_menu_link = get_field('is_highlight_menu_link', $item->ID);
							if ($is_highlight_menu_link) :
							?>
								<li class="drawer-handheld-menu__menu-item">

									<a href="<?= $item->url; ?>" id="<?= $item->ID; ?>" class="link link--large link--nodecoration<?= $item->menu_item_children ? ' submenu-opener' : ''; ?> <?= in_array('highlight', $item->classes) ? 'highlight' : ''; ?>" <?= $item->menu_item_children ? 'data-toggle="collapse" data-target="#submenu' . $item->ID . '" aria-expanded="false" aria-controls="submenu' . $item->ID . '"' : ''; ?>>
										<?= $item->title; ?>
										<span class="submenu-indicator"></span>
									</a>

									<?php if ($item->menu_item_children) : ?>
										<ul class="submenu collapse" id="submenu<?= $item->ID; ?>">
											<?php foreach ($item->menu_item_children as $subitem) : ?>
												<li class="drawer-handheld-menu__menu-item">
													<a href="<?= $subitem->url; ?>" class="link link--large link--nodecoration"><?= $subitem->title; ?></a>
												</li>
											<?php endforeach; ?>
										</ul>
									<?php endif; ?>
								</li>

							<?php endif; ?>

						<?php endforeach; ?>

					</ul>
				<?php endif; ?>

				</section>

			</div>

			<div class="drawer-handheld-menu__container drawer-handheld-menu__footer">

				<div class="magazine">

					<a class="magazine__image-wrapper" href="<?= 'hu' === COUNTRY ? $magazine_url : get_the_permalink($magazine_id); ?>">
						<img src="<?= $magazine_image_url; ?>"
                             alt="<?= get_the_title($magazine_id); ?>"
                             srcset="<?= get_the_post_thumbnail_url($magazine_id, 'drawer_magazine2x') ?> 2x"
                             loading="lazy"
                        >
					</a>

					<h4 class="magazine__title">
						<?= $magazine_title; ?>
					</h4>

					<p class="magazine__label basic-16">
						<?= $magazine_label; ?>
					</p>

					<div class="magazine__excerpt">

						<p class="basic-18">
							<?= $magazine_excerpt; ?>
						</p>


					</div>

					<div class="magazine__button-wrapper">
						<?php
                        $firstButton = new Button(
                            __('Learn more', 'FORBES'),
                            'hu' === COUNTRY ? $magazine_url : get_the_permalink($magazine_id),
                            'large',
                            'primary',
                            false,
                            false,
                            'after-icon after-icon--arrow button--full-width',
                            ''
                        );
							echo $firstButton->render();
						?>
						<?php
						if($magazine_second_button_label && $magazine_second_button_link) :
							$secondButton = new Button($magazine_second_button_label, $magazine_second_button_link, 'large', 'secondary', false, false, 'after-icon after-icon--arrow button--full-width', '');
							echo $secondButton->render();
						endif; ?>
					</div>

				</div>

			</div>

		</div>

	</div>

	<div class="handheld-overlay"></div>

</div>
