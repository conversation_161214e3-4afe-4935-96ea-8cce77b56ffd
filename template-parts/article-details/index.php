<?php

/**
 * The id of the post
 * @var string
 */
$id = $args['id'] ?? null;

/**
 * The read time of the article
 * @var string
 */
$read_time = get_post_read_time($id) ?? null;

/**
 * The length of the podcast (from ACF)
 * @var string
 */
$podcast_length = 'cz' === COUNTRY ? get_field('reading_time', $id) : (get_field('podcast_length', $id) ? get_field('podcast_length', $id) . esc_html__(' minutes', 'FORBES') : null);

/**
 * Whether this card is shown in the search results
 * @var boolean
 */
$search = $args['search'] ?? false;

/**
 * Whether this card is shown in the daily cover block
 * @var boolean
 */
$daily_cover = $args['daily_cover'] ?? false;

/**
 * Whether to hide the read time on the post
 * @var boolean
 */
$hide_read_time = $args['hide_read_time'] ?? false;

/**
 * Whether the card is in a slider
 * @var boolean
 */
$is_slider = $args['slider'] ?? false;

/**
 * Whether the card is in a slider
 * @var boolean
 */
$slider_type = $is_slider ? $args['slider_type'] ?? null : null;

/**
 * Whether the card is in a slider
 * @var boolean
 */
$design = $args['design'] ?? false;

/**
 * Whether the card is in the most read block slider
 * @var boolean
 */
$is_most_read = $args['is_most_read'] ?? false;

/**
 * Is featured large article type
 * @var boolean
 */
$is_featured_large = $args['is_featured_large'] ?? false;

/**
 * The category name
 * @var string
 * */
$category_name = $args['category_name'] ?? null;

/**
 * The category link
 * @var string
 * */
$category_link = $args['category_link'] ?? '';

/**
 * The read time of the post
 * @var string
 * */
$read_time = $args['read_time'] ?? '';

/**
 * The index of the post
 * @var string
 * */
$index = $args['index'] ?? null;

/**
 * Get custom additional classes
 */
$classes = (function () use ($hide_read_time, $daily_cover, $is_slider, $is_most_read) {
	$additional_classes = '';

	if ($hide_read_time) {
		$additional_classes .= ' hide-read';
	}

	if ($daily_cover) {
		$additional_classes .= ' daily-cover';
	}

	if ($is_slider) {
		$additional_classes .= ' slider';
	}

	if ($is_most_read) {
		$additional_classes .= ' cat-visible';
	}

	return $additional_classes;
})();
?>

<?php if ($id) : ?>

	<div class="article-details<?= $classes; ?>">

		<?php

			$author = get_the_main_author($id) ?? null;
			$author_name = get_the_main_author($id)->name ?? null;
			$author_link = get_the_main_author($id)->archive_link ?? null;

		?>


		<?php if (($design === 'old' || $is_most_read || $is_featured_large)) : ?>

			<a href="<?= $category_link; ?>" class="article-card__category cta-link-tag after-icon after-icon--line<?= $design === 'old' ? ' relative' : ''; ?><?= $index ? ' article-card__category--index' : ''; ?>" data-index="<?= $index ? $index . '.' : '';?>">

				<?= $category_name; ?>

			</a>

		<?php endif; ?>


		<?php if ($author_name && $author_link) : ?>

			<?php if ($is_slider && $slider_type === 'on-card') : ?>

				<span class="article-details__author footnote"><?= $author_name; ?></span>

			<?php else : ?>

				<a href="<?= $author_link; ?>" class="article-details__author footnote"><?= $author_name; ?></a>

			<?php endif; ?>

		<?php endif; ?>


		<span class="article-details__publish-time footnote"><?= frontend_get_publish_time($id, true); ?></span>


		<?php if ($search) : ?>

			<span class="article-details__read-time cta-link-tag"><?= get_the_date(esc_html__('j. M Y', 'FORBES'), $id); ?></span>

		<?php elseif ($podcast_length && !$hide_read_time) : ?>

			<span class="article-details__read-time footnote"><?= $podcast_length; ?></span>

		<?php elseif ($read_time && !$hide_read_time) : ?>

			<span class="article-details__read-time <?= $design !== 'old' ? 'footnote' : 'cta-link-tag' ?>"><?= $read_time; ?></span>

		<?php endif; ?>

	</div>

<?php endif; ?>
