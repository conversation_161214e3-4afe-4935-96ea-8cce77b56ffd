.article-details {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  row-gap: $spacing-02;

  &.daily-cover {
    margin-top: $spacing-02;
  }

  &__author {
    white-space: nowrap;
    transition: all 0.3s ease;
    margin-right: $spacing-02;

    @media (hover: hover) {
      &:hover {
        color: $color-text-brand;
      }
    }

    &.hide {
      display: none;
    }
  }

  &__read-time,
  &__author,
  &__publish-time {
    color: $color-text-secondary;
    font-size: 1.4rem;
    margin-bottom: $spacing-00;
    text-decoration: none;
  }

  &__publish-time {
    position: relative;
    margin-left: $spacing-02;
    margin-right: $spacing-02;

    &:before {
      content: '';
      position: absolute;
      left: -0.8rem;
      top: 50%;
      transform: translateY(-50%);
      width: 0.2rem;
      height: 0.1rem;
      border-bottom: 0.2rem solid $color-text-secondary;
      border-radius: 50%;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    margin-bottom: $spacing-00;
    padding-left: $spacing-00;
  }
}
