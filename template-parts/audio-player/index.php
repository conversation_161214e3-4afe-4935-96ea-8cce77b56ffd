<?php

$post_id = $args['post_id'] ?? get_the_ID();
$class_name = $args['class_name'] ?? '';

$is_generating = AudioArticleQueue::is_article_in_queue($post_id) || AudioArticleQueue::is_article_in_progress($post_id);
$required_access_type = get_field('audio_article_required_access_type', 'option') ?? 'audio';
$has_audio_access = forbes_user_has_access($required_access_type);
$enable_short_version = get_field('audio_article_enable_short_version', 'option') ?? true;

$audio_url = get_field('audio', $post_id);
$audio_url_locked = get_field('audio_locked', $post_id);
$audio_locker_suffix = get_field('audio_article_locker_suffix', 'option') ?? '';
$thumbnail_url = get_the_post_thumbnail_url($post_id, 'comment_card_desktop2x') ?? '';

$source = $has_audio_access ? $audio_url : $audio_url_locked;
$suffix = $has_audio_access ? '' : $audio_locker_suffix;

if (!$enable_short_version && !$has_audio_access && !empty($audio_url)) {
	$source = $audio_locker_suffix;
	$suffix = '';
}

if (!$source && !$is_generating) {
	return; // No audio URL and not generating audio, so we don't need to show the player.
} ?>

<div
	class="react-audio-player"
	data-audio-generating="<?= $is_generating ? 'true' : 'false'; ?>"
	data-has-audio-access="<?= $has_audio_access ? 'true' : 'false'; ?>"
	data-source="<?= esc_attr($source); ?>"
	data-suffix="<?= esc_attr($suffix); ?>"
	data-thumbnail="<?= esc_attr($thumbnail_url); ?>"
	data-class-name="<?= esc_attr($class_name); ?>"
></div>
