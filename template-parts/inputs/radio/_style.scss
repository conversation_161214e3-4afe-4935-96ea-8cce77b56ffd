.custom-radio-wrapper {
  cursor: pointer;

  .custom-radio {
    cursor: pointer;
    position: relative;
    border: 0.1rem solid $color-input-border-default;
    margin-right: $spacing-02;
    display: flex;
    align-items: center;
    border-radius: 50%;
    transition: all 0.3s ease;

    &:has(.custom-radio__input:checked) {
      border-color: $color-surface-brand-dark;
      background-color: $color-surface-brand-dark;
    }

    &.error {
      border-color: $color-input-border-error;

      & + .input-group__label-wrapper label {
        color: $color-input-border-error;

        a {
          color: $color-text-error;
        }
      }
    }

    &:after {
      content: attr(data-label);
      display: inline-block;
      margin-left: calc(100% + $spacing-02);
      font-size: 1.4rem;
      line-height: 2rem;
      color: $color-text-primary;
      white-space: nowrap;
    }

    &__input {
      position: absolute;
      opacity: 0;
      cursor: pointer;
      height: 0;
      width: 0;

      &:checked ~ .custom-radio__indicator {
        opacity: 1;
      }
    }

    &__indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: $color-surface-primary;
      opacity: 0;
      border-radius: 50%;
      transition: opacity 0.3s ease;
      width: 0.8rem;
      height: 0.8rem;
    }

    &--small {
      width: 1.6rem;
      height: 1.6rem;
    }

    &--medium {
      width: 2.4rem;
      height: 2.4rem;

      .custom-radio {
        &__indicator {
          width: 1.2rem;
          height: 1.2rem;
        }
      }
    }

    &--large {
      width: 3.2rem;
      height: 3.2rem;

      .custom-radio {
        &__indicator {
          width: 1.6rem;
          height: 1.6rem;
        }
      }
    }

    &:has(.custom-radio__input:disabled) {
      pointer-events: none;
      opacity: 0.6;
    }
  }

  &.disabled {
    pointer-events: none;

    * {
      color: $color-button-secondary-disabled !important;
    }

    .discount {
      border-color: $color-button-secondary-disabled !important;

      &:before {
        background-color: $color-button-secondary-disabled !important;
      }
    }

    .custom-radio {
      border-color: $color-button-secondary-disabled !important;
      background-color: $color-surface-primary !important;

      &__indicator {
        background-color: $color-button-secondary-disabled;
        opacity: 1;
      }
    }
  }

  @media (hover: hover) {
    &:hover {
      .custom-radio {
        &__indicator {
          background-color: $color-input-border-hover;
          opacity: 1;
        }
      }

      .custom-radio__input:checked + .custom-radio__indicator {
        background-color: $color-surface-primary;
      }
    }
  }
}
