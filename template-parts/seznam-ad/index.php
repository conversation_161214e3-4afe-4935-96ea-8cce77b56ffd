<?php if (defined('CONTENT_TYPE') && CONTENT_TYPE === 'life') : ?>

<script>
	// Function to detect device type based on screen width
	function isDesktop() {
		return window.innerWidth >= 992;
	}

	let adCreated = false;  // Flag to track if the ad div has been created
	let adsLoaded = false; // Global flag to ensure ads load only once

	// Function to create an ad div inside the #seznamAdZone div
	function createAdDiv(id) {
		if (adCreated) return;  // Exit if the ad has already been created

		const div = document.createElement('div');
		div.id = id;
		div.style.margin = '8rem auto 4rem auto';
		div.style.maxWidth = '100%';

		const adZone = document.getElementById('seznamAdZone');
		if (adZone) {
			adZone.appendChild(div);
			adCreated = true;  // Mark that the ad has been created
		} else {
			console.error('Element with id "seznamAdZone" not found.');
		}
	}

	// Function to reset the session cookie
	function resetSessionCookie() {
		if (typeof sssp !== 'undefined' && sssp.setSessionCookie) {
			sssp.setSessionCookie();
		} else {
			console.error('sssp or sssp.setSessionCookie is not defined.');
		}
	}

	// Function to handle ad loading after cookie reset
	function loadAds() {
		// Determine which ad zone to display based on device type
		if (isDesktop()) {
			createAdDiv('ssp-zone-350266'); // Create desktop ad div
			// Fetch and display Seznam ads for desktop
			sssp.getAds([
				{
					"zoneId": 350266, // unique identifier of the advertising zone
					"id": "ssp-zone-350266", // ID of the element where the ad is displayed
					"width": 970 // maximum width of the ad in the zone
				}
			]);
		} else {
			createAdDiv('ssp-zone-350269'); // Create mobile ad div
			// Fetch and display Seznam ads for mobile
			sssp.getAds([
				{
					"zoneId": 350269, // unique identifier of the advertising zone
					"id": "ssp-zone-350269", // ID of the element where the ad is displayed
					"width": 480 // maximum width of the ad in the zone
				}
			]);
		}
	}

	(function initAdLoading() {
		function checkConsentAndLoadAds() {
			// Exit early if ads are already loaded
			if (adsLoaded) return;

			if (window.Cookiebot && window.Cookiebot.consent) {
				// Check if the user has given consent for marketing cookies
				if (window.Cookiebot.consent.marketing) {
					// Check if the visitor comes from a Seznam session
					if (typeof sssp !== 'undefined' && sssp.displaySeznamAds()) {
						loadAds();
						adsLoaded = true; // Stop further checks after ads have been loaded
					}
				}
			}

			// Continue checking every 500ms only if ads haven't been loaded
			if (!adsLoaded) {
				setTimeout(checkConsentAndLoadAds, 500);
			}
		}

		// Event handler for when consent is initially determined
		function onConsentReady() {
			checkConsentAndLoadAds();
		}

		// Event handler for any consent change
		function onConsentChange() {
			checkConsentAndLoadAds();
		}

		// Add the event listeners
		window.addEventListener('CookiebotOnConsentReady', onConsentReady);
		window.addEventListener('CookieConsent', onConsentChange);

		// Start the consent check loop
		checkConsentAndLoadAds();
	})();
</script>

<?php else :?>

<script>
	// Function to detect device type based on screen width
	function isDesktop() {
		return window.innerWidth >= 992;
	}

	let adCreated = false;  // Flag to track if the ad div has been created
	let adsLoaded = false; // Global flag to ensure ads load only once

	// Function to create an ad div inside the #seznamAdZone div
	function createAdDiv(id) {
		if (adCreated) return;  // Exit if the ad has already been created

		const div = document.createElement('div');
		div.id = id;
		div.style.margin = '8rem auto 4rem auto';
		div.style.maxWidth = '100%';

		const adZone = document.getElementById('seznamAdZone');
		if (adZone) {
			adZone.appendChild(div);
			adCreated = true;  // Mark that the ad has been created
		} else {
			console.error('Element with id "seznamAdZone" not found.');
		}
	}

	// Function to reset the session cookie
	function resetSessionCookie() {
		if (typeof sssp !== 'undefined' && sssp.setSessionCookie) {
			sssp.setSessionCookie();
		} else {
			console.error('sssp or sssp.setSessionCookie is not defined.');
		}
	}

    // Function to handle ad loading after cookie reset
    function loadAds() {
        // Determine which ad zone to display based on device type
        if (isDesktop()) {
            createAdDiv('ssp-zone-350260'); // Create desktop ad div
            // Fetch and display Seznam ads for desktop
            sssp.getAds([
                {
                    "zoneId": 350260, // unique identifier of the advertising zone
                    "id": "ssp-zone-350260", // ID of the element where the ad is displayed
                    "width": 970 // maximum width of the ad in the zone
                }
            ]);
        } else {
            createAdDiv('ssp-zone-350263'); // Create mobile ad div
            // Fetch and display Seznam ads for mobile
            sssp.getAds([
                {
                    "zoneId": 350263, // unique identifier of the advertising zone
                    "id": "ssp-zone-350263", // ID of the element where the ad is displayed
                    "width": 480 // maximum width of the ad in the zone
                }
            ]);
        }
    }

	(function initAdLoading() {
		function checkConsentAndLoadAds() {
			// Exit early if ads are already loaded
			if (adsLoaded) return;

			if (window.Cookiebot && window.Cookiebot.consent) {
				// Check if the user has given consent for marketing cookies
				if (window.Cookiebot.consent.marketing) {
					// Check if the visitor comes from a Seznam session
					if (typeof sssp !== 'undefined' && sssp.displaySeznamAds()) {
						loadAds();
						adsLoaded = true; // Stop further checks after ads have been loaded
					}
				}
			}

			// Continue checking every 500ms only if ads haven't been loaded
			if (!adsLoaded) {
				setTimeout(checkConsentAndLoadAds, 500);
			}
		}

		// Event handler for when consent is initially determined
		function onConsentReady() {
			checkConsentAndLoadAds();
		}

		// Event handler for any consent change
		function onConsentChange() {
			checkConsentAndLoadAds();
		}

		// Add the event listeners
		window.addEventListener('CookiebotOnConsentReady', onConsentReady);
		window.addEventListener('CookieConsent', onConsentChange);

		// Start the consent check loop
		checkConsentAndLoadAds();
	})();
</script>

<?php endif; ?>
