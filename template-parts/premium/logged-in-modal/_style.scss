.logged-in-modal {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: $color-surface-secondary;
  width: 100%;
  opacity: 0;
  z-index: -1;
  transform: translateY(100%);
  transition: all 0.3s ease;

  &__close-button {
    display: none;
  }

  &__content-wrapper {
    padding: $spacing-06-24;
    max-width: 75%;
    margin: auto;
  }

  &__top-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacing-06-24;
  }

  &__email {
    font-size: 2.6rem;
    font-family: $font-archivo;
    font-weight: 700;
  }

  &__text-container {
    text-align: center;

    & > p {
      font-size: 1.6rem;
    }

    & > p:not(:last-child) {
      margin-bottom: $spacing-02;
    }
  }

  &__no-sub-container {
    @include flexbox-properties;
    flex-direction: column;

    .button {
      margin-top: $spacing-04-16;
      margin-bottom: $spacing-04-16;
    }
  }

  &__thank-you-text,
  &__buy-sub-text {
    font-size: 1.8rem;
    margin-bottom: $spacing-02;
  }

  &__note {
    font-size: 1.8rem;
    font-style: italic;
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    height: calc(100vh - 4.8rem);
    padding-top: $spacing-08-40;

    .container {
      height: 100%;
    }

    &__close-button {
      position: absolute;
      top: 2.4rem;
      right: 2.4rem;
      display: block;
      width: 2rem;
      height: 2rem;
      @include mask-properties;
      mask-image: url('assets/icons/icon-close-amber.svg');
      -webkit-mask-image: url('assets/icons/icon-close-amber.svg');
      background-color: $color-link-visited;
      cursor: pointer;
    }

    &__content-wrapper {
      width: 100%;
      height: 100%;
      max-width: none;
      padding: $spacing-00;
      padding-top: $spacing-08-40;
    }

    &__top-row {
      flex-direction: column;
    }

    &__email {
      font-size: 1.8rem;
      margin-bottom: $spacing-06-24;
    }
  }
}
