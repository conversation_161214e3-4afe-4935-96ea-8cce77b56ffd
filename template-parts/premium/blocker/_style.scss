.premium-blocker {
  margin: $spacing-00 $spacing-00 $spacing-00 -8.6rem;
  position: relative;
  background-color: $color-surface-primary;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: $spacing-08-40;
  box-shadow: $box-shadow-level-2;

  &:before {
    content: '';
    width: 100%;
    height: 30rem;
    position: absolute;
    left: 0;
    bottom: 100%;
    background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 0) 100%);
  }

  .tag {
    margin-bottom: $spacing-02;

    &__icon {
      display: flex;

      img {
        padding-right: $spacing-00;
      }
    }
  }

  h4.heading {
    margin-bottom: $spacing-04-16;
    font-weight: 700;
  }

  &__description {
    color: $color-text-secondary;
    font-family: $font-archivo;
    font-size: 1.6rem;
    font-weight: 500;
    line-height: 2.24rem;
    margin-bottom: $spacing-04-16;
  }

  &__button-wrapper {
    display: flex;
    align-items: center;
    gap: $spacing-02;

    .link {
      color: $color-surface-primary;
      padding-left: $spacing-02;

      @media (hover: hover) {
        &:hover {
          color: $color-subscribe-bar-highlight !important;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin: $spacing-00;
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    &__button-wrapper {
      flex-direction: column;

      .link {
        padding-left: $spacing-00;
        margin-top: $spacing-03-12;
      }

      .button {
        width: 100%;
      }
    }
  }

  background-color: $color-surface-secondary-invert;

  h4.heading {
    color: $color-surface-primary;
  }

  .tag {
    color: $color-subscribe-bar-highlight;

    &__icon {
      background-color: $color-subscribe-bar-highlight;
    }

    &--icon {
      padding-left: $spacing-00 !important;
    }
  }

  .premium-blocker {
    &__description {
      color: $color-input-border-default;
    }

    .button {
      &--primary {
        background-color: $color-button-primary-invert;
        color: $color-text-primary;

        .icon-wrapper {
          margin-right: $spacing-01;

          svg {
            path {
              stroke: $color-text-primary;
            }
          }
        }
      }

      &--secondary {
        background-color: $color-surface-secondary-invert;
        color: $color-text-primary-invert;
        border-color: $color-text-primary-invert;
      }
    }
  }
}

.single--brandvoice {
  .premium-blocker {
    background: $color-surface-secondary;
    background: rgba(var(--surface-secondary-rgb), 0.5);

    &:before {
      content: none;
    }
  }
}

#reactPremiumBlockerButton {
  #remp_cta {
    margin: $spacing-00;
    padding: $spacing-00;
    box-shadow: none;

    &:before {
      content: none;
    }
  }
}

#remp_lock_anchor {
  transform: translateY(-150px);
}

html.dark-mode {
  .premium-blocker {
    &:before {
      background: linear-gradient(180deg, rgba(2, 6, 23, 0) 0%, #020617 100%);
    }
  }
}

