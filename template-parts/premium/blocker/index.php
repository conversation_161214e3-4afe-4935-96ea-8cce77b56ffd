<?php

/**
 * The type of permission needed to view the article
 * @var string
 */
$needed = $args['type'] ?? "";

/**
 * The type of permission the user has
 * @var string
 */
$current = $args['types'] ?? "";

/**
 * The elements of the block
 * @var array
 */
$blocker_elements = get_field('blocker_block', 'option');

/**
 * The label of the optionial link
 * @var string
 */
$optional_link_label = $blocker_elements['optional_link_label'] ?? '';

/**
 * The URL of the optional link
 * @var string
 */
$optional_link_url = $blocker_elements['optional_link_url'] ?? '';

/**
 * The tag of the block
 * @var string
 */
$tag = $blocker_elements['tag'] ?? '';

if(defined('COUNTRY')) {
	$country = COUNTRY;
}
?>

<div id="remp_lock_anchor"></div>
<div class="premium-blocker premium-blocker--<?php echo $country; ?>" id='remp_cta' data-needed='<?= $needed; ?>' data-current='<?= $current; ?>'>

	<?php if($tag) :
		$tag = new Tag(
			text: $tag,
			icon: get_template_directory_uri() . '/template-parts/premium/blocker/icon-diamond.svg'
		);
		echo $tag->render();
	endif; ?>

	<script>
		const optionalLinkLabel = <?php echo json_encode($optional_link_label); ?>;
		const optionalLinkUrl = <?php echo json_encode($optional_link_url); ?>;

		window.premiumOptionalLink = {
			premiumOptionalLinkLabel: optionalLinkLabel,
			premiumOptionalLinkUrl: optionalLinkUrl
		};
	</script>

	<div id="reactPremiumBlockerButton" data-needed="<?= $needed; ?>"></div>

</div>
