.remp-login-form {
  display: inline-flex;
  justify-content: center;

  &__close-button {
    display: none;
  }

  .paywall-form {
    text-align: center;
    font-size: 1.4rem;
    font-family: $font-archivo;

    h3 {
      font-family: $font-archivo !important;
      margin-bottom: $spacing-04-16;
    }

    &-input {
      background-color: $color-divider;
      border: 0.1rem solid transparent;
      width: 100%;
      padding: $spacing-04-16;
      margin-bottom: $spacing-02;
      font-weight: 400;
      font-size: 1.6rem;
      line-height: 2.4rem;
      color: $color-text-secondary;
      transition: all 0.3s ease;

      @media (hover: hover) {
        &:hover {
          background-color: $color-surface-primary;
          border: 0.1rem solid $color-link-visited;
        }
      }

      &:active,
      &:focus {
        color: $color-text-primary;
        background-color: $color-surface-primary;
        border: 0.1rem solid $color-link-visited;
        box-shadow: 0 0 0 0.2rem $color-text-brand;
      }

      &.error {
        background-color: $color-surface-primary;
        border-color: $color-text-error;
        color: $color-text-error;

        &::placeholder {
          color: $color-text-error;
        }
      }
    }

    &-login-wrapper {
      display: flex;
      flex-direction: column-reverse;
      align-items: center;
      margin-bottom: $spacing-04-16;

      a {
        font-family: $font-archivo;
        font-size: 1.4rem;
        line-height: 1.6rem;
        margin-bottom: $spacing-06-24;
        color: $color-text-secondary;
      }

      button {
        max-width: 12rem;
        display: inline-flex;
      }
    }

    &-registration-wrapper {
      margin-top: $spacing-04-16;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    width: 100%;

    &__close-button {
      position: absolute;
      top: 2.4rem;
      right: 2.4rem;
      display: block;
      width: 2rem;
      height: 2rem;
      @include mask-properties;
      mask-image: url('assets/icons/icon-close-amber.svg');
      -webkit-mask-image: url('assets/icons/icon-close-amber.svg');
      background-color: $color-link-visited;
      cursor: pointer;
    }

    .paywall-form {
      width: 100%;

      h3 {
        text-align: left;
      }

      &-login-wrapper {
        a {
          margin-top: $spacing-06-24;
        }
      }

      &-input {
        padding: $spacing-04-16;
        margin-bottom: $spacing-04-16;
      }

      &-input[type='password'] {
        margin-bottom: $spacing-00;
      }
    }
  }
}
