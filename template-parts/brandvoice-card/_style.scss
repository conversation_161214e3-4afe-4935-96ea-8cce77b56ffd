.brandvoice-card {
  position: relative;
  width: 100%;
  max-width: 63.5rem;
  height: 0;
  display: flex !important;
  justify-content: center;
  align-items: flex-end !important;
  padding: $spacing-06-24;
  padding-top: 56.25%;
  overflow: hidden;
  text-decoration: none !important;

  @supports (aspect-ratio: 16/9) {
    aspect-ratio: 16/9;
    padding-top: $spacing-06-24;
    height: auto;
  }

  &__image-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    z-index: 0;
    overflow: hidden;

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(180deg, rgba(4, 149, 108, 0.25) 0%, rgba(0, 0, 0, 0.9) 100%);
    }
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease-in-out;
  }

  &__content-wrapper {
    text-align: center;
    z-index: 1;
  }

  &__title {
    color: $color-white;
    margin-bottom: $spacing-03-12 !important;
  }

  &__author {
    color: $color-white;
    text-transform: uppercase;
    font-size: 1.4rem;
    line-height: 1.6rem;
  }

  &.smaller {
    padding: $spacing-04-16;

    .brandvoice-card {
      &__title {
        font-size: 1.8rem;
        line-height: 2.4rem;
        margin-bottom: $spacing-00 !important;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        visibility: visible;
      }
    }
  }

  &--text-below {
    flex-direction: column;
    padding: $spacing-00 !important;
    aspect-ratio: unset;
    align-items: unset !important;

    .brandvoice-card {
      &__image-wrapper {
        position: relative;
        top: unset;
        left: unset;
        right: unset;
        bottom: unset;
        padding-top: 56.25%;
        overflow: hidden;

        @supports (aspect-ratio: 16/9) {
          padding-top: $spacing-00;
          height: auto;
          aspect-ratio: 16/9;
        }

        &:after {
          content: none;
        }
      }

      &__content-wrapper {
        padding-top: $spacing-03-12;
        text-align: left;
        display: flex;
        flex-direction: column-reverse;
      }

      &__title {
        color: $color-text-primary !important;
        text-align: left;
        font-size: 1.8rem;
        line-height: 2.7rem;
      }

      &__author {
        color: $color-surface-invert;
        margin-bottom: $spacing-01;
        font-family: $font-archivo;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    max-width: 100%;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    padding: $spacing-04-16 $spacing-05-20;

    &__title {
      margin-bottom: $spacing-00;
    }
  }
}
