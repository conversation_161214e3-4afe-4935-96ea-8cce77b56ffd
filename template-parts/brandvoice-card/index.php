<?php

/**
 * The ID of the post
 * @var int
 */
$id = $args['post_id'] ?? null;

/**
 * The ID of the category of the post
 * @var int
 */
$category_id = $id ? get_the_category($id)[0]->term_id : null;

/**
 * Show badge on the card
 * @var boolean
 */
$badge = $category_id ? get_field('toggle_badge', CATEGORY_TYPE . '_' . $category_id) : null;

/**
 * Show the author on the card
 * @var boolean
 */
$show_author = $args['show_author'] ?? false;

/**
 * Placement of the text (on or below image)
 * @var string
 */
$text_placement = $args['text_placement'] ?? null;

/**
 * Placement of the text (on or below image)
 * @var string
 */
$badge_position = $args['badge_position'] ?? null;

/**
 * If there are 3 cards in a row
 * @var boolean
 */
$three_in_row = $args['three_in_a_row'] ?? null;

if ('cz' === COUNTRY) {
	/**
	 *  Coauthor(s) of the article
	 * @var array
	 */
	$authors = get_all_the_authors(get_the_ID()) ? array_reverse(get_the_terms(get_the_ID(), 'coauthor')) : null;
	$author_id = is_array($authors) ? $authors[0]?->term_id : '';
} else {
	$author_id = get_post_field('post_author', $id);
}

$author_name = 'cz' === COUNTRY ? get_term($author_id)?->name : get_the_author_meta('display_name', $author_id);
$manual_authors = get_field('authors', $id);

if (is_array($manual_authors) && !empty($manual_authors)) {
	$author_id = array_filter($manual_authors, fn ($author) => $author['main_author'])[0]['author'] ?? $manual_authors[0];
	$author_name = 'cz' === COUNTRY ? get_term($author_id)?->name : get_the_author_meta('display_name', $author_id);
}
?>

<?php if ($id) : ?>

	<a href="<?= get_permalink($id); ?>" class="brandvoice-card<?= 'below' === $text_placement ? ' brandvoice-card--text-below' : ''; ?><?= $three_in_row ? ' smaller' : ''; ?>">

		<div class="brandvoice-card__image-wrapper">

			<?= get_the_post_thumbnail($id, 'medium', array('class' => 'brandvoice-card__image', 'alt' => get_the_title($id))); ?>

			<?php if ('below' === $text_placement && $badge) : ?>

				<?php get_template_part('template-parts/article-badge/index', null, array('id' => $category_id, 'brandvoice' => true, 'below-card' => true, 'middle' => $badge_position == 'middle')); ?>

			<?php endif; ?>

		</div>

		<div class="brandvoice-card__content-wrapper">

			<?php if ('below' !== $text_placement && $badge) : ?>

				<?php get_template_part('template-parts/article-badge/index', null, array('id' => $category_id, 'brandvoice' => true, 'middle' => $badge_position == 'middle')); ?>

			<?php endif; ?>

			<h4 class="brandvoice-card__title"><?= get_the_title($id); ?></h4>

			<?php if ($show_author) : ?>

				<span class="brandvoice-card__author"><?= $author_name; ?></span>

			<?php endif; ?>

		</div>

	</a>

<?php endif; ?>
