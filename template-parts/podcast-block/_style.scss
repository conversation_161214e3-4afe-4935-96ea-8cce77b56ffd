.podcast-block {
  background-color: $color-surface-secondary;
  margin: $spacing-00 $spacing-00 $spacing-08-40 -8.6rem;
  padding: $spacing-08-40;
  max-width: 73rem;

  &__player-wrapper {
    margin-bottom: $spacing-02;
  }

  &__description-wrapper {
    margin-bottom: $spacing-04-16;

    p {
      color: $color-text-secondary;
      font-size: 1.4rem;
      font-weight: 500;
      line-height: 1.68rem;
      font-family: $font-archivo;
    }
  }

  &__buttons-wrapper {
    align-items: center;
    display: flex;
    justify-content: space-evenly;
    gap: $spacing-02;

    .button {
      width: 50%;
      line-height: 1.8rem;
    }
  }

  iframe {
    max-height: 15.5rem;
  }

  &--onepage {
    padding: $spacing-08-40;
    margin: auto;

    .podcast-block {
      &__buttons-wrapper {
        gap: $spacing-02;
        justify-content: flex-start;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin: $spacing-00 $spacing-00 $spacing-07-32;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-left: calc((100% - 100vw) / 2);
    padding: $spacing-04-16;
    width: 100vw;

    &__description-wrapper {
      margin-bottom: $spacing-04-16;
    }

    &__buttons-wrapper {
      align-items: center;
      flex-direction: column;
      justify-content: center;

      .button {
        width: 100%;
      }
    }

    &__button {
      &--second {
        margin-bottom: $spacing-00;
      }
    }

    &--onepage {
      width: 100%;

      .podcast-block {
        &__button {
          margin-bottom: $spacing-00;
          width: 100%;
        }
      }
    }
  }
}
