<?php
/**
 * The Track ID (which is actually a full URL) of the Spotify embed
 * @var string
 */
// First try to retrieve it with get_field, if not, then with get_post_meta because of the preview mode
$track_id = $args['track_id'] ?? get_field('spotify_track_id') ?? get_post_meta(get_the_ID(), 'spotify_track_id', true) ?? null;

/**
 * Whether the block is on a onepage page
 * @var boolean
 */
$is_onepage = isset($args['track_id']);

/**
 * The podcast block's ACF fields
 * @var array
 */
$podcast_block = get_field('podcast', 'option');

/**
 * The block's description
 * @var string
 */
$description = $podcast_block['block_description'] ?? null;

/**
 * The link for the buttons
 * @var string
 */
$buttons_link = $podcast_block['block_button_link'] ?? null;

/**
 * The first button's label
 * @var string
 */
$first_button_label = $podcast_block['block_first_button'] ?? null;

/**
 * The second button's label
 * @var string
 */
$second_button_label = $podcast_block['block_second_button'] ?? null;

if ($track_id): ?>

    <div class="podcast-block<?php echo $is_onepage ? ' podcast-block--onepage' : '';?>">
        <div class="podcast-block__player-wrapper">
            <figure class="wp-block-embed is-type-rich is-provider-spotify wp-block-embed-spotify wp-embed-aspect-21-9 wp-has-aspect-ratio">
                <div id="embed-iframe" data-src="<?php echo $track_id; ?>" class="wp-block-embed__wrapper">
                </div>
            </figure>
        </div>

        <div class="podcast-block__content-wrapper">
            <div class="podcast-block__description-wrapper">
                <?php if ($description): ?>
                    <p><?php echo $description; ?></p>
                <?php endif; ?>
            </div>

            <?php if ($buttons_link): ?>
                <div class="podcast-block__buttons-wrapper">
                    <?php if ($first_button_label): ?>
                        <a href="<?php echo $buttons_link; ?>" target="_blank" rel="noopener" class="podcast-block__button button button--large button--primary"><?php echo $first_button_label; ?></a>
                    <?php endif; ?>

                    <?php if ($second_button_label): ?>
                        <a href="<?php echo $buttons_link; ?>" target="_blank" rel="noopener" class="podcast-block__button podcast-block__button--second button button--large button--secondary"><?php echo $second_button_label; ?></a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

<?php endif; ?>
