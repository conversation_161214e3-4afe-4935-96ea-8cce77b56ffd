.theme-switcher {
  background-color: var(--post-background-color, $color-surface-primary);
  top: 0;
  left: 0;
  position: sticky;
  width: 100%;
  z-index: 2;

  &.open {
    .theme-switcher {
      &__inner-container {
        box-shadow: $box-shadow-level-4;
      }

      &__button {
        &::after {
          transform: translateY(-50%) rotate(180deg);
        }
      }
    }
  }

  &__inner-container {
    align-items: center;
    display: flex;
    flex-direction: column;
  }

  &__content-container {
    border-bottom: 0.1rem solid $color-divider;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-block: $spacing-04-16;
    width: 100%;
  }

  &__content-wrapper {
    align-items: center;
    display: flex;
    gap: $spacing-02;
  }

  &__image-wrapper {
    height: 5.6rem;
    overflow: hidden;
    position: relative;
    width: 5.6rem;
  }

  &__image {
    height: 100%;
    left: 0;
    object-fit: cover;
    position: absolute;
    top: 0;
    transition: all 0.3s ease;
    width: 100%;
  }

  &__title-wrapper {
    @media (hover: hover) {
      &:hover {
        .theme-switcher__title {
          color: var(--post-hover-font-color, $color-link-visited);

          &:after {
            background-color: var(--post-hover-font-color, $color-link-visited);
          }
        }
      }
    }
  }

  &__title {
    margin-bottom: $spacing-00;
    position: relative;
    text-decoration: underline;
    transition: 0.3s all ease;

    &:after {
      content: '';
      background-color: $color-text-primary;
      display: block;
      height: 1.6rem;
      @include mask-properties;
      mask-image: url('assets/icons/icon-link-arrow.svg');
      -webkit-mask-image: url('assets/icons/icon-link-arrow.svg');
      position: absolute;
      right: -2rem;
      top: 50%;
      transform: translateY(-50%);
      transition: all 0.3s ease;
      width: 1.6rem;
    }
  }

  &__description {
    & > p {
      color: $color-text-secondary;
      font-size: 1.2rem;
      line-height: 2rem;
    }
  }

  &__button {
    cursor: pointer;
    color: var(--post-font-color, $color-link-visited);
    font-size: 1.6rem;
    line-height: 3.2rem;
    padding-right: $spacing-04-16;
    position: relative;
    text-decoration: underline;
    transition: all 0.3s ease;
    white-space: nowrap;

    @media (hover: hover) {
      &:hover {
        color: var(--post-hover-font-color, $color-text-brand) !important;

        &:after {
          background-color: var(--post-hover-font-color, $color-text-brand) !important;
        }
      }
    }

    &:after {
      content: '';
      background-color: var(--post-font-color, $color-link-visited);
      display: block;
      height: 1.4rem;
      @include mask-properties;
      mask-image: url('assets/icons/icon-chevron-down.svg');
      -webkit-mask-image: url('assets/icons/icon-chevron-down.svg');
      position: absolute;
      right: -0.2rem;
      top: 50%;
      transform: translateY(-50%);
      transition: all 0.3s ease;
      width: 1.4rem;
    }
  }

  &__articles-container {
    height: 0;
    opacity: 0;
    overflow: hidden;
  }

  &__articles-inner-container {
    align-items: flex-start;
    display: grid;
    gap: $spacing-04-16 $spacing-07-32;
    grid-template-columns: repeat(3, 1fr);
    padding-block: $spacing-04-16;
  }

  &__article {
    color: initial;
    font-size: 1.6rem;
    line-height: 2.4rem;
    text-decoration: none;

    @media (hover: hover) {
      &:hover {
        color: var(--post-hover-font-color, $color-text-brand) !important;
      }
    }
  }

  &__divider {
    border-bottom: 0.1rem solid $color-divider;
    grid-column: 1 / -1;
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    position: relative;
    top: 0;
    z-index: 0;
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &__content-container {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-02;
    }

    &__image-wrapper {
      min-width: 5.6rem;
    }

    &__title {
      &:after {
        content: none;
      }
    }

    &__description {
      display: none;
    }

    &__articles-inner-container {
      grid-template-columns: 1fr;
    }

    &__divider {
      display: none;
    }

    &__article {
      font-size: 1.4rem;
      line-height: 2rem;
    }
  }
}
