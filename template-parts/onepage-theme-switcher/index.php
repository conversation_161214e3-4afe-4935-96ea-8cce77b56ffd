<?php
global $parent_one_page_id;

if ($parent_one_page_id) {
	/**
	 * Get the OnePager article anchor field group
	 * @var array
	 */
	$article_anchor_field_group = get_field('article_anchor', $parent_one_page_id);

	/**
	 * Get the OnePager article anchor title
	 * @var string
	 * */
	$title = $article_anchor_field_group['title'];

	/**
	 * Get the OnePager article anchor short description
	 * @var string
	 * */
	$short_description = $article_anchor_field_group['short_description'];

	/**
	 * Get the OnePager article anchor description
	 * @var string
	 * */
	$description = $article_anchor_field_group['description'];

	/**
	 * Get the OnePager article anchor image
	 * @var string
	 * */
	$image_id = $article_anchor_field_group['image'];

	/**
	 * Get the OnePager article anchor category type
	 * 	@var string
	 * */
	$category_type = $article_anchor_field_group['category_type'];

	/**
	 * Get the OnePager article anchor category text
	 * @var string
	 */
	$category_text = $category_type === 'text' ? $article_anchor_field_group['category_text'] : null;

	/**
	 * Get the OnePager article anchor category image
	 * @var string
	 */
	$category_image = $category_type === 'image' ? $article_anchor_field_group['category_image'] : null;

	/**
	 * Get the OnePager article anchor related articles
	 * @var array
	 */
	$related_articles = get_field('related_articles', $parent_one_page_id);
	$related_articles = array_values(array_filter($related_articles, fn ($article) => $article['article'] !== get_the_ID()));
}
?>

<?php if ($parent_one_page_id) : ?>

	<div class="theme-switcher">

		<div class="theme-switcher__inner-container container">

			<div class="theme-switcher__content-container">

				<div class="theme-switcher__content-wrapper">

					<?php if ($image_id) : ?>

						<a href="<?= get_the_permalink($parent_one_page_id); ?>" class="theme-switcher__image-wrapper">
							<?= wp_get_attachment_image($image_id, 'small', false, array('class' => 'theme-switcher__image')); ?>
						</a>

					<?php endif; ?>

					<div class="theme-switcher__text-wrapper">

						<?php if ($title) : ?>

							<a href="<?= get_the_permalink($parent_one_page_id); ?>" class="theme-switcher__title-wrapper">
								<h5 class="theme-switcher__title"><?= $title; ?></h5>
							</a>

						<?php endif; ?>

						<?php if ($short_description) : ?>

							<div class="theme-switcher__description"><?= $short_description; ?></div>

						<?php endif; ?>

					</div>

				</div>

				<div class="theme-switcher__button-wrapper">

					<span id="theme-switcher-button" class="theme-switcher__button"><?= esc_html__('Explore Theme', 'FORBES') ?></span>

				</div>

			</div>

			<div class="theme-switcher__articles-container">

				<div class="theme-switcher__articles-inner-container">

					<?php if (!empty($related_articles)) : ?>

						<?php foreach ($related_articles as $key => $related_article) : ?>

							<a href="<?= get_the_permalink($related_article['article']); ?>" class="theme-switcher__article"><?= get_the_title($related_article['article']) ?></a>

							<?php if ($key !== 0 && ($key + 1) % 3 === 0) : ?>

								<div class="theme-switcher__divider"></div>

							<?php endif; ?>

						<?php endforeach; ?>

					<?php endif; ?>

				</div>

			</div>

		</div>

	</div>

<?php endif; ?>
