document.addEventListener('DOMContentLoaded', () => {
  jQuery(function ($) {
    const $themeSwitcher = $('.theme-switcher');

    setThemeSwitcherTopOffset();
    addDropdownFunctionality();

    let timeout;
    window.addEventListener('resize', () => {
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        setThemeSwitcherTopOffset();
      }, 200);
    });

    /**
     * Sets the top offset of the theme switcher.
     *
     * @return {undefined} This function does not return a value.
     */
    function setThemeSwitcherTopOffset() {
      if (window.innerWidth <= 992) {
        $themeSwitcher.css({
          top: 0,
        });
        return;
      }

      if (!$themeSwitcher.length) return;

      const $navigation = $('.navigation');

      let offset = $navigation.height();

      const $adminbar = $('#wpadminbar');
      if ($adminbar.length) {
        offset += $adminbar.height();
      }

      $themeSwitcher.css({
        top: offset,
      });
    }

    function addDropdownFunctionality() {
      const $articlesContainer = $('.theme-switcher__articles-container');
      const $animateButton = $('#theme-switcher-button');

      $animateButton.on('click', () => {
        const scrollHeight = $articlesContainer[0].scrollHeight;

        $articlesContainer.animate(
          {
            height: $themeSwitcher.hasClass('open') ? '0px' : scrollHeight + 'px',
            opacity: $themeSwitcher.hasClass('open') ? '0' : '1',
          },
          200
        );

        $themeSwitcher.toggleClass('open');
      });
    }
  });
});
