.nav-hover-modals {
  position: absolute;
  z-index: -2;
  width: 100%;
  left: 0;
  top: 100%;
  height: 0;
  //transition: height 0.3s ease-out;

  &.active {
    height: 396px;
    box-shadow: var(--box-shadow-level-4);
  }
}

.nav-hover-modal {
  display: flex;
  position: absolute;
  top: -100px;
  box-shadow: $box-shadow-level-4;
  z-index: 1;
  left: 50%;
  transform: translateX(-50%);
  padding: 0 !important;
  height: 0;
  overflow: hidden;

  &:before {
    content: '';
    background-color: $color-surface-primary;
    width: 100%;
    height: 100%;
    position: absolute;
    right: 100%;
    box-shadow: $box-shadow-level-4;
    z-index: -1;
  }

  &:after {
    content: '';
    background-color: $color-surface-primary;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 100%;
    box-shadow: $box-shadow-level-4;
    z-index: -1;
  }

  &.active {
    top: 0;
    padding: 0;
    height: 100%;
    overflow: visible;
  }

  &.reverse {
    flex-direction: row-reverse;
    left: unset;
    right: 0;
  }

  &__subcategories-wrapper {
    display: flex;
    flex-direction: column;
    background-color: $color-surface-primary;
    padding: 4rem 4.5rem 4rem 1.5rem;
    max-height: 39.6rem;
    gap: 2.4rem;
    width: 100%;
  }

  &__subcategories-special-wrapper {
    display: flex;
    column-gap: 3rem;
  }

  &__subcategories {
    display: grid;
    height: 26rem;
    row-gap: $spacing-02;
    column-gap: 3rem;
    width: 100%;

    justify-content: flex-start;
    align-items: stretch;
    align-content: stretch;

    grid-template-columns: repeat(auto-fit, minmax(100px, 267px));
    grid-template-rows: repeat(6, 1fr);
    grid-auto-flow: column;

    &-menu-wrapper {
      width: 100%;
    }

    &-title {
      font-size: 1.4rem;
      font-weight: 500;
      color: $color-text-secondary;
      line-height: 1.4;
      margin-bottom: .8rem;
    }

    &:has(&-title) {
      .nav-hover-modal__subcategory:nth-child(8),
      .nav-hover-modal__subcategory:nth-child(14),
      .nav-hover-modal__subcategory:nth-child(20) {
        margin-top: 2.7rem;
      }
    }
  }

  &__special-list {
    display: flex;
    flex-direction: column;
    row-gap: $spacing-02;
    align-self: stretch;
    flex-shrink: 0;

    &-wrapper {
      width: auto;
    }

    &-title {
      font-size: 1.4rem;
      font-weight: 500;
      color: $color-text-secondary;
      line-height: 1.4;
    }

    &-items {
      display: flex;
      flex-direction: column;
      row-gap: $spacing-02;
    }
  }

  &__special-list-item {
    font-size: 1.4rem;
    font-weight: 600;
    line-height: 1.4;
    color: $color-text-primary;
    min-height: $spacing-07-32;
    width: 20.6rem;
    transition: color 0.3s ease;
    display: inline-block;
    padding: 0.6rem 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &.current {
      text-decoration: underline;
    }

    @media (hover: hover) {
      &:hover {
        text-decoration: underline;
        color: $color-text-primary !important;
      }
    }
  }

  &__subcategory {
    white-space: nowrap;
    font-size: 2.2rem;
    font-weight: 600;
    line-height: 1.32;
    color: $color-text-primary;
    min-height: $spacing-07-32;
    width: 100%;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    font-stretch: 88%;
    font-variation-settings: 'wdth' 88;

    &.current {
      text-decoration: underline;
    }

    @media (hover: hover) {
      &:hover {
        text-decoration: underline;
        color: $color-text-primary !important;
      }
    }
  }

  &__archive-link {
    white-space: nowrap;
    color: $color-text-primary;
    font-variant-numeric: lining-nums tabular-nums;
    font-family: $font-archivo !important;
    font-size: 1.6rem;
    font-weight: 600;
    line-height: 1.4em;
    text-transform: none;
    transition: color 0.3s ease;

    @media (hover: hover) {
      &:hover {
        text-decoration: underline;
        color: $color-text-primary !important;
      }
    }

    &:after {
      background-color: $color-text-primary;
    }
  }

  &__magazine-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 3rem 2.4rem 2.4rem 2.4rem;
    background-color: $color-surface-secondary;
    max-width: 30rem;
    width: 100%;
  }

  &__magazine-label {
    white-space: nowrap;
    color: $color-text-brand;
    margin-bottom: $spacing-04-16;
    text-transform: none;
  }

  &__magazine-inner-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    height: 100%;
  }

  &__magazine-image-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;

    &:after {
      content: '';
      position: absolute;
      bottom: -5%;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('data:image/png;base64,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');
      background-position: -8px 103%;
      background-repeat: no-repeat;
    }
  }

  &__magazine-container {
    width: 16rem;
    height: 21rem;
    position: relative;
    overflow: hidden;
    transform: rotate(12deg);
    z-index: 1;
  }

  &__magazine {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  &__magazine-text-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: stretch;
    width: 100%;

    h6 {
      font-family: $font-noto-serif;
      font-size: 1.8rem;
      line-height: 1.32;
      font-weight: 600;
      color: $color-text-primary;
      text-align: center;
      width: 100%;
    }

    time {
      text-transform: lowercase;
      font-size: 1.4rem;
      line-height: 1.4;
      font-weight: 500;
      color: $color-text-secondary;
      text-align: center;
      width: 100%;
    }
  }

  &__magazine-link {
    margin-top: $spacing-02;
    white-space: nowrap;
    color: $color-text-primary;
    font-weight: 600;
    font-size: 1.4rem;
    line-height: 1.4;
    letter-spacing: 0.02em;
    text-align: center;
    justify-content: center;

    padding: 1.4rem 1.6rem;

    border: 2px solid $color-text-primary;

    &:after {
      background-color: $color-text-primary !important;
    }

    @media (hover: hover) {
      &:hover {
        color: $color-text-primary !important;
        box-shadow: 0px 20px 25px -5px #0000001a;
      }
    }
  }

  &__magazine-excerpt {
    width: 16rem;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
    margin-bottom: $spacing-05-20;
    color: $color-text-secondary;
    font-variant-numeric: lining-nums tabular-nums;
    font-family: $font-archivo !important;
    font-size: 1.4rem;
    font-weight: 500;
    line-height: 120%;
    transition: color 0.3s ease;
  }
}
