<?php

/**
 * The ID of the current menu item
 * @var WP_Nav_Menu
 */
$menu_item = $args['menu_item'] ?? null;

/**
 * The ID of the primary menu
 * @var int
 */
$primary_menu_id = $args['primary_menu_id'] ?? null;

/**
 * The submenu items
 * @var array
 */
$child_items = $menu_item && $primary_menu_id ? wp_get_nav_menu_items($primary_menu_id) : null;

if ($child_items) {
	$child_items = array_filter($child_items, function ($item) use ($menu_item) {
		return $item->menu_item_parent == $menu_item->ID;
	});

    $child_items = array_slice($child_items, 0, 24);
}

$childCount = ! empty($child_items) ? count($child_items) : 0;

/**
 * Position of the modal
 * @var string
 */
$position = $args['position'] ?? null;

/**
 * Navigation title
 * @var string
 */
$navigation_title = get_post_meta($menu_item->ID, 'navigation_title', true) ?? null;

/**
 * Link text to category
 * @var string
 */
$link_text_to_category = get_post_meta($menu_item->ID, 'link_text_to_category', true) ?? '';

/**
 * Special list title
 * @var string
 */
$special_list_title = get_post_meta($menu_item->ID, 'special_list_title', true) ?? null;

/**
 * Special list
 * @var array|false|null $special_list
 */
$special_list = ($childCount > 18) ? null : get_field('special_list', $menu_item->ID) ?? null;

/**
 *  The ID of the latest magazine post
 * @var int|null
 */
$exceedsLimit = ($childCount > 12 && ! empty($special_list)) ||
                ($childCount > 18);
$magazine_id  = ! empty($child_items) && $exceedsLimit ? null : get_post_meta($menu_item->ID, 'related_magazine', true) ?? null;

/**
 * The base URL of the site
 * @var string
 */
$base_url = $_SERVER['SERVER_NAME'];
?>

<?php if (!empty($child_items)) : ?>

	<div class="nav-hover-modal container" data-menu-id="<?= $menu_item->ID ?>">

		<div class="nav-hover-modal__subcategories-wrapper">

			<div class="nav-hover-modal__subcategories-special-wrapper">

                <div class="nav-hover-modal__subcategories-menu-wrapper">
                    <?php
                    if ($navigation_title): ?>
                        <div class="nav-hover-modal__subcategories-title">
                            <?= $navigation_title ?>
                        </div>
                    <?php
                    endif; ?>

                    <div class="nav-hover-modal__subcategories">

                        <?php
                        foreach ($child_items as $child_item) : ?>

                            <?php
                            $is_outside_url = ! str_contains($child_item->url, $base_url);
                            ?>

                            <a href="<?= $child_item->url; ?>" <?= $is_outside_url ? 'target="_blank" rel="noopener"' : '' ?>
                               class="nav-hover-modal__subcategory link link--small link--nodecoration"><?= $child_item->title; ?></a>

                        <?php
                        endforeach; ?>

                    </div>
                </div>

                <div class="nav-hover-modal__special-list-wrapper">
                    <?php
                    if ($special_list_title): ?>
                        <div class="nav-hover-modal__special-list-title">
                            <?= $special_list_title ?>
                        </div>
                    <?php
                    endif; ?>

                    <div class="nav-hover-modal__special-list">

                        <div class="nav-hover-modal__special-list-items">

                            <?php
                            if ($special_list) :
                                foreach ($special_list as $item) : ?>

                                    <a href="<?= $item['link']['url']; ?>"
                                       class="nav-hover-modal__special-list-item link link--small link--nodecoration"><?= $item['link']['title']; ?></a>

                                <?php
                                endforeach;
                            endif;
                            ?>

                        </div>
                    </div>
                </div>
			</div>

            <?php
            if ( ! str_starts_with($menu_item->url, '#')) { ?>
                <div class="nav-hover-modal__archive-link-wrapper">

                    <a href="<?= $menu_item->url; ?>" class="nav-hover-modal__archive-link link link--small
                    link--nodecoration link--icon after-icon after-icon--arrow"><?= $link_text_to_category !== '' ?
                            $link_text_to_category : __('See More', 'FORBES')
                        ?></a>

                </div>
                <?php
            } ?>

		</div>

		<?php if ($magazine_id) : ?>

			<div class="nav-hover-modal__magazine-wrapper">

				<div class="nav-hover-modal__magazine-inner-wrapper">

					<div class="nav-hover-modal__magazine-image-wrapper">

						<a href="<?= get_the_permalink($magazine_id); ?>">
							<div class="nav-hover-modal__magazine-container">

								<?php
								if (has_post_thumbnail($magazine_id)) : ?>

									<?= get_the_post_thumbnail(
										$magazine_id,
										'thumbnail',
										['class' => 'nav-hover-modal__magazine', 'alt' => get_the_title($magazine_id),
                                         'loading' => 'lazy',
                                         'srcset'  => get_the_post_thumbnail_url(
                                                          $magazine_id,
                                                          'small'
                                                      ) . ' 2x'
                                        ]
									) ?>

								<?php
								endif; ?>

							</div>
						</a>

					</div>

					<div class="nav-hover-modal__magazine-text-container">

						<h6><?= get_the_title($magazine_id) ?></h6>

						<time datetime="<?= get_the_date('Y-m-d', $magazine_id) ?>"
							  class="nav-hover-modal__date"><?= get_the_date('F Y', $magazine_id) ?></time>

						<a href="<?= get_the_permalink($magazine_id); ?>"
						   class="nav-hover-modal__magazine-link link link--small link--nodecoration link--icon after-icon after-icon--arrow"><?= esc_html__(
								'Find out more!',
								'FORBES'
							); ?></a>

					</div>

				</div>

			</div>

		<?php endif; ?>

	</div>

<?php endif; ?>
