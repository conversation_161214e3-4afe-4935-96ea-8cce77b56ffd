.breaking-news-slider {
  margin-bottom: $spacing-07-32;

  &__news-wrapper {
    position: relative;
  }

  &__news-item {
    max-width: 25.5rem;
    text-decoration: none !important;
    align-items: flex-start;
  }

  &__read-time {
    display: block;
    margin-bottom: $spacing-01;
    color: $color-text-secondary;

    &--home-mobile {
      display: none;
    }
  }

  &__news-title {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;

    @media (hover: hover) {
      &:hover {
        text-decoration: underline;
      }
    }
  }

  &__image-wrapper {
    position: relative;
    height: 0;
    padding-top: 56.25%;
    width: 100%;
    margin-bottom: $spacing-01;
  }

  &__image {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }

  &--single {
    margin-bottom: $spacing-09-48;
    box-sizing: content-box;

    .splide__list {
      height: auto;
    }

    .slider {
      &__next-arrow,
      &__prev-arrow {
        width: 2.4rem;
        height: 2.4rem;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-09-48;

    &--single {
      margin-bottom: $spacing-07-32;
      padding: $spacing-06-24 $spacing-00 $spacing-06-24 $spacing-00;
    }

    .slider {
      &__prev-arrow-wrapper {
        display: none !important;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__read-time {
      display: none;

      &--home-mobile {
        display: block;
      }
    }

    &--single {
      .breaking-news-slider {
        &__read-time {
          display: block;
          &--home-mobile {
            display: none;
          }
        }
      }
    }
  }
}

.home-breaking-news__background {
  position: absolute;
  top: 0;
  height: 100%;
  width: 100vw;
  left: calc((100% - 100vw) / 2);
  background-color: $color-surface-secondary;
  z-index: 0;
}

.home-breaking-news.type-b {
  margin-bottom: $spacing-08-40;

  .breaking-news-slider {
    position: relative;
    padding-block: $spacing-06-24;
    z-index: 1;

    &__read-time {
      font-size: 1.2rem;
      line-height: 1.44rem;
      font-weight: 600;
      color: $color-tag-life-hover;
      margin-bottom: $spacing-01;
      text-transform: none !important;
    }

    @keyframes flip {
      from {
        transform: none;
      }

      to {
        transform: scaleY(-1);
      }
    }

    @-webkit-keyframes flip {
      from {
        transform: none;
      }

      to {
        transform: scaleY(-1);
      }
    }

    &__featured-publish-time {
      margin-right: $spacing-00;
    }

    &--single {
      .breaking-news-slider {
        &__title {
          color: $color-tag-life-default;
        }
      }
    }
  }

  .category-header {
    margin-bottom: $spacing-00;
    padding-top: $spacing-06-24;
  }
}

@media screen and (max-width: map-get($container-max-widths, lg )) {
  .home-breaking-news.type-b {
    margin-bottom: $spacing-07-32;

    .breaking-news-slider {
      width: initial;
      margin-left: $spacing-00;
      padding-left: $spacing-00;

      &--single {
        padding-bottom: $spacing-07-32;

        .breaking-news-slider {
          &__news-wrapper {
            padding-bottom: $spacing-07-32;
            padding-left: $spacing-00;
            margin-right: -1.6rem;
          }

          &__news-item {
            &:first-child {
              margin-left: $spacing-02;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .home-breaking-news.type-b {
    margin-bottom: $spacing-02;

    .breaking-news-slider {
      padding-top: $spacing-04-16;
      padding-bottom: $spacing-06-24;
      margin-bottom: $spacing-00;

      &__read-time {
        line-height: 2.4rem !important;
        display: block;

        &--home-mobile {
          display: none;
        }
      }
    }
  }
}
