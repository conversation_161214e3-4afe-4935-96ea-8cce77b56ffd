document.addEventListener('DOMContentLoaded', function () {
  jQuery(function ($) {
    if (!$('.breaking-news-slider').length) return;

    const newsSliders = $('.breaking-news-slider__news-wrapper');

    const initSliders = () => {
      newsSliders.each((index, slider) => {
        const type = $(slider).data('type');

        const isSingle = $(slider).parent().hasClass('breaking-news-slider--single');

        const splide = new Splide(slider, {
          type: 'slide',
          perPage: isSingle ? 3 : 4,
          perMove: 1,
          gap: '3rem',
          pagination: false,
          arrows: true,
          focus: 'number',
          omitEnd: true,
          speed: 300,
          breakpoints: {
            992: {
              perPage: 2.5,
              arrows: false,
              pagination: true,
            },
            768: {
              perPage: 1.5,
              arrows: false,
            },
          },
          classes:
            type === 'b'
              ? {
                  arrows: 'splide__arrows splide__arrows--gray',
                  arrow: 'splide__arrow splide__arrow--bn',
                  pagination: 'splide__pagination splide__pagination--bn',
                }
              : {},
        }).mount();

        window.splideSliders = window.splideSliders || [];
        window.splideSliders.push(splide);
      });
    };

    const isAdmin = $('body').hasClass('wp-admin');

    if (isAdmin && wp) {
      const { select, subscribe } = wp.data;
      const closeListener = subscribe(() => {
        const isReady = select('core/editor').__unstableIsEditorReady();
        if (isReady) {
          closeListener();

          initSliders();
        }
      });
    } else {
      initSliders();
    }
  });
});
