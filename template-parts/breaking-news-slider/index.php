<?php

/**
 * Whether the block is on the home page or in a single article
 * @var string
 */
$location = $args['location'] ?? '';

/**
 * Whether to show posts from the last 24hours or the latest 15 regardless of publish date
 * @var boolean
 */
$last_24_hours = $args['last_24'] ?? false;

/**
 * The breaking news tag ID
 * @var int
 */
$breaking_news_tag_id = get_field('breaking_news_tag', 'option');
$breaking_news_tag    = get_term($breaking_news_tag_id);

/**
 * Whether to display read time or publish date above the titles
 */
$tag_display = get_field('tag_display');

/**
 * The brandvoice category IDs, that need to be excluded from the query
 * @var array
 */
$brandvoice_category_ids = !empty(get_field('brandvoice_categories', 'option')) ? get_field('brandvoice_categories', 'option') : [];

/**
 * The ID of the print category
 * @var int
 */
$print_category = get_field('print_category', 'option');

// Combine both category IDs into a single array for exclusion
$excluded_categories = array_filter(array_merge($brandvoice_category_ids, [$print_category]));

$categories_to_exclude = !empty($excluded_categories) ? array(
    'relation' => 'AND',
    array(
        'taxonomy' => CATEGORY_TYPE,
        'field'    => 'term_id',
        'terms'    => $excluded_categories,
        'operator' => 'NOT IN',
    )
) : array();

$query_args = array(
	'page'			   => $location,
	'id' 			   => 'breaking_news',
	'posts_per_page'   => 15,
	'post_type'   	   => 'post',
	'post_status' 	   => 'publish',
	'order_by'	  	   => 'date',
	'order'		  	   => 'DESC',
	'tax_query'		   => $categories_to_exclude,
	'post__not_in'   => [get_the_ID()],
);

if ($last_24_hours) {
	$query_args['posts_per_page'] = -1;
	$query_args['date_query'] = array(
		array(
			'after'  => '24 hours ago'
		)
	);
}

if ($breaking_news_tag instanceof \WP_Term) {
    $query_args['tax_query'][] = array(
        'taxonomy' => $breaking_news_tag->taxonomy,
        'field'    => 'term_id',
        'terms'    => array($breaking_news_tag->term_id)
    );
}

$query = new WP_Query($query_args);
?>

<?php if ($query->have_posts()) : ?>

	<div class="breaking-news-slider<?= 'single' === $location ? ' breaking-news-slider--single' : ''; ?>">
		<section id="news-slider" class="breaking-news-slider__news-wrapper splide" aria-label="Breaking News Carousel" data-type="b">

			<div class="splide__track">

				<div class="splide__list">

					<?php while ($query->have_posts()) : $query->the_post(); ?>

						<a href="<?php the_permalink(); ?>" class="breaking-news-slider__news-item splide__slide">

							<div>

								<span class="breaking-news-slider__read-time cta-link-tag"><?= $tag_display === 'publishdate' ? frontend_get_publish_time(get_the_ID()) : get_post_read_time(get_the_ID()); ?></span>
								<span class="breaking-news-slider__read-time breaking-news-slider__read-time--home-mobile cta-link-tag"><?= get_post_read_time(get_the_ID()); ?></span>

								<h4 class="breaking-news-slider__news-title link-14"><?php the_title(); ?></h4>

							</div>

						</a>

					<?php endwhile; ?>

					<?php wp_reset_query(); ?>

				</div>

			</div>

		</section>

	</div>

<?php endif; ?>

<?php if (is_admin() && !$query->have_posts()) : ?>

	<p><?= esc_html__('No posts found from the last 24 hours, block is not shown', 'FORBES'); ?></p>

<?php endif; ?>
