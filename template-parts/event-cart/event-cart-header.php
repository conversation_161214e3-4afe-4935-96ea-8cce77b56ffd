<?php
/**
 * Event Cart Header Template
 *
 * Displays the header section for event cart pages
 * Based on Figma design: node-id=7363-28987
 */

?>

<header class="tw:flex tw:flex-col tw:items-center tw:gap-10 tw:w-full tw:max-w-[730px] tw:mx-auto tw:my-10">

  <!-- Hero Image Section -->
  <div class="tw:relative tw:w-full tw:max-w-[730px] tw:h-[158px] tw:overflow-hidden 
      tw:shadow-[0px_12px_27px_0px_rgba(0,0,0,0.16),0px_49px_49px_0px_rgba(0,0,0,0.14),0px_110px_66px_0px_rgba(0,0,0,0.08),0px_196px_79px_0px_rgba(0,0,0,0.02)]">

    <!-- Background Image -->
    <?php
    if ($ticket_banner = get_field('ticket_banner')):
      ?>
      <?= wp_get_attachment_image(
      $ticket_banner,
      'full',
      false,
      array(
        'class' => 'tw:absolute tw:inset-0 tw:w-full tw:h-full tw:object-cover',
        'alt'   => get_the_title()
      )
    ) ?>
    <?php
    endif; ?>

  </div>

  <!-- Text Content Section -->
  <div class="tw:flex tw:flex-col tw:gap-6 tw:w-full tw:max-w-[730px]">

    <!-- Headline Section -->
    <h2 class="h2-condensed tw:text-center">
      <?= get_the_title() ?>
    </h2>

    <!-- Description Message -->
    <div class="basic-16-new tw:text-text-secondary tw:text-center">
      <?php
        $description = get_field('ticket_text');
        echo $description;
      ?>
    </div>

    <!-- Info Links Section -->
    <div class="tw:flex tw:flex-row tw:justify-center tw:gap-6 tw:max-w-[730px] tw:w-full tw:min-h-fit">

      <!-- Date/Time Link -->
      <?php
      $event_date = get_field(
        'cz' === (defined('COUNTRY') ? COUNTRY : 'cz') ? 'eventStartDate' : 'starting_date'
      );
      if (!empty($event_date)):
      ?>
        <div class="tw:flex tw:flex-row tw:justify-center tw:items-center tw:gap-1 tw:bg-white tw:w-fit tw:h-fit">
          <!-- Calendar Icon -->
          <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.5 2V4M5.5 2V4M3.5 7H13.5M2.5 3H14.5V14H2.5V3Z" stroke="#020617" stroke-width="1.5"
                  stroke-linecap="square" />
          </svg>
  
          <span class="link-16-new tw:text-link-default">
              <?php
                $formatted_date = $event_date ? date('j. M Y', strtotime($event_date)) : '4. jún 2026';
                $formatted_time = date('H:i', strtotime($event_date)) ?: '08:30';
                echo $formatted_date . ' ' . $formatted_time;
              ?>
          </span>
        </div>
      <?php endif; ?>

      <!-- Location Link -->
      <?php $event_location = get_field('miesto_konania'); 
      if (!empty($event_location)):
      ?>
        <div class="tw:flex tw:flex-row tw:justify-center tw:items-center tw:gap-1 tw:bg-white tw:w-fit tw:h-fit">
          <!-- Location Pin Icon -->
          <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M8.49978 8.74866C9.5647 8.74866 10.428 7.88538 10.428 6.82046C10.428 5.75554 9.5647 4.89225 8.49978 4.89225C7.43486 4.89225 6.57157 5.75554 6.57157 6.82046C6.57157 7.88538 7.43486 8.74866 8.49978 8.74866Z"
              stroke="#020617" stroke-width="1.5" stroke-linecap="square" />
            <path
              d="M13.3203 6.82051C13.3203 11.159 8.49983 14.5333 8.49983 14.5333C8.49983 14.5333 3.67932 11.159 3.67932 6.82051C3.67932 5.54203 4.1872 4.31592 5.09122 3.4119C5.99524 2.50787 7.22135 2 8.49983 2C9.77831 2 11.0044 2.50787 11.9085 3.4119C12.8125 4.31592 13.3203 5.54203 13.3203 6.82051Z"
              stroke="#020617" stroke-width="1.5" stroke-linecap="square" />
          </svg>
  
          <span class="link-16-new tw:text-link-default">
              <?php
                echo $event_location;
              ?>
          </span>
        </div>
      <?php endif; ?>

      <!-- More Info Link -->
      <a href="<?= esc_url(remove_query_arg('cart')) ?>"
         class="tw:flex tw:flex-row tw:justify-center tw:items-center tw:gap-1 tw:bg-white tw:w-fit tw:h-fit 
         tw:!no-underline">
                <span class="link-16-new tw:text-link-default">
                    <?php
                    _e('More info about the event', 'FORBES')
                    ?>
                </span>
        <!-- External Link Icon -->
        <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M3.5 13L13.5 3M13.5 3V9M13.5 3H7.5" stroke="#020617" stroke-width="1.5" stroke-linecap="square" />
        </svg>
      </a>

    </div>

  </div>

</header>
