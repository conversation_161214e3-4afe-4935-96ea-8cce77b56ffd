<?php
// Prepare event cart data
$event_items = []; // This should be populated with actual event ticket data
$back_url = remove_query_arg('cart'); // Remove cart parameter to go back to event page
$checkout_url = '/checkout'; // This should be the actual checkout URL

// For now, let's add some sample data to test the component
$event_items = [
  [
    'id' => '1',
    'eventName' => get_the_title(),
    'ticketType' => 'Early Bird (Dospelý)',
    'price' => 89.0,
    'currency' => '€',
    'quantity' => 0,
  ],
  [
    'id' => '2',
    'eventName' => get_the_title(),
    'ticketType' => 'Early Bird (Študent)',
    'price' => 39.0,
    'currency' => '€',
    'quantity' => 0,
  ],
];
?>

<main class="single-event">

  <div class="container">

    <?php get_template_part('template-parts/event-cart/event-cart-header'); ?>

    <div id="reactEventCart"
         data-event-items="<?php echo esc_attr(json_encode($event_items)); ?>"
         data-back-url="<?php echo esc_attr($back_url); ?>"
         data-checkout-url="<?php echo esc_attr($checkout_url); ?>">
    </div>

  </div>

</main>
