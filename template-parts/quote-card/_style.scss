.quote-card {
  padding: $spacing-12-80 $spacing-13-120;

  &__text {
    position: relative;
    color: $color-white;
    font-size: 3.2rem;
    font-weight: 700;
    font-family: $font-noto-serif;
    line-height: 4rem;
    margin-bottom: $spacing-07-32;

    &:before,
    &:after {
      content: '';
      width: 5.6rem;
      height: 4.4rem;
      position: absolute;
      background: url('assets/icons/icon-quote.svg') no-repeat;
      background-size: contain;
    }

    &__date {
      color: $color-text-secondary;
      margin-bottom: $spacing-07-32;
    }

    &:before {
      top: 0;
      left: -7.8rem;
    }

    &:after {
      bottom: 0;
      right: -7.8rem;
      transform: rotate(180deg);
    }
  }

  &__name {
    font-family: $font-archivo;
    color: $color-white;
    line-height: 2rem;
    font-size: 1.8rem;
    font-weight: 500;
    margin-left: $spacing-06-24;
  }

  &__image {
    width: 72rem;
    height: 72rem;
  }
}

@media screen and (max-width: map-get($container-max-widths, lg)) {
  .quote-card {
    padding: $spacing-10-56 $spacing-04-16;

    &__text {
      font-size: 2rem;
      line-height: 2.8rem;

      &:before,
      &:after {
        width: 3.2rem;
        height: 2.5rem;
      }

      &:before {
        top: -3.5rem;
        left: 0;
      }

      &:after {
        bottom: -5.5rem;
        right: 0;
      }
    }

    &__date {
      margin-bottom: $spacing-11-64;
    }
  }
}
