<?php

$postId = $args['post_id'] ?? get_the_ID();
$recommendation = new Forbes\Recommendation\Manager();

$strategies = array_map(
	fn(Forbes\Recommendation\Strategy $strategy): array => [
		'identifier' => $strategy->getIdentifier(),
		'weight' => $strategy->getWeight(),
	],
	$recommendation->getStrategiesForPost($postId)
);

$postsByStrategy = [];

if (current_user_can('administrator')) {
	foreach ($strategies as $strategy) {
		$posts = $recommendation
			->getStrategyByIdentifier($strategy['identifier'])
			->getPosts($postId);

		$postsByStrategy[$strategy['identifier']] = wp_list_pluck($posts, 'ID');
	}
} ?>

<div
	id="reactAIRecommendation"
	data-strategies=<?= esc_attr(json_encode($strategies)) ?>
	data-debug="<?= esc_attr(json_encode($postsByStrategy)) ?>"
></div>
