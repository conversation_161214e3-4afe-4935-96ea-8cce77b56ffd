.article-card {
  &__excerpt {
    -webkit-line-clamp: 4;
    text-decoration: none;

    @media (hover: hover) {
      &:hover {
        color: $color-text-primary !important;
      }
    }
  }

  &__image-wrapper {
    position: relative;
    height: 0;
    width: 100%;
    padding-top: 56.25%;
    overflow: hidden;
    margin-bottom: $spacing-04-16;

    @supports (aspect-ratio: 16/9) {
      aspect-ratio: 16/9;
      padding-top: $spacing-00;
      height: auto;
    }

    @media (hover: hover) {
      &:hover {
        + .article-card__content-wrapper .heading {
          text-decoration: underline !important;
        }
      }
      &:hover {
        + .article-card__content-wrapper .article-card__excerpt {
          color: $color-text-primary !important;
        }
      }
    }
  }

  &__title-wrapper {
    @media (hover: hover) {
      &:hover {
        .heading {
          text-decoration: underline;
        }

        ~ .article-card__excerpt {
          color: $color-text-primary !important;
        }
      }
    }
  }

  &__title-link {
    text-decoration: none;

    .hovered {
      text-decoration: underline !important;
    }
  }

  &__image {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    @include background-properties;
    background-size: cover;
    object-fit: cover;
    transition: transform 0.3s ease-in-out;

    img {
      object-fit: cover;
      min-height: 100%;
      max-height: 100%;
      width: 100%;
    }
  }

  &__category,
  &__tag-wrapper {
    position: absolute;
    bottom: 0;
    left: 0;

    &--content {
      display: none;
    }

    &--image {
      display: block;
    }

    &.relative {
      position: relative;
      font-weight: 400 !important;
      font-size: 1.4rem !important;
      line-height: 1.6rem !important;
      padding: $spacing-00;
      background-color: transparent;
    }

    &:before {
      width: 2rem;
    }
  }

  .article-meta {
    margin: $spacing-02 $spacing-00 $spacing-00;
    border: none;

    &__button--share,
    &__button--premium,
    &__button--bookmark span {
      display: none !important;
    }
  }

  &__content-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &:has(.article-card__excerpt:hover) .heading {
      text-decoration: underline !important;
    }
  }

  h4.heading {
    font-size: 1.8rem;
    line-height: 2.376rem;
  }

  &__read-time,
  &__read-time-text {
    color: $color-text-secondary;
  }

  &__read-time {
    position: relative;
    &:before {
      content: '';
      position: absolute;
      left: -0.8rem;
      top: 50%;
      transform: translateY(-50%);
      width: 0.2rem;
      height: 0.1rem;
      border-bottom: 0.2rem solid $color-text-secondary;
      border-radius: 50%;
    }

    &--home-mobile {
      display: none;
    }
  }

  &--small {
    display: flex;

    .article-meta {
      &__reading-time {
        display: none !important;
      }
    }

    .article-card {
      &__excerpt {
        display: none;
      }

      &__image-wrapper {
        margin-bottom: $spacing-00;
        min-width: 25.5rem;
        max-width: 25.5rem;
        height: 14.4rem;
        margin-right: $spacing-04-16;
      }
    }

    &.article-card--search {
      .article-card {
        &__image-wrapper {
          min-width: 16rem;
          max-width: 16rem;
          height: 11rem;

          .badge {
            display: flex;
          }
        }
      }

      .article-details {
        &__read-time {
          display: none;
        }
        &__publish-time {
          &:after {
            content: none;
          }
        }
      }
    }
  }

  &--featured,
  &--featured-large {
    .article-meta {
      &__button--share,
      &__button--premium,
      &__button--bookmark span {
        display: initial !important;
      }

      .link {
        &--save {
          .link__text {
            display: block;
          }
        }
      }
    }

    .article-card__title {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 4;
      overflow: hidden;
      visibility: visible;
      transition: color 0.3s ease;

      &.old {
        font-weight: 500 !important;
      }
    }

    .article-card__image-wrapper {
      margin-bottom: $spacing-04-16;
    }

    .article-card__excerpt {
      -webkit-line-clamp: 3;
    }
  }

  &--featured-large {
    .article-card {
      &__content-wrapper {
        padding: $spacing-06-24 $spacing-08-40 $spacing-00 $spacing-00;
      }

      &__category {
        position: relative;
        padding: $spacing-00;
        margin-right: $spacing-04-16;
        line-height: 1.8rem;

        &:after {
          content: '';
          position: absolute;
          left: 100%;
          top: 50%;
          transform: translateY(-50%);
          width: 0.2rem;
          height: 0.1rem;
          border-bottom: 0.2rem solid $color-text-secondary;
          border-radius: 50%;
        }
      }

      &__tag-wrapper {
        &--content {
          display: block;
          position: static;
          margin-bottom: $spacing-01;

          .tag {
            padding: $spacing-00 $spacing-04-16 $spacing-00 $spacing-06-24;
            background-color: transparent;
          }
        }

        &--image {
          display: none;
        }
      }
    }

    h4.heading {
      font-size: 2.8rem;
      line-height: 3.36rem;
    }

    .article-meta {
      margin-top: $spacing-04-16;
    }
  }

  &--featured {
    h4.heading {
      font-size: 2.2rem;
      line-height: 2.904rem;
    }

    &__excerpt {
      -webkit-line-clamp: 3;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl)) {
    &--small {
      .article-card {
        &__image-wrapper {
          max-width: 50%;
          min-width: 50%;
          height: auto;
          margin-right: $spacing-04-16;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    &--featured,
    &--featured-large {
      .article-card {
        &__title {
          padding-left: $spacing-00;
        }
      }

      .article-meta {
        margin: $spacing-04-16 $spacing-00 $spacing-00;
        border: none;

        &__button--share,
        &__button--bookmark span {
          display: none !important;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &--featured,
    &--featured-large {
      .article-card {
        .article-details {
          padding-left: $spacing-00;
        }

        &__image-wrapper {
          margin-bottom: $spacing-03-12;
        }

        &__category {
          display: none;
        }

        &__title {
          font-size: 2.4rem;
          line-height: 3.2rem;
        }
      }
    }

    &--featured-large {
      margin-left: calc((100% - 100vw) / 2);
      width: 100vw;

      h4.heading {
        font-size: 2.5rem;
        line-height: 3rem;
      }

      .article-card__excerpt {
        -webkit-line-clamp: 8;
      }
    }

    &--small {
      position: relative;
      padding-bottom: $spacing-09-48;

      .article-meta {
        position: absolute;
        width: 100%;
        bottom: 0;
        left: 0;

        &__button--share {
          display: initial !important;
        }
      }

      .article-card {
        &__tag-wrapper {
          &--content {
            display: block;
            position: static;
          }

          &--image {
            display: none;
          }
        }

        &__image-wrapper {
          min-width: 12.8rem !important;
          max-width: 12.8rem !important;
          height: 7.2rem;
          margin-top: $spacing-07-32;
        }

        &__content-wrapper {
          max-width: 100% !important;
        }
      }
    }
  }
}
