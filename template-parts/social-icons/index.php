<?php

/**
 * The ID of the current post
 * @var int
 */
$id = $args['post_id'] ?? null;

/**
 * Whether its for a list item
 * @var boolean
 */
$is_list_item = $args['list'] ?? false;

if ($id || $is_list_item) {
	/**
	 * Facebook link
	 * @var string
	 */
	$facebook = $is_list_item ? ($args['facebook'] ?? null) : 'http://www.facebook.com/sharer.php?u=' . get_the_permalink($id);

	/**
	 * Twitter Link
	 * @var string
	 */
	$twitter = $is_list_item ? ($args['twitter'] ?? null) : 'http://twitter.com/share?url=' . get_the_permalink($id);

	/**
	 * LinkedIn Link
	 * @var string
	 */
	$linkedin = $is_list_item ? ($args['linkedin'] ?? null) : 'http://www.linkedin.com/shareArticle?mini=true&url=' . get_the_permalink($id);
}

if ($is_list_item) {
	/**
	 * Youtube link
	 * @var string
	 */
	$youtube = $args['youtube'] ?? null;

	/**
	 * TikTok link
	 * @var string
	 */
	$tiktok = $args['tiktok'] ?? null;

	/**
	 * Instagram Link
	 * @var string
	 */
	$instagram = $args['instagram'] ?? null;
}
?>

<?php if ($id || $is_list_item) : ?>

	<div class="social-icons h-d--flex h-justify-content--start">

		<?php if ($facebook) : ?>

			<a href="<?= $facebook; ?>" target="_blank" rel="noopener" class="social-icons__icon icon--facebook"></a>

		<?php endif; ?>

		<?php if ($twitter) : ?>

			<a href="<?= $twitter; ?>" target="_blank" rel="noopener" class="social-icons__icon icon--twitter"></a>

		<?php endif; ?>

		<?php if ($linkedin) : ?>

			<a href="<?= $linkedin; ?>" target="_blank" rel="noopener" class="social-icons__icon icon--linkedin"></a>

		<?php endif; ?>

		<?php if (isset($tiktok)) : ?>

			<a href="<?= $tiktok; ?>" target="_blank" rel="noopener" class="social-icons__icon icon--tiktok"></a>

		<?php endif; ?>

		<?php if (isset($youtube)) : ?>

			<a href="<?= $youtube; ?>" target="_blank" rel="noopener" class="social-icons__icon icon--youtube"></a>

		<?php endif; ?>

		<?php if (isset($instagram)) : ?>

			<a href="<?= $instagram; ?>" target="_blank" rel="noopener" class="social-icons__icon icon--instagram"></a>

		<?php endif; ?>

		<div class="copy-url__wrapper">

			<button class="copy-url social-icons__icon icon--url h-cursor--pointer" aria-label="Copy Url"></button>

			<div class="copy-url__confirmation-wrapper">
				<span class="copy-url__confirmation footnote"><?= esc_html__('Link Copied', 'FORBES'); ?></span>
			</div>

		</div>

	</div>

<?php endif; ?>
