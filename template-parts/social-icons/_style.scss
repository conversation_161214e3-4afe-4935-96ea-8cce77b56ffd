.social-icons {
  margin: $spacing-00 -1.1rem;

  &__icon {
    @include mask-properties;
    width: 1.8rem;
    height: 1.8rem;
    margin: $spacing-00 1.1rem;
    background-color: $color-text-secondary;
    transition: all 0.3s ease;
    padding: $spacing-00;

    @media (hover: hover) {
      &:hover {
        background-color: $color-text-brand;
      }
    }

    &.icon--facebook {
      mask-image: url('assets/icons/icon-facebook.svg');
      -webkit-mask-image: url('assets/icons/icon-facebook.svg');
    }

    &.icon--twitter {
      mask-image: url('assets/icons/icon-twitter.svg');
      -webkit-mask-image: url('assets/icons/icon-twitter.svg');
    }

    &.icon--linkedin {
      mask-image: url('assets/icons/icon-linkedin.svg');
      -webkit-mask-image: url('assets/icons/icon-linkedin.svg');
      display: inline-block;
    }

    &.icon--instagram {
      mask-image: url('assets/icons/icon-instagram.svg');
      -webkit-mask-image: url('assets/icons/icon-instagram.svg');
      display: inline-block;
    }

    &.icon--youtube {
      mask-image: url('assets/icons/icon-youtube.svg');
      -webkit-mask-image: url('assets/icons/icon-youtube.svg');
      display: inline-block;
    }

    &.icon--tiktok {
      mask-image: url('assets/icons/icon-tik-tok.svg');
      -webkit-mask-image: url('assets/icons/icon-tik-tok.svg');
      display: inline-block;
    }

    &.icon--url {
      mask-image: url('assets/icons/icon-url.svg');
      -webkit-mask-image: url('assets/icons/icon-url.svg');
    }
  }

  .copy-url-hu {
    &.clicked {
      animation: shake 1s;
    }
  }

  .copy-url {
    &__wrapper {
      position: relative;
    }

    &__confirmation-wrapper {
      position: absolute;
      bottom: -5rem;
      left: 50%;
      transform: translateX(-50%);
      background-color: $post-background-color;
      padding: $spacing-02 $spacing-04-16;
      border: 0.1rem solid $color-surface-secondary;
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;

      &:after {
        content: '';
        position: absolute;
        top: -1rem;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 1rem solid transparent;
        border-right: 1rem solid transparent;

        border-bottom: 1rem solid $color-surface-secondary;
      }

      &.active {
        opacity: 1;
        z-index: 0;
      }
    }

    &__confirmation {
      font-size: 1.2rem;
      white-space: nowrap;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    padding-left: $spacing-02;

    .copy-url {
      &__confirmation-wrapper {
        left: unset;
        transform: none;
        right: 0;

        &:after {
          left: unset;
          transform: none;
          right: 1rem;
        }
      }
    }
  }
}

@keyframes shake {
  0% {
    transform: translateX(0.1rem);
  }
  10% {
    transform: translateX(-0.1rem);
  }
  20% {
    transform: translateX(0.1rem);
  }
  30% {
    transform: translateX(-0.1rem);
  }
  40% {
    transform: translateX(0.1rem);
  }
  50% {
    transform: translateX(-0.1rem);
  }
  60% {
    transform: translateX(0.1rem);
  }
  70% {
    transform: translateX(-0.1rem);
  }
  80% {
    transform: translateX(0.1rem);
  }
  90% {
    transform: translateX(-0.1rem);
  }
  100% {
    transform: translateX(0);
  }
}
