<!doctype html>
<html class="forbes-frontend" <?php
language_attributes(); ?>>

<head>
	<meta charset="<?php
	bloginfo('charset'); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="format-detection" content="telephone=no">
	<?php
	if ('cz' === COUNTRY) {
		Head\get_tag_meta();
	}; ?>
	<link rel="profile" href="https://gmpg.org/xfn/11">
	<link rel="icon" type="image/png" href="<?= get_template_directory_uri() . '/assets/icons/favicon-32x32.png' ?>"
		  sizes="32x32">
	<link rel="icon" type="image/png" href="<?= get_template_directory_uri() . '/assets/icons/favicon-16x16.png' ?>"
		  sizes="16x16">

	<!-- Pass constants to window -->
	<script>
		<?php if (defined('AI_API_URL')) : ?>
		window.AI_API_URL = <?= json_encode(AI_API_URL); ?>;
		<?php endif; ?>
		<?php if (defined('FEATURE_TOGGLE_MY_ACCOUNT')) : ?>
		window.FEATURE_TOGGLE_MY_ACCOUNT = <?= FEATURE_TOGGLE_MY_ACCOUNT ? 'true' : 'false'; ?>;
		<?php endif; ?>
	</script>

	<script>
		window.forbesTranslations = {};

		window.forbesCustomLeaderboardAdSlot = <?php echo json_encode(get_field('custom_leaderboard_ad_slot')); ?>;
	</script>

	<!-- Include Seznam scripts for CZ sites -->
	<?php
	if (COUNTRY === 'cz'): ?>
		<script type="text/javascript">
			// Function to set a cookie
			function setCookie(name, value, minutes, domain) {
				const expires = new Date(Date.now() + minutes * 60 * 1000).toUTCString();
				const domainAttribute = domain ? `; domain=${domain}` : '';
				const secureAttribute = location.protocol === 'https:' ? '; Secure' : '';
				const sameSiteAttribute = '; SameSite=None'; // Adjust if necessary

				document.cookie = `${name}=${value}; expires=${expires}; path=/${domainAttribute}${secureAttribute}${sameSiteAttribute}`;
			}

			// Function to get a cookie by name
			function getCookie(name) {
				const nameEQ = name + '=';
				const ca = document.cookie.split(';').map(c => c.trim());

				for (let i = 0; i < ca.length; i++) {
					if (ca[i].indexOf(nameEQ) === 0) return ca[i].substring(nameEQ.length);
				}

				return null;
			}

			// Function to check if the user is from Seznam Newsfeed
			function isNewsfeedUser() {
				const referrer = document.referrer.toLowerCase();
				const urlParams = new URLSearchParams(window.location.search.toLowerCase());
				const isFromSeznam = referrer.includes('seznam.cz') || (urlParams.has('utm_source') && urlParams.get('utm_source') === 'www.seznam.cz');

				console.log('Referrer:', referrer);
				console.log('UTM Source:', urlParams.get('utm_source'));
				console.log('isNewsfeedUser:', isFromSeznam ? 'User is from Seznam Newsfeed' : 'User is not from Seznam Newsfeed');

				return isFromSeznam;
			}

			// Function to set a session in both localStorage and cookies
			function setNewsfeedSession() {
				const sessionKey = 'szn_newsfeed_session';
				const sessionValue = 'szn_newsfeed';
				const expirationTimeInMinutes = 30; // Adjust if needed
				const domain = '.forbes.cz'; // Set domain to share cookie across subdomains

				// Set in localStorage
				const expirationTime = Date.now() + expirationTimeInMinutes * 60 * 1000;
				const sessionData = {
					value: sessionValue,
					expiry: expirationTime,
				};
				localStorage.setItem(sessionKey, JSON.stringify(sessionData));
				console.log('setNewsfeedSession:', 'Session set in localStorage for 30 minutes');

				// Set in cookies
				setCookie(sessionKey, sessionValue, expirationTimeInMinutes, domain);
				console.log('setNewsfeedSession:', 'Session cookie set for 30 minutes with domain', domain);
			}

			// Function to check if the session is still valid
			function getNewsfeedSession() {
				const sessionKey = 'szn_newsfeed_session';

				// Try to get from cookies first
				const cookieValue = getCookie(sessionKey);
				if (cookieValue) {
					console.log('getNewsfeedSession:', 'Session found in cookies');
					return cookieValue;
				}

				// If not found in cookies, try to get from localStorage
				const sessionData = localStorage.getItem(sessionKey);
				if (!sessionData) {
					console.log('getNewsfeedSession:', 'No existing session found');
					return null;
				}

				const data = JSON.parse(sessionData);
				if (new Date().getTime() > data.expiry) {
					localStorage.removeItem(sessionKey);
					console.log('getNewsfeedSession:', 'Session expired and removed');
					return null;
				}

				console.log('getNewsfeedSession:', 'Session is valid');
				return data.value;
			}

			// Main logic to detect and set the traffic key
			(function() {
				if (isNewsfeedUser()) {
					setNewsfeedSession();
				}

				const sessionValue = getNewsfeedSession();

				if (sessionValue) {
					window.__pxPageConfig = window.__pxPageConfig || {};
					window.__pxPageConfig.traffic = sessionValue;

					console.log('Traffic key set to:', window.__pxPageConfig.traffic);
				} else {
					console.log('Traffic key not set as session is not valid');
				}
			})();
		</script>

		<script async src="https://ssp.seznam.cz/static/js/ssp.js"></script>
	<?php
	endif; ?>

	<?php
	// If the cookiebot plugin is not active, include the Google Tag Manager code
	if ( ! function_exists('cookiebot_active') || ! cookiebot_active()) {
		$gtmScriptSrc  = 'https://www.googletagmanager.com/gtm.js?id=GTM-' . Head\get_GTM_id();
		$gtmSnippetId  = 'GTM-' . Head\get_GTM_id();
		$inlineSnippet = false;

		if (defined('COUNTRY') && COUNTRY === 'sk') {
			$gtmScriptSrc  = "https://xyz.forbes.sk/1vivnqsglt.js?c7mom=aWQ9R1RNLVRXVFI2SFc%3D&page=1";
			$gtmSnippetId  = 'c7mom=aWQ9R1RNLVRXVFI2SFc%3D&page=1';
			$inlineSnippet = true;
		} elseif (defined('COUNTRY') && COUNTRY === 'cz') {
			$gtmScriptSrc  = "https://xyz.forbes.cz/1vivnqsglt.js?c7mom=aWQ9R1RNLVBSMjc0Wg%3D%3D&page=1";
			$gtmSnippetId  = 'c7mom=aWQ9R1RNLVBSMjc0Wg%3D%3D&page=1';
			$inlineSnippet = true;
		}
		?>

		<!-- Google Tag Manager -->
		<script>
			(function(w, d, s, l, i) {
				w[l] = w[l] || [];
				w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
				var f = d.getElementsByTagName(s)[0],
					j = d.createElement(s)
					<?php if (! $inlineSnippet): ?>
					, dl = l != 'dataLayer' ? '&l=' + l : ''
					<?php endif; ?>;
				j.async = true;
				j.src = '<?php echo $gtmScriptSrc; ?>'<?php if (! $inlineSnippet): ?> + dl<?php endif; ?>;
				f.parentNode.insertBefore(j, f);
			})(window, document, 'script', 'dataLayer', '<?php echo $gtmSnippetId ?>');
		</script>
		<!-- End Google Tag Manager -->

		<?php
	}

	/**
	 * @return void
	 */
	function insertGoogleAnalyticsTag(): void
	{
		if (defined('COUNTRY') && COUNTRY === 'cz') { ?>
			<!-- Google tag (gtag.js) -->
			<script data-cookieconsent="statistics, marketing" async
					src="https://www.googletagmanager.com/gtag/js?id=G-04NDTVJD7C"></script>
			<script type="text/javascript" data-cookieconsent="statistics, marketing">
				window.dataLayer = window.dataLayer || [];

				function gtag() {
					dataLayer.push(arguments);
				}

				gtag('js', new Date());

				gtag('config', 'G-04NDTVJD7C');
			</script>
			<?php
		}
	}

	insertGoogleAnalyticsTag();
	?>

	<?php
	wp_head(); ?>

	<?php
	// Define $baseURI before any conditions
	$baseURI = '';
	switch (COUNTRY) {
		case 'cz':
			$baseURI = 'https://stage.forbes.cz/';
			break;
		case 'sk':
			$baseURI = 'https://stage.forbes.sk/';
			break;
		case 'hu':
			$baseURI = 'https://stage.forbes.hu/';
			break;
		default:
			$baseURI = 'https://stage.forbes.cz/';
			break;
	}

	if (($_SERVER['HTTP_X_AUTH_BEARER'] ?? '') === 'blablaheslojejasne') : ?>
		<esi:include src="/auth/esi/" />
	<?php
	elseif (WP_DEBUG == true) : ?>
		<?php
		$token = $_COOKIE['n_token'] ?? $_GET['token'] ?? '';
		if ($token) {
			$client = new \GuzzleHttp\Client([
				'base_uri' => $baseURI,
			]);
			try {
				echo $client->get('auth/fetchUser/', [
					'query' => [
						'token' => $token,
					],
					'auth'  => ['letMe', 'in'],
				])->getBody()->getContents();
			} catch (\Exception $ex) {
				// Consider logging the exception or handling it as required
			}
		}
		?>
	<?php
	endif; ?>

	<!-- Get all available public newsletter lists, authors, topics -->
	<?php
	getNewsletterLists(); ?>

	<!-- Print url of default user avatar to window -->
	<?php
	print_default_user_avatar_to_window(); ?>

	<!-- Print onboarding text data to window -->
	<?php
	print_onboarding_fields_to_window(); ?>

	<!-- Process AutoLogin -->
	<script>
		(() => {
			var urlParams = new URLSearchParams(window.location.search);
			var token = urlParams.get('autologin');
			if (token) {
				var baseUrl = '<?=DN_REMP_CRM_HOST?>';
				var currentUrlWithoutToken = window.location.href.split('?')[0];
				var encodedBackUrl = encodeURIComponent(currentUrlWithoutToken);
				var loginUrl = baseUrl + '/sign/processAutoLogin/' + token + '?back=' + encodedBackUrl;

				document.documentElement.classList.add('no-scroll');
				window.location.href = loginUrl;
			}
		})();
	</script>

	<!-- Pass configuration to React -->
	<script>
		window.crmUrl = '<?php echo DN_REMP_CRM_HOST; ?>';
		window.country = '<?php echo COUNTRY; ?>';
		window.baseUri = '/';
	</script>

	<?php
	if (defined('CONTENT_TYPE') && CONTENT_TYPE === "life") : ?>
		<script>
			window.contentType = 'life';
		</script>
		<style>
			:root {
				/* Regular mode styles */
				--color-text-brand: #BE185D !important;
				--color-link-hover: #BE185D !important;
				--color-tag-default: #BE185D !important;
				--color-tag-hover: #DB2777 !important;
			}

			/* Dark mode styles */
			.dark-mode :root {
				--color-text-brand: #F9A8D4 !important;
				--color-link-hover: #F9A8D4 !important;
				--color-tag-default: #F9A8D4 !important;
				--color-tag-hover: #F472B6 !important;
			}
		</style>
	<?php
	endif; ?>

	<?php
	if (COUNTRY === 'cz' && APP_ENV != 'production') { ?>

		<!-- Initialize Google Consent Mode -->

		<?php
	if (defined('WP_DEBUG') && ! WP_DEBUG) : ?>
		<script>
			window.dataLayer = window.dataLayer || [];

			function gtag() {
				dataLayer.push(arguments);
			}

			gtag('consent', 'default', {
				'ad_storage': 'denied',
				'analytics_storage': 'denied',
				'ad_user_data': 'denied',
				'ad_personalization': 'denied',
			});
		</script>
	<?php
	else : ?>
		<script>
			// fake consent for ads
			window.dataLayer = window.dataLayer || [];

			function gtag() {
				dataLayer.push(arguments);
			}

			gtag('consent', 'update', {
				'ad_storage': 'granted',
				'analytics_storage': 'granted',
				'ad_user_data': 'granted',
				'ad_personalization': 'granted',
				'wait_for_update': 500,
			});
		</script>
	<?php
	endif; ?>

	<?php
	if (defined('WP_DEBUG') && ! WP_DEBUG) : ?>
		<!-- Quantcast CMP Integration -->
		<script>
			document.addEventListener('DOMContentLoaded', () => {
				const locale = document.body.dataset.locale;
				let id;
				switch (locale) {
					case 'hu':
						id = '9_DaYuusTCpdu';
						break;
					case 'sk':
						id = '-asK1DScQFH82';
						break;
					case 'cz':
						id = 'GAZUxabXUu49b';
						break;
				}

				if (!id) return;

				var host = window.location.hostname;
				var element = document.createElement('script');
				var firstScript = document.getElementsByTagName('script')[0];
				var url = 'https://cmp.quantcast.com/choice/' + id + '/' + host + '/choice.js?tag_version=V2';
				element.async = true;
				element.type = 'text/javascript';
				element.src = url;
				firstScript.parentNode.insertBefore(element, firstScript);

				var TCF_LOCATOR_NAME = '__tcfapiLocator';
				var queue = [];
				var win = window;
				var cmpFrame;

				function addFrame() {
					var doc = win.document;
					var otherCMP = !!win.frames[TCF_LOCATOR_NAME];
					if (!otherCMP) {
						if (doc.body) {
							var iframe = doc.createElement('iframe');
							iframe.style.cssText = 'display:none';
							iframe.name = TCF_LOCATOR_NAME;
							doc.body.appendChild(iframe);
						} else {
							setTimeout(addFrame, 5);
						}
					}
					return !otherCMP;
				}

				function tcfAPIHandler() {
					var gdprApplies;
					var args = arguments;
					if (!args.length) {
						return queue;
					} else if (args[0] === 'setGdprApplies') {
						if (args.length > 3 && args[2] === 2 && typeof args[3] === 'boolean') {
							gdprApplies = args[3];
							if (typeof args[2] === 'function') {
								args[2]('set', true);
							}
						}
					} else if (args[0] === 'ping') {
						var retr = { gdprApplies: gdprApplies, cmpLoaded: false, cmpStatus: 'stub' };
						if (typeof args[2] === 'function') {
							args[2](retr);
						}
					} else {
						queue.push(args);
					}
				}

				function postMessageEventHandler(event) {
					var msgIsString = typeof event.data === 'string';
					var json = {};
					try {
						if (msgIsString) {
							json = JSON.parse(event.data);
						} else {
							json = event.data;
						}
					} catch (ignore) {
					}
					var payload = json.__tcfapiCall;
					if (payload) {
						window.__tcfapi(payload.command, payload.version, function(retValue, success) {
							var returnMsg = {
								__tcfapiReturn: {
									returnValue: retValue,
									success: success,
									callId: payload.callId,
								},
							};
							if (msgIsString) {
								returnMsg = JSON.stringify(returnMsg);
							}
							if (event && event.source && event.source.postMessage) {
								event.source.postMessage(returnMsg, '*');
							}
						}, payload.parameter);
					}
				}

				while (win) {
					try {
						if (win.frames[TCF_LOCATOR_NAME]) {
							cmpFrame = win;
							break;
						}
					} catch (ignore) {
					}
					if (win === window.top) break;
					win = win.parent;
				}
				if (!cmpFrame) {
					addFrame();
					win.__tcfapi = tcfAPIHandler;
					win.addEventListener('message', postMessageEventHandler, false);
				}
			});
		</script>
	<?php
	endif; ?>

		<!-- Google Tag Manager -->
		<script>
			(function(w, d, s, l, i) {


				w[l] = w[l] || [];
				w[l].push({
					'gtm.start': new Date().getTime(),
					event: 'gtm.js',
				});
				var f = d.getElementsByTagName(s)[0],
					j = d.createElement(s),
					dl = l != 'dataLayer' ? '&l=' + l : '';
				j.async = true;
				j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
				f.parentNode.insertBefore(j, f);


				function consentUpdateListener(tcData, success) {
					if (success && (tcData.eventStatus === 'useractioncomplete' || tcData.eventStatus === 'tcloaded')) {
						// Update Google consent mode based on the user's choices
						var adStorageConsent = tcData.purpose.consents[1] || false;
						var personalizationConsent = tcData.purpose.consents[2] || false;
						var analyticsStorageConsent = tcData.purpose.consents[3] || false;

						// Update Google's data layer with the user's consent
						dataLayer.push({
							'event': 'consent_update',
							'ad_storage_consent': adStorageConsent ? 'granted' : 'denied',
							'analytics_storage_consent': analyticsStorageConsent ? 'granted' : 'denied',
							'ad_user_data': adStorageConsent ? 'granted' : 'denied',
							'ad_personalization': personalizationConsent ? 'granted' : 'denied',
						});
					}
				}

				// Listen for consent updates from the CMP
				if (typeof w.__tcfapi === 'function') {
					w.__tcfapi('addEventListener', 2, consentUpdateListener);
				}
			})(window, document, 'script', 'dataLayer', 'GTM-<?= Head\get_GTM_id(); ?>');
		</script>
	<?php
	} ?>

	<?php
	if (is_page_template('template-thank-you.php')) : ?>
		<!-- Google tag (gtag.js) -->
		<script async src="https://www.googletagmanager.com/gtag/js?id=<?= Head\get_GTM_id(); ?>"></script>
		<script>
			window.dataLayer = window.dataLayer || [];

			function gtag() {
				dataLayer.push(arguments);
			}

			gtag('js', new Date());

			gtag('config', '<?= Head\get_GTM_id(); ?>');
		</script>
		<!-- End Google Tag Manager -->

	<?php
	insertGoogleAnalyticsTag(); ?>

		<script type="text/javascript" src="https://c.seznam.cz/js/rc.js"></script>
	<?php
	endif; ?>

	<style>
		:root {
			--googlead-text: "<?= esc_html_e('▴ Advertisement', 'FORBES') ?>";
			--googlead-text-no-arrow: "<?= esc_html_e('advertisement', 'FORBES') ?>";
			--googlead-close-text: "<?= esc_html_e('Close Ad', 'FORBES') ?>";
		}
	</style>

	<?php
	wp_enqueue_script(
		'bootstrap-script',
		get_template_directory_uri() . '/minified-js/bootstrap/bootstrap.min.js',
		array('jquery'),
		get_template_directory() . '/minified-js/bootstrap/bootstrap.min.js',
		true
	);
	wp_enqueue_script(
		'popper-script',
		get_template_directory_uri() . '/minified-js/bootstrap/popper.min.js',
		array('jquery'),
		get_template_directory() . '/minified-js/bootstrap/popper.min.js',
		true
	);
	?>

	<script>
		const eventLog = (eventName, event) => {
			if (!event?.slot) {
				console.error('Invalid event or missing slot.');
				return;
			}
			const {
				slot,
			} = event;
			const slotElementId = slot.getSlotElementId();
			const adUnitPath = slot.getAdUnitPath();
			console.log(`${eventName} - ${slotElementId} - ${adUnitPath}`);
		};
		const slotRequested = (event) => {
			eventLog('SlotRequestedEvent', event);
		};
		const slotOnLoad = (event) => {
			eventLog('adSlotEventOnLoad', event);
		};
		const slotResponseReceived = (event) => {
			const {
				slot,
			} = event;
			const responseInfo = slot.getResponseInformation();
			const slotElementId = slot.getSlotElementId();
			if (!responseInfo) {
				console.error('No response information available for slot', slotElementId);
				return;
			}
			eventLog('adSlotEventResponseReceived', event);
			const {
				campaignId,
				creativeId,
				lineItemId,
				sourceAgnosticCreativeId,
				sourceAgnosticLineItemId,
			} = responseInfo;
			console.log(`
				slot el. ID	=> ${slotElementId}
				campaign ID => ${campaignId}
				lineItem ID => ${lineItemId}
				creative ID => ${creativeId}
				SCA ID 		=> ${sourceAgnosticCreativeId} (source agnostic creative ID)
				SCL ID 		=> ${sourceAgnosticLineItemId} (source agnostic lineItem ID)
			`);
		};
		const handleSlotRenderEnded = (event, slot) => {
			const {
				isEmpty,
				campaignId,
				lineItemId,
				creativeId,
				sourceAgnosticCreativeId,
				sourceAgnosticLineItemId,
				size,
				advertiserId,
			} = event;
			const slotId = event.slot.getSlotElementId();
			const creativeSize = typeof size === 'string' ? size.split(',').map((s) => parseInt(s, 10)) :
				size;
			if (creativeSize === null) {
				console.error(`Creative size is not defined for slot [${slotId}]`);
				return;
			}
			console.log(`
				Ad Slot name >> ${slot.configuration.slotName}
				Ad server slot ID >> [${slotId}]
				Ad slot Dom ID >> [${slot.idDOM}]
				Ad Unit path >> [${slot.adUnitPath}]
				Rendered sizes [${size}${creativeSize !== size ? ` -> ${creativeSize}` : ''}]
				Campaign ID  >> [ ${campaignId} ]
				LineItem ID  >> [ ${lineItemId} ]
				Creative ID  >> [ ${creativeId} ]
				Creative ID  >> [ ${sourceAgnosticCreativeId} ] - source agnostic
				LineItem ID  >> [ ${sourceAgnosticLineItemId} ] - source agnostic
				Advertiser ID > [ ${advertiserId} ]
				advertiser >> [ ${slot?.renderEvent?.advertiserId} ]
				isEmpty >> [ ${isEmpty} ]
			`);
		};
		window.googletag = window.googletag || {
			cmd: [],
		};
		const setGamEventListeners = () => {
			const enableLogs = localStorage.getItem('enableGamLogs');
			if (!enableLogs) {
				return;
			}
			googletag.cmd.push(() => {
				googletag.pubads().addEventListener('slotRequested', (event) => slotRequested(
					event));
				googletag.pubads().addEventListener('slotOnload', (event) => slotOnLoad(event));
				googletag.pubads().addEventListener('slotResponseReceived', (event) =>
					slotResponseReceived(event));
				googletag.pubads().addEventListener('slotRenderEnded', (event) => {
					const slot = event.slot;
					if (!slot) {
						console.error('slotRenderEnded: slot is null');
						return;
					}
					handleSlotRenderEnded(event, slot);
				});
			});
		};
		setGamEventListeners();
	</script>


</head>

<body
	<?php
	body_class(); ?>
	data-hide-ads="<?= esc_attr(get_field('show_ads', get_the_ID())); ?>"
	data-show-ads="<?= esc_attr(get_field('hide_ads', get_the_ID())); ?>"
>
<?php
wp_body_open(); ?>

<!-- Google Tag Manager (noscript) -->
<noscript>
	<iframe src="<?= htmlspecialchars(Head\getGtmIframeSrc(), ENT_QUOTES, 'UTF-8') ?>" height="0" width="0"
			style="display:none;visibility:hidden"></iframe>
</noscript>
<!-- End Google Tag Manager (noscript) -->

<?php
if ('hu' === COUNTRY) : ?>
	<?php
	get_template_directory() . '/inc/snippets/head/facebook_analytics.php'; ?>
<?php
endif; ?>

<?php
include get_template_directory() . '/inc/snippets/head/gemius_tracking.php'; ?>


<?php
/**
 * If the current page is a single post, this script will check if the post has
 * a redirect URL set using the Advanced Custom Fields plugin.
 * If a redirect URL is set, the script will redirect the user to that URL.
 *
 * @param  void
 *
 * @return void
 */
if (is_single()) : ?>
	<script>
		const redirectUrl = '<?= get_field('redirect_url', get_the_ID()); ?>';
		if (redirectUrl) {
			window.location.href = redirectUrl;
		}
	</script>
<?php
endif; ?>

<noscript>
	<div class="noscript">
</noscript>

<div class="view-mode">