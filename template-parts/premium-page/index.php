<?php
$premium_tag = get_field('premium_tag', 'option') ?? '';

$args = array(
	'posts_per_page'	=> 14,
	'orderby'			=> 'date',
	'order'				=> 'DESC',
	'paged'				=> '1',
	'tax_query'			=> array(
		array(
			'field'		=> 'term_id',
			'terms'		=> $premium_tag,
			'taxonomy'	=> CATEGORY_TYPE
		)
	),
);

$premium_posts_query = new WP_Query($args);

$magazine_args = array(
	'post_type' 		=> 'magazine',
	'posts_per_page'	=> -1,
	'orderby'			=> 'date',
	'order'				=> 'DESC',
	'meta_query'		=> array(
		array(
			'key'       => 'issue_date',
			'value'     => '^' . date('Y'),
			'compare'   => 'REGEXP',
		)
	),
);

$magazine_query = new WP_Query($magazine_args);

$tags = get_field('tags');
$title = get_field('title');
?>

<h3 class="page-premium__mid-title">
	<?= $title; ?>
</h3>

<div class="page-premium__filters">

	<div class="loading-overlay"></div>

	<div class="page-premium__filters__top">

		<div class="page-premium__filters__year-wrapper">

			<label for="year"><?= esc_html__('Year', 'FORBES') ?></label>

			<select class="page-premium__filters__year" name="year" id="year">

				<?php
				$years = range(2021, date('Y'));
				$years_reverse = array_reverse($years);
				?>

				<?php foreach ($years_reverse as $year) : ?>
					<option value="<?= $year ?>"><?= $year ?></option>
				<?php endforeach; ?>
			</select>

		</div>

		<div class="page-premium__overlay-wrapper">

			<div class="overlay start"></div>

			<div id="premium-magazines-wrapper" class="page-premium__filters__magazines splide">

				<?php if ($magazine_query->have_posts()) : ?>

					<div class="splide__track">

						<div class="splide__list">
							<?php while ($magazine_query->have_posts()) : $magazine_query->the_post(); ?>
								<?= get_template_part('template-parts/premium-page/components/magazine-filter', null, ['magazine_id' => get_the_ID()]) ?>
							<?php endwhile; ?>

						</div>

					</div>

				<?php endif; ?>

			</div>

		</div>

	</div>

	<div class="page-premium__filters__bottom splide">

		<div class="splide__track">

			<div class="splide__list">

				<div class="page-premium__filters__tag clearTagFilter splide__slide">

					<?= esc_html__('všechny kategorie', 'FORBES') ?>

				</div>

				<div class="page-premium__filters__tag premiumType splide__slide" data-tagid="online">
					<?= esc_html__('premium web', 'FORBES') ?>
				</div>

				<div class="page-premium__filters__tag premiumType splide__slide" data-tagid="print">
					<?= esc_html__('premium print', 'FORBES') ?>
				</div>

				<?php foreach ($tags as $tag) : ?>

					<div class="page-premium__filters__tag tagSelector splide__slide" data-tagid="<?= $tag->term_id ?>">
						<?= $tag->name ?>
					</div>

				<?php endforeach; ?>

			</div>

		</div>

	</div>

</div>

<h4 id="no-result-text" class="page-premium__no-result"><?= esc_html__('No articles found', 'FORBES')?></h4>

<div id="premium-articles-wrapper" class="page-premium__articles">


	<?php if ($premium_posts_query->have_posts()) : ?>

		<?php while ($premium_posts_query->have_posts()) : $premium_posts_query->the_post(); ?>

			<?php get_template_part('template-parts/article-card/index', null, array('post_id' => get_the_ID(), 'article_card_type' => 'archive')); ?>

		<?php endwhile; ?>

	<?php endif; ?>

</div>

<?php if ($premium_posts_query->found_posts > 14) : ?>

	<div id="premium-page-pagination">
		<?php
		get_template_part(
			'template-parts/pagination/index',
			null,
			[
				'posts_found' => $premium_posts_query->found_posts,
				'paged' => '1',
				'is_single_magazine' => true,
				'max_num_pages' => $premium_posts_query->max_num_pages,
				'pagination_string' => 'paged1'
			]
		);
		?>
	</div>

<?php endif; ?>
