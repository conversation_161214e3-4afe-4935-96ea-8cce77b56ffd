html.dark-mode {
  .page-premium__filters__year {
    background: url('assets/icons/icon-chevron-down-dark.svg') 100% 50% no-repeat;
  }
}

.page-premium {
  padding-bottom: $spacing-11-64;
  padding-top: $spacing-08-40;

  &__title-wrapper {
    padding-bottom: $spacing-04-16;
  }

  &__date {
    margin-left: $spacing-05-20;
  }

  &__mid-title {
    font-size: 4.2rem;
    line-height: 5.2rem;
    margin-bottom: $spacing-07-32;
    margin-top: $spacing-11-64;
  }

  &__overlay-wrapper {
    position: relative;
    overflow: hidden;
    max-width: 100%;

    .overlay {
      position: absolute;
      z-index: 1;
      bottom: 0;
      left: 0;
      right: 0;
      pointer-events: none;
      background-image: linear-gradient(
        to right,
        rgba(var(--surface-secondary-rgb), 1),
        rgba(var(--surface-secondary-rgb), 0.8) 10%,
        rgba(var(--surface-secondary-rgb), 0) 15%,
        rgba(var(--surface-secondary-rgb), 0) 85%,
        rgba(var(--surface-secondary-rgb), 0.8) 90%,
        rgba(var(--surface-secondary-rgb), 1) 100%
      );
      width: 100%;
      height: 100%;

      &.end {
        background-image: linear-gradient(
          to right,
          rgba(var(--surface-secondary-rgb), 1),
          rgba(var(--surface-secondary-rgb), 0.8) 10%,
          rgba(var(--surface-secondary-rgb), 0) 15%,
          rgba(var(--surface-secondary-rgb), 0) 100%
        );
      }

      &.start {
        background-image: linear-gradient(
          to right,
          rgba(var(--surface-secondary-rgb), 0),
          rgba(var(--surface-secondary-rgb), 0) 85%,
          rgba(var(--surface-secondary-rgb), 0.8) 90%,
          rgba(var(--surface-secondary-rgb), 1) 100%
        );
      }
    }
  }

  &__filters {
    background-color: $color-surface-secondary;
    padding: $spacing-04-16;
    margin-bottom: $spacing-07-32;
    position: relative;

    &.loading {
      pointer-events: none;

      &::after {
        content: ' ';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background-color: rgba(var(--surface-secondary-rgb), $alpha: 0.5);
      }
      .loading-overlay {
        display: inline-block;
      }
    }

    &__top {
      padding-bottom: $spacing-04-16;
      display: flex;
      align-items: center;
      border-bottom: 0.1rem solid $color-divider;
    }

    .magazine-arrow {
      &__next,
      &__prev {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2.4rem;
        height: 2.4rem;
        @include mask-properties;
        mask-image: url('assets/icons/icon-chevron-right.svg');
        -webkit-mask-image: url('assets/icons/icon-chevron-right.svg');
        background-color: $color-text-secondary;
        z-index: 10;
        cursor: pointer;
      }

      &__prev {
        transform: rotate(180deg) translateY(50%);
        right: auto;
        left: 0;
        display: none;
      }
    }

    &__magazines {
      overflow-x: scroll;
      position: relative;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    &__magazine,
    &__magazines {
      display: flex;
      align-items: center;
    }

    &__year-wrapper {
      margin-right: $spacing-02;
    }

    &__year-wrapper,
    &__magazine {
      margin-right: $spacing-02;
      padding: $spacing-02 $spacing-04-16;
      background-color: $color-surface-primary;
    }

    &__magazine {
      cursor: pointer;
      min-width: max-content;
      transition: background-color 0.3s ease-out;

      &__image {
        height: 4.8rem;
        width: auto;
        object-fit: contain;
        margin-right: $spacing-02;
      }

      &__issue {
        font-size: 1.2rem;
        line-height: 2rem;
        color: $color-text-secondary;
        white-space: nowrap;
        transition: color 0.3s ease-out;
      }

      &__name {
        font-size: 1.8rem;
        line-height: 2.8rem;
        color: $color-text-secondary;
        white-space: nowrap;
        transition: color 0.3s ease-out;
      }

      &:hover,
      &.active {
        background-color: $color-text-primary;
        box-shadow: $box-shadow-level-1;

        .page-premium__filters__magazine__name,
        .page-premium__filters__magazine__issue {
          color: $post-background-color;
        }
      }
    }

    label {
      display: block;
      font-size: 1.2rem;
      line-height: 2rem;
    }

    &__year {
      -webkit-appearance: none;
      border: none;
      font-size: 1.8rem;
      line-height: 2.8rem;
      background-color: transparent;
      padding-right: $spacing-05-20;
      background: url('assets/icons/icon-chevron-down.svg') 100% 50% no-repeat;
    }

    &__bottom {
      padding-top: $spacing-04-16;
      display: flex;
      align-items: center;
      overflow-x: scroll;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    &__tag {
      cursor: pointer;
      padding: $spacing-02 $spacing-04-16;
      font-size: 1.4rem;
      line-height: 1.6rem;
      color: $color-text-secondary;
      margin-right: $spacing-02;
      background-color: $color-surface-primary;
      transition:
        background-color 0.3s ease-out,
        color 0.3s ease-out,
        box-shadow 0.3s ease-out;
      display: flex;
      align-items: center;
      white-space: nowrap;
      text-transform: uppercase;
      font-family: $font-archivo;

      @media (hover: hover) {
        &:hover {
          background-color: $color-text-primary;
          box-shadow: $box-shadow-level-2;
          color: $post-background-color;
        }
      }

      &.active {
        background-color: $color-text-primary;
        box-shadow: $box-shadow-level-1;
        color: $post-background-color;

        &::after {
          content: '';
          display: inline-block;
          margin-left: $spacing-02;
          @include mask-properties;
          mask-image: url('assets/icons/icon-cross.svg');
          -webkit-mask-image: url('assets/icons/icon-cross.svg');
          height: 1.8rem;
          width: 1.8rem;
          background-color: $post-background-color;
        }
      }
    }
  }

  &__no-result {
    display: none;
  }

  &__articles {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: $spacing-07-32;

    .article-card {
      flex: 48%;

      &:nth-child(2n) {
        padding-top: $spacing-09-48;
      }
    }
  }

  // TODO: Temporary fix for the article card bookmarks fetching problem
  .article-meta__buttons {
    display: none;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-bottom: $spacing-07-32;

    &__mid-title {
      font-size: 2.6rem;
      line-height: 3.6rem;
      margin-bottom: $spacing-04-16;
    }
    &__filters {
      &__top {
        flex-direction: column;
      }

      &__year-wrapper {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: $spacing-00;
        margin-bottom: $spacing-04-16;
      }

      &__magazines {
        max-width: 100%;
      }

      label {
        font-size: 1.6rem;
        line-height: 2.4rem;
      }
    }

    &__articles {
      grid-template-columns: repeat(1, 1fr);

      .article-card {
        &:nth-child(2n) {
          padding-top: $spacing-00;
        }
      }
    }

    &__no-result {
      text-align: center;
    }
  }

  .loading-overlay {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: inline-block;
    width: 8rem;
    height: 8rem;
    display: none;
    z-index: 1;
  }

  .loading-overlay:after {
    content: ' ';
    display: block;
    width: 6.4rem;
    height: 6.4rem;
    margin: $spacing-02;
    border-radius: 50%;
    border: 0.6rem solid $color-white;
    border-color: $color-white transparent $color-white transparent;
    animation: dobule-ring 1.2s linear infinite;
  }
  @keyframes dobule-ring {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
