jQuery(function ($) {
  $(document).ready(() => {
    const magazineSelector = '.page-premium__filters__magazine';
    const magazineContainerSelector = '.page-premium__filters__magazines';
    const tagSelector = '.page-premium__filters__tag';
    const tagContainerSelector = '.page-premium__filters__bottom';
    const yearSelector = '.page-premium__filters__year';

    let currentMagazine;
    const currentTags = [];
    let year;
    let searchInprogress = false;
    let magazineSearchInprogress = false;
    const premiumTypes = [];

    let magazineSplide;
    let tagSplide;

    let currentPage = 1;

    if (window.crm_user) {
      $('.premium-header').remove();
      $('.subscription-why-subscribe-2').remove();
    }

    const initSliders = () => {
      magazineSplide = new Splide(magazineContainerSelector, {
        type: 'slide',
        autoWidth: true,
        gap: '0.8rem',
        arrows: true,
        pagination: false,
        focus: 'number',
        omitEnd: true,
        speed: 300,
        classes: {
          arrows: 'splide__arrows splide__arrows--premium-magazine',
        },
      }).mount();

      tagSplide = new Splide(tagContainerSelector, {
        type: 'slide',
        autoWidth: true,
        gap: '0.8rem',
        arrows: true,
        pagination: false,
        focus: 'number',
        omitEnd: true,
        speed: 300,
        classes: {
          arrows: 'splide__arrows splide__arrows--premium',
        },
      }).mount();

      window.splideSliders = window.splideSliders || [];
      window.splideSliders.push(tagSplide);
      window.splideSliders.push(magazineSplide);
    };

    initSliders();

    const addMagazineListener = () => {
      $(magazineSelector).click((e) => {
        if (searchInprogress) return;
        const selectedMagazine = $(e.target).closest(magazineSelector).data('relatedtag');

        $(magazineSelector).removeClass('active');

        if (selectedMagazine === currentMagazine) {
          currentMagazine = null;
        } else {
          currentMagazine = selectedMagazine;
          $(e.target).closest(magazineSelector).addClass('active');
        }
        filter();
      });
    };

    addMagazineListener();

    $(tagSelector).click((e) => {
      if (searchInprogress) return;

      if ($(e.target).hasClass('clearTagFilter')) {
        $(tagSelector).removeClass('active');
      } else if ($(e.target).hasClass('premiumType')) {
        const selectedType = $(e.target).data('tagid');

        if (premiumTypes.includes(selectedType)) {
          const index = premiumTypes.indexOf(selectedType);
          premiumTypes.splice(index, 1);
          $(e.target).removeClass('active');
        } else {
          premiumTypes.push(selectedType);
          $(e.target).addClass('active');
        }
      } else {
        const selectedTag = $(e.target).data('tagid');

        if (currentTags.includes(selectedTag)) {
          const index = currentTags.indexOf(selectedTag);
          currentTags.splice(index, 1);
          $(e.target).removeClass('active');
        } else {
          currentTags.push(selectedTag);
          $(e.target).addClass('active');
        }
      }

      tagSplide.refresh();

      filter();
    });

    $(yearSelector).change((e) => {
      if (magazineSearchInprogress) return;

      year = $(e.target).val();

      getMagazines();
    });

    const addPaginationListener = () => {
      $('.page-numbers').click((e) => {
        e.preventDefault();

        if ($(e.target).hasClass('pagination__next-link')) {
          currentPage = Number(currentPage) + 1;
        } else if ($(e.target).hasClass('pagination__prev-link')) {
          currentPage = Number(currentPage) - 1;
        } else {
          currentPage = $(e.target).html();
        }

        filter(currentPage);
      });
    };

    addPaginationListener();

    const filter = (page = 1) => {
      if (searchInprogress) return;

      searchInprogress = true;
      $('.page-premium__filters').addClass('loading');

      $.ajax({
        type: 'GET',
        // eslint-disable-next-line no-undef
        url: ajaxUrl,
        data: {
          action: 'frontend_filter_premium_articles',
          currentMagazine,
          currentTags,
          premiumTypes,
          page,
        },
        success: function (response) {
          response = JSON.parse(response);

          if (response.results && response.articles) {
            $('#no-result-text').hide();
            $('#premium-articles-wrapper').html(response.articles);
            $('#premium-page-pagination').html(response.pagination);

            addPaginationListener();
          } else {
            $('#no-result-text').show();
            $('#premium-articles-wrapper').html('');
            $('#premium-page-pagination').html('');
          }

          searchInprogress = false;

          $('.page-premium__filters').removeClass('loading');
        },
        error: function (e) {
          searchInprogress = false;

          $('.page-premium__filters').removeClass('loading');

          console.log(e);
        },
      });
    };

    const getMagazines = () => {
      if (magazineSearchInprogress) return;

      magazineSearchInprogress = true;

      $('.page-premium__filters').addClass('loading');

      $.ajax({
        type: 'GET',
        // eslint-disable-next-line no-undef
        url: ajaxUrl,
        data: {
          action: 'frontend_filter_magazine_by_year',
          year,
        },
        success: function (response) {
          response = JSON.parse(response);

          $('#premium-magazines-wrapper').find('.splide__list').html(response.magazines);

          addMagazineListener();

          magazineSearchInprogress = false;

          magazineSplide.refresh();

          $('.page-premium__filters').removeClass('loading');
        },
        error: function (e) {
          $('.page-premium__filters').removeClass('loading');

          magazineSearchInprogress = false;

          console.log(e);
        },
      });
    };
  });
});
