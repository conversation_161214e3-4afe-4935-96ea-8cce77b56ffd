.admin-bar {
  .archive-template {
    &__tags {
      top: calc(13.2rem + 3.2rem);

      @media screen and (max-width: map-get($container-max-widths, md)) {
        top: calc(8.8rem + 4.6rem);
      }

      @media screen and (max-width: map-get($container-max-widths, sm)) {
        top: calc(6.4rem + 4.6rem);
      }
    }
  }
}

.archive-template {
  $post-font-color: var(--post-label-color, $color-text-primary);
  background-color: $color-surface-primary;

  &__header {
    padding-top: $spacing-08-40;
    margin-bottom: 2.6rem;

    .heading {
      font-size: 4.6rem;
    }

    &--background {
      background-color: $color-surface-secondary;
      padding-bottom: $spacing-11-64;
      margin-bottom: $spacing-10-56;
    }
  }

  &__pinned-articles {
    padding-top: $spacing-07-32;

    .article-card {
      &__details-wrapper {
        display: none;
      }

      &__category {
        background-color: $color-surface-secondary !important;
      }

      &__title {
        font-size: 2.6rem;
        line-height: 2.8rem;
        font-weight: 500 !important;
        color: $color-text-primary;
      }
    }
  }

  &__content-wrapper {
    padding-bottom: $spacing-12-80;
  }

  .googlead-container--leaderboard {
    &.mobile {
      display: none;
    }
  }

  &__title-wrapper {
    display: flex;
    align-items: flex-start;
    gap: $spacing-04-16;
    margin-bottom: $spacing-06-24;
  }

  &__description-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-04-16;
    max-width: 63.5rem;
    margin-bottom: 2.6rem;

    img {
      border-radius: $spacing-01;
    }

    p {
      font-family: $font-archivo;
      font-weight: 500;
      font-size: 1.6rem;
      line-height: 2.2rem;
      color: $color-text-secondary;
    }
  }

  &__tags {
    margin-bottom: $spacing-08-40;
    position: sticky;
    top: 13.2rem;
    z-index: 1;
    background-color: $color-surface-primary;

    @media screen and (max-width: map-get($container-max-widths, md)) {
      top: calc(8.8rem);
    }

    @media screen and (max-width: map-get($container-max-widths, sm)) {
      top: calc(6.4rem);
    }
  }

  &__tags-wrapper {
    &.splide {
      margin-right: -$spacing-08-40;
      margin-left: -$spacing-08-40;

      @media screen and (max-width: map-get($container-max-widths, sm)) {
        margin-right: -$spacing-04-16;
        margin-left: -$spacing-04-16;
      }

      .splide__slide {
        margin-right: $spacing-06-24;
      }

      .splide__arrow {
        height: $spacing-09-48;
        width: $spacing-08-40;
        transition: opacity 0.3s ease;

        &--prev {
          border-right: 0.1rem solid $color-divider !important;
          background-color: $color-white;
          opacity: 1;
          border-radius: unset;
          left: -1px;
          top: 0;
          height: 100%;
          border-bottom: 1px solid $color-divider;
          transform: none;

          @media (hover: hover) {
            &:hover {
              opacity: 1;
            }
          }

          .icon--chevron-left {
            display: block;
            width: $spacing-06-24;
            height: $spacing-06-24;

            transform: rotate(180deg);

            background-color: $color-icon-secondary;

            mask-image: url('assets/icons/icon-chevron-right.svg');
            -webkit-mask-image: url('assets/icons/icon-chevron-right.svg');
            @include mask-properties;
          }
        }

        &--next {
          border-left: 0.1rem solid $color-divider !important;
          background-color: $color-white;
          opacity: 1;
          border-radius: unset;
          right: -1px;
          bottom: 0;
          top: 0;
          height: 100%;
          border-bottom: 1px solid $color-divider;
          transform: none;

          @media (hover: hover) {
            &:hover {
              opacity: 1;
            }
          }

          .icon--chevron-right {
            display: block;
            width: $spacing-06-24;
            height: $spacing-06-24;

            background-color: $color-icon-secondary;

            mask-image: url('assets/icons/icon-chevron-right.svg');
            -webkit-mask-image: url('assets/icons/icon-chevron-right.svg');
            @include mask-properties;
          }
        }

        &:disabled {
          opacity: 0;
          pointer-events: unset;
          cursor: unset;
        }

        &:before {
          display: none;
        }
      }
    }
  }

  &__tags-wrapper-inner {
    position: relative;
    overflow-x: scroll;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  &__tags-wrapper-divider {
    width: 100vw;
    height: 0.1rem;
    background-color: $color-divider;
    margin-top: -0.1rem;
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
  }

  .article-meta {
    &__author {
      max-width: 20ch;
    }
  }

  &__tag {
    display: flex;
    align-items: center;
    gap: $spacing-01;
    font-size: 1.4rem;
    font-weight: 500;
    line-height: 2rem;
    font-family: 'Archivo';
    text-decoration: none;
    color: $color-text-secondary;
    padding-top: 1.4rem;
    padding-bottom: 1.4rem;
    margin-left: $spacing-03-12;
    margin-right: $spacing-03-12;
    white-space: nowrap;
    border-bottom: 0.1rem solid transparent;
    transition: all 0.3s ease;
    min-width: fit-content;

    &:first-child {
      margin-left: $spacing-00;
    }

    &:last-child {
      margin-right: $spacing-00;
    }

    &.active {
      color: var(--post-label-color, $color-text-primary);
      border-color: var(--post-label-color, $color-text-primary);

      .archive-template__tag-icon {
        background-color: var(--post-label-color, $color-text-primary);
      }
    }

    @media (hover: hover) {
      &:hover {
        color: var(--post-label-color, $color-text-primary) !important;

        .archive-template__tag-icon {
          background-color: var(--post-label-color, $color-text-primary);
        }
      }
    }

    &-icon {
      @include mask-properties;
      width: 1.6rem;
      height: 1.6rem;
      background-color: $color-icon-secondary;
    }
  }

  &__articles-wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: $spacing-07-32 $spacing-12-80;
    margin-bottom: $spacing-07-32;

    &.with-ad {
      grid-gap: $spacing-07-32 $spacing-10-56;
    }
  }

  &__ad-container {
    position: sticky;
    position: -webkit-sticky;
    top: 22rem;

    div[id*='google_ads_iframe'] {
      display: flex;
      justify-content: center;
    }

    &--mobile {
      position: relative;
      top: unset;
      display: none;
    }
  }

  &--author {
    margin-top: $spacing-00;
    padding-top: $spacing-00;

    .archive-template {
      &__articles-wrapper {
        grid-template-columns: repeat(3, 1fr);
      }
    }
  }

  .article-details {
    &__author {
      @media (hover: hover) {
        &:hover {
          color: var(--hover-color, $color-link-visited) !important;
        }
      }
    }
  }

  .article-card {
    @media (hover: hover) {
      &:hover {
        .article-card {
          &__title {
            color: var(--post-label-color, $color-link-visited) !important;
          }
        }
      }
    }
    &__category {
      background-color: $color-surface-primary;
      background-color: var(--post-background-color);
      color: var(--post-label-color, $color-link-visited);

      &:before {
        border-color: var(--post-label-color, $color-link-visited);
      }

      @media (hover: hover) {
        &:hover {
          color: var(--post-label-hover-color, $color-text-brand);

          &:before {
            border-color: var(--post-label-hover-color, $color-text-brand);
          }
        }
      }
    }

    &__author {
      @media (hover: hover) {
        &:hover {
          color: var(--post-label-hover-color, $color-link-visited) !important;
        }
      }
    }

    &:nth-of-type(even) {
      padding-top: $spacing-09-48;
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, lg )) {
  .archive-template {
    &__articles-wrapper {
      grid-gap: $spacing-07-32;
    }

    .article-card {
      &:nth-of-type(even) {
        padding-top: $spacing-07-32;
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .archive-template {
    &__header {
      padding: $spacing-08-40 $spacing-00 $spacing-00;
      margin-bottom: $spacing-00;

      &--background {
        padding-bottom: $spacing-04-16;
        margin-bottom: $spacing-09-48;
      }
    }

    &__pinned-articles {
      padding-top: $spacing-06-24;

      .article-card {
        &:first-child {
          margin-bottom: $spacing-07-32;
        }
      }
    }

    &__content-wrapper {
      padding-bottom: $spacing-07-32;
    }

    .googlead-container--leaderboard {
      display: none;

      &.mobile {
        display: block;
        margin-bottom: $spacing-00;
        margin-left: -1.5rem;

        & > div {
          padding-top: $spacing-04-16;
        }
      }
    }

    &__description-wrapper {
      max-width: none;
    }

    &__articles-wrapper {
      display: flex;
      flex-direction: column;
      column-gap: $spacing-07-32;

      .article-card {
        &__image-wrapper {
          display: block;
        }

        &__content-wrapper {
          padding-right: $spacing-04-16;
        }

        &:first-child {
          .article-card {
            &__title {
              font-size: 2.3rem;
              line-height: 3.2rem;
            }
          }
        }
        &:nth-of-type(even) {
          padding-top: $spacing-00;
        }

        .article-meta {
          &__author {
            max-width: 15ch;
          }
        }
      }
    }

    &__ad-container {
      &--mobile {
        display: block;
      }
    }

    &--author {
      padding-top: $spacing-00;

      .archive-template {
        &__articles-wrapper {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
