<?php

$special = $args['special'] ?? null; // podcast, comment, video

$currentTerm = $special ? null : ($args['term'] ?? get_queried_object());

$parentTerm = $currentTerm && $currentTerm->parent
	? get_term($currentTerm->parent, $currentTerm->taxonomy)
	: $currentTerm;

$pinnedArticleIds = array();
$description = $parentTerm ? term_description($parentTerm->term_id) : null;
$title = $parentTerm ? $parentTerm->name : get_the_title();

// if category/tag archive
if ($currentTerm instanceof \WP_Term) {
	$pinned_articles = get_field('pin_articles', CATEGORY_TYPE . '_' . $currentTerm->term_id);

	if ($pinned_articles) {
		foreach ($pinned_articles as $article) {
			if ($article['article'] != false) {
				$pinnedArticleIds[] = $article['article'];
			}
		}
	}
}

$hideTitle = empty($title) || get_field('hide_title');
$hideDescription = empty($description);
$hideTabs = !($parentTerm instanceof \WP_Term) || empty(get_term_children($parentTerm->term_id, $parentTerm->taxonomy));

/**
 * The ID of the print tag
 * @var string
 */
$print_category = get_field('print_category', 'option') ?? null;

$paged = get_query_var('paged') ?: 1;

$query_args = array(
	'posts_per_page'    => get_option('posts_per_page'),
	'post_type'         => 'post',
	'post_status'       => 'publish',
	'order_by'          => 'date',
	'order'             => 'DESC',
	'paged'             => $paged,
	'tax_query'         => $currentTerm instanceof \WP_Term && !$special ? array(
		array(
			'taxonomy'  => $currentTerm->taxonomy,
			'field'     => 'term_id',
			'terms'     => array($currentTerm->term_id)
		),
		array(
            'taxonomy'  => CATEGORY_TYPE,
            'field'     => 'term_id',
            'terms'     => $print_category,
            'operator'  => 'NOT IN',
        ),
	) : array(),
);

// If podcast or comments page
if ($special) {
	if ($special === 'podcast') {
		$special_tags = get_field('podcast_tags', 'option') ?? [];

		if (COUNTRY === 'hu') {
			$query_args['meta_query'] = array(
				array(
					'key'    => 'type',
					'value'    => $special
				)
			);
		} elseif (!empty($special_tags)) {
			$query_args['tax_query'] = array(
				array(
					'taxonomy'    => CATEGORY_TYPE,
					'field'    => 'term_id',
					'terms'    => $special_tags
				)
			);
		}
	} elseif ($special === 'comment') {
		$comment_tag = get_field('comment_tag', 'option');
		$special_tags = $comment_tag ? [$comment_tag] : [];
		$special_tags = array_column($special_tags, 'term_id');

		if (!empty($special_tags)) {
			$query_args['tax_query'] = array(
				array(
					'taxonomy' => CATEGORY_TYPE,
					'field' => 'term_id',
					'terms' => $special_tags
				)
			);
		}
	} elseif ($special === 'video') {
		$query_args['meta_query'] = array(
			array(
				'key' => 'type',
				'value' => $special
			)
		);
	}
}

$query = new WP_Query($query_args); ?>

<div class="archive-template">

	<?php if (COUNTRY === 'hu') : ?>
		<?php get_template_part('template-parts/leaderboard-ad/index', null, array('page' => 'archive')) ?>
	<?php endif; ?>

	<div class="archive-template__header">
		<div class="container">
			<!-- Title -->
			<?php if (!$hideTitle) : ?>
				<div class="archive-template__title-wrapper">
					<?php echo (new Heading($title, 'h1', null, '700'))->render(); ?>
				</div>
			<?php endif; ?>

			<!-- Description -->
			<?php if (!$hideDescription) : ?>
				<div class="archive-template__description-wrapper">
					<?= $description; ?>
				</div>
			<?php endif; ?>

        </div>
    </div>

    <div class="container archive-template__tags">
        <!-- Tabs -->
        <?php
        if ( ! $hideTabs) : ?>
            <?php
            get_template_part('template-parts/tabs/term-tabs', null, ['parent' => $parentTerm, 'current' => $currentTerm]); ?>
        <?php
        endif; ?>
    </div>

    <div class="container">
        <!-- Pinned articles -->
        <?php
        if ( ! empty($pinnedArticleIds) && count($pinnedArticleIds) > 1) : ?>
            <div class="row archive-template__pinned-articles">
                <?php
                foreach ($pinnedArticleIds as $id) : ?>
                    <div class="col-lg-6">
                        <?php
                        get_template_part('template-parts/article-card/index', null, array('post_id' => $id, 'article_card_type' => 'archive')); ?>
                    </div>
                <?php
                endforeach; ?>
            </div>
        <?php
        endif; ?>
    </div>

	<div class="container">
		<div class="archive-template__content-wrapper">
			<div class="row">

				<div class="col-12 <?= ('cz' !== COUNTRY) ? ' col-md-8' : ''; ?>">

					<div class="archive-template__articles-wrapper<?= 'cz' !== COUNTRY ? ' with-ad' : '' ?>">

						<?php if ($query->have_posts()) : ?>

							<?php $index = 0; ?>

								<?php while ($query->have_posts()) : $query->the_post();

									if (in_array(get_the_ID(), $pinnedArticleIds) && count($pinnedArticleIds) > 1) {
									continue; // Skip this iteration if it's a pinned event
								} ?>

								<?php if (2 === $index && 'hu' === COUNTRY) : ?>

									<?php get_template_part('template-parts/leaderboard-ad/index', null, array('page' => 'archive', 'mobile' => true)) ?>

								<?php endif; ?>

								<?php get_template_part('template-parts/article-card/index', null, array('post_id' => get_the_ID(), 'article_card_type' => 'archive')); ?>

								<?php $index++; ?>

							<?php endwhile; ?>

						<?php endif; ?>

					</div>

				</div>

				<?php if ('cz' !== COUNTRY) : ?>

					<div class="col-12 col-md-4">

						<div class="archive-template__ad-container archive-template__ad-container--desktop googlead-container h-d--md--none">
							<div id="category-ad-desktop" class="googlead"></div>
						</div>

					</div>

				<?php endif; ?>

			</div>

			<div class="row">

				<div class="col-12">

					<?php get_template_part('template-parts/pagination/index', null, array('posts_found' => $query->found_posts, 'paged' => $paged, 'max_num_pages' => $query->max_num_pages)) ?>

				</div>

			</div>
		</div>
	</div>

</div>
