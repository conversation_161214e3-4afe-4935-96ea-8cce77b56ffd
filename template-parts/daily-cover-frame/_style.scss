.daily-cover-frame {
  position: absolute;
  top: 3.2rem;
  left: 3.2rem;
  bottom: 3.2rem;
  right: 3.2rem;
  border: 0.7rem solid rgba(255, 255, 255, 0.8);

  &__label {
    position: absolute;
    height: 2.4rem;
    top: -1.3rem;
    left: 50%;
    transform: translateX(-50%);
    color: $color-white;
    background-color: $color-text-error;
    padding: $spacing-00 $spacing-02;
    @include flexbox-properties;
    margin: $spacing-00;
  }

  &--single {
    top: 4rem;
    right: 4rem;
    bottom: 4rem;
    left: 4rem;

    .daily-cover-frame__label {
      height: 4rem;
      top: -2.2rem;
      font-size: 1.8rem;
    }
  }

  &--normal {
    top: 1.3rem;
    right: 1.3rem;
    bottom: 1.3rem;
    left: 1.3rem;

    .daily-cover-frame__label {
      height: 2rem;
      top: -1.3rem;
      font-size: 1.2rem;
    }
  }

  &--small {
    top: 1rem;
    left: 1rem;
    bottom: 1rem;
    right: 1rem;

    .daily-cover-frame__label {
      white-space: nowrap;
      font-size: 1rem;
      height: 2rem;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &--single {
      top: 2rem;
      left: 2rem;
      bottom: 2rem;
      right: 2rem;

      .daily-cover-frame__label {
        font-size: 1.4rem;
        height: 2.4rem;
        top: -1.5rem;
      }
    }
  }
}
