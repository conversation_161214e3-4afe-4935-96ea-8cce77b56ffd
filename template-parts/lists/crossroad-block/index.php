<?php

/**
 * The lists of the post
 * @var array
 */
$lists = $args['lists'] ?? null;

?>


<?php if (!empty($lists)) : ?>

	<div class="crossroad-block items-<?= count($lists); ?>">

		<?php foreach ($lists as $list_item) : ?>

			<?php
			/**
			 * The slug of the sublist
			 */
			$slug = sanitize_title_with_dashes(frontend_no_accent($list_item['title']));

			$list_url = get_the_permalink(get_the_ID()) . $slug;

			if (is_preview()) {
				global $post;
				$post_slug = get_post_field('post_name', $post->ID);
				$list_url = get_post_type_archive_link('list') . $post_slug . '/' . $slug;
			}

			/**
			 * Custom redirect url on crossroad pages
			 */
			$custom_redirect_url = $list_item['custom_redirect_url'] ?? null;

			?>
			<a href="<?= $custom_redirect_url ? $custom_redirect_url : $list_url; ?>" class="crossroad-block__item">

				<div class="crossroad-block__item-image-wrapper">

					<?php if ($list_item['featured_image']) : ?>

						<?= wp_get_attachment_image($list_item['featured_image'], 'medium', false, ['class' => 'crossroad-block__item-image', 'alt' => $list_item['title'] ?? '']) ?>

					<?php endif; ?>

				</div>

				<div class="crossroad-block__item-details-wrapper">

					<?php if ($list_item['title']) : ?>

						<h5 class="crossroad-block__item-title"><?= $list_item['title']; ?></h5>

						<button class="crossroad-block__item-link cta-link-tag after-icon after-icon--arrow"><?= esc_html__('Show List', 'FORBES'); ?></button>

					<?php endif; ?>

				</div>

			</a>

		<?php endforeach; ?>

	</div>

<?php endif; ?>
