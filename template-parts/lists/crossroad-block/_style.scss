.crossroad-block {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: $spacing-07-32;
  margin-bottom: $spacing-10-56;

  &.items-2,
  &.items-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  &__item {
    color: initial;
    text-decoration: none;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    @media (hover: hover) {
      &:hover {
        .crossroad-block {
          &__item-title {
            text-decoration: underline;
          }
        }
      }
    }
  }

  &__item-image-wrapper {
    width: 100%;
    height: auto;
    overflow: hidden;
    display: flex;
    margin-bottom: $spacing-03-12;
  }

  &__item-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  &__item-title {
    transition: color 0.3s ease;
    margin-bottom: $spacing-02;
  }

  &__item-link {
    color: $color-link-visited;
    padding: $spacing-00;
    display: inline-flex;
    cursor: pointer;

    @media (hover: hover) {
      &:hover {
        color: $color-text-brand;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    grid-template-columns: repeat(auto-fill, minmax(21rem, 1fr));
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-bottom: $spacing-08-40;
  }

  @media screen and (max-width: map-get($container-max-widths, sm )) {
    grid-gap: $spacing-04-16;

    &__item-link {
      margin-bottom: $spacing-00;
    }
  }
}
