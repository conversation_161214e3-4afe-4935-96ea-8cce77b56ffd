.list-pagination {
  @include flexbox-properties;
  margin-bottom: $spacing-09-48;

  &__previous,
  &__next {
    width: 2rem;
    height: 3rem;
    @include mask-properties;
    mask-image: url('assets/icons/icon-chevron-right.svg');
    -webkit-mask-image: url('assets/icons/icon-chevron-right.svg');
    background-color: $color-text-secondary;
    cursor: pointer;
    transition: all 0.3s ease;

    @media (hover: hover) {
      &:hover {
        background-color: $color-text-primary;
      }
    }
  }

  &__next {
    margin-left: $spacing-07-32;
  }

  &__previous {
    margin-right: $spacing-07-32;
    transform: rotate(180deg);
  }

  &__pages-wrapper {
    margin: $spacing-00 -0.8rem;
  }

  &__page {
    line-height: 3rem;
    width: 4.1rem;
    height: 3.2rem;
    border: 0.1rem solid $color-text-primary;
    display: inline-block;
    text-align: center;
    text-decoration: none;
    color: $color-text-primary;
    margin: $spacing-00 $spacing-02;
    transition: all 0.3s ease;

    @media (hover: hover) {
      &.current,
      &:hover {
        background-color: $color-text-primary;
        color: $color-surface-primary !important;
      }
    }
  }

  &.first {
    .list-pagination {
      &__previous {
        display: none;
      }

      &__page:first-child {
        display: none;
      }
    }
  }

  &.last {
    .list-pagination {
      &__next {
        display: none;
      }

      &__page:last-child {
        display: none;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-bottom: $spacing-07-32;
  }
}
