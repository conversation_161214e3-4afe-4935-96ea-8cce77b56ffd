<?php
$total_pages = $args['pages'] ?? null;

$current = $args['current'] ?? null;

$previous_link = $current == 1 ? null : add_query_arg('slide', $current - 1);
$next_link = $current == $total_pages ? null : add_query_arg('slide', $current + 1);

?>

<?php if ($total_pages && $current) : ?>

	<div class="list-pagination<?= $current == 1 ? ' first' : '';?><?= intval($current) === intval($total_pages) ? ' last' : '';?>">

		<a class="list-pagination__previous" href="<?= $previous_link; ?>"></a>

		<div class="list-pagination__pages-wrapper">

			<?php for ($i = 0; $i < 3; $i++) : ?>

				<?php
				$page;
				$url;
					switch ($i) {
						case 0:
							$page = intval($current) - 1;
							$url = $previous_link;
							break;
						case 1:
							$page = intval($current);
							$url = '#';
							break;
						case 2:
							$page = intval($current) + 1;
							$url = $next_link;
							break;
					}
					?>

				<a href="<?= $url;?>" class="list-pagination__page cta-link-tag<?= 1 === $i ? ' current' : '' ;?>"><?= $page;?></a>

			<?php endfor; ?>

		</div>

		<a class="list-pagination__next" href="<?= $next_link; ?>"></a>

	</div>

<?php endif; ?>
