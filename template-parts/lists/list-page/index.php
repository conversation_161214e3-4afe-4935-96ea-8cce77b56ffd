<?php

/**
 * The number of posts to show per page
 * @var int
 */
$posts_per_page = get_field('posts_per_page') ?? 10;

/**
 * Whether the list has a crossroad page
 * @var boolean
 */
$is_crossroad = $args['crossroad'] ?? false;

/**
 * Whether the list is via a crossroad page
 * @var boolean
 */
$is_sub_list = $args['is_sub_list'] ?? false;

/**
 * If sublist, the array of values of the list
 * @var array|null
 */
$chosen_list = $is_sub_list ? $args['chosen_list'] : null;

/**
 * True if we want to hide the recommended ad
 * @var boolean
 */
$hide_recommendation_placeholder = (get_field('ad_settings'))['recommended_articles'] ?? false;

/**
 * Reverse numbering
 * @var boolean
 */
$reverse_numbering = !$is_crossroad ? ($is_sub_list ? $chosen_list['crossroad_sections']['reverse_numbering'] : get_field('sections')['reverse_numbering']) : false;

/**
 * Show rakning on list item
 * @var boolean
 */
$show_ranking = !$is_crossroad ? ($is_sub_list ? $chosen_list['crossroad_sections']['show_ranking'] : get_field('sections')['show_ranking']) : false;

/**
 * If the list is full width layout
 * @var boolean
 */
$full_width_layout = !$is_crossroad ? ($is_sub_list ? $chosen_list['crossroad_sections']['full_width_layout'] : get_field('sections')['full_width_layout']) : false;

/**
 * the list's sponsors
 * @var array
 */
$sponsors = get_field('partners');

/**
 * The description of the list
 * @var string
 */
$description = ($is_sub_list ? $chosen_list['description'] : get_field('description')) ?? null;

/**
 * Featured image of the list
 * @var string
 */
$featured_image = $chosen_list && $chosen_list['list_image'] ? $chosen_list['list_image'] : get_post_thumbnail_id(get_the_ID(  ));

/**
 * Whether to show methodology
 * @var boolean
 */
$show_methodology = $is_crossroad ? (get_field('crossroad_methodology') ?? false) : (($is_sub_list ? $chosen_list['crossroad_sections']['methodology'] : get_field('sections')['methodology']) ?? false);

$frbsurl = $is_sub_list ? $chosen_list['frbsurl'] : null;

$primary_category = frontend_get_primary_tag(get_the_ID());
$category_id = $primary_category?->term_id ?? null;
$all_categories = get_all_the_terms(get_the_ID());
$terms = array_map(
	fn ($term) => [
		'id' => $term->term_id,
		'name' => $term->name,
		'slug' => $term->slug,
	],
	$all_categories ?: [],
);

if ($frbsurl) : ?>
	<script>
		const customListUrl = '<?= $frbsurl ?>';
	</script>
<?php endif;

if ($is_crossroad) {
	/**
	 * The lists belonging to this crossroads page
	 * @var array
	 */
	$crossroad_lists = $args['crossroad_lists'] ?? array();
} else {
	/**
	 * The items on the list
	 * @var array
	 */
	$list_items = $args['items'] ?? array();

	/**
	 * The total number of list items
	 * @var int|null
	 */
	$total_list_items = $list_items ? count($list_items) : null;

	/**
	 * Featured data column header
	 * @var string
	 */
	$featured_data_name = \Lists\ListData::get_featured_data();

	/**
	 * Whether to show custom ordering
	 * @var booleand
	 */
	$show_custom_ordering = $is_sub_list ? ($chosen_list['crossroad_sections']['custom_ordering']) : (get_field('sections')['custom_ordering'] ?? false);

	/**
	 * Custom order criterias
	 * @var array(string)
	 */
	$custom_order_criterias = \Lists\ListData::get_custom_order_criterias($is_sub_list ? null : get_the_ID(), $is_sub_list ? $chosen_list['crossroad_content'] : null);

	/**
	 * Whether to show layout switcher
	 * @var boolean
	 */
	$show_layout_switcher =  $is_sub_list ? ($chosen_list['crossroad_sections']['layout_switcher'] ?? false) : (get_field('sections')['layout_switcher'] ?? false);

	if ($full_width_layout) {
		$show_layout_switcher = false;
	}

	/**
	 * Set if we are not on the first page of the list
	 * @var string
	 */
	$current_page = get_query_var('slide', '1');

	if (isset($_GET['slide'])) {
		$current_page = $_GET['slide'];
	}

	/**
	 * The ID of the current list
	 */
	$id = $is_sub_list ? get_the_ID() . '_' . ($args['chosen_list_index'] ?? '') : get_the_ID();
}
?>

<?php if (!empty($list_items) || !empty($crossroad_lists)) : ?>

	<main class="page-list" <?= $is_crossroad ? '' : 'data-listid=' . $id . ''; ?> data-perpage="<?= $posts_per_page; ?>">

		<div class="container">

			<div class="header<?= !$featured_image ? ' no-image' : ''; ?>">

				<?php if ($is_sub_list) : ?>

					<a href="<?= get_the_permalink(); ?>" class="header__back-button cta-link-tag after-icon after-icon--back-arrow"><?= esc_html__('Back', 'FORBES'); ?></a>

				<?php endif; ?>

				<?php if (!empty($sponsors)) : ?>

					<div class="header__partners">

						<span class="header__logo-label caption"><?= count($sponsors) === 1 ? esc_html__('List Partner', 'FORBES') : esc_html__('List Partners', 'FORBES'); ?></span>

						<div class="header__partners-wrapper">

							<?php foreach ($sponsors as $sponsor) : ?>

								<a href="<?= $sponsor['url'] ?? '#'; ?>" target="_blank" rel="noopener" class="header__logo-wrapper">

									<?php if ($sponsor['image']) : ?>

										<?= wp_get_attachment_image($sponsor['image'], 'thumbnail', false, ['class' => 'header__sponsor-logo', 'alt' => esc_html__('List Sponsor', 'FORBES')]); ?>

									<?php endif; ?>

								</a>

							<?php endforeach; ?>

						</div>

					</div>

				<?php endif; ?>

				<?php if ($featured_image) : ?>

					<?php
					$ratio_class = (function_exists('is_new_featured_image') && is_new_featured_image($featured_image)) ? ' header__image-wrapper--16x9' : '';
					?>
					<div class="header__image-wrapper<?= $ratio_class; ?>">

						<?php
						// Determine image size based on upload date
						$image_size    = 'full';
						$image_size_2x = 'full';
						if (function_exists('is_new_featured_image') && is_new_featured_image($featured_image)) {
							$image_size    = 'list_featured_16_9';
							$image_size_2x = 'list_featured_16_9_2x';
						}
						echo wp_get_attachment_image(
							$featured_image,
							$image_size,
							false,
							[
								'class'         => 'header__image',
								'alt'           => $chosen_list['title'] ?? get_the_title(),
								'loading'       => false,
								'fetchpriority' => 'high',
								'srcset'        => wp_get_attachment_image_url(
													   $featured_image,
													   $image_size_2x
												   ) . ' 2x'
							]
						);
						?>

					</div>

				<?php endif; ?>

				<div class="header__content-wrapper">

					<?php $pageSubTitle = new Tag(
						text: esc_html__('Lists and Extras', 'FORBES'),
						url: get_post_type_archive_link('list')
						);

						echo $pageSubTitle->render(); ?>

					<?php $listTitle = new Heading($is_sub_list ? $chosen_list['title'] : get_the_title(), 'h1', null, '700', 'noto-serif'); echo $listTitle->render(); ?>

					<div class="header__excerpt-wrapper">

						<?php if ($description) : ?>

							<?= $description; ?>

						<?php elseif (has_excerpt()) : ?>

							<p class="header__excerpt"><?= $is_sub_list ? $chosen_list['description'] : get_the_excerpt(); ?></p>

						<?php endif; ?>

					</div>

				</div>

			</div>

			<div class="body">

				<?php if ((!empty($show_custom_ordering) || !empty($show_layout_switcher)) && !$is_crossroad) : ?>

					<div class="body__buttons-wrapper<?= !$show_custom_ordering ? ' no-sorting' : ''; ?>">

						<?php if (!empty($show_custom_ordering) && !empty($custom_order_criterias)) : ?>

							<div id="sorting" class="body__sorting">

								<span id="selected-sorting" class="body__sorting-selected cta-link-tag"><?= esc_html__('Default Order', 'FORBES'); ?></span>

								<div class="body__sorting-options">

									<span class="body__sorting-option cta-link-tag active" data-value="default"><?= esc_html__('Default Order', 'FORBES'); ?></span>

									<?php foreach ($custom_order_criterias as $criteria) : ?>

										<span class="body__sorting-option cta-link-tag" data-value="<?= strtolower(frontend_no_accent($criteria)); ?>"><?= $criteria; ?></span>

									<?php endforeach; ?>

								</div>

							</div>

						<?php endif; ?>

						<?php if ($show_layout_switcher) : ?>

							<button id="layout-swticher" class="body__layout-switcher" type="button" aria-label="Switch Layout">
								<div class="body__layout-switcher-row-wrapper">
									<div class="body__layout-switcher-row body__layout-switcher-row--top"></div>
									<div class="body__layout-switcher-row body__layout-switcher-row--bottom"></div>
								</div>
								<div class="body__layout-switcher-card-wrapper">
									<div class="body__layout-switcher-card body__layout-switcher-card--top-left"></div>
									<div class="body__layout-switcher-card body__layout-switcher-card--top-right"></div>
									<div class="body__layout-switcher-card body__layout-switcher-card--bottom-left"></div>
									<div class="body__layout-switcher-card body__layout-switcher-card--bottom-right"></div>
								</div>
							</button>

						<?php endif; ?>

					</div>

				<?php endif; ?>

				<?php if ($is_crossroad && !empty($crossroad_lists)) : ?>

					<?php get_template_part('template-parts/lists/crossroad-block/index', null, ['lists' => $crossroad_lists]); ?>

				<?php else : ?>

					<div class="body__list-wrapper<?= $reverse_numbering ? ' reverse' : ''; ?><?= $full_width_layout ? ' row-layout' : '' ?>">

						<?php foreach ($list_items as $key => $item) : ?>

							<?php get_template_part('template-parts/lists/list-item/index', null, ['key' => $reverse_numbering ? count($list_items) - $key : $key + 1, 'item' => $item, 'featured' => $featured_data_name, 'show_ranking' => $show_ranking, 'full_width_layout' => $full_width_layout]); ?>

						<?php endforeach; ?>

					</div>

					<?php if ($total_list_items > $posts_per_page) : ?>

						<?php get_template_part('template-parts/pagination/index', null, array('posts_found' => $total_list_items, 'paged' => $current_page, 'max_num_pages' => ceil(($total_list_items / $posts_per_page)), 'is_single_magazine' => true, 'pagination_string' => 'slide')) ?>

					<?php endif; ?>

				<?php endif; ?>

				<?php if ($show_methodology) : ?>

					<div class="body__list-methodology">

						<?php get_template_part('template-parts/lists/list-methodology/index', null, ['is_sub_list' => $is_sub_list, 'chosen_list' => $chosen_list]); ?>

					</div>

				<?php endif; ?>

			</div>

			<?php if (!get_field('hide_recommendation')) : ?>
				<div id="reactAIRecommendation"></div>
				<?php // get_template_part('components/related-posts/index', null, ['list' => true, 'hide_ad_placeholder' => $hide_recommendation_placeholder]); ?>
			<?php endif; ?>
		</div>

	</main>

<?php endif; ?>

<script type="text/javascript">
	window.Article = <?= wp_json_encode([
							'id'					=> get_the_ID(),
							'categoryId'			=> $category_id,
							'tags'					=> $terms,
							'huTags'				=> !is_wp_error(get_the_tags(get_the_ID())) && get_the_tags(get_the_ID()) ? get_the_tags(get_the_ID()) : [],
							'country'				=> COUNTRY,
						], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) ?>
</script>
