@import '../../../assets/scss/base/mixins';

.page-list {
  .container {
    padding-bottom: 10.4rem;
  }

  .header {
    padding-top: $spacing-06-24;
    margin-bottom: $spacing-09-48;
    @include flexbox-properties;
    flex-direction: column;

    &.no-image {
      padding-top: $spacing-08-40;

      .header {
        &__title {
          margin-bottom: $spacing-00;
        }

        &__content-wrapper {
          margin-top: $spacing-00;
        }
      }
    }

    &__back-button {
      align-self: flex-start;
      margin-bottom: $spacing-07-32;
      margin-left: 10rem;
      transition: all 0.3s;

      &:before {
        background-color: $color-icon-primary;
      }

      @media (hover: hover) {
        &:hover:before {
          background-color: $color-text-brand;
        }
      }
    }

    &__partners {
      @include flexbox-properties;
      flex-direction: column;
      margin-bottom: $spacing-06-24;
      max-width: 75%;
    }

    &__partners-wrapper {
      @include flexbox-properties;
      flex-wrap: wrap;
      margin: $spacing-00 -1.6rem -1.6rem -1.6rem;
    }

    &__logo-wrapper {
      max-width: 15rem;
      height: auto;
      width: auto;
      margin: $spacing-00 $spacing-04-16 $spacing-04-16;
    }

    &__logo-label {
      color: $color-text-secondary;
      margin-bottom: $spacing-04-16;
    }

    &__sponsor-logo {
      max-width: 10.7rem;
      max-height: 10.7rem;
      object-fit: contain;
      width: auto;
    }

    &__image-wrapper {
      height: 40rem;
      width: 100%;

      &--16x9 {
        height: auto !important;
        max-height: none !important;
        aspect-ratio: 16/9;

        .header__image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }
      }
    }
    &__image {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }

    &__content-wrapper {
      width: 73rem;
      margin: auto;
      background-color: $color-surface-primary;
      margin-top: -5rem;
      position: relative;
      padding: $spacing-06-24;
      padding-bottom: $spacing-00;
    }

    &__post-type {
      margin-bottom: $spacing-03-12;
    }

    &__title {
      margin-bottom: $spacing-06-24;
    }

    &__excerpt-wrapper {
      margin: $spacing-00 -2.4rem;
      margin-top: $spacing-07-32;

      & > * {
        margin-left: $spacing-00;
        margin-right: $spacing-00;
      }
    }
  }

  .body {
    &__buttons-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 73rem;
      margin: auto;
      margin-bottom: $spacing-08-40;

      &.no-sorting {
        justify-content: flex-end;
      }
    }

    &__sorting {
      cursor: pointer;
      border: 0.1rem solid $color-text-secondary;
      padding: $spacing-02 $spacing-07-32 $spacing-02 $spacing-04-16;
      position: relative;
      min-width: 18rem;
      transition: background-color 0.3s ease;

      &:after {
        content: '';
        position: absolute;
        top: 40%;
        right: 1.9rem;
        border: 0.1rem solid $color-text-primary;
        border-width: 0 0.1rem 0.1rem 0;
        display: inline-block;
        padding: $spacing-01;
        transform: translateY(-50%) rotate(45deg);
      }

      @media (hover: hover) {
        &:hover {
          background-color: $color-divider;
        }
      }
    }

    &__sorting-options {
      width: 26.4rem;
      position: absolute;
      top: calc(100% + 0.8rem);
      left: 0;
      padding: $spacing-08-40 $spacing-07-32;
      background-color: $color-surface-primary;
      box-shadow: $box-shadow-level-4;
      z-index: -1;
      opacity: 0;
      pointer-events: none;
      transition: all 0.3s ease;

      &.active {
        pointer-events: all;
        opacity: 1;
        z-index: 9;
      }
    }

    &__sorting-option {
      display: block;
      color: $color-text-secondary;
      margin-bottom: $spacing-07-32;
      padding-right: $spacing-07-32;
      transition: color 0.3s ease;
      cursor: pointer;

      &:first-child {
        padding-top: $spacing-00;
      }

      &:last-child {
        margin-bottom: $spacing-00;
      }

      @media (hover: hover) {
        &:hover {
          color: $color-text-primary;
        }
      }

      &.active {
        color: $color-text-primary;
        position: relative;

        &:after {
          content: '';
          width: 1.5rem;
          height: 1.2rem;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: 0;
          @include mask-properties;
          mask-image: url('assets/icons/icon-tick.svg');
          -webkit-mask-image: url('assets/icons/icon-tick.svg');
          background-color: $color-link-visited;
        }
      }
    }

    &__layout-switcher {
      height: 1.6rem;
      width: 1.6rem;
      position: relative;
      padding: $spacing-00;
      cursor: pointer;

      &.row-layout {
        .body__layout-switcher-row-wrapper {
          display: none;
        }
        .body__layout-switcher-card-wrapper {
          display: block;
        }
      }

      @media (hover: hover) {
        &:hover {
          * {
            border-color: $color-divider;
          }
        }
      }
    }

    &__layout-switcher-row-wrapper {
      height: 100%;
      width: 100%;
    }

    &__layout-switcher-row {
      width: 100%;
      height: 0.7rem;
      width: 100%;
      border: 0.15rem solid $color-divider;
      position: absolute;
      left: 0;
      transition: border-color 0.3s ease;

      &--top {
        top: 0;
      }

      &--bottom {
        bottom: 0;
      }
    }

    &__layout-switcher-card-wrapper {
      display: none;
      height: 100%;
      width: 100%;
    }

    &__layout-switcher-card {
      height: 0.7rem;
      width: 0.7rem;
      border: 0.15rem solid $color-divider;
      position: absolute;
      transition: border-color 0.3s ease;

      &--top-left {
        top: 0;
        left: 0;
      }

      &--top-right {
        top: 0;
        right: 0;
      }

      &--bottom-left {
        bottom: 0;
        left: 0;
      }

      &--bottom-right {
        bottom: 0;
        right: 0;
      }
    }

    &__list-wrapper {
      width: 73rem;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: $spacing-07-32;
      margin: auto;
      margin-bottom: $spacing-09-48;

      &.row-layout {
        grid-template-columns: 1fr;
        grid-gap: $spacing-04-16;
      }
    }

    &__list-methodology {
      margin-top: $spacing-11-64;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    .header {
      padding-top: $spacing-05-20;

      &__logo-wrapper {
        margin-bottom: $spacing-04-16;
      }

      &__image-wrapper {
        height: 33.5rem;
      }

      &__content-wrapper {
        width: 77rem;
      }

      &__title {
        margin-bottom: $spacing-09-48;
      }
    }

    .body {
      &__list-wrapper {
        margin-bottom: $spacing-08-40;
      }

      &__buttons-wrapper,
      &__list-wrapper {
        width: 77rem;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    .container {
      padding-bottom: $spacing-09-48;
    }

    .header {
      padding-top: $spacing-04-16;

      &.no-image {
        margin-top: $spacing-07-32;

        .header {
          &__logo-wrapper {
            margin-top: $spacing-00;
            margin-bottom: $spacing-07-32;
          }
        }
      }

      &__back-button {
        margin-left: $spacing-00;
      }

      &__partners {
        max-width: none;
      }

      &__partners-wrapper {
        margin: $spacing-00 -0.8rem -0.8rem -0.8rem;
      }

      &__logo-wrapper {
        margin: $spacing-00 $spacing-02 $spacing-02 $spacing-02;
      }

      &__image-wrapper {
        width: 100%;
        height: auto;
        margin-bottom: $spacing-06-24;
      }

      &__content-wrapper {
        width: 100%;
        padding: $spacing-00;
        margin: $spacing-00;
      }

      &__excerpt-wrapper {
        margin: $spacing-00;
      }
    }

    .body {
      &__buttons-wrapper,
      &__list-wrapper {
        width: 100%;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    .header {
      margin-bottom: $spacing-06-24;

      &.no-image {
        margin-top: $spacing-04-16;
      }

      &__post-type {
        margin-bottom: $spacing-02;
      }

      &__title {
        margin-bottom: $spacing-06-24;
      }
    }

    .body {
      &__buttons-wrapper {
        margin-bottom: $spacing-06-24;
      }

      &__sorting {
        min-width: 14rem;
      }

      &__list-wrapper {
        &.row-layout {
          grid-gap: $spacing-06-24;
        }
      }
      &__list-methodology {
        margin-top: $spacing-08-40;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, sm )) {
    .container {
      padding-bottom: $spacing-07-32;
    }
    .body {
      &__list-wrapper {
        grid-template-columns: 1fr;
      }
    }
  }
}
