document.addEventListener('DOMContentLoaded', () => {
  const listPageElement = document.querySelector('.page-list');

  if (!listPageElement) return;

  const listWrapper = document.querySelector('.body__list-wrapper');

  if (!listWrapper) return;

  const listItems = [...listWrapper.children];
  const allItems = listItems.length;

  const layoutSwitcher = document.getElementById('layout-swticher');
  const customSorting = document.getElementById('sorting');
  const paginationButtons = Array.from(document.querySelectorAll('.list-pagination__page'));

  const listId = listPageElement.dataset.listid ?? null;
  const layoutSwitcherIsOn = !!layoutSwitcher;
  const customSortingIsOn = !!customSorting;
  const perPage = parseInt(listPageElement.dataset.perpage) ?? 10;

  const params = new Proxy(new URLSearchParams(window.location.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  });

  const currentPageNumber = params.slide ?? 1;
  let currentOrdering = params.ordering ?? 'default';
  let currentLayout = params.layout ?? 'card';

  if (!layoutSwitcherIsOn) {
    currentLayout = 'card';
    saveSettings();
  }

  if (!customSortingIsOn) {
    currentOrdering = 'default';
    saveSettings();
  }

  renderListItems(getElementsToShow());

  if (!params.ordering && !params.layout) {
    checkLocalStorageForSettings();
  } else {
    saveSettings();
  }

  params.ordering && applySorting();
  params.ordering && updateCardHrefs();
  params.layout && applyLayoutToListItems(currentLayout, true);

  /*
		Layout switch toggling
	*/
  if (layoutSwitcherIsOn) {
    layoutSwitcher.addEventListener('click', () => {
      layoutSwitcher.classList.toggle('row-layout');
      const newLayout = layoutSwitcher.classList.contains('row-layout') ? 'row' : 'card';

      currentLayout = newLayout;

      applyLayoutToListItems(newLayout);

      paginationButtons.length && applyChangesToPaginationButtons('layout');

      saveSettings();
    });
  }

  /*
		Custom sorting logic
	*/
  if (customSortingIsOn) {
    customSorting.addEventListener('click', () => {
      document.querySelector('.body__sorting-options').classList.add('active');
    });

    document.addEventListener('click', (e) => {
      if (e.target !== customSorting && e.target !== document.querySelector('.body__sorting-selected')) {
        document.querySelector('.body__sorting-options').classList.remove('active');
      }
    });

    const sortingOptions = customSorting.querySelectorAll('.body__sorting-option');

    sortingOptions.forEach((option) => {
      option.addEventListener('click', () => {
        sortingOptions.forEach((option) => option.classList.remove('active'));
        option.classList.add('active');

        document.querySelector('.body__sorting-selected').innerHTML = option.innerHTML;

        document.querySelector('.body__sorting-options').classList.remove('active');

        currentOrdering = option.dataset.value;

        paginationButtons.length && applyChangesToPaginationButtons('ordering');

        saveSettings();

        sortItems(option.dataset.value, listItems, listWrapper, currentPageNumber);
      });
    });
  }

  /*
		Sorting logic if order is set in URL param
	*/
  function applySorting(order = currentOrdering) {
    if (!customSortingIsOn) return;
    sortItems(order);

    const filter = Array.from(document.querySelectorAll('.body__sorting-option')).filter(
      (element) => element.dataset.value === currentOrdering
    )[0];

    if (filter) {
      document.querySelector('.body__sorting-option.active').classList.remove('active');
      filter.classList.add('active');
      document.getElementById('selected-sorting').innerHTML = filter.innerHTML;
    }
  }

  /**
   * Sort elements by a given key
   */
  function sortItems(key) {
    listItems.sort((a, b) => parseInt(a.dataset[key]) - parseInt(b.dataset[key]));

    renderListItems(getElementsToShow());
  }

  /*
		Get the appropriate 10 elements to show
	*/
  function getElementsToShow() {
    return listItems.slice(
      currentPageNumber === 1 ? 0 : (currentPageNumber - 1) * perPage,
      currentPageNumber === 1 ? perPage : (currentPageNumber - 1) * perPage + perPage
    );
  }

  /*
		Render the selected elements in the list
	*/
  function renderListItems(listItems) {
    if (!Array.isArray(listItems)) return;

    listItems.forEach((item, index) => {
      const rankingLabel = item.querySelector('.list-item__ranking');
      if (rankingLabel) {
        if (listWrapper.classList.contains('reverse')) {
          rankingLabel.innerHTML = allItems - index - (currentPageNumber * perPage - perPage);
        } else {
          rankingLabel.innerHTML = index + 1 + (currentPageNumber * perPage - perPage);
        }
      }
    });
    listWrapper.replaceChildren(...listItems);
  }

  /*
		Apply the given layout by adding/removing css classes to/from list items
	*/
  function applyLayoutToListItems(layout = currentLayout, initial) {
    if (!layoutSwitcherIsOn) return;
    if (layout === 'row') {
      initial && layoutSwitcher.classList.add('row-layout');
      listWrapper.classList.add('row-layout');
      listItems.forEach((item) => item.classList.add('row-layout'));
    } else {
      listWrapper.classList.remove('row-layout');
      listItems.forEach((item) => item.classList.remove('row-layout'));
    }
  }

  /*
		If ordering or layout changes, change the href of the pagination buttons
	*/
  function applyChangesToPaginationButtons(type) {
    paginationButtons.forEach((button) => {
      const url = new URL(button.href);
      if (type === 'layout') {
        url.searchParams.set('layout', currentLayout);
      } else {
        url.searchParams.set('ordering', currentOrdering);
      }
      button.href = url;
    });
  }

  /*
		If ordering or layout changes save the settings to local storage
	*/
  function saveSettings() {
    const newSettings = {
      layout: currentLayout ?? '',
      ordering: currentOrdering ?? '',
    };

    if (listId) {
      localStorage.setItem('list_id_' + listId, JSON.stringify(newSettings));
    }

    updateURL();
  }

  function checkLocalStorageForSettings() {
    let settings = localStorage.getItem('list_id_' + listId);
    if (settings) {
      settings = JSON.parse(settings);
      if (settings.layout && layoutSwitcherIsOn) {
        currentLayout = settings.layout;
        applyLayoutToListItems(currentLayout, true);
      }
      if (settings.ordering && customSortingIsOn) {
        currentOrdering = settings.ordering;
        applySorting();
      }
      updateURL();
    }
  }

  function updateURL() {
    const url = new URL(location.href);
    if (currentLayout !== 'card') {
      url.searchParams.set('layout', currentLayout);
    } else {
      url.searchParams.delete('layout');
    }

    if (currentOrdering !== 'default') {
      url.searchParams.set('ordering', currentOrdering);
    } else {
      url.searchParams.delete('ordering');
    }

    history.replaceState(null, '', url);
    updateCardHrefs();
  }

  function updateCardHrefs() {
    if (listItems.length) {
      listItems.forEach((item) => {
        const imageWrapper = item.querySelector('.list-item__image-wrapper');
        const link = item.querySelector('.list-item__link');

        const url = link ? new URL(link.getAttribute('href')) : null;

        if (currentOrdering !== 'default') {
          url && url.searchParams.set('ordering', currentOrdering);
        } else {
          url && url.searchParams.delete('ordering');
        }

        if (url) {
          link && link.setAttribute('href', url);
          imageWrapper && imageWrapper.setAttribute('href', url);
        }
      });
    }
  }
});
