<?php
global $wp;

/**
 * The list item
 * @var array
 */
$item = $args['item'] ?? null;

/**
 * The ranking of the item
 * @var int
 */
$ranking = $args['key'] ?? null;

/**
 * Turn off numbering on list
 * @var boolean
 */
$show_ranking = $args['show_ranking'] ?? true;

/**
 * The name of the item
 * @var string
 */
$title = $item['title'] ?? null;

/**
 * The URL of the featured image of the item
 * @var string
 */
$image = $item['image'] ?? null;

/**
 * The featured data's array key
 * @var string
 */
$featured_data_key = $args['featured'] ?? null;

/**
 * Featured Data
 * @var mixed
 */
$featured_data = $item['custom'][$featured_data_key] ?? null;

/**
 * Custom ranking
 * @var string
 */
$custom_ranking = $item['custom']['custom_ranking'] ?? null;

/**
 * Types of list item
 * @var array
 */
$types = array();

/**
 * The URL to the item's single page
 * @var string
 */
$url = isset($item['slug']) ? home_url(add_query_arg(array(), $wp->request)) . '/' . $item['slug'] . '/' : '#';

if (is_preview()) {
	if (get_field('crossroad_page')) {
		if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
			$url = "https://";
		} else {
			$url = "http://";
		}
		$url .= $_SERVER['HTTP_HOST'];
		$url .= $_SERVER['REQUEST_URI'];
		$url .= '/' . $item['slug'];
	} else {
		global $post;
		$post_slug = get_post_field('post_name', $post->ID);
		$url = isset($item['slug']) ? get_post_type_archive_link('list') . $post_slug . '/' . $item['slug'] . '/' : '#';
	}
}


/**
 * Whether to show the items in a 16/9 image dimensions
 */
$full_width_layout = $args['full_width_layout'] ?? false;

?>

<?php if ($item) : ?>

	<?php if (have_rows('content')) : ?>

		<?php while (have_rows('content')) : the_row(); ?>
			<?php $types[] = get_sub_field('type'); ?>
		<?php endwhile; ?>

	<?php endif; ?>

	<div class="list-item <?= $full_width_layout ? 'row-layout full-width' : '' ?>" <?php foreach ($item['order'] as $key => $value) {
																						echo 'data-' . frontend_no_accent($key) . '=' . $value . ' ';
																					} ?>>

		<?php if ($ranking && $show_ranking && !in_array("custom_ranking", $types)) : ?>

			<div class="list-item__ranking-wrapper">
				<span class="list-item__ranking callout"><?= $ranking; ?></span>
			</div>

		<?php elseif ($show_ranking && $custom_ranking != null) : ?>

			<div class="list-item__ranking-wrapper list-item__ranking-wrapper--custom">
				<span class="list-item__ranking--custom callout"><?= $custom_ranking; ?></span>
			</div>

		<?php endif; ?>

		<?php if ($image && $title && $url) : ?>

			<a href="<?= $url; ?>" class="list-item__image-wrapper">

				<img class="list-item__image"
                     srcset="<?= $image ?>?r=eyJ3Ijo3MDAsImgiOjcwMCwicSI6OTB9 2x"
                     src="<?= $image ?>?r=eyJ3IjozNTAsImgiOjM1MCwicSI6OTB9"
                     alt="<?= $title; ?>">

				<div class="h-d--flex list-item__title-wrapper">
					<h4 class="list-item__title"><?= $title; ?></h4>
				</div>


			</a>

			<div class="list-item__bottom-wrapper">

				<div class="list-item__details-wrapper">

					<?php if ($title) : ?>

						<a href="<?= $url; ?>" class="list-item__title list-item__title--row">
							<h4><?= $title; ?></h4>
						</a>

					<?php endif; ?>

					<div class="list-item__detail-wrapper">

						<?php if ($featured_data_key) : ?>

							<span class="list-item__detail-key callout"><?= $featured_data_key; ?></span>

						<?php endif; ?>

						<?php if ($featured_data) : ?>

							<span class="list-item__detail-value callout"><?= $featured_data; ?></span>

						<?php endif; ?>

					</div>

				</div>

				<a class="list-item__link button button--medium button--secondary after-icon after-icon--arrow" href="<?= $url; ?>"><?= esc_html__('Read more', 'FORBES'); ?></a>

			</div>

		<?php endif; ?>
	</div>

<?php endif; ?>
