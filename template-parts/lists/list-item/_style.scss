@import '../../../assets/scss/base/mixins';

.list-item {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  &__ranking-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 4rem;
    height: 4rem;
    z-index: 1;
    @include flexbox-properties;
    background-color: $color-surface-primary;

    &--custom {
      width: fit-content;
      padding: $spacing-01;
    }
  }

  &__image-wrapper {
    position: relative;
    width: 100%;
    height: auto;
    flex-grow: 1;
    margin-bottom: $spacing-02;
    color: initial;
    text-decoration: none;
    overflow: hidden;
    aspect-ratio: 1 / 1;

    @media (hover: hover) {
      &:hover {
        .list-item {
          &__title {
            text-decoration: underline;
          }
        }
      }
    }
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  &__title-wrapper {
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    text-align: right;
    min-width: 35rem;
    width: min-content;
  }

  &__title {
    background-color: $color-surface-primary;
    padding: $spacing-01 $spacing-02;
    text-align: right;
    transition: color 0.3s ease;
    display: inline;
    color: $color-text-primary;

    &--row {
      display: none;
    }
  }

  &__bottom-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__details-wrapper,
  &__detail-wrapper {
    display: flex;
    flex-direction: column;
  }

  &__details-wrapper {
    margin-right: $spacing-06-24;
  }

  &__detail-key {
    color: $color-text-secondary;
  }

  &__link {
    white-space: nowrap;
    color: $color-text-primary;
    background-color: $color-surface-primary;
  }

  &.row-layout {
    min-height: 0;
    display: flex;
    width: 100%;
    padding-bottom: $spacing-04-16;
    border-bottom: 0.1rem solid $color-divider;

    &:last-child {
      border: none;
    }

    .list-item {
      &__image-wrapper {
        width: 16rem;
        margin-right: $spacing-08-40;
        margin-bottom: $spacing-00;
      }

      &__title {
        display: none;

        &--row {
          display: block;
          position: relative;
          padding: $spacing-00;
          margin-bottom: $spacing-02;
          text-align: left;
          color: initial;
          text-decoration: none;

          h4 {
            transition: color 0.3s ease;
          }

          @media (hover: hover) {
            &:hover {
              h4 {
                color: $color-text-brand;
              }
            }
          }
        }
      }

      &__bottom-wrapper {
        flex-grow: 1;
        justify-content: space-between;
      }

      &__detail-wrapper {
        flex-direction: row;
      }

      &__detail-key {
        margin-right: $spacing-01;

        &:after {
          content: '-';
          margin-left: $spacing-01;
        }
      }
    }

    &.full-width {
      position: relative;
      border-bottom: none;

      .list-item {
        color: $color-text-primary;

        &__image-wrapper {
          width: 100%;
          aspect-ratio: 16 / 9;
        }

        &__bottom-wrapper {
          justify-content: flex-end;
          margin-top: $spacing-03-12;
        }

        &__details-wrapper {
          position: absolute;
          left: 2.4rem;
          bottom: 2.4rem;
          padding: $spacing-02 $spacing-04-16;
          background-color: $color-surface-primary;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    &__image-wrapper {
      margin-bottom: $spacing-03-12;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__title-wrapper {
      min-width: 33rem;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &.row-layout {
      border-bottom: none;
      padding-bottom: $spacing-00;

      .list-item {
        &__image-wrapper {
          margin-right: $spacing-06-24;
        }

        &__bottom-wrapper {
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;
        }

        &__details-wrapper {
          margin-bottom: $spacing-02;
        }

        &__detail-wrapper {
          flex-direction: column;
        }

        &__detail-key {
          &:after {
            content: none;
          }
        }
      }

      &.full-width {
        .list-item {
          &__detail-wrapper {
            flex-direction: row;
          }

          &__link {
            margin-left: $spacing-08-40;
            margin-top: $spacing-06-24;
          }
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, sm  )) {
    &.row-layout {
      .list-item:not(.full-width) {
        &__image-wrapper {
          min-width: 16rem;
          max-width: 16rem;
          padding-top: $spacing-00;
          margin-bottom: $spacing-02;
        }

        &__image {
          position: relative;
          top: unset;
          left: unset;
        }
      }
    }
  }
}
