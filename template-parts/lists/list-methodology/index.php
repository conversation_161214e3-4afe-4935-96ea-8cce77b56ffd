<?php

/**
 * Whether the list is via a crossroad page
 * @var boolean
 */
$is_sub_list = $args['is_sub_list'] ?? false;

/**
 * If sublist, the array of values of the list
 * @var array|null
 */
$chosen_list = $is_sub_list ? $args['chosen_list'] : null;

/**
 * The blocks ACF fields
 * @var array
 */
$methodology = $is_sub_list ? $chosen_list['methodology_group'] : get_field('methodology');

/**
 * The title
 * @var string
 */
$title = $methodology['title'] ?? null;

/**
 * Description of the block
 * @var string
 */
$description = $methodology['description'] ?? null;

/**
 * The details in the details section
 * @var array
 */
$details = $methodology['details'] ?? null;

/**
 * The infos in the info section
 * @var array
 */
$infos = $methodology['info'] ?? null;
?>

<div class="list-methodology">

	<div class="list-methodology__title-wrapper">

		<div class="list-methodology__title-icon"></div>

		<?php if ($title) : ?>

			<span class="list-methodology__title minititle"><?= $title; ?></span>

		<?php endif; ?>

	</div>

	<?php if ($description) : ?>

		<div class="list-methodology__description-wrapper gutenberg-content">

			<?= $description; ?>

		</div>

	<?php endif; ?>

	<?php if (!empty($details)) : ?>

		<div class="list-methodology__details-wrapper">

			<?php foreach ($details as $detail) : ?>

				<div class="list-methodology__detail">

					<?php if ($detail['label']) : ?>

						<span class="list-methodology__detail-label cta-link-tag"><?= $detail['label']; ?></span>

					<?php endif; ?>

					<?php if ($detail['value']) : ?>

						<h4 class="list-methodology__detail-value"><?= $detail['value']; ?></h4>

					<?php endif; ?>

				</div>

			<?php endforeach; ?>

		</div>

	<?php endif; ?>

	<?php if (!empty($infos)) : ?>

		<div class="list-methodology__infos-wrapper">

			<?php foreach ($infos as $info) : ?>

				<?php if ($info['label'] && $info['text']) : ?>

					<div class="list-methodology__info-wrapper">

						<div class="list-methodology__info-title-wrapper">

							<h5 class="list-methodology__info-title"><?= $info['label']; ?></h5>

							<div class="list-methodology__info-icon"></div>

						</div>

						<div class="list-methodology__info-text-wrapper gutenberg-content">

							<?= $info['text']; ?>

						</div>

					</div>

				<?php endif; ?>

			<?php endforeach; ?>

		</div>

	<?php endif; ?>

</div>
