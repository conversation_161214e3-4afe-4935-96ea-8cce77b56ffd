.list-methodology {
  width: 73rem;
  padding: $spacing-08-40;
  background-color: $color-surface-secondary;
  margin: auto;
  margin-bottom: 10.4rem;

  .gutenberg-content * {
    width: 100%;
  }

  &__title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-06-24;
  }

  &__title-icon {
    width: 1.8rem;
    height: 1.8rem;
    margin-right: $spacing-02;
    @include mask-properties;
    mask-image: url('assets/icons/icon-Info-2.svg');
    -webkit-mask-image: url('assets/icons/icon-Info-2.svg');
    background-color: $color-text-primary;
  }

  &__description-wrapper {
    margin-bottom: $spacing-06-24;
  }

  &__details-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: $spacing-08-40;
  }

  &__detail {
    padding-left: $spacing-06-24;
    border-left: 0.1rem solid $color-divider;
  }

  &__detail-label {
    display: block;
    margin-bottom: $spacing-02;
  }

  &__info-wrapper {
    border-bottom: 0.1rem solid $color-divider;
    margin-bottom: $spacing-07-32;

    &:last-child {
      margin-bottom: $spacing-00;
    }

    &.active {
      .list-methodology {
        &__info-icon {
          &:after {
            opacity: 0;
          }
        }

        &__info-text-wrapper {
          opacity: 1;
        }
      }
    }
  }

  &__info-title-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    @media (hover: hover) {
      &:hover {
        .list-methodology {
          &__info-title {
            color: $color-text-primary;
          }

          &__info-icon {
            &:before,
            &:after {
              border-color: $color-text-primary;
            }
          }
        }
      }
    }
  }

  &__info-title {
    color: $color-text-secondary;
    transition: color 0.3s ease;
  }

  &__info-icon {
    width: 1.4rem;
    height: 1.4rem;
    position: relative;

    &:before,
    &:after {
      content: '';
      position: absolute;
      transition: all 0.3s ease;
    }

    &:before {
      width: 100%;
      border-bottom: 0.1rem solid $color-text-secondary;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    &:after {
      height: 100%;
      border-left: 0.1rem solid $color-text-secondary;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      transition: opacity 0.3s ease;
    }
  }
  &__description-wrapper,
  &__info-text-wrapper {
    * {
      font-size: 1.6rem !important;
      line-height: 2.4rem !important;
    }

    & > * {
      margin: $spacing-00 $spacing-00 $spacing-04-16 !important;
    }
  }

  &__info-text-wrapper {
    margin-bottom: $spacing-02;
    overflow: hidden;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: all 0.3s ease;
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    padding: $spacing-08-40;
    width: 77rem;

    &__title-wrapper {
      margin-bottom: $spacing-06-24;
    }

    &__description-wrapper {
      margin-bottom: $spacing-06-24;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    width: 100%;
    padding: $spacing-08-40;
    margin-bottom: $spacing-09-48;

    &__info-text-wrapper,
    &__description-wrapper {
      * {
        font-size: 1.4rem !important;
        line-height: 2rem !important;
      }

      ul {
        li {
          &:before {
            top: 0.7rem !important;
          }
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, sm  )) {
    padding: $spacing-06-24;
    margin-bottom: $spacing-07-32;

    &__description-wrapper {
      margin-bottom: $spacing-07-32;
    }

    &__details-wrapper {
      flex-direction: column;
    }

    &__detail {
      margin-bottom: $spacing-08-40;

      &:last-child {
        margin-bottom: $spacing-00;
      }
    }

    &__info-text {
      font-size: 1.4rem;
      line-height: 2rem;
    }
  }
}
