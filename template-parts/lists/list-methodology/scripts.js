document.addEventListener('DOMContentLoaded', () => {
  const methodology = document.querySelector('.list-methodology');

  if (!methodology) return;

  if (!methodology.querySelector('.list-methodology__infos-wrapper')) return;

  const accordions = methodology.querySelectorAll('.list-methodology__info-wrapper');

  const openAccordion = (accordion) => {
    const content = accordion.querySelector('.list-methodology__info-text-wrapper');
    accordion.classList.add('active');
    content.style.maxHeight = content.scrollHeight + 'px';
  };

  const closeAccordion = (accordion) => {
    const content = accordion.querySelector('.list-methodology__info-text-wrapper');
    accordion.classList.remove('active');
    content.style.maxHeight = null;
  };

  accordions.forEach((accordion) => {
    const header = accordion.querySelector('.list-methodology__info-title-wrapper');
    const content = accordion.querySelector('.list-methodology__info-text-wrapper');

    header.onclick = () => {
      if (content.style.maxHeight) {
        closeAccordion(accordion);
      } else {
        accordions.forEach((accordion) => closeAccordion(accordion));
        openAccordion(accordion);
      }
    };
  });
});
