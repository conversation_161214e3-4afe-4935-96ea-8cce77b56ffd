.custom-card {
  &--color {
    padding: $spacing-06-24 !important;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  &--m,
  &--l {
    .custom-card {
      &__title {
        font-size: 3.2rem;
        line-height: 4rem;
      }
    }
  }

  &__wrapper {
    display: block !important;
    text-decoration: none;
    color: initial;
  }

  &__title {
    color: $color-text-primary;
  }

  &__content {
    p {
      color: $color-text-primary;
    }
  }

  &__image-wrapper {
    position: relative;
    aspect-ratio: 10/15;
    margin-bottom: $spacing-03-12;
    overflow: hidden;
  }

  &__image {
    position: absolute;
    inset: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  &__buttons-container {
    margin-top: $spacing-03-12;
    display: flex;
    align-items: center;
    gap: $spacing-02;
  }
}
