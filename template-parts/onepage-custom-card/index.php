<?php

/**
 * Whether the card is in a carousel
 * @var bool
 */
$is_carousel = $args['is_carousel'];

/**
 * The size of the card
 * @var string
 */
$size = $is_carousel ? 's' : $args['size'];

/**
 * The type of the card
 * @var string
 */
$type = $args['type'];

/**
 * The card
 * @var array
 */
$card = $args['card'];

/**
 * The title
 * @var string
 */
$title = $card['title'];

/**
 * The content
 * @var string
 */
$content = $card['content'];

/**
 * Background Image
 * @var string
 */
$bg_image = $type === 'image' ? $card['bg_image'] : null;

/**
 * Buttons
 * @var array
 */
$buttons = $card['buttons'];

/**
 * The clickthrough URL
 * @var string
 */
$url = isset($card['link']) ? $card['link'] : '';

if (!$url && !empty($buttons)) {
    $url = $buttons[0]['link'];
}

$classes = " custom-card--{$size}";
if ($is_carousel) {
	$classes .= ' splide__slide';
}

?>

<div class="custom-card<?= $classes; ?>">	

	<a href="<?php echo $url; ?>" class="custom-card__wrapper">

		<?php if ($bg_image) : ?>

			<div class="custom-card__image-wrapper">

				<?= wp_get_attachment_image($bg_image, 'medium', false, ['class' => 'custom-card__image']) ?>

			</div>

		<?php endif; ?>

		<?php if ($title) : ?>

			<h4 class="custom-card__title"><?= $title; ?></h4>

		<?php endif; ?>

		<?php if ($content) : ?>

			<div class="custom-card__content">
				<?= $content; ?>
			</div>

		<?php endif; ?>

	</a>

	<?php if (!empty($buttons)) : ?>

		<div class="custom-card__buttons-container">

			<?php foreach ($buttons as $index => $button) : ?>

				<?php if ($button['label'] && $button['link']) : ?>

					<a href="<?= $button['link']; ?>" class="custom-card__button button button--medium <?= $index === 0 ? 'button--primary' : 'button--secondary'; ?>" <?= $button['new_tab'] ? " target='_blank' rel='noopener'" : ''; ?>><?= $button['label']; ?></a>

				<?php endif; ?>

			<?php endforeach; ?>

		</div>

	<?php endif; ?>

</div>
