<?php

/**
 * The type of the table
 * @var string
 */
$type = $args['type'] ?? '';

/**
 * The title of the table
 * @var string
 */
$title = $type === 'active' ? esc_html__('Active Subscriptions', 'FORBES') : esc_html__('Past Subscriptions', 'FORBES');

/**
 * The empty message
 * @var string
 * */
$emptyMessage = $type === 'active' ? esc_html__('No active subscriptions found', 'FORBES') : esc_html__('There are no past subscriptions at the moment', 'FORBES');
?>

<?php if ($type) : ?>

	<div class="my-subscriptions__section my-subscriptions__section--<?= $type; ?>">

		<div class="my-subscriptions__section-header">

			<h4 class="my-subscriptions__section-title my-subscriptions__section-title--<?= $type; ?> archivo"><?= $title; ?></h4>

		</div>

		<div id="<?= $type; ?>-subscriptions-empty-state" class="my-subscriptions__empty-container empty-state">
			<p class="empty-message"><?= $emptyMessage; ?></p>
		</div>

		<table class="my-subscriptions__table my-subscriptions__table--<?= $type; ?>">

			<thead class="my-subscriptions__table-head">

				<tr class="my-subscriptions__table-header-row">

					<th class="my-subscriptions__table-header basic basic-14"><?= esc_html__('Subscription Type', 'FORBES'); ?></th>

					<th class="my-subscriptions__table-header basic basic-14"><?= esc_html__('Valid from', 'FORBES'); ?></th>

					<th class="my-subscriptions__table-header basic basic-14"><?= esc_html__('Valid to', 'FORBES'); ?></th>

				</tr>

			</thead>

			<tbody id="<?= $type; ?>-subscriptions" class="my-subscriptions__table-body">

				<tr id="<?= $type; ?>-sub-proto" class="my-subscriptions__table-row">

					<td class="my-subscriptions__table-data-wrapper">

						<span class="my-subscriptions__data-label-mobile basic basic-14"><?= esc_html__('Subscription Type', 'FORBES'); ?></span>

						<div class="my-subscriptions__data-inner-wrapper">

							<span class="my-subscriptions__table-data my-subscriptions__table-data--type basic basic-16"></span>

							<span class="my-subscriptions__data-label basic basic-14"></span>

						</div>

					</td>

					<td class="my-subscriptions__table-data-wrapper">

						<span class="my-subscriptions__data-label-mobile basic basic-14"><?= esc_html__('Valid from', 'FORBES'); ?></span>

						<span class="my-subscriptions__table-data my-subscriptions__table-data--from basic basic-16"></span>

					</td>


					<td class="my-subscriptions__table-data-wrapper">

						<span class="my-subscriptions__data-label-mobile basic basic-14"><?= esc_html__('Valid to', 'FORBES'); ?></span>

						<span class="my-subscriptions__table-data my-subscriptions__table-data--to basic basic-16"></span>

					</td>

					<td class="my-subscriptions__table-data-wrapper">

						<div class="my-subscriptions__table-data-button-container">

							<?php if ($type === 'active') : ?>

								<div class="my-subscriptions__table-data-button-inner-container">

									<button class="my-subscriptions__action-button button button--secondary button--medium">...</button>

									<ul class="my-subscriptions__dropdown">

										<li class="my-subscriptions__dropdown-item my-subscriptions__dropdown-item--extend">

											<a href="#" class="my-subscriptions__dropdown-link my-subscriptions__dropdown-link--extend link"><?= esc_html__('Renew now', 'FORBES'); ?></a>

										</li>

										<li class="my-subscriptions__dropdown-item">

											<span class="my-subscriptions__dropdown-link my-subscriptions__dropdown-link--cancel link"><?= esc_html__('Cancel Subscription', 'FORBES'); ?></span>

										</li>

									</ul>

								</div>

							<?php else : ?>

								<a href="#" class="my-subscriptions__action-button my-subscriptions__action-button--renew button button--secondary button--medium"><?= esc_html__('+ Renew', 'FORBES') ?></a>

							<?php endif; ?>

						</div>

						<div class="my-subscriptions__table-data-button-container my-subscriptions__table-data-button-container--mobile">

							<?php if ($type === 'active') : ?>

								<div class="my-subscriptions__dropdown-item--extend">
									<a href="#" class="my-subscriptions__dropdown-link my-subscriptions__dropdown-link--extend button button--secondary button--medium"><?= esc_html__('Renew now', 'FORBES'); ?></a>
								</div>
								<button class="my-subscriptions__dropdown-link my-subscriptions__dropdown-link--cancel button button--primary button--medium"><?= esc_html__('Cancel Subscription', 'FORBES'); ?></button>

							<?php else : ?>

								<a href="#" class="my-subscriptions__action-button my-subscriptions__action-button--renew button button--secondary button--medium"><?= esc_html__('+ Renew', 'FORBES') ?></a>

							<?php endif; ?>

						</div>

					</td>

				</tr>

			</tbody>

		</table>

	</div>

<?php endif; ?>