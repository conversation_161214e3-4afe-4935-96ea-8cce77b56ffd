<?php
$review = $args['review'] ?? null;
?>

<?php if ($review) : ?>

	<div class="magazine-review">

		<?php if ($review['image']) : ?>

			<div class="magazine-review__image-wrapper">

				<?= wp_get_attachment_image($review['image'], 'thumbnail', false, ['class' => 'magazine-review__image', 'alt' => $review['author'] ? $review['author'] . esc_html__(' - review') : esc_html__('Review')]) ?>

			</div>

		<?php endif; ?>

		<div class="magazine-review__details">

			<?php if ($review['title']) : ?>

				<span class="magazine-review--title minititle"><?= $review['title']; ?></span>

			<?php endif; ?>

			<?php if ($review['quote']) : ?>

				<p class="magazine-review__quote callout"><?= '"' . strip_tags($review['quote']) . '"'; ?></p>

			<?php endif; ?>

			<?php if ($review['author']) : ?>

				<div class="magazine-review__author-wrapper">

					<span class="magazine-review__author caption"><?= '- ' . $review['author']; ?></span>

				</div>

			<?php endif; ?>

		</div>

	</div>

<?php endif; ?>
