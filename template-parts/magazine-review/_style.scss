.magazine-review {
  display: flex;
  align-items: center;

  &__image-wrapper {
    max-width: 10rem;
    max-height: 10rem;
    height: auto;
    width: auto;
    margin-right: $spacing-04-16;
  }

  &__image {
    max-height: 10rem;
    max-width: 10rem;
    object-fit: contain;
  }

  &__title {
    display: block;
    margin-bottom: $spacing-02;
  }

  &__quote {
    font-style: italic;
    color: $color-surface-invert;
    margin-bottom: $spacing-02;
  }

  &__author-wrapper {
    text-align: right;
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    align-items: flex-start;

    &__image-wrapper {
      max-height: 8rem;
      max-width: 8rem;
      margin-right: $spacing-02;
    }

    &__image {
      max-height: 8rem;
      max-width: 8rem;
    }
  }
}
