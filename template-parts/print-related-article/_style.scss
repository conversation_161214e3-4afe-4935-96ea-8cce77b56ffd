.print-related-article {
  .header {
    &__content {
      padding: $spacing-06-24 $spacing-06-24 $spacing-08-40 !important;

      .heading {
        margin-bottom: $spacing-11-64;
      }

      .tag {
        margin-bottom: $spacing-02 !important;
      }

      .author {
        &__details {
          margin-top: $spacing-00;
        }
      }
    }

    &__excerpt {
      p,
      a {
        margin-bottom: $spacing-08-40;
        font-weight: 600;
        font-family: $font-archivo;
      }
    }

    &__button {
      &.after-icon {
        &:after {
          background-color: $color-surface-primary;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    .header {
      &__author {
        margin-top: $spacing-00 !important;
      }

      &__content {
        padding: $spacing-00 !important;

        .heading {
          margin-bottom: $spacing-08-40;
        }
      }
    }
  }
}
