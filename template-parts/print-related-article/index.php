<?php
/**
 * The post id used for the article card
 * @var int
 */
$id = $args['post_id'] ?? null;

/**
 * Get the primary image for the article
 */
$primary_image = get_field('primaryImage', $id) ?: get_post_thumbnail_id($id);

/**
 * The category object of the posts to show
 * @var object
 * */
$main_term = $id ? frontend_get_primary_tag($id) : null;

/**
 * The category ID of the posts to show
 * @var int
 */
$main_term_id = $main_term ? $main_term->term_id : null;

/**
 * Retrieves all the authors of a specific post.
 *
 * @param int $post_id The ID of the post.
 * @return array An array of authors for the post.
 */
$authors = get_all_the_authors($id);

$badge = false;
if ($main_term_id) {
	$badge = get_field('toggle_badge', CATEGORY_TYPE . '_' . $main_term_id);
}

/**
 * Get the badge toggle for the specified category term
 * @var array|false
 */
$badge_toggle = get_field('badge', CATEGORY_TYPE . '_' . $main_term_id) ?? false;

/**
 * Get the URL of the badge icon from the badge toggle, or null
 * @var string|null
 */
$label_icon = null;
if (is_array($badge_toggle) && isset($badge_toggle['icon'])) {
	$label_icon = wp_get_attachment_url($badge_toggle['icon']) ?? null;
}

/**
 * Get the label from the badge toggle, or the post category
 * @var string
 */
$label = $main_term->name;
if (is_array($badge_toggle) && isset($badge_toggle['label'])) {
	$label = $badge_toggle['label'];
}

/**
 * Get the first paragraph of the post content
 */
$first_paragraph = '';
if ($id) {
    $post_content = get_post_field('post_content', $id);
    $post_content = apply_filters('the_content', $post_content);
    if (preg_match('/<p>(.*?)<\/p>/', $post_content, $matches)) {
        $first_paragraph = $matches[0];
    }
}

?>

<script>
	window.printRelatedArticle = <?php echo json_encode(get_the_ID()); ?>
</script>

<div class="print-related-article">

	<?php
	if ($primary_image) : ?>

		<div>
			<div class="header__image-wrapper">

				<img src="<?= wp_get_attachment_url($primary_image, 'large') ?>" alt="<?= get_the_title(); ?>"
					 fetchpriority="high">

				<?php
					$photo_credit = new PhotoCredit($primary_image);
					$photo_credit->render();
				?>

			</div>
		</div>

	<?php endif; ?>

	<div class="header__content-wrapper<?= (!$primary_image) ? ' header__content-wrapper--no-image' : ''; ?>">

		<div class="header__content">

			<?php
				if($label) :
					$tag = new Tag(
						text: $label,
						icon: $label_icon,
						hasBackground: false,
						url: get_term_link($main_term_id),
					);
					echo $tag->render();
				endif;
			?>

			<h3 class="h3-noto heading"><?php echo get_the_title(); ?></h3>

			<div class="row">

					<div class="col-12 col-lg-10">

						<div class="header__author">

							<?php

							if (!is_wp_error($authors) && !empty($authors)) :

								foreach ($authors as $author) :

									$authorComponent = new Author(
										authorId: $author->term_id,
										type: get_field('authorRole', 'coauthor_' . $author->term_id),
										name: $author->name,
										profileImage: get_the_author_image_url($author->term_id),
										bio: '',
										position: '',
										followLink: null,
										url: $author->archive_link,
										buttonClass: 'author__follow-button--transparent',
									);
									$authorComponent->render();

									endforeach;

								endif;

							?>

						</div>

						<div class="header__excerpt">
                            <?php echo $first_paragraph; ?>
                        </div>

						<?php
                        $button = new Button(
                            __("Read the full article", 'FORBES'),
                            get_the_permalink(),
                            'large',
                            'primary',
                            false,
                            false,
                            'button--full-width after-icon after-icon--arrow header__button',
                            ''
                        );
							echo $button->render();
						?>

					</div>

				</div>
		</div>

	</div>

</div>
