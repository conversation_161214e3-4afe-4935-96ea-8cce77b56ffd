<?php

$parent = $args['parent'] ?? null;
$current = $args['current'] ?? null;

if ($parent === null) {
	return; // No way to render tabs without a parent term.
}

$tabs = [
	[
		'label' => get_term_meta($parent->term_id, 'override_tabs_label', true) ?: $parent->name,
		'slug' => 'all',
		'active' => !$current || $current->term_id === $parent->term_id,
		'url' => get_term_link($parent),
        'icon' => get_template_directory_uri() . '/assets/images/forbes-topic-default.svg',
	]
];

$children = get_terms([
	'taxonomy' => $parent->taxonomy,
	'parent' => $parent->term_id,
	'hide_empty' => false,
]);

foreach ($children as $child) {
	$tabs[] = [
		'label' => get_term_meta($child->term_id, 'override_tabs_label', true) ?: $child->name,
		'slug' => $child->slug,
		'active' => $current && $child->term_id === $current->term_id,
		'url' => get_term_link($child),
		'icon' => get_term_image_url($child->term_id),
	];
}

get_template_part('template-parts/tabs/tabs', null, [
	'tabs' => $tabs,
]);

wp_enqueue_script(
    'tabs-scripts',
    get_template_directory_uri() . '/template-parts/tabs/scripts.js',
    ['jquery'],
    filemtime(get_template_directory() . '/template-parts/tabs/scripts.js'),
    true
);
