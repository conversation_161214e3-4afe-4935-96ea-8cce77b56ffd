<?php

$tabs = $args['tabs'] ?? [];

?>

<div id="archive-template__tags-slider" class="archive-template__tags-wrapper splide" aria-label="Splide">
    <div class="splide__arrows">
        <button class="splide__arrow splide__arrow--prev">
            <i class="icon icon--chevron-left"></i>
        </button>
        <button class="splide__arrow splide__arrow--next">
            <i class="icon icon--chevron-right"></i>
        </button>
    </div>

    <div class="archive-template__tags-wrapper-inner splide__track">
        <div class="splide__list">
            <?php
            foreach ($tabs as $tab):
                $tab['classNames'][] = 'splide__slide';

                ?>
                <?php
                get_template_part('template-parts/tabs/tab', null, $tab); ?>
            <?php
            endforeach; ?>
        </div>
	</div>
	<div class="archive-template__tags-wrapper-divider"></div>
</div>
