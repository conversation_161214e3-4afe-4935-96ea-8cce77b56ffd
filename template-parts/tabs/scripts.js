// Document ready
document.addEventListener('DOMContentLoaded', function () {
  const tabsSplide = new Splide('.archive-template__tags-wrapper', {
    perPage: 1,
    perMove: 5,
    autoWidth: true,
    drag: 'free',
    snap: false,
    focus: 0,
    omitEnd: true,
    pagination: false,
    padding: { right: '4rem', left: '4rem' },
    breakpoints: {
      575: {
        padding: { right: '1.6rem', left: '1.6rem' },
        perPage: 1,
        perMove: 1,
        arrows: false,
      },
    },
  });

  tabsSplide.on('mounted', () => {
    const activeTab = document.querySelector('.archive-template__tag.active');

    const activeTabIndex = [...activeTab.parentElement.children].indexOf(activeTab);

    tabsSplide.go(activeTabIndex);
  });

  tabsSplide.mount();
});
