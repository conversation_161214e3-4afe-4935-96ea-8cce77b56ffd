<?php

/**
 * Every magazine published
 * @var array
 */
$posts = $args['posts'] ?? null;

/**
 * Is premium page
 */
$is_premium = $args['premium'] ?? false;

/**
 * The ID of the latest issue of the regular magazine
 * @var int
 */
$latest_magazine_id = Navigation\frontend_get_latest_magazine_id();

?>

<?php if (!empty($posts)) : ?>

	<main class="magazine-archive">

		<div class="container">

			<?php if ($is_premium) : ?>

				<div class="magazine-archive__premium-content">

					<?php the_content(); ?>

				</div>

			<?php else : ?>

				<?php $pageTitle = new Heading(esc_html__('The issues of Forbes Magazine', 'FORBES'), 'h1', null, '700', 'noto-serif'); echo $pageTitle->render(); ?>

			<?php endif; ?>

			<?php if ($latest_magazine_id) : ?>

				<?php
				$issue_month = get_field('issue_date', $latest_magazine_id) ? frontend_translate_date(get_field('issue_date', $latest_magazine_id), 'M') : null;
				?>

				<h3 class="magazine-archive__latest-issue-section-title"><?= esc_html__('Latest Issue', 'FORBES'); ?></h3>

				<div class="magazine-archive__latest-issue">

					<div class="magazine-archive__latest-issue-wrapper">

						<a href="<?= get_the_permalink($latest_magazine_id); ?>" class="magazine-archive__latest-issue-image-wrapper">

							<?php $image = get_the_post_thumbnail($latest_magazine_id, 'medium', array('class' => 'magazine-archive__latest-issue-image', 'alt' => get_the_title($latest_magazine_id))); ?>

							<?php if ($image) : ?>

								<?= $image; ?>

							<?php endif; ?>

						</a>

						<div class="magazine-archive__latest-issue-details-wrapper">

							<?php if ($issue_month) : ?>

								<a href="<?= get_the_permalink($latest_magazine_id) ?>" class="magazine-archive__latest-issue-month minititle"><?= $issue_month; ?></a>

							<?php endif; ?>

							<a href="<?= get_the_permalink($latest_magazine_id); ?>" class="magazine-archive__latest-issue-title">
								<h4><?= get_the_title($latest_magazine_id); ?></h4>
							</a>

							<div class="magazine-archive__latest-issue-content">

								<p><?= strip_tags(get_the_content(null, false, $latest_magazine_id)); ?></p>

							</div>

							<div class="magazine-archive__latest-issue-buttons-wrapper">

								<?php
								/**
								 * The URL that leads to the subscription page
								 * @var string
								 */
								$url_sub = get_field('url_sub', $latest_magazine_id);

								/**
								 * The URL that leads to the shop page of the magazine
								 * @var string
								 */
								$url_issue = get_field('url_issue', $latest_magazine_id);
								?>

								<?php if ($url_issue) : ?>

									<a class="magazine-archive__latest-issue-button button button--medium button--primary cta-link-tag" href="<?= $url_issue; ?>" rel="noopener" target="_blank"><?= strtoupper(esc_html_e('Buy this issue', 'FORBES') ?? ''); ?></a>

								<?php endif; ?>

								<?php if ($url_sub) : ?>

									<a class="magazine-archive__latest-issue-button magazine-single__button--issue button button--medium button--primary cta-link-tag" href="<?= $url_sub; ?>" rel="noopener" target="_blank"><?= strtoupper(esc_html_e('Buy subscription', 'FORBES') ?? ''); ?></a>

								<?php endif; ?>

							</div>

						</div>

					</div>

				</div>

			<?php $posts = array_filter($posts, fn ($post) => $latest_magazine_id !== $post->ID); ?>
			<?php endif; ?>

			<?php if(count($posts)) : ?>

			<h3 class="magazine-archive__section-title"><?= esc_html__('Other Issues', 'FORBES'); ?></h3>

			<div class="magazine-archive__grid">

				<?php foreach ($posts as $post) : ?>

					<?php get_template_part('template-parts/magazine-card/index', null, ['post' => $post]); ?>

				<?php endforeach; ?>

			</div>

			<?php endif; ?>

		</div>

	</main>

<?php endif; ?>
