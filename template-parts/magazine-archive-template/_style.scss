.magazine-archive {
  .container {
    padding-bottom: $spacing-11-64;
  }

  &__premium-content {
    padding-top: $spacing-07-32;
    margin-bottom: $spacing-07-32;
  }

  h1 {
    padding-top: $spacing-07-32;
    margin-bottom: $spacing-07-32;
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: $spacing-09-48 $spacing-07-32;
  }

  &__latest-issue {
    margin-bottom: $spacing-08-40;
    display: flex;

    &-section-title {
      margin-bottom: $spacing-07-32;
    }

    &-wrapper {
      text-decoration: none;
      color: initial;
      display: flex;
      align-items: flex-start;
    }

    &-image-wrapper {
      display: block;
      flex: 2;
      min-width: 26rem;
      max-width: 26rem;
      height: auto;
      margin-right: $spacing-07-32;
      overflow: hidden;
    }

    &-image {
      object-fit: contain;
      height: 100%;
      width: 100%;
      transition: all 0.3s ease;
    }

    &-title {
      margin-bottom: $spacing-02;
      text-decoration: none;
      color: $color-text-primary;
    }

    &-month {
      display: block;
      margin-bottom: $spacing-02;
      color: $color-text-primary;
    }

    &-content {
      max-width: 65%;
      display: -webkit-box;
      -webkit-line-clamp: 8;
      -webkit-box-orient: vertical;
      overflow: hidden;
      visibility: visible;
      color: $color-text-primary !important;
      margin-bottom: $spacing-04-16;
    }

    &-buttons-wrapper {
      display: flex;
    }

    &-button {
      margin-right: $spacing-06-24;
    }
  }

  &__section-title {
    margin-bottom: $spacing-07-32;
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    &__grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__latest-issue {
      &-content {
        max-width: 100%;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    .container {
      padding-bottom: $spacing-07-32;
    }

    &__grid {
      grid-gap: $spacing-07-32;
      grid-template-columns: 1fr;
    }

    h1 {
      margin-bottom: $spacing-07-32;
    }

    &__latest-issue {
      &-wrapper {
        flex-direction: column;
      }

      &-image-wrapper {
        min-width: 100%;
        max-width: 100%;
        margin-right: $spacing-00;
        margin-bottom: $spacing-04-16;
      }

      &-title {
        text-align: center;
        margin-bottom: $spacing-04-16;
      }
    }
  }
}
