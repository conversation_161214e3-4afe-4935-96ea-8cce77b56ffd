<?php

/**
 * The product id used for the product card
 * @var int
 */
$id = $args['post_id'] ?? null;

/**
 * The background color of the product
 * @var string
 * */
$background_color = $args['background_color'] ?? null;

/**
 * The dark background color of the product on dark mode
 * @var string
 * */
$dark_background_color = $args['dark_background_color'] ?? null;

/**
 * The categories of the products to show
 * @var int
 */
$terms = get_the_terms($id, 'product-category');

/**
 * The image of the product
 * @var string
 * */
$image = get_field('image', $id);

/**
 * The background image of the product
 * @var string
 * */
$background_image = get_field('background_image', $id);

/**
 * The short description of the product
 * @var string
 * */
$short_description = get_field('short_description', $id);

/**
 * The price of the product
 * @var string
 * */
$price = get_field('price', $id);

/**
 * The label of the website
 * @var string
 * */
$website_label = get_field('website_label', $id);

/**
 * The URL of the website
 * @var string
 * */
$website_url = get_field('website_url', $id);

/**
 * Set special appearance to the product card
 * @var boolean
 * */
$special = get_field('special', $id);

?>

<div class="product-card product-card--light <?php if ($special) : ?> product-card--special <?php endif; ?>" style="<?php if ($special) : ?> background-image: url(<?= wp_get_attachment_url($background_image); ?>); <?php else : ?> background-color:<?= $background_color;
																																																														endif; ?>;">

	<div class="product-card__layer"></div>

	<?php if ($terms != false) : ?>
		<span class="product-card__category">
			<?= $terms[0]->name; ?>
		</span>
	<?php endif; ?>

	<div class="product-card__wrapper">
		<div class="product-card__image-wrapper">
			<?php if ($image && $special === false) : ?>
				<?= wp_get_attachment_image($image, 'full', "", ["class" => "product-card__image h-object-fit--contain"]); ?>
			<?php endif; ?>
		</div>

		<div class="product-card__details">
			<h4 class="product-card__title">
				<?= get_the_title($id); ?>
			</h4>

			<?php if ($short_description) : ?>
				<div class="product-card__short-description">
					<?= $short_description; ?>
				</div>
			<?php endif; ?>

			<?php if ($price) : ?>
				<span class="product-card__price">
					<?= $price; ?>
				</span>
			<?php endif; ?>

		</div>
	</div>

	<?php if ($website_url && $website_label) : ?>
		<a rel="noopener" target="_blank" class="product-card__link" href="<?= $website_url; ?>">
			<?= $website_label; ?>
		</a>
	<?php endif; ?>

</div>

<div class="product-card product-card--dark <?php if ($special) : ?> product-card--special <?php endif; ?>" style="<?php if ($special) : ?> background-image: url(<?= wp_get_attachment_url($background_image); ?>); <?php else : ?> background-color: <?= $dark_background_color;
																																																														endif; ?>;">

	<div class="product-card__layer"></div>

	<?php if ($terms != false) : ?>
		<span class="product-card__category">
			<?= $terms[0]->name; ?>
		</span>
	<?php endif; ?>

	<div class="product-card__wrapper">
		<div class="product-card__image-wrapper">
			<?php if ($image && $special === false) : ?>
				<?= wp_get_attachment_image($image, 'full', "", ["class" => "product-card__image h-object-fit--contain"]); ?>
			<?php endif; ?>
		</div>

		<div class="product-card__details">
			<h4 class="product-card__title">
				<?= get_the_title($id); ?>
			</h4>

			<?php if ($price) : ?>
				<span class="product-card__price">
					<?= $price; ?>
				</span>
			<?php endif; ?>

		</div>
	</div>

	<?php if ($website_url && $website_label) : ?>
		<a rel="noopener" target="_blank" class="product-card__link" href="<?= $website_url; ?>">
			<?= $website_label; ?>
		</a>
	<?php endif; ?>

</div>
