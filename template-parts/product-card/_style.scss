html.dark-mode {
  .product-card {
    &__category {
      background-color: $color-divider;
    }

    &--light {
      display: none;
    }

    &--dark {
      display: block;
    }
  }
}

.product-card {
  min-height: 36rem;
  height: 100%;
  padding: 1.6rem;
  position: relative;
  transition: all 0.3s;

  &--light {
    display: block;
  }

  &--dark {
    display: none;
  }

  @media (hover: hover) {
    &:hover {
      margin-top: -1rem;
    }
  }

  &--special {
    @include background-properties;
    background-size: cover;

    .product-card__wrapper {
      justify-content: flex-end;
    }

    .product-card__layer {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.8) 150.04%);
    }

    .product-card__title {
      color: $color-white;
    }

    .product-card__price,
    .product-card__short-description p {
      color: #fcfcfdb8;
      font-size: 1.6rem;
    }
  }

  &__category {
    position: absolute;
    top: 0;
    left: 0;
    padding: $spacing-04-16 $spacing-03-12;
    background-color: $color-white;
    color: $color-link-visited;
    font-weight: 700;
    font-size: 1.2rem;
    text-transform: uppercase;
    font-family: $font-archivo;
  }

  &__wrapper {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    height: 100%;
    font-family: $font-archivo;
    padding-bottom: $spacing-05-20;
  }

  &__details {
    position: relative;
    display: flex;
    flex-direction: column;
  }

  &__image-wrapper {
    width: 100%;
    height: 20rem;
    margin-bottom: $spacing-05-20;

    img {
      width: 100%;
      height: 100%;
    }
  }

  &__title {
    margin-bottom: $spacing-01;
    font-weight: 600;
    font-size: 2.4rem;
    line-height: 3.27rem;
    color: $color-text-primary;
  }

  &__price,
  &__short-description p {
    color: $color-text-secondary;
    font-size: 1.6rem;
    font-weight: 500;
    line-height: 1.936rem;
    font-family: $font-archivo !important;
    margin-bottom: $spacing-02;
  }

  &__link {
    color: $color-link-visited;
    font-size: 1.6rem;
    font-weight: 500;
    line-height: 1.936rem;
    font-family: $font-archivo;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: flex-end;
    padding: $spacing-00 $spacing-04-16 $spacing-04-16;
  }
}
