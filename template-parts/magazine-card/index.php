<?php
$post = $args['post'] ?? null;

$post_id = $post ? $post->ID : null;

/**
 * The month the magazine is issued in
 * @var string
 */
$issue_month = get_field('issue_date') ? frontend_translate_date(get_field('issue_date'), 'M') : null;

/**
 * The cover of the magazine
 * @var string
 */
$image = $post_id ? get_the_post_thumbnail($post_id, 'medium', array('class' => 'magazine-card__image', 'alt' => get_the_title($post_id))) : null;

/**
 * The label to override the default publish month label with
 * @var string
 */
$label = get_field('label', $post_id) ?? null;
?>

<?php if ($post_id) : ?>

	<div class="magazine-card">

		<div class="magazine-card__wrapper">

			<a href="<?= get_the_permalink($post_id); ?>" class="magazine-card__image-wrapper">

				<?php if ($image) : ?>

					<?= $image; ?>

				<?php endif; ?>

			</a>

			<a href="<?= get_the_permalink($post_id); ?>" class="magazine-card__title">
				<h4><?= get_the_title($post_id); ?></h4>
			</a>

		</div>

		<?php if ($issue_month) : ?>

			<a href="<?= get_the_permalink($post_id); ?>" class="magazine-card__issue-month minititle"><?= $label ?? $issue_month; ?></a>

		<?php endif; ?>

		<?php
			$button = new Button(esc_html__('View', 'FORBES'), get_the_permalink($post_id), 'medium', 'primary', false, false, 'magazine-card__button', '');
			echo $button->render();
		?>

	</div>

<?php endif; ?>
