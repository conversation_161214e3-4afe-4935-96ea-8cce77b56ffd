.magazine-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  @media (hover: hover) {
    &:hover {
      .magazine-card {
        &__title {
          text-decoration: underline;
        }
      }
    }
  }

  &__image-wrapper {
    width: 100%;
    height: auto;
    max-height: 35rem;
    margin-bottom: $spacing-04-16;
    overflow: hidden;
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: all 0.3s ease;
  }

  &__wrapper {
    @include flexbox-properties;
    flex-direction: column;
    width: 100%;
  }

  &__issue-month {
    color: $color-text-primary;
    text-decoration: none;
    margin-bottom: $spacing-04-16;
  }

  &__title {
    margin-bottom: $spacing-04-16;
    text-align: center;
    transition: all 0.3s ease;
    color: $color-text-primary;
    text-decoration: none;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__image-wrapper {
      max-height: none;
    }

    &__content-wrapper {
      width: 100%;
    }

    &__button {
      width: 100%;
    }
  }
}
