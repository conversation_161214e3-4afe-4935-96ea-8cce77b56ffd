<!-- BrandVoice and Advoice Modal -->
<?php

if ( is_single() && (is_brandvoice(get_the_ID()) || is_advoice(get_the_ID()))) {
  $modal = get_field('brandvoice', 'category_' . forbes_legacy_frontend_get_primary_tag(get_the_ID())?->term_id)['modal'];

  if (empty($modal)) {
    return;
  }
  
  $sponsorModal = new BootstrapModal(
    id: 'sponsorModal',
    size: 'large',
    title: $modal['title'],
    content: $modal['description'],
  );
  
  echo $sponsorModal->render();
}

?>
