.brandvoice-modal {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba($color: #000000, $alpha: 0.64);
  z-index: -1;
  opacity: 0;

  &__wrapper {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 6rem;
    background-color: $color-surface-primary;
    background-color: $post-background-color;
    padding: $spacing-08-40;
    max-width: 73rem;

    &.admin-bar {
      top: 9.2rem;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-06-24;
    color: $color-text-secondary;
  }

  &__close-button {
    width: 3.4rem;
    height: 3.4rem;
    background-image: url('assets/icons/icon-close-amber.svg');
    @include background-properties;
    cursor: pointer;
  }

  &__description {
    margin-bottom: $spacing-06-24;
    color: $color-text-secondary;

    & > p {
      margin-bottom: $spacing-06-24;
    }

    & > p:last-child {
      margin-bottom: $spacing-00;
    }
  }

  &__contact-wrapper {
    display: flex;
  }

  &__avatar {
    margin-right: $spacing-04-16;
    height: 4.8rem;
    width: 4.8rem;
    border-radius: 50%;
    object-fit: contain;
  }

  &__contact {
    display: flex;
    flex-direction: column;
  }

  &__name {
    margin-bottom: $spacing-01;
  }

  &__phone,
  &__email {
    font-size: 1.6rem;
    line-height: 2.4rem;
    margin-bottom: $spacing-01;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__wrapper {
      min-width: 75%;
      top: 50% !important;
      transform: translateX(-50%) translateY(-50%);
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__wrapper {
      top: 6.7rem !important;
      transform: translateX(-50%);
      width: 100%;
      padding: $spacing-06-24 $spacing-04-16;

      &.admin-bar {
        top: 10.9rem !important;
      }
    }

    &__header {
      margin-bottom: $spacing-04-16;
    }

    &__description {
      & > p {
        font-size: 1.4rem;
        line-height: 2rem;
      }
    }
  }
}
