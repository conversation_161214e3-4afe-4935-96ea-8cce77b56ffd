.page-authors {
  .authors-header {
    position: relative;

    h1 {
      width: 63.5rem;
      max-width: 100%;

      margin-bottom: $spacing-09-48;
    }

    .inner {
      padding-top: $spacing-08-40;
      display: flex;
      flex-direction: column;
    }
  }

  .author {
    &__bio {
      display: none;
    }
  }

  .authors-content {
    &__button-wrapper {
      margin-top: $spacing-08-40;
      margin-bottom: $spacing-12-80;
      @include flexbox-properties;
    }
  }

  .authors__grid {
    > div {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: $spacing-07-32; /* Adjust gap size as needed */
      padding: $spacing-08-40 $spacing-00;

      .MuiBox-root {
        position: absolute;
      }
    }
  }

  .followed-content__grid,
  .authors__grid {
    #reactAuthorBoxTop {
      > div {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: $spacing-07-32; /* Adjust gap size as needed */
        padding: $spacing-08-40 $spacing-00;

        .MuiBox-root {
          position: absolute;
        }
      }
    }
  }

  .authors__pagination-wrapper {
    margin: $spacing-09-48;
  }
}

@media screen and (max-width: map-get($container-max-widths, lg)) {
  .page-authors {
    .authors {
      &__grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    .authors-content {
      &__button-wrapper {
        margin-bottom: $spacing-08-40;
        margin-top: $spacing-00;
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md)) {
  .page-authors {
    .authors-header {
      h1.heading {
        margin-bottom: $spacing-08-40;
      }
    }

    .authors {
      &__grid {
        > div {
          grid-template-columns: repeat(1, 1fr);
          grid-gap: $spacing-06-24;
        }
      }
    }
  }
}
