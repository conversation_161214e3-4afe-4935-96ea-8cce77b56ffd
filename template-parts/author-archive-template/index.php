<?php

/**
 * The query for the authors
 * @var object
 */
$authors = $args['authors'] ?? null;

/**
 * Whether we are on the CZ site
 * @var boolean
 */
$is_cz = COUNTRY === 'cz';
?>

<?php if ($authors && is_array($authors)) : ?>
<div class="container">
	<div class="authors__grid">

		<?php if (!empty($authors)) : ?>

			<?php foreach ($authors as $author) : ?>

				<?php $author = new Author(
						$is_cz ? get_term($author)->name : get_the_author_meta('display_name', $author),
						frontend_get_user_profile_picture($author, $is_cz),
						get_term($author)->description,
						null,
						null,
						null,
						$is_cz ? get_term_link($author) : get_author_posts_url($author)
					);

					$author->render(); ?>

			<?php endforeach; ?>

		<?php endif; ?>

	</div>

	<div class="authors__pagination-wrapper">

		<div class="justDesktop"></div>

		<div>

			<?php
			/**
			 * The current page in the paginated users
			 * @var int
			 */
			$current_page = get_query_var('paged') ? (int) get_query_var('paged') : 1;

			/**
			 * The total users found in the query
			 * @var int
			 */
			$total_users = $args['total_users'];

			/**
			 * The total number of pages in this query
			 * @var int
			 */
			$num_pages = ceil($total_users / 30);
			?>

		</div>

		<?php get_template_part('template-parts/pagination/index', null, array('posts_found' => $total_users, 'paged' => $current_page, 'max_num_pages' => $num_pages)) ?>

	</div>
</div>
<?php endif; ?>
