.pagination {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: $spacing-12-80;

  .pagination-container {
    width: 12rem;
  }

  &__wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: $spacing-00 -1.2rem;

    .page-numbers {
      color: $color-text-primary;
      padding: $spacing-01;
      @include flexbox-properties;
      width: 4rem;
      height: 4rem;
      font-size: 1.4rem;
      margin: $spacing-00 $spacing-01;
      text-decoration: none;
      font-family: $font-archivo;
      font-weight: 600;
      line-height: 1.68rem;
      position: relative;

      &.current {
        color: $color-surface-primary;
        background-color: $color-text-primary;
      }

      &.prev,
      &.next {
        margin: $spacing-00;
      }

      @media (hover: hover) {
        &:hover {
          text-decoration: underline;

          &.current,
          &.dots {
            text-decoration: none;
          }
        }
      }
    }
  }

  &__next-link,
  &__prev-link {
    width: 2.4rem;
    height: 2.4rem;
    @include mask-properties;
    mask-image: url('assets/icons/icon-chevron-right.svg');
    -webkit-mask-image: url('assets/icons/icon-chevron-right.svg');
    background-color: $color-text-secondary;

    @media (hover: hover) {
      &:hover {
        background-color: $color-icon-primary;
      }
    }
  }

  &__prev-link {
    transform: rotate(180deg);
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    justify-content: center;
    flex-direction: column;
    margin-top: $spacing-08-40;

    .button {
      margin-bottom: $spacing-08-40;
    }
  }
}
