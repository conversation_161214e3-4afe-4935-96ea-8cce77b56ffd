<?php

/**
 * The number of post found
 * @var int
 */
$posts_found = $args['posts_found'] ?? 0;

/**
 * The number of the current page
 * @var int
 */
$paged = max(1, $args['paged'] ?? 1);

/**
 * The max number of pages for this query
 * @var int
 */
$max_num_pages = intval($args['max_num_pages'] ?? 1);

/**
 * Whether the pagination is for a single magazine listing
 * @var boolean
 */
$is_single_magazine = $args['is_single_magazine'] ?? false;

/**
 * Pagination base string
 * @var string
 */
$pagination_string = $args['pagination_string'] ?? null;

?>

<?php if ($is_single_magazine || $posts_found > 8) : ?>

	<div class="pagination">

		<div class="justDesktop pagination-container"></div>

		<div>
			<?php $url = get_pagenum_link(intval($paged) + 1); ?>

			<?php if (!$is_single_magazine && $paged !== $max_num_pages) : ?>

				<?php
					$paginationButton = new Button(esc_html__('Load More', 'FORBES'), $url, 'medium', 'primary', false, false, '', '');
					echo $paginationButton->render();
				?>

			<?php endif; ?>

		</div>

		<div class="pagination__wrapper">
			<?php
			$pagination_args = [
				'total'        	=> $max_num_pages,
				'end_size'     	=> 1,
				'mid_size'     	=> 1,
				'prev_next'    	=> true,
				'prev_text'    	=> '<span class="pagination__prev-link"></span>',
				'next_text'    	=> '<span class="pagination__next-link"></span>',
				'current' 		=> $paged,
			];

			if($pagination_string) {
				$pagination_args['base'] =  '?' . $pagination_string . '=%#%';
				$pagination_args['format'] = '?' . $pagination_string . '=%#%';
			}

			echo paginate_links($pagination_args);
			?>
		</div>

	</div>

<?php endif; ?>
