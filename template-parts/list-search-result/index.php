<?php

/**
 * The id of the list (if set, this is a list)
 * @var int|null
 */
$list_id = $args['list_id'] ?? null;

/**
 * The list item (if set, this is a list item)
 * @var stdClass|null
 */
$list_item = $args['list_item'] ?? null;

/**
 * The permalink of the list or list item
 * @var string
 */
$permalink = '';

/**
 * The image of the list or list item
 * @var string
 */
$image = '';

/**
 * The title of the list or list item
 * @var string
 */
$title = '';

/**
 * The type of the list or list item
 * @var string
 */
$type = '';

if ($list_id) {
	$image = get_the_post_thumbnail($list_id, 'medium', ['class' => 'list-search-result__image', 'alt' => get_the_title($list_id)]);
	$permalink = get_the_permalink($list_id);
	$title = get_the_title($list_id);
	$type = esc_html__('List', 'FORBES');
} elseif ($list_item) {
	$title = get_the_title( $list_item );
	$image_src = get_field('image_source', $list_item);
	$image = "<img class='list-search-result__image' src='{$image_src}' alt='{$title}'>";
	$permalink = get_field('full_url', $list_item);
	$type = wp_get_post_terms( $list_item, 'list-item-category' )[0]->name;
}
?>

<?php if ($list_id ?? $list_item) : ?>

	<a href="<?= $permalink ?>" class="list-search-result">

		<div class="list-search-result__image-wrapper">

			<?php if ($image) : ?>

				<?= $image ?>

			<?php endif; ?>

		</div>

		<div class="list-search-result__content-wrapper">
			<span class="list-search-result__type footnote"><?= $type ?></span>
			<h4 class="list-search-result__title callout"><?= $title ?></h4>
		</div>

	</a>

<?php endif; ?>
