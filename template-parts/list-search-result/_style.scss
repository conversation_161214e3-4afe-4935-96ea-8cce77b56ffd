.list-search-result {
  display: flex;
  margin-bottom: $spacing-05-20;
  text-decoration: none;
  width: 100%;

  &:last-of-type {
    margin-bottom: $spacing-00;
  }

  @media (hover: hover) {
    &:hover {
      .list-search-result {
        &__title {
          text-decoration: none;
        }
      }
    }
  }

  &__image-wrapper {
    height: 11rem;
    margin-right: $spacing-04-16;
    max-width: 16rem;
    min-width: 16rem;
    overflow: hidden;
    position: relative;
  }

  &__image {
    @include background-properties;
    background-size: cover;
    cursor: pointer;
    height: 100%;
    left: 0;
    object-fit: cover;
    position: absolute;
    top: 0;
    transition: transform 0.3s ease-in-out;
    width: 100%;
  }

  &__content-wrapper {
    height: 100%;
    width: 100%;
  }

  &__type {
    color: $color-text-secondary;
    display: block;
    font-size: 1.4rem;
    margin-bottom: $spacing-00;
    margin-bottom: $spacing-02;
    text-decoration: none;
  }

  &__title {
    -webkit-box-orient: vertical;
    color: $color-text-primary;
    display: -webkit-box;
    font-size: 1.8rem;
    font-weight: 600 !important;
    -webkit-line-clamp: 4;
    line-height: 2.5rem;
    overflow: hidden;
    transition: color 0.3s ease;
    visibility: visible;
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    margin-block: $spacing-05-20;

    &:last-of-type {
      margin-bottom: $spacing-05-20;
    }

    &__image-wrapper {
      display: block;
      min-width: 12.8rem;
      max-width: 12.8rem;
      height: 7.2rem;
    }

    &__title {
      font-size: 1.6rem;
      line-height: 2.4rem;
    }
  }
}
