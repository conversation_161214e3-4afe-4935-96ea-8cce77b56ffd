<?php
$id = $args['event_id'] ?? get_the_ID();

/**
 * The thumbnail of the event
 * @var string
 */
$image = get_the_post_thumbnail( $id, 'medium_large', array('class' => 'event-and-special-card__image', 'alt' => get_the_title($id) ) );

if (!$image) {
	$image = '<img class="event-and-special-card__image" alt="' . get_the_title( $id ) . '" src="' . get_template_directory_uri(  ) . '/assets/images/forbes_placeholder.webp' . '"/>';
}

/**
 * Whether the card is for a special post
 * @var boolean
 */
$is_special = $args['special'] ?? false;

/**
 * Whether the card is for a list post
 * @var boolean
 */
$is_list = $args['list'] ?? false;

if(!$is_special) {
	$raw_date = get_field('starting_date');
	$starting_date = $raw_date ? frontend_translate_date($raw_date) : null;
}

/**
 * The outside URL for the card (on special card only)
 * @var string
 */
$url = get_field('url');
?>

<?php if( ( $is_special && $url ) || (!$is_special && $image) || $is_list ):?>

	<a class="event-and-special-card" href="<?= $url ?? get_the_permalink($id);?>">

		<div class="event-and-special-card__wrapper">

			<div class="event-and-special-card__image-wrapper">

				<?= $image;?>

			</div>

			<div class="event-and-special-card__details-wrapper">

				<h4 class="event-and-special-card__title"><?php echo get_the_title($id);?></h4>

				<?php if( isset( $starting_date ) ):?>

					<span class="event-and-special-card__date callout"><?= $starting_date;?></span>

				<?php endif;?>

			</div>

		</div>

	</a>

<?php endif;?>
