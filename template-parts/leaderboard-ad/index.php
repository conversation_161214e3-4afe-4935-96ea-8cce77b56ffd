<?php

/**
 * The page this tp is being invoked in
 */
$page = isset($args['page']) ? $args['page'] : '';

/**
 * Whether it is a mobile placement on an archive page
 * @var boolean
 */
$is_mobile = isset($args['mobile']) ? $args['mobile'] : false;

$id = 'leaderboard-ad';

switch ($page) {
	case 'single':
		$id = 'article-leaderboard-ad';
		break;
	case 'archive':
		$id = $is_mobile ? 'archive-leaderboard-ad-mobile' : 'archive-leaderboard-ad';
}

$top_ads_whitespace = '';
if (is_front_page() || is_single()) {
    $top_ads_whitespace = 'top-ads-whitespace';
}

?>

<section <?php if ('home' === $page) : echo 'id="home-leaderboard-ad-container"';
endif; ?> class="googlead-container googlead-container--leaderboard <?php
if ($is_mobile) : echo 'mobile'; endif; ?> <?php
echo $top_ads_whitespace ?>">

	<div id="<?= $id ? $id : ''; ?>" class="googlead h-m--auto h-d--flex h-flex--column h-align-items--center"></div>

</section>
