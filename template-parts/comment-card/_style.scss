.comment-card {
  @media (hover: hover) {
    &:hover {
      .comment-card__title {
        text-decoration: underline !important;
      }
    }
  }

  text-decoration: none;
  color: initial;
  align-items: flex-start;

  &__wrapper {
    display: flex;
  }

  &__image-wrapper {
    min-width: 6.4rem;
    max-width: 6.4rem;
    min-height: 6.4rem;
    max-height: 6.4rem;
    margin-right: $spacing-03-12;
    border-radius: 50%;
  }

  &__image {
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 50%;
  }

  &__content-wrapper {
    display: flex;
    flex-direction: column;
  }

  .tag {
    position: relative;
    color: $color-link-default;
    font-size: 1.4rem;
    line-height: 1.96rem;
    font-weight: 600;
    padding: $spacing-00;
    margin-bottom: $spacing-02;

    &:before {
      content: none;
    }
  }

  &__title {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
    transition: color 0.3s ease;
    z-index: 0;

    @media (hover: hover) {
      &:hover {
        text-decoration: underline;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    &__image-wrapper {
      min-width: 4.8rem;
      max-width: 4.8rem;
      min-height: 4.8rem;
      max-height: 4.8rem;
      margin-right: $spacing-02;
    }

    &__tag {
      margin-bottom: $spacing-01;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &__image-wrapper {
      margin-right: $spacing-04-16;
    }
  }
}
