<?php
/**
 * Get the main author of the post
 * @var object
 */
$main_author = get_the_main_author(get_the_ID());

/**
 * Get the author datas
 * @var array
 */
$author_datas = get_author_datas($main_author->term_id);

$author_image_id = ( static function () use ( $main_author ) {
	$image_id = get_field( 'profileImage', 'coauthor_' . $main_author->term_id );

	if ( ! $image_id ) {
		$image_id = get_field( 'profile_picture', 'user_' . $main_author->term_id );
	}

	return $image_id;
} )();
?>

<?php if ($author_datas) : ?>

	<a href="<?= get_the_permalink(get_the_ID()); ?>" class="comment-card splide__slide">

		<div class="comment-card__wrapper">

			<div class="comment-card__image-wrapper">

				<img src="<?= wp_get_attachment_image_url( $author_image_id, 'comment_card_desktop' ) ?>"
					 srcset="<?= wp_get_attachment_image_url( $author_image_id,
						 'comment_card_desktop2x' ) ?> 2x"
					 alt="<?= $author_datas['name']; ?>" class="comment-card__image">

			</div>

			<div class="comment-card__content-wrapper">

				<?php if ($author_datas['name']) : ?>

					<div class="comment-card__tag-wrapper">
						<?php
							$tag = new Tag(text: $author_datas['name']);
							echo $tag->render();
						?>
					</div>

					<?php endif; ?>

				<h4 class="comment-card__title basic-16"><?= get_the_title(get_the_ID()); ?></h4>

			</div>

		</div>

	</a>

<?php endif; ?>
