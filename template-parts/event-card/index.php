<?php

/**
 * If the card is a featured card this is the ID of the post
 * @var boolean
 */
$featured = $args['featured'] ?? null;

$id = $args['id'] ?? $featured ?? get_the_ID();

/**
 * The link to the event (either external URL of page link)
 * @var string
 */
$url = get_field('outside_url', $id) ?: get_the_permalink($id);

/**
 * The featured image of the event
 * @var int
 */
$image = $featured ? get_the_post_thumbnail( $id, 'home_events_carousel',
	[
		'class'  => 'event-card__image',
		'srcset' => get_the_post_thumbnail_url( $id, 'home_events_carousel2x' ) . ' 2x'
	] ) : wp_get_attachment_image( get_field( 'horizontal_image', $id ),
	'home_events_carousel',
	false, [
		'class'  => 'event-card__image',
		'srcset' => wp_get_attachment_image_url( get_field( 'horizontal_image', $id ),
				'home_events_carousel2x' ) . ' 2x'
	] );

// If no image was found for specific size, try to look for default one
if (!$image) {
	$image = get_the_post_thumbnail($id, 'post-thumbnail', ['class' => 'event-card__image']) ?? null;
}

$starting_date_field_name = 'cz' === COUNTRY ? 'eventStartDate' : 'starting_date';
/**
 * The date of the event
 * @var string
 */
$date = get_field($starting_date_field_name, $id);
?>

<a href="<?= $url; ?>" <?= (get_field('outside_url', $id)) ? ' target="_blank" rel="noopener"' : ''; ?> class="event-card<?= $featured ? ' event-card--featured' : ''; ?> splide__slide">

	<div class="event-card__image-wrapper">

		<?php if (get_field('upload_square_image', $id) && get_field('square_image', $id)) : ?>

			<?= wp_get_attachment_image(get_field('square_image', $id), 'large', "", ["class" => "event-card__image"]); ?>

		<?php elseif ($image) : ?>

			<?= $image; ?>

		<?php endif; ?>


		<?php if ($featured) : ?>

			<?php
			$start_date = frontend_my_str_to_time($date);
			$now = intval(date('U'));
			$diff = $start_date - $now;
			$days = intval(ceil($diff / 60 / 60 / 24));

			if (0 === $days) {
				$remaining_days = esc_html__('Today', 'FORBES');
			} elseif (1 === $days) {
				$remaining_days = esc_html__('Tomorrow', 'FORBES');
			} else {
				$remaining_days = esc_html__('In just ', 'FORBES') . $days . esc_html__(' days', 'FORBES');
			}
			?>

			<?php if ($days >= 0) : ?>

				<span class="event-card__countdown"><?= $remaining_days; ?></span>

			<?php endif; ?>

		<?php endif; ?>

	</div>

	<div class="event-card__content-wrapper">

		<?php if ($date) : ?>

			<span class="event-card__date callout"><?= frontend_translate_date($date); ?></span>

		<?php endif; ?>

		<h2 class="event-card__title"><?= get_the_title($id); ?></h2>

		<?php if (has_excerpt()) : ?>

			<p class="event-card__excerpt callout"><?= get_the_excerpt($id); ?></p>

		<?php endif; ?>

	</div>

</a>
