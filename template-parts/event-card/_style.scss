.event-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start !important;
  text-decoration: none;
  color: initial;
  width: 25.5rem;

  @media (hover: hover) {
    &:hover {
      .event-card__title {
        text-decoration: underline;
      }
    }
  }

  &__image-wrapper {
    aspect-ratio: 1 / 1;
    height: auto;
    width: 100%;
    margin-bottom: $spacing-03-12;
    overflow: hidden;
  }

  &__image {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  &__date,
  &__excerpt {
    font-family: $font-archivo !important;
    font-size: 1.4rem !important;
    line-height: 2.1rem !important;
    color: $color-text-secondary;
    margin-bottom: $spacing-01;
  }

  &__title {
    font-size: 1.8rem !important;
    line-height: 2.7rem !important;
    font-weight: 600 !important;
    margin-bottom: $spacing-01;
    color: $color-text-primary;
    transition: color 0.3s ease;
  }

  &__excerpt {
    display: -webkit-box;
    -webkit-line-clamp: 8;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
  }

  &--featured {
    grid-column: 1 / span 2;
    width: 63.5rem;

    .event-card {
      &__image-wrapper {
        position: relative;
        aspect-ratio: 16 / 9;
      }

      &__countdown {
        position: absolute;
        left: 0;
        bottom: 0;
        background-color: $color-text-brand;
        padding: $spacing-02 $spacing-03-12;
        color: $color-white;
        font-family: $font-archivo;
        font-size: 1.2rem;
        line-height: 1.4rem;
        font-weight: 700;
        text-transform: uppercase;
      }

      &__date {
        color: $color-text-brand;
        font-weight: 500;
      }

      &__title {
        font-size: 3.2rem;
        line-height: 4.3rem;
        font-weight: 700;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    &--featured {
      width: 25.5rem;
      grid-column: 1 / span 1;

      .event-card {
        &__image-wrapper {
          aspect-ratio: 1 / 1;
        }
      }
    }
  }
}
