<?php

/**
 * The query of posts to show in the grid
 * @var object
 */
$query = isset($args['query']) ? $args['query'] : null;

/**
 * The category ID of the posts to show
 * @var int
 */
$cat_id = isset($args['category']) ? $args['category'] : null;

/**
 * Whether to replace the last article with a magazine promo
 * @var boolean
 */
$replace_last_item = isset($args['replace_last_item']) ? $args['replace_last_item'] : false;

if ($replace_last_item && isset($args['magazine'])) {
	/**
	 * The URL of the promo
	 * @var string
	 */
	$promo_url = $args['magazine']['url'];

	/**
	 * The image of the promo
	 * @var string
	 */
	$promo_image = $args['magazine']['image'];
}
?>

<?php if ($query && $query->have_posts()) : ?>

	<div class="articles-grid">

		<div class="articles-grid__grid">

			<?php while ($query->have_posts()) : $query->the_post(); ?>

				<?php
				$article_card_type = 'normal';
				get_template_part('template-parts/article-card/index', null, array('post_id' => get_the_ID(), 'article_card_type' => $article_card_type, 'category' => $cat_id));
				?>

			<?php endwhile; ?>

			<?php if ($replace_last_item) : ?>

				<div class="articles-grid__magazine-wrapper">

					<?php if ($promo_image && $promo_url) : ?>

						<a href="<?= $promo_url; ?>" target="'_blank" rel="noopener"><?= wp_get_attachment_image($promo_image, 'medium', false, array('class' => 'articles-grid__magazine-image', 'alt' => 'Magazine Promo')) ?></a>

					<?php endif; ?>

				</div>

			<?php endif; ?>

			<?php wp_reset_query(); ?>

		</div>

	</div>

<?php endif; ?>
