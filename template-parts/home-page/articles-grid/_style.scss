.articles-grid {
  &__grid {
    @include grid-columns(3);
    grid-gap: $spacing-07-32;
  }

  .article-card {
    &:nth-child(2),
    &:nth-child(5) {
      padding-top: $spacing-06-24;
    }

    &:nth-child(3),
    &:nth-child(6) {
      padding-top: $spacing-09-48;
    }
  }

  &__magazine-wrapper {
    height: 31.6rem;
    width: 100%;
    position: relative;
    overflow: hidden;
  }

  &__magazine-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
    transition: all 0.3s ease;
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    &__magazine-wrapper {
      height: auto;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__grid {
      display: block;
    }

    .article-card {
      margin-bottom: $spacing-06-24;
    }

    &__magazine-wrapper {
      height: 25rem;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    .article-card {
      padding-top: $spacing-00 !important;

      &__image-wrapper {
        display: block !important;
      }

      &:last-child {
        margin-bottom: $spacing-00;
      }
    }
  }
}
