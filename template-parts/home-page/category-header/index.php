<?php
/**
 * The ID of the category (if exists)
 * @var int
 */
$id = isset($args['category']) ? $args['category'] : null;

/**
 * The title of the section
 * @var string
 */
$section_title = get_field('title') ?: ($id ? get_term($id)->name : '');

/**
 * Determine if the title should be a link
 * @var bool
 */
$title_link = get_field('title_link') ?: false;

/**
 * Determine if the header should be hidden
 * @var bool
 */
$hide_header = get_field('hide_header') ?: false;

/**
 * The type of the link
 * @var string
 */
$section_link_type = get_field('section_link_type') ?: 'page_link';

/**
 * The text of the link
 * @var string
 */
$section_link_text = get_field('section_link_text') ?: __('Go to category', 'FORBES');

/**
 * Icon next to the link based on the link type
 * @var string
 */
$link_icon = get_field('section_link_icon') === 'external' ? get_template_directory_uri() . '/assets/icons/ds2024/icon-external-link.svg' : get_template_directory_uri() . '/assets/icons/ds2024/icon-internal-link.svg';

/**
 * The URL of the link
 * @var string
 */
$section_url = '';
if ($section_link_type == 'page_link') {
    $section_url = get_field('section_page_link');
} elseif ($section_link_type == 'url') {
    $section_url = get_field('section_url');
} elseif ($section_link_type == 'category_archive' && isset($args['category'])) {
    $id = $args['category'];
    $section_title = get_field('title') ?: ($id ? get_term($id)->name : '');
    $term_link = get_term_link(get_term($id), get_term($id)->taxonomy);

    // Check if term_link is a valid URL or WP_Error
    if (!is_wp_error($term_link)) {
        $section_url = $term_link;
    }
}

/**
 * Determines if we should show an icon
 * @var bool
 */
$show_icon = get_field('show_icon') ?: false;

/**
 * The URL of the icon
 * @var string
 */
$icon = get_field('icon') ?: null;
$icon_url = $icon ? wp_get_attachment_url($icon) : get_template_directory_uri() . '/assets/icons/ds2024/icon-f.svg';
?>

<?php if (!$hide_header) : ?>
    <div class="category-header">
		<div class="category-header__title-wrapper">
			<?php if ($show_icon && $icon_url) : ?>
				<div class="category-header__icon-wrapper">
					<span class="category-header__icon" style="mask-image: url('<?php echo esc_url($icon_url); ?>'); -webkit-mask-image: url('<?php echo esc_url($icon_url); ?>');"></span>
				</div>
			<?php endif; ?>


			<?php if($title_link && $section_url) : ?>
				<a href="<?php echo esc_url($section_url); ?>" class="category-header__title-link">
					<?php $categoryTitle = new Heading(ucfirst($section_title), 'h4', null, '700', 'archivo');
					echo $categoryTitle->render();
					?>
				</a>
			<?php else :
				$categoryTitle = new Heading(ucfirst($section_title), 'h4', null, '700', 'archivo');
				echo $categoryTitle->render();
			 endif; ?>
		</div>

        <?php if($section_url) :
        	$link = new Link($section_link_text, $section_url, 'small', get_field('section_link_icon') === 'external' ? true : false, null, $link_icon, 'after', '');
        	echo $link->render();
		endif; ?>
    </div>
<?php endif; ?>
