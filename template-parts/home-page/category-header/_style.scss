.category-header {
  width: 100%;
  padding: $spacing-00;
  margin-bottom: $spacing-06-24;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;

  &__title-wrapper {
    @include flexbox-properties;
    gap: $spacing-02;
  }

  &__title-link {
    text-decoration: none;

    @media (hover: hover) {
      &:hover {
        color: $color-text-primary !important;
        text-decoration: underline;
      }
    }
  }

  &__icon-wrapper {
    width: 3.2rem;
    min-width: 3.2rem;
    height: 3.2rem;
    min-height: 3.2rem;
    background-color: $color-icon-primary;
    @include flexbox-properties;
  }

  &__icon {
    width: 1.6rem;
    height: 1.6rem;
    @include mask-properties;
    background-color: $color-white;
  }

  .link {
    margin-left: $spacing-02;
    @include flexbox-properties;

    &__text {
      white-space: nowrap;
    }

    .icon-wrapper {
      @include flexbox-properties;
      margin-left: $spacing-01;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    .heading {
      font-size: 2.5rem;
    }

    .link {
      margin-left: $spacing-01;

      &__text {
        display: none;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, sm )) {
    margin-bottom: $spacing-04-16;
  }
}
