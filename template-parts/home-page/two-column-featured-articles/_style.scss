.two-column-featured-articles {
  &__regular-wrapper {
    display: grid;
    grid-template-columns: minmax(0, 1fr);
    grid-template-rows: repeat(auto-fit, 12.5rem);
    column-gap: $spacing-07-32;
    row-gap: $spacing-06-24;
  }

  .article-card--small {
    .article-card {
      &__image-wrapper {
        max-width: 21rem;
        min-width: 21rem;
        height: 100%;
        aspect-ratio: 16 / 9;
      }

      &__image {
        height: auto;
      }

      &__content-wrapper {
        flex: 1;
      }
    }

    .heading {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__featured-wrapper {
      margin-bottom: $spacing-03-12;
    }

    &__regular-wrapper {
      display: block;

      .article-card {
        margin-bottom: $spacing-06-24;
      }
    }

    &__article-wrapper:last-child {
      .article-card {
        margin-bottom: $spacing-00;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md  )) {
    .article-card {
      margin-bottom: $spacing-06-24;

      .cta-link-tag {
        margin-bottom: $spacing-00;

        .after-icon {
          margin-bottom: $spacing-00;
        }
      }
    }

    &__featured-wrapper {
      margin-bottom: $spacing-06-24;

      .article-card {
        margin-bottom: $spacing-03-12;
      }
    }

    &__regular-wrapper {
      .two-column-featured-articles__article-wrapper:last-child {
        .article-card {
          margin-bottom: $spacing-00;
        }
      }
    }
  }
}
