<?php
$query = $args['query'] ?? null;

/**
* The category ID of the posts to show
* @var int
*/
$cat_id = $args['category'] ?? null;

$query && pageHome\Exclusions::getInstance()->setExclusions(wp_list_pluck( $query->posts, 'ID' ));
?>


<?php if( $query && $query->have_posts() ):?>

	<div class="two-column-featured-articles">

		<div class="row">

			<div class="col-12 col-lg-6 two-column-featured-articles__featured-wrapper">

				<?php $key = 0;?>
				<?php while( $query->have_posts() ): $query->the_post();?>

					<div class="two-column-featured-articles__article-wrapper">

					<?php
						$article_card_type = $key === 0 ? 'featured' : 'small';
						get_template_part( 'template-parts/article-card/index', null, array( 'post_id' => get_the_ID(), 'category' => $cat_id, 'article_card_type' => $article_card_type, 'hide_read-time' => 0 !== $key ) );
					?>

					</div>

					<?php if( 0 === $key ):?>

						</div>
						<div class="col-12 col-lg-6 two-column-featured-articles__regular-wrapper">

					<?php endif;?>

				<?php $key++;?>
				<?php endwhile;?>


			</div>

		</div>

	</div>

<?php endif;?>
