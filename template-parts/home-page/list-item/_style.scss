.home-list-item {
  display: flex;
  align-items: center;
  padding: $spacing-04-16 $spacing-00;
  border-top: 0.1rem solid $color-divider;
  text-decoration: none;
  color: initial;

  @media (hover: hover) {
    &:hover {
      .home-list-item {
        &__name {
          text-decoration: underline;
        }
      }
    }
  }

  &__image-wrapper {
    overflow: hidden;
    border-radius: 50%;
    min-width: 8rem;
    max-width: 8rem;
    height: 8rem;
    -webkit-mask-image: -webkit-radial-gradient(white, black);
  }

  &__image {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  &__text-wrapper {
    margin-left: $spacing-04-16;
    display: flex;
    flex-direction: column;
  }

  &__name {
    color: $color-text-primary;
    transition: color 0.3s ease;
  }

  &__name,
  &__first-line,
  &__second-line {
    margin: $spacing-00;
    line-height: 2.6rem;
  }

  &__first-line,
  &__second-line {
    color: $color-surface-invert;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .home-list-item {
    &__image-wrapper {
      min-width: 4.8rem;
      max-width: 4.8rem;
      height: 4.8rem;
    }

    &__text-wrapper {
      margin-left: $spacing-03-12;
    }

    &__second-line {
      font-size: 1.2rem;
      line-height: 2rem;
    }
  }
}
