<?php

/**
 * The ID of the left side special article to be shown
 * @var array(int)
 */
$special = $args['special'] ?? null;

/**
 * The ID of the right side special article to be shown
 * @var array(int)
 */
$right_side_special_article = $args['right_side_special_article'] ?? null;

/**
 * The link to the special
 * @var string
 */
$url_left = $special ? get_field('cz' === COUNTRY ? 'redirect' : 'url', $special) : '';
$url_right = $right_side_special_article ? get_field('cz' === COUNTRY ? 'redirect' : 'url', $right_side_special_article) : '';

/**
 * The url for the desktop image
 * @var string
 */
$image_src_left = $special ? (get_the_post_thumbnail_url($special, 'large') ?: wp_get_attachment_image_url(get_field('bigListingImage', $special), 'large', false)) : null;
$image_src_right = $right_side_special_article ? (get_the_post_thumbnail_url($right_side_special_article, 'large') ?: wp_get_attachment_image_url(get_field('bigListingImage', $right_side_special_article), 'large', false)) : null;

/**
 * The url for the mobile image
 * @var string
 */
$mobile_image_src_left = $special ? wp_get_attachment_image_url(get_field('cz' === COUNTRY ? 'bigListingImage' : 'mobile_image', $right_side_special_article), 'medium', false) : null;
$mobile_image_src_right = $right_side_special_article ? wp_get_attachment_image_url(get_field('cz' === COUNTRY ? 'bigListingImage' : 'mobile_image', $right_side_special_article), 'medium', false) : null;

?>

<?php if ($special) : ?>

	<div class="two-column-specials">

		<div class="row">

			<div class="col-12 col-md-8">

				<a href="<?= $url_left; ?>" rel="noopener" target="_blank" class="two-column-specials__special">

					<img src="<?= $image_src_left; ?>" alt="<?= get_the_title($special); ?>" class="two-column-specials__special-image<?= $mobile_image_src_left ? ' two-column-specials__special-image--desktop' : '' ?>">

					<?php if ($mobile_image_src_left) : ?>

						<img src="<?= $mobile_image_src_left; ?>" alt="<?= get_the_title($special); ?>" class="two-column-specials__special-image two-column-specials__special-image--mobile">

					<?php endif; ?>

				</a>

			</div>

			<div class="col-12 col-md-4">

				<?php if ('hu' === COUNTRY) : ?>

					<div class="two-column-specials__ad-wrapper googlead-container tw:my-[2rem] tw:md:my-0">

						<?php if (is_admin()) : ?>

							<p class="two-column-specials__admin-placeholder">
								<?= esc_html__('300x250 ad is rendered here', 'FORBES') ?>
							</p>

						<?php else : ?>

							<div id="featured-special-ad" class="googlead"></div>

						<?php endif; ?>


					</div>

				<?php elseif ($right_side_special_article) : ?>

					<div class="two-column-specials__small-special">

						<a href="<?= $url_right; ?>" rel="noopener" target="_blank" class="two-column-specials__special">

							<img src="<?= $image_src_right; ?>" alt="<?= get_the_title($right_side_special_article); ?>" class="two-column-specials__special-image<?= $mobile_image_src_right ? ' two-column-specials__special-image--desktop' : '' ?>">

							<?php if ($mobile_image_src_right) : ?>

								<img src="<?= $mobile_image_src_right; ?>" alt="<?= get_the_title($right_side_special_article); ?>" class="two-column-specials__special-image two-column-specials__special-image--mobile">

							<?php endif; ?>

						</a>

					</div>

				<?php endif; ?>

			</div>

		</div>

	</div>

<?php endif; ?>
