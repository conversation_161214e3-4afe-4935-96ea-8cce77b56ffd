.two-column-specials {
  &__special {
    height: 30rem;
    width: 100%;
    overflow: hidden;
  }

  &__special-image {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: all 0.3s ease-in-out;

    &--mobile {
      display: none;
    }
  }

  &__ad-wrapper {
    height: 100%;
    width: 100%;
    @include flexbox-properties;

    .googlead {
      margin-top: $spacing-00;
    }
  }

  &__admin-placeholder {
    text-align: center;
    padding: $spacing-06-24;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    .col-12:last-child {
      .two-column-specials__special {
        margin-bottom: $spacing-00;
      }
    }
    &__special {
      height: 25rem;

      @supports (aspect-ratio: 16/11) {
        height: auto;
        aspect-ratio: 16/11;
      }

      &:first-child {
        margin-bottom: $spacing-06-24;
      }
    }

    &__special-image {
      &--desktop {
        display: none;
      }

      &--mobile {
        display: block;
      }
    }
  }
}
