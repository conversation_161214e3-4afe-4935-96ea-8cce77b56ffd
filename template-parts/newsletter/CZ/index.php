<?php

/**
 * The type of newsletter form to render
 * @var string
 */
$newsletter = $args['newsletter'] ?? null;

/**
 * The label for the subscribe button
 * @var string
 */
$button_label = $args['button_label'] ?? null;

/**
 * The form actions
 * @var object
 */
$form_actions = array(
	'espresso' 	 => 'https://forbes.us9.list-manage.com/subscribe/post?u=5b8c945a2a8bd4124bbe2c1cc&amp;id=7e62846cb2',
	'cocktail' 	 => 'https://forbes.us9.list-manage.com/subscribe/post?u=5b8c945a2a8bd4124bbe2c1cc&amp;id=2fce4799ab',
	'cryptoshot' => 'https://forbes.us9.list-manage.com/subscribe/post?u=5b8c945a2a8bd4124bbe2c1cc&amp;id=c11c749073',
);

/**
 * The name attributes for the hidden inputs
 * @var object
 */
$hidden_input_names = array(
	'espresso' 	 => 'b_5b8c945a2a8bd4124bbe2c1cc_7e62846cb2',
	'cocktail' 	 => 'b_5b8c945a2a8bd4124bbe2c1cc_2fce4799ab',
	'cryptoshot' => 'b_5b8c945a2a8bd4124bbe2c1cc_c11c749073',
);
?>

<?php if ($newsletter) : ?>

	<div id="mc_embed_signup">
		<form action="<?= $form_actions[$newsletter]; ?>" method="post" id="mc-embedded-subscribe-form" name="mc-embedded-subscribe-form" class="validate" novalidate>
			<div id="mc_embed_signup_scroll">
				<div class="mc-field-group">
					<div class="mc-field-group-wrapper">
						<input type="email" value="" name="EMAIL" class="required email input" id="mce-EMAIL" placeholder="Váš e-mail" />
						<input type="submit" value="<?= $button_label ?? 'ODEBÍRAT'; ?>" name="subscribe" id="mc-embedded-subscribe" class="button button--medium button-primary" />
					</div>
				</div>
				<div id="mce-responses">
					<div class="response" id="mce-error-response" style="display:none"></div>
					<div class="response" id="mce-success-response" style="display:none"></div>
				</div>
				<div style="position: absolute; left: -5000px;" aria-hidden="true"><input type="text" name="<?= $hidden_input_names[$newsletter]; ?>" tabindex="-1" value=""></div>
			</div>
		</form>
	</div>
	<?php if (!is_admin()) : ?>
		<script type='text/javascript' src='//s3.amazonaws.com/downloads.mailchimp.com/js/mc-validate.js' defer></script>
		<script type='text/javascript' defer>
			jQuery(document).ready(() => {
				(function($) {
					window.fnames = new Array();
					window.ftypes = new Array();
					fnames[0] = 'EMAIL';
					ftypes[0] = 'email';

					/*
					 * Translated default messages for the $ validation plugin.
					 * Locale: CS
					 */
					$.extend($.validator.messages, {
						required: "Tento údaj je povinný.",
						email: "Prosím, zadejte platný e-mail.",
					});
				}(jQuery));
				var $mcj = jQuery.noConflict(true);
			});
		</script>
		<!--End mc_embed_signup-->
	<?php endif; ?>
<?php endif; ?>
