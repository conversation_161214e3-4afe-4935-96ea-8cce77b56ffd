<!-- Begin Mailchimp Signup Form -->
<div id="mc_embed_signup">

	<form action="https://forbes.us14.list-manage.com/subscribe/post?u=20fc2946df0b39d3d845208f3&amp;id=5d6f550842" method="post" id="mc-embedded-subscribe-form" name="mc-embedded-subscribe-form" class="validate" novalidate>

		<div id="mc_embed_signup_scroll">

			<div class="mc-field-group">

				<div class="mc-field-wrapper">

					<input type="email" value="" name="EMAIL" class="required email input" id="mce-EMAIL" placeholder="<?= esc_html__('Your E-Mail address', 'FORBES') ?>" />
                    <input type="submit" value="<?= get_field('button_label') ? get_field('button_label') : esc_html__('Subscribe', 'FORBES'); ?>" name="subscribe"
                           id="mc-embedded-subscribe" class="button button--medium button--primary"/>

				</div>

			</div>

			<div id="mce-responses" class="clear">

				<div class="response" id="mce-error-response" style="display:none"></div>
				<div class="response" id="mce-success-response" style="display:none"></div>

			</div> <!-- real people should not fill this in and expect good things - do not remove this or risk form bot signups-->

			<div style="position: absolute; left: -5000px;" aria-hidden="true"><input type="text" name="b_20fc2946df0b39d3d845208f3_5d6f550842" tabindex="-1" value=""></div>
		</div>
	</form>
</div>
<?php if (!is_admin()) : ?>
	<script type='text/javascript' defer>
		jQuery(document).ready(() => {
			(function($) {

				window.fnames = new Array();
				window.ftypes = new Array();
				fnames[0] = 'EMAIL';
				ftypes[0] = 'email';
				fnames[1] = 'FNAME';
				ftypes[1] = 'text';
				fnames[2] = 'LNAME';
				ftypes[2] = 'text';
				/*
				 * Translated default messages for the $ validation plugin.
				 * Locale: HU
				 */
				$.extend($.validator.messages, {
					required: "Kötelező megadni.",
					email: "Érvényes e-mail címnek kell lennie.",
				});
			}(jQuery));
			var $mcj = jQuery.noConflict(true);
		});
	</script>
<?php endif; ?>
<!--End mc_embed_signup-->
