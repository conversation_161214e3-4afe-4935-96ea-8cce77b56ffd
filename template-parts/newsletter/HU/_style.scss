body[data-locale='hu'] {
  .page-newsletter,
  .newsletter-signup {
    .newsletter {
      margin-bottom: 0;
    }

    #mc_embed_signup {
      .mc-field-wrapper {
        display: flex;
      }

      .mc-field-wrapper,
      .mc-field-group {
        #mce-EMAIL {
          flex: 1;
          border: none;
          background-color: #e5e7eb;
          font-size: 1.6rem;
          padding: 1rem 1.6rem;
          color: #4b5563;
        }

        #mc-embedded-subscribe {
          padding: 1.2rem 5rem;
          color: #f3f3f4;
        }
      }
    }

    div.mce_inline_error {
      background-color: transparent !important;
      color: #121212 !important;
      padding: 0 !important;
      margin: 2.4rem !important;
      margin-left: 0 !important;
      font-size: 1.4rem;
      font-weight: 700;
    }

    #mce-responses {
      padding: 0 !important;
      font-size: 1.4rem;
      font-weight: 700;
    }

    @media screen and (max-width: map-get($container-max-widths, md )) {
      #mc_embed_signup {
        .mc-field-group {
          .mc-field-wrapper {
            flex-direction: column;

            #mce-EMAIL {
              margin-bottom: 0.8rem;
            }
          }
        }
      }
    }
  }

  .newsletter-signup {
    #mc_embed_signup {
      .mc-field-wrapper,
      .mc-field-group {
        width: 100%;

        #mce-EMAIL {
          flex: 1;
          margin-right: 0.4rem;
          max-width: 43rem;
        }

        #mc-embedded-subscribe {
          padding: 1.4rem 1.6rem;
        }
      }

      .mc-field-wrapper {
        display: flex;
        justify-content: center;
      }

      div.mce_inline_error {
        margin: 0.8rem 0 0 0 !important;
        color: #c70528 !important;
      }

      .response {
        margin-top: 0.8rem;
        color: #c70528;
      }
    }

    @media screen and (max-width: map-get($container-max-widths, md )) {
      #mc_embed_signup {
        .mc-field-group {
          .mc-field-wrapper {
            flex-direction: row;

            .mce_inline_error {
              margin: 0 !important;
            }
          }

          #mce-EMAIL,
          #mc-embedded-subscribe {
            height: 4.4rem;
          }
        }
      }
    }
  }
}
