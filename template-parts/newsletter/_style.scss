.newsletter {
  margin-bottom: 5.2rem;
  padding: 4rem;
  background-color: #f3f4f6;

  &__title {
    display: block;
    margin-bottom: 0.8rem !important;
  }

  &__description {
    margin-bottom: 1.6rem;
    color: #4b5563;

    & > p {
      font-size: 1.6rem;
      line-height: 2.4rem;
      color: #19181f;
    }

    &.sk {
      margin-bottom: 2.4rem;
    }
  }

  &__form-wrapper {
    margin-bottom: 0.8rem;
  }

  &__consent-wrapper {
    & > p,
    a {
      font-weight: 400;
      font-size: 1.6rem;
      line-height: 2.4rem;
      color: #4b5563;
      opacity: 0.6;

      a {
        color: #121212;
        background-color: #fde68a;
        text-decoration: underline;
        opacity: 1;
        transition: color 0.3s ease;

        @media (hover: hover) {
          &:hover {
            color: #b45309;
          }
        }
      }
    }
  }

  &--single {
    .newsletter {
      &__consent-wrapper {
        & > p {
          font-size: 1.2rem;
        }
      }
    }
  }
}

.gutenberg-content {
  .newsletter {
    &--single {
      .newsletter {
        &__consent-wrapper {
          & > p {
            a {
              font-size: 1.2rem;
              line-height: 2.4rem;
              background-color: unset;
              color: #d97706;

              @media (hover: hover) {
                &:hover {
                  color: #b45309;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, lg )) {
  .newsletter {
    margin-bottom: 3.2rem;
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .newsletter {
    padding: 2.4rem;

    &__description {
      & > p {
        font-size: 1.4rem;
        line-height: 2rem;
      }
    }
  }
}
