<?php

/**
 * The block's title
 * @var string
 */
$title = $args['title'] ?: get_field('espresso', 'option')['title'] ?: null;

/**
 * The block's description
 * @var string
 */
$description = $args['description'] ?: get_field('espresso', 'option')['description'] ?: null;

/**
 * The consent text
 * @var string
 */
$consent = $args['consent'] ?: get_field('consent', 'option') ?: null;

if ('cz' === COUNTRY) {
	/**
	 * The type of newsletter to render
	 * @var string
	 */
	$newsletter = $args['newsletter'] ?? null;

	/**
	 * The text for the submit button
	 * @var string
	 */
	$button_label = get_field('button_label', 'option') ?? '';
}
?>

<div class="newsletter<?= is_single() || is_page_template('page-design-system--page.php') ? ' newsletter--single' : ''; ?>">

	<div class="newsletter__content-wrapper">

		<?php if ($title) : ?>

			<h3 class="newsletter__title minititle"><?= $title; ?></h3>

		<?php endif; ?>

		<?php if ($description) : ?>

			<div class="newsletter__description callout<?= 'sk' === COUNTRY ? ' sk' : ''; ?>"><?= $description; ?></div>

		<?php endif; ?>

		<div class="newsletter__form-wrapper">

			<?php if ('hu' === COUNTRY) : ?>

				<?php get_template_part('template-parts/newsletter/HU/index'); ?>

			<?php elseif ('sk' === COUNTRY) : ?>

				<?php get_template_part('template-parts/newsletter/SK/index'); ?>

			<?php elseif ('cz' === COUNTRY) : ?>

				<?php get_template_part('template-parts/newsletter/CZ/index', null, array('newsletter' => $newsletter, 'button_label' => $button_label)); ?>

			<?php endif; ?>

		</div>

		<?php if ($consent) : ?>

			<div class="newsletter__consent-wrapper"><?= $consent ?></div>

		<?php endif; ?>

	</div>

</div>
