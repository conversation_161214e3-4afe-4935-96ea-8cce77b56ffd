.brandvoice-author-card {
  &__author-wrapper {
    display: flex;
    margin-bottom: $spacing-08-40;
  }

  &__logo-wrapper {
    margin-right: $spacing-04-16;
  }

  &__logo {
    object-fit: contain;
    height: 6.4rem;
    width: 6.4rem;
    margin-right: $spacing-05-20;
  }

  &__text-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
  }

  &__label,
  &__author {
    color: $color-text-secondary;
  }

  &__label {
    a {
      color: $color-link-default;
      text-decoration: underline;
      text-decoration-style: solid;

      &:hover {
        color: $color-link-hover;
      }
    }
  }

  &__author {
    margin-bottom: $spacing-02;
  }

  &__author-link {
    text-decoration: underline;
    color: $color-text-secondary;

    &:hover {
      color: $color-text-secondary;
    }
  }

  &__button {
    color: $color-link-default;
    padding: $spacing-00;
    cursor: pointer;
    text-decoration: underline;
    text-decoration-style: solid;

    &:hover {
      color: $color-link-hover;
    }
  }

  &__share-wrapper {
    padding-top: $spacing-06-24;
    display: flex;
    align-items: center;
  }

  &__share-label {
    margin-right: $spacing-06-24;
    color: $color-text-secondary;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__author-wrapper {
      flex-direction: column;
    }

    &__logo-wrapper {
      @include flexbox-properties;
      margin-bottom: $spacing-04-16;
    }
  }
}
