<?php
/**
 * Template Name: Home Page
 */

get_header();

// Check if CONTENT_TYPE is set to 'life'
if (defined('CONTENT_TYPE') && CONTENT_TYPE === 'life') {
    // Fetch the custom home page ID from ACF options
    $custom_homepage_id = get_field('custom_homepage_id', 'option');
} else {
    $custom_homepage_id = null;
}

$exclusions = [];
?>

<main class="page-home">
    <div class="container">
        <?php
        // If a custom homepage ID is set, fetch and display its content
        if (!empty($custom_homepage_id)) {
            $args = array(
                'p' => $custom_homepage_id, // ID of the page
                'post_type' => 'page'
            );
            $custom_query = new WP_Query($args);

            if ($custom_query->have_posts()) {
                while ($custom_query->have_posts()) {
                    $custom_query->the_post();
                    the_content();
                }
                wp_reset_postdata();
            }
        } else {
            // Otherwise, display the default content of the current page
            while (have_posts()) {
                the_post();
                the_content();
            }
        }
        ?>
    </div>
</main>

<?php get_footer(); ?>