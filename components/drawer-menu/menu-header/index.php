<?php

class DrawerMenuHeader {
    private $favicon;
    private $dark_favicon;
    private string $avatarSrc;

    public function __construct($favicon, $dark_favicon) {
        $this->favicon = $favicon;
        $this->dark_favicon = $dark_favicon;
        $this->avatarSrc = get_template_directory_uri() . '/assets/icons/icon-user-new.png';
    }

    public function render(): void {
        global $authentication_page_slug;
        $savedArticlesUrl = frontend_translate('saved-articles', 'route') ?? 'saved-articles';

        $avatarSrc = $this->sanitizeAvatarSrc($this->avatarSrc);

        // Logged in state
        echo <<<EOT
            <a href="/{$authentication_page_slug}/#/{$savedArticlesUrl}" class="drawer-handheld-menu__user-wrapper" id="handheld-menu-li" style="display: none;">
                <div class="drawer-handheld-menu__user-info">
                    <img id="drawer-menu-user-avatar" class="drawer-handheld-menu__user-avatar"
                        src="{$avatarSrc}" alt="">
                    <h5 id="drawer-menu-user-name" class="drawer-handheld-menu__user-name link link--large link--nodecoration"></h5>
                </div>
                <span id="drawer-menu-user-saved-articles" class="drawer-handheld-menu__user-saved-articles"></span>
            </a>
        EOT;

        // Logged out state
        $label = __('Login / Sign up', 'FORBES');
        echo <<<EOT
            <div class="drawer-handheld-menu__login-wrapper" id="handheld-menu-lo" style="display: none;">
                <a rel="noopener" href="/$authentication_page_slug" class="drawer-handheld-menu__login link link--large link--nodecoration">
                    <div class="drawer-handheld-menu__login-icon-wrapper">
                        {$this->renderFavicons()}
                    </div>
                    {$label}
                </a>
            </div>
        EOT;

        $search_label = __('Hledat na Forbes.cz', 'FORBES');
        echo <<<EOT
            <div class="drawer-handheld-menu__search-wrapper" id="handheld-menu-search">
                <button class="button-icon search-opener-btn" aria-label="Search">
                    <i class="icon icon--search"></i>
                </button>

                <span class="drawer-handheld-menu__search-label link link--large link--nodecoration">{$search_label}</span>
            </div>
        EOT;

        echo <<<EOT
        <script>
            const handheldMeniLi = document.getElementById('handheld-menu-li');
            const handheldMeniLo = document.getElementById('handheld-menu-lo');

            if (window.crm_user) {
                handheldMeniLi.style.display = 'flex';
            } else {
                handheldMeniLo.style.display = 'flex';
            }
        </script>
        EOT;
    }

    /**
     * Sanitizes the avatar src to ensure it only contains the URL.
     *
     * @param string $avatarSrc The avatar source which could be an URL or an <img> tag.
     * @return string The sanitized URL.
     */
    private function sanitizeAvatarSrc(string $avatarSrc): string {
        // Check if the string contains an <img> tag
        if (strpos($avatarSrc, '<img') !== false) {
            // Use DOMDocument to parse and extract the src attribute
            $doc = new DOMDocument();
            @$doc->loadHTML($avatarSrc);
            $tags = $doc->getElementsByTagName('img');
            if ($tags->length > 0) {
                $avatarSrc = $tags->item(0)->getAttribute('src');
            }
        }

        return $avatarSrc;
    }

    private function renderFavicons(): string {
        $faviconsHtml = '';

        if ($this->favicon) {
	        $faviconsHtml .= wp_get_attachment_image( $this->favicon, 'thumbnail', "", [
		        "class"  => "drawer-handheld-menu__favicon drawer-handheld-menu__favicon--light",
		        "srcset" => ""
	        ] );
        }
        if ($this->dark_favicon) {
	        $faviconsHtml .= wp_get_attachment_image( $this->dark_favicon, 'thumbnail', "", [
		        "class"  => "drawer-handheld-menu__favicon drawer-handheld-menu__favicon--dark",
		        "srcset" => ""
	        ] );
        }

        return $faviconsHtml;
    }

}

