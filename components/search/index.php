<?php

/**
 * Wether we are displaying search results or not
 * @var boolean
 */
$is_search = !empty(get_search_query());

/**
 * The ACF fields for search modal (from an options menu).
 * @var array
 * */
$search_modal = get_field('search_modal', 'option');

/**
 * The object ID-s for taxonomies to display.
 * @var number[]
 */
$featured_taxonomy_ids = $search_modal ? $search_modal['featured_taxonomies'] : null;
?>

<div class="search-modal <?= $is_search ? 'open' : ''; ?>">

	<div class="search <?php if (is_admin_bar_showing()) : echo 'admin-bar';
						endif; ?>">

		<div class="container search__container">

			<div class="search__header">

				<?php render_site_logo(defined('CONTENT_TYPE') ? CONTENT_TYPE : null); ?>

				<button id="search-closer-btn" class="button-icon" aria-label="Close Search">

					<i class="icon icon--cross"></i>

				</button>

			</div>

			<div class="search__body">

				<div class="row">

					<div class="h-col--10 h-col--lg--12 h-offset--1 h-offset--lg--0 h-d--flex h-flex--column">

						<div class="search__text-input-wrapper">

							<input id="search-field" class="search__text-field lead" type="text" placeholder="<?php esc_html_e('Search', 'FORBES'); ?>" autocomplete="off">

							<button id="search-button" class="search__submit-button button-icon" aria-label="Init Search">

								<i class="icon icon--search"></i>

							</button>

						</div>

					</div>

				</div>

				<div class="row">

					<div class="h-col--10 h-col--lg--12 h-offset--1 h-offset--lg--0 h-d--flex h-flex--column">

						<div id="search-categories-wrapper" class="search__result-categories-wrapper"></div>

					</div>

				</div>

				<div class="row search__results-wrapper">

					<div class="h-col--10 h-col--lg--12 h-offset--1 h-offset--lg--0 h-d--flex h-flex--column">

						<div class="row">

							<div class="h-col--8 h-col--xl--10 h-col--lg--12">

								<div id="search-loading" class="search__loading-wrapper">

									<h4 class="search__loading-text"><?= esc_html__('Searching...', 'FORBES'); ?></h4>

								</div>

								<div id="posts-wrapper">

									<div id="author-results" class="search__results"></div>
									<div id="lists-results" class="search__results"></div>
									<div id="search-results" class="search__results"></div>
									<h4 class="search__no-result-text"><?= esc_html__('No results found, please try a different keyword!', 'FORBES'); ?></h4>

									<button id="load-more" class="search__load-more button button--medium button--primary" data-loading="<?= esc_html__('Loading...', 'FORBES'); ?>"><?= esc_html__('Load more posts...', 'FORBES'); ?></button>

								</div>

							</div>

							<div class="h-col--4 h-d--xl--none">

								<div class="search__ad-container search__ad-container--desktop googlead-container">

									<div id="search-ad-desktop" class="googlead"></div>

								</div>

							</div>

						</div>

					</div>

				</div>

				<div class="row">

					<div class="h-col--10 h-col--lg--12 h-offset--1 h-offset--lg--0 h-d--flex h-flex--column">

						<div id="before-search" class="search__before-search-wrapper">

							<h4>

								<?php esc_html_e('Or try exploring the topics', 'FORBES'); ?>

							</h4>

							<?php if (is_array($featured_taxonomy_ids) && !empty($featured_taxonomy_ids)) : ?>

								<div class="search__featured-taxonomies">

									<?php foreach ($featured_taxonomy_ids as $taxonomy_id) : ?>

										<?php $term = get_term($taxonomy_id); ?>

										<?php if ($term && !is_wp_error($term)) : ?>

											<a href="<?= get_term_link($term); ?>" class="search__featured-taxonomy link link--small">
												<?= $term->name; ?>
											</a>

										<?php endif; ?>

									<?php endforeach; ?>

								</div>

							<?php endif; ?>

						</div>

					</div>

				</div>

			</div>

		</div>

	</div>

</div>
