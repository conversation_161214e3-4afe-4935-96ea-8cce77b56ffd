body.admin-bar {
  .search-modal {
    top: 3.2rem;
  }

  @media screen and (max-width: 782px) {
    .search-modal {
      top: 4.6rem;
    }
  }
}

.search-modal {
  position: fixed;
  pointer-events: none;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: $color-surface-primary;
  transition: opacity 0.5s linear;
  z-index: 99; // to overlay sub-menu

  &.open {
    opacity: 1;
    position: fixed;
    pointer-events: auto;
  }

  #search-closer-btn {
    .icon {
      background-color: $color-icon-primary;
    }

    @media (hover: hover) {
      &:hover {
        .icon {
          background-color: $color-text-brand;
        }
      }
    }
  }

  #search-button {
    .icon {
      background-color: $color-text-primary;
    }
  }
}

.search {
  padding-bottom: $spacing-11-64;
  min-height: 100vh;

  &.admin-bar {
    min-height: calc(100vh - 3.2rem);
  }

  &__container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    padding: $spacing-07-32 $spacing-00;
    border-bottom: 0.1rem solid $color-divider;
  }

  &__body {
    flex: 1 0 auto;
    margin-top: $spacing-09-48;
  }

  &__text-input-wrapper {
    border: 0.2rem solid $color-text-primary;
    box-shadow: $box-shadow-level-1;
    display: flex;
  }

  &__text-field {
    height: 100%;
    border: none;
    flex: 1 0 auto;
    padding: $spacing-04-16 $spacing-00 $spacing-04-16 $spacing-04-16;
    margin: $spacing-00;
    background-color: $color-surface-primary;

    &::placeholder {
      color: $color-text-primary;
      font-size: 2rem;
      line-height: 2.8rem;
    }
  }

  &__submit-button {
    flex: 0 0 auto;
    padding: $spacing-04-16;
    margin: $spacing-00;
  }

  &__details-wrapper {
    flex: 1 0 auto;
  }

  &__loading-wrapper {
    display: none;
    margin-top: $spacing-07-32;
  }

  &__result-categories-wrapper {
    margin: $spacing-06-24 -0.4rem $spacing-08-40;
    display: flex;
    flex-wrap: wrap;
  }

  &__result-category {
    margin: $spacing-00 $spacing-01 $spacing-02;
    white-space: nowrap;

    &.active {
      background-color: $color-text-primary;
      color: $color-surface-primary;
      display: flex;
      align-items: center;
      position: relative;

      &:after {
        content: '';
        @include mask-properties;
        margin-left: $spacing-02;
        mask-image: url('assets/icons/icon-cross.svg');
        -webkit-mask-image: url('assets/icons/icon-cross.svg');
        height: 1.8rem;
        width: 1.8rem;
        background-color: $color-surface-primary;
      }
    }
  }

  &__results {
    display: flex;
    flex-wrap: wrap;

    .article-card {
      width: 100%;
      margin: $spacing-05-20 $spacing-00;

      .tag {
        margin-bottom: $spacing-00;
        background-color: $color-surface-primary;
      }
    }

    .author-result {
      width: 45%;

      .author {
        &__avatar-wrapper {
          height: 5rem;
          width: 5rem;
          max-width: 5rem;
          border-radius: 50%;
          overflow: hidden;

          &.brandvoice-author {
            border-radius: 0;
            max-width: unset;
          }

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }

  &__results-wrapper {
    margin-bottom: $spacing-07-32;
  }

  &__no-result-text {
    margin-top: $spacing-10-56;
    display: none;
  }

  &__before-search-wrapper {
    text-align: center;
    margin-top: $spacing-09-48;
  }

  &__featured-taxonomies {
    margin-top: $spacing-02;
  }

  &__featured-taxonomy {
    margin: $spacing-00 $spacing-05-20;
    font-size: 1.2rem;
  }

  &__ad-container {
    display: none;
    position: sticky;
    top: 8rem;

    &--mobile {
      position: relative;
      top: unset;
      height: 25rem;
      @include flexbox-properties;
      margin: auto;
      margin-top: $spacing-13-120;
    }
  }

  &__load-more {
    margin: auto;
    margin-top: $spacing-07-32;
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    &.admin-bar {
      min-height: calc(100vh - 46px);
    }

    &__header {
      padding-top: $spacing-06-24;
      padding-bottom: $spacing-06-24;
    }

    &__loading-wrapper {
      margin-top: $spacing-07-32;
      text-align: center;
    }

    &__result-categories-wrapper {
      margin-top: $spacing-07-32;
      margin-bottom: $spacing-07-32;
      flex-wrap: nowrap;
      overflow-x: scroll;
      -ms-overflow-style: none;
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    &__no-result-text {
      text-align: center;
      margin-top: $spacing-07-32;
    }

    &__featured-taxonomy {
      margin: $spacing-00 $spacing-04-16 $spacing-06-24;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &__header {
      padding-top: $spacing-04-16;
      padding-bottom: $spacing-04-16;
    }

    &__before-search-wrapper {
      h4 {
        text-align: center;
      }
    }

    &__body {
      margin-top: $spacing-07-32;
    }

    &__results {
      .article-card {
        margin: $spacing-00;
      }
    }

    #author-results {
      justify-content: space-between;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, sm)) {
    &__before-search-wrapper {
      margin-top: $spacing-11-64;
    }
  }
}
