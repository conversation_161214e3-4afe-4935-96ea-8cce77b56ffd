<?php

add_action('wp_ajax_frontend_search_posts', 'frontend_search_posts');
add_action('wp_ajax_nopriv_frontend_search_posts', 'frontend_search_posts');

/**
 * Search posts, users and lists
 * @return void
 */
function frontend_search_posts() {
    /**
     * The query word
     * @var string
     */
    $keyword = $_REQUEST['keyword'] ?? '';

    if ($keyword === '') wp_die();

    /**
     * The categories to filter by
     * @var array(int)
     */
    $cat_id = $_REQUEST['cat_id'] ?? null;

    /**
     * The current page of the results
     * @var int
     */
    $page = $_REQUEST['page'] ?? 1;

    /**
     * The number of posts per page
     * @var int
     */
    $post_per_page = 12;

    /**
     * The ID of the brandvoice category
     * @var int
     */
    $brandvoice_categories = !empty(get_field('brandvoice_categories', 'option')) ? get_field('brandvoice_categories', 'option') : [];

    /**
     * The ID of the print category to exclude
     * @var int
     */
    $print_category_id = get_field('print_category', 'option');

    $list_search_args = [
        'offset'            => ($page - 1) * $post_per_page,
        'posts_per_page'    => $post_per_page,
        'post_type'         => array('list-item'),
        'post_status'       => 'publish',
        'orderby'           => 'date',
        'order'             => 'ASC',
        's'                 => $keyword,
        'fields'            => 'ids',
        'tax_query'         => is_plugin_active('searchwp/index.php') ? array() : array(
            array(
                'taxonomy' => CATEGORY_TYPE,
                'field'    => 'term_id',
                'terms'    => [$print_category_id],
                'operator' => 'NOT IN',
            ),
        ),
    ];

    $list_search = new WP_Query($list_search_args);
    $list_search_results = $list_search->posts;

    /**
     * Get the author search results
     */
    $author_args = [
        'taxonomy'   => 'coauthor',
        'hide_empty' => true,
        'search'     => $keyword
    ];

    $author_search = get_terms($author_args);

    /**
     * The arguments for the WP query
     * @var array
     */
    $args = array(
        'offset'            => ($page - 1) * $post_per_page,
        'posts_per_page'    => $post_per_page,
        'post_type'         => array('list', 'post'),
        'post_status'       => 'publish',
        'orderby'           => array(
            'post_type' => 'ASC',
            'date'      => 'DESC',
        ),
        'order'             => 'DESC',
        's'                 => $keyword,
        'fields'            => 'ids',
        'tax_query'         => is_plugin_active('searchwp/index.php') ? array() : array(
            array(
                'taxonomy' => CATEGORY_TYPE,
                'field'    => 'term_id',
                'terms'    => [$print_category_id],
                'operator' => 'NOT IN',
            ),
        ),
    );

    /**
     * Add the category filter to the query
     */
    if ($cat_id) {
        if ('cz' === COUNTRY) {
            $args['tax_query'][] =
                array(
                    'taxonomy'  => CATEGORY_TYPE,
                    'field'     => 'term_id',
                    'terms'     => $cat_id,
                );
        } else {
            $args['category__in'] = $cat_id;
        }
    }

    /**
     * Initialize the WP query
     */
    if (is_plugin_active('searchwp/index.php')) {
        $query = new SWP_Query($args);
    } else {
        $query = new WP_Query($args);
    }

    /**
     * The results of the query
     * @var array
     */
    $results = $query->posts;

    if ($results || !empty($list_search_results) || !empty($author_search)) {

        /**
         * The string to be rendered on frontend containing the post templates
         * @var string
         */
        $articles = '';

        $authors = '';

        $list_items = '';

        /**
         * The string to be rendered on frontend containing the list templates
         */
        if (!empty($list_search_results)) {
            foreach ($list_search_results as $list_item) {
                ob_start();
                get_template_part('template-parts/list-search-result/index', null, ['list_item' => $list_item]);
                $list_item_content = ob_get_clean();
                $list_items = $list_items . $list_item_content;
            }
        }

        /*
         * Get the author card template for each found author and add it to the authors string
         */
        if (!empty($author_search)) {
            foreach ($author_search as $author_obj) {
                ob_start();
                get_template_part('template-parts/author-search-result/index', null, ['author' => $author_obj]);
                $author = ob_get_clean();
                $authors = $authors . $author;
            }
        }

        /**
         * Get the article card template for each found post and add it to the articles string
         */
        foreach ($results as $id) {
            ob_start();
            if (get_post_type($id) === 'list') {
                get_template_part('template-parts/list-search-result/index', null, ['list_id' => $id]);
            } else {
                get_template_part('template-parts/article-card/index', null, ['post_id' => $id, 'article_card_type' => 'small', 'search' => true]);
            }
            $article = ob_get_clean();
            $articles = $articles . $article;
        }

        /**
         * All the categories of the found posts
         * @var array(int)
         */
        $categories = [];

        /**
         * String containing the category buttons built from the categories array
         * @var string
         */
        $categories_json = '';

        /*
        Get the categories out of the found posts and create the filter buttons
        */
        if (!$cat_id) {
            foreach ($results as $id) {
                $terms = 'cz' === COUNTRY ? wp_get_post_terms($id, CATEGORY_TYPE) : get_the_category($id);
                if (is_array($terms) && !empty($terms)) {
                    foreach ($terms as $term) {
                        $categories[$term->term_id] = $term->name;
                    }
                }
            }

            asort($categories);

            foreach ($categories as $id => $name) {
                $categories_json .= '<button class="search__result-category button button--secondary button--medium" data-category="' . $id . '">' . $name . '</button>';
            }
        }

        /**
         * Return the results as a JSON object
         */
        echo json_encode(array(
            'results'           => true,
            'categories'        => $categories_json ?? '',
            'number_of_articles' => $query->post_count,
            'articles'          => $articles,
            'authors'           => $authors,
            'list_items'        => $list_items,
            'query'             => $query,
        ));
    } else {
        echo json_encode(array(
            'results' => false,
        ));
    }

    wp_die();
}
