jQuery(function ($) {
  // THe search input field
  const searchField = $('#search-field');

  // The search icon in the input field
  const searchButton = $('#search-button');

  // The search modal
  const searchModal = $('.search-modal');

  // The load more button
  const loadMoreButton = $('#load-more');

  // The search results container
  const searchResultsContainer = $('#search-results');

  // The text to show when no results are found
  const noResultsText = $('.search__no-result-text');

  // The search loading indicator
  const searchLoading = $('#search-loading');

  // The posts wrapper
  const postsWrapper = $('#posts-wrapper');

  // The ad wrapper
  const searchAdContainer = $('.search__ad-container');

  // The categories wrapper
  const searchCategoriesWrapper = $('#search-categories-wrapper');

  // The current URL
  const url = new URL(location.href);

  // The current query word if any
  const searchParam = url.searchParams.get('search');

  // The current query word
  let searchWord;

  // The currently toggled filter categories
  let toggledCategory = '';

  // Whether a search is already in progress
  let searchInprogress = false;

  // The current page of the search results (results limited to 12 posts)
  let page = 1;

  /**
   * Search functionality
   */
  function search(keyword = '', category = null) {
    // If search is already in progress or keyword is empty or shorter than 3 chars, exit
    if (
      searchInprogress ||
      (!category && (keyword === '' || keyword.length < 3)) ||
      (keyword === searchWord && category === null)
    )
      return;

    // Set search flag to true
    searchInprogress = true;

    searchWord = keyword;

    url.searchParams.set('search', keyword);
    history.pushState(null, '', url);

    /*
			Add class to the input field to change style
		*/
    $('.search__text-input-wrapper').addClass('active');

    /*
			Show/reveal necessary elements
		*/
    searchLoading.show();
    !category && searchCategoriesWrapper.hide();
    postsWrapper.hide();
    $('#before-search').hide();
    searchAdContainer.hide();

    // If new keyword search, reset keyword and page variables
    if (!category) {
      searchWord = keyword;
      page = 1;
    }

    /*
			Ajax query to get the posts
		*/
    $.ajax({
      type: 'GET',
      // eslint-disable-next-line no-undef
      url: ajaxUrl,
      data: {
        action: 'frontend_search_posts',
        keyword: searchWord,
        cat_id: category,
        page,
      },
      success: function (response) {
        response = JSON.parse(response);
        if (response.results) {
          noResultsText.hide();
          searchResultsContainer.html(response.articles);
          $('#author-results').html(response.authors);
          $('#lists-results').html(response.list_items);
          searchAdContainer.show();
          loadMoreButton.show();

          response.categories && searchCategoriesWrapper.html(response.categories);
          searchCategoriesWrapper.show();

          if (response.number_of_articles < 12) {
            loadMoreButton.hide();
          }
        } else {
          noResultsText.show();
          searchResultsContainer.html('');
          $('#lists-results').html('');
          $('#author-results').html('');
          searchCategoriesWrapper.hide();
          searchAdContainer.hide();
          loadMoreButton.hide();
        }

        postsWrapper.show();
        searchLoading.hide();
        !category && setFilters();
        searchInprogress = false;
      },
      error: function (e) {
        console.log(e);
        searchInprogress = false;
      },
    });
  }

  /*
		Init functionality for category filter buttons
	*/
  function setFilters() {
    $('.search__result-category').on('click', (e) => {
      if (searchInprogress) return;

      const button = $(e.target);
      button.toggleClass('active');

      if (button.hasClass('active')) {
        $('.search__result-category').not(e.target).removeClass('active');
        toggledCategory = button.data('category');
      } else {
        toggledCategory = '';
      }
      search(searchWord, toggledCategory);
    });
  }

  /**
   * Load more posts
   */
  function loadMorePosts() {
    if (searchInprogress) return;

    searchInprogress = true;
    page++;

    const buttonLabel = loadMoreButton.html();
    const loadingLabel = loadMoreButton.data('loading');

    loadMoreButton.html(loadingLabel);

    $.ajax({
      type: 'GET',
      // eslint-disable-next-line no-undef
      url: ajaxUrl,
      data: {
        action: 'frontend_search_posts',
        keyword: searchWord,
        cat_id: toggledCategory,
        page,
      },
      success: function (response) {
        response = JSON.parse(response);
        if (response.results && response.articles) {
          searchResultsContainer.html(searchResultsContainer.html() + response.articles);
          if (response.number_of_articles < 12) {
            loadMoreButton.hide();
          }
        } else {
          loadMoreButton.hide();
        }
        searchInprogress = false;
        loadMoreButton.html(buttonLabel);
      },
      error: function (e) {
        console.log(e);
      },
    });
  }

  /**
   * Reset default state on exit
   */
  function resetSearch() {
    searchWord = null;
    url.searchParams.delete('search');
    history.replaceState(null, '', url);
    searchCategoriesWrapper.hide();
    postsWrapper.hide();
    searchLoading.hide();
    searchAdContainer.hide();
    $('#before-search').show();
    $(searchField).val('');
    $('.search__text-input-wrapper').removeClass('active');
    noResultsText.hide();
  }

  function openModal() {
    $('body').addClass('no-scroll');
    searchField.focus();
    searchModal.addClass('open');

    $('.drawer-handheld-menu-wrapper').removeClass('open');
  }

  /**
   * Hide elements by default
   *  */
  searchCategoriesWrapper.hide();
  postsWrapper.hide();
  searchAdContainer.hide();

  if (searchParam) {
    openModal();
    $(searchField).val(searchParam);
    search(searchParam, null);
  }

  /**
   * Open modal
   */
  $('.search-opener-btn').on('click', openModal);
  $('.drawer-handheld-menu__search-wrapper').on('click', openModal);
  $('.button--search-opener').on('click', openModal);

  /**
   * Close search modal
   */
  $('#search-closer-btn').on('click', (e) => {
    setTimeout(() => {
      resetSearch();
    }, 600);

    searchModal.removeClass('open');
    // Remove body scroll lock
    $('body, html').removeClass('no-scroll');
  });

  /**
   * Trigger search on enter button
   */
  $(searchField).on('keyup', (e) => {
    if (e.keyCode === 13) {
      search($(searchField).val(), null);
    }
  });

  /**
   * Trigger search on click on search icon
   */
  $(searchButton).on('click', () => search($(searchField).val(), null));

  /**
   * Trigger loading of more posts on button click
   */
  loadMoreButton.on('click', loadMorePosts);
});
