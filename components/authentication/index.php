<?php

/**
 * Form to display
 * @var string
 */
$type = $args['type'];

/**
 * Config form
 * @var array
 */
$form_config = $args['config'];

/**
 * The temporary CRM user email
 * @var string
 */
$email = $args['email'];

/**
 * If the form should include the header
 * @var bool
 */
$header = $args['header'] === 'true';

/**
 * The temporary CRM user name
 * @var string
 * */
$username = isset($args['username']) && $args['username'] !== ' ' ? $args['username'] : null;

$headerUserName = $username ? explode(' ', $username)[0] : null;

/**
 * User avatar URL. Fallback is the FORBES logo.
 */
$user_avatar_url = get_template_directory_uri() . '/assets/images/forbes-initials-logo.svg';

if ($type === 'formEmailValidation') {
	if ($email) {
		$instructions .= $email;
	}
}
?>

<div class="auth-form<?= $type === 'email_validation' ? ' auth-form--email-validation ' : ''; ?>">

	<?php if ($header) : ?>

		<?php if (!empty($form_config['header'])) : ?>

			<?php get_template_part('components/authentication/header/index', null, ['name' => $headerUserName, 'type' => $form_config['header']['type'], 'avatar_url' => $user_avatar_url]); ?>

		<?php endif; ?>

		<?php if (!empty($form_config['title'])) : ?>

			<?php get_template_part('components/authentication/title/index', null, ['name' => $username ?: $email, 'text' => $form_config['title']]); ?>

		<?php endif; ?>

		<?php if (!empty($form_config['instructions'])) : ?>

			<p class="auth-form__instructions"><?= strip_tags($form_config['instructions']); ?></p>

		<?php endif; ?>

	<?php endif; ?>

	<?php if (!empty($form_config['action'])) : ?>

		<form id="auth-form" class="form" action="<?= $form_config['action'] ?>" novalidate <?= $type === 'formEmailValidation' && !empty($temp_email) ? 'data-user-email="' . $temp_email . '"' : null; ?>>

			<?php foreach ($form_config['fields'] as $field) : ?>

				<div class="form__field-wrapper">

					<?php
					get_template_part(
						'components/authentication/input-field/index',
						null,
						[
							'type' => $field['type'],
							'placeholder' => $field['placeholder'] ?? null,
							'name' => $field['name'],
							'pattern' => $field['pattern'] ?? null,
							'maxlength' => $field['maxlength'] ?? null,
							'value' => $field['value'] ?? null,
							'validation' => $field['validation'] ?? null,
							'showLabel' => !$header
						]
					);
					?>

				</div>

			<?php endforeach; ?>

			<?php if (!empty($form_config['submit_label'])) : ?>

				<?php if ($form_config['action'] !== 'password_reset_email_sent') : ?>

					<button type="submit" class="submit-button button button--primary button--medium">

						<div class="loading-overlay"></div>

						<span><?= $form_config['submit_label'] ?></span>

					</button>

				<?php endif; ?>

			<?php endif; ?>

		</form>

	<?php endif; ?>

	<?php if (!empty($form_config['footer'])) : ?>

		<?php
		$component_name = $form_config['footer']['type'];
		$args = $form_config['footer']['args'];

		get_template_part('components/authentication/' . $component_name . '/index', null, $args);
		?>

	<?php endif; ?>

</div>