<?php

/**
 * Redirect url
 * @var string
 */
$redirect_url = $args['redirectURL'] ?: urlencode('https://' . DOMAIN);

/**
 * Google sign in url
 * @var string
 */
$google_sign_in_url = defined('DN_REMP_CRM_HOST')
	? sprintf('%s/users/google/sign?url=%s', rtrim(DN_REMP_CRM_HOST, '/'), $redirect_url)
	: '';

$facebook_sign_in_url = defined('DN_REMP_CRM_HOST')
	? sprintf('%s/users/facebook/sign?url=%s', rtrim(DN_REMP_CRM_HOST, '/'), $redirect_url)
	: '';
?>

<div class="separator">
	<span class="separator__text"><?= esc_html__('OR', 'FORBES') ?></span>

	<div class="separator__line"></div>
</div>

<div class="sso-container">

	<?php if ($google_sign_in_url) : ?>

		<a href="<?= $google_sign_in_url; ?>" class="button button--secondary button--medium">
			<i class="sso-container__icon sso-container__icon--google"></i> <?= esc_html__('Continue with', 'FORBES') . ' Google' ?>
		</a>

	<?php endif; ?>

	<?php if ($facebook_sign_in_url) : ?>

		<a href="<?= $facebook_sign_in_url; ?>" class="button button--secondary button--medium">
			<i class="sso-container__icon sso-container__icon--facebook"></i> <?= esc_html__('Continue with', 'FORBES') . ' Facebook' ?>
		</a>

	<?php endif; ?>

	<div class="separator">
		<div class="separator__line"></div>
	</div>
</div>
