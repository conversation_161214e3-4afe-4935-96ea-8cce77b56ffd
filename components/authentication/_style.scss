.auth-form {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 44.5rem;
  text-align: center;

  &__title {
    margin-bottom: $spacing-04-16;
  }

  &__instructions {
    color: $color-text-secondary;
    font-size: 1.8rem;
    font-weight: 400;
    letter-spacing: 0.108px;
    line-height: 175%;
    margin-bottom: $spacing-04-16;
    text-align: center;
  }

  &__greeting-wrapper {
    margin-bottom: $spacing-06-24;

    &--no-name {
      @include flexbox-properties;

      .auth-form {
        &__logo {
          margin-bottom: $spacing-00;
        }
      }
    }
  }

  &__logo {
    display: inline;
    height: 3.2rem;
    width: 3.2rem;
    margin-bottom: -0.4rem;
    margin-right: $spacing-02;

    &.rounded {
      border-radius: 50%;
    }
  }

  &__greeting {
    display: inline;
  }

  .form {
    display: flex;
    flex-flow: column nowrap;

    &__field-wrapper {
      margin-bottom: $spacing-02;
    }

    .form-field {
      display: flex;
      flex-flow: column;

      .input-label {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-01;
        font-size: 1.6rem;
        color: $color-text-secondary;
        font-weight: 500;
      }

      &__reveal-button {
        align-items: center;
        background-color: transparent;
        display: none;
        padding-inline: $spacing-04-16;
        position: absolute;
        right: 0;
        bottom: 1.7rem;

        &.active {
          display: block;
        }
      }

      &__pwd-input-wrapper {
        display: flex;
        position: relative;

        .input-wrapper {
          width: 100%;
        }
      }

      &__error {
        display: none;
        text-align: left;
      }

      &__live-validation {
        margin: $spacing-02 $spacing-00 $spacing-00;
        text-align: left;

        & > p {
          font-size: 1.3rem;
          line-height: 2rem;
          margin-bottom: $spacing-01;
          font-family: $font-archivo;
        }

        ul {
          margin: $spacing-00;
        }

        li {
          align-items: center;
          color: $color-text-secondary;
          font-family: $font-noto-serif;
          display: flex;
          justify-content: flex-start;
          margin-bottom: $spacing-01;

          &:last-child {
            margin-bottom: $spacing-00;
          }

          &:before {
            content: url('assets/icons/icon-circle.svg');
            display: inline-block;
            height: 1.4rem;
            margin-right: $spacing-01;
            width: 1.4rem;
          }

          &.passed {
            color: $color-text-success;

            &:before {
              content: url('assets/icons/icon-circle-check.svg');
            }
          }
        }
      }

      &.error {
        .form-field__input,
        .form-field__input::placeholder {
          background-color: $color-surface-primary;
          border-color: $color-text-error;
          color: $color-text-error;
        }

        .form-field__error {
          display: block;
        }
      }
    }
  }

  .link {
    font-family: $font-archivo;
    font-size: 1.6rem;
    font-style: normal;
    font-weight: 600;
    line-height: 120%; /* 19.2px */
    text-decoration-line: underline;
    color: $color-link-default;
    transition: color 0.3s ease;
    cursor: pointer;

    @media (hover: hover) {
      &:hover {
        color: $color-link-hover;
      }
    }
  }

  .submit-button {
    position: relative;
    width: 100%;

    .loading-overlay {
      display: none;
    }

    &.loading {
      opacity: 0.75;
      pointer-events: none;

      &:after {
        background-color: rgba(var(--surface-secondary-rgb), $alpha: 0.5);
        content: ' ';
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
      }

      .loading-overlay {
        display: inline-block;
      }

      span {
        display: none;
      }
    }
  }

  .loading-overlay {
    display: inline-block;
    height: 1.6rem;
    width: 1.6rem;

    &::after {
      animation: double-ring 1.2s linear infinite;
      border: 0.2rem solid $color-surface-primary;
      border-color: $color-surface-primary transparent $color-surface-primary transparent;
      border-radius: 50%;
      content: ' ';
      display: block;
      height: 1.6rem;
      width: 1.6rem;
    }
  }

  @keyframes double-ring {
    0% {
      transform: scale(1.5) rotate(0deg);
    }
    100% {
      transform: scale(1.5) rotate(360deg);
    }
  }

  .links,
  .buttons {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: $spacing-02;
    justify-content: center;
    margin: $spacing-06-24 $spacing-00;
  }

  .buttons {
    &__button {
      font-family: $font-archivo;
      font-size: 1.6rem;
      font-style: normal;
      font-weight: 600;
      line-height: 120%; /* 19.2px */
      color: $color-text-brand;
      text-decoration: underline;
      transition: color 0.3s ease;

      &:disabled {
        opacity: 0.6;
        pointer-events: none;
      }
    }
  }

  .sso-container {
    display: flex;
    flex-flow: column nowrap;
    gap: $spacing-02;

    .separator {
      display: none;
    }

    .button {
      width: 100%;
    }

    &__icon {
      @include background-properties;
      margin-right: $spacing-01;
      height: 1.6rem;
      width: 1.6rem;

      &--google {
        background-image: url('assets/icons/icon-google.svg');
      }

      &--facebook {
        background-image: url('assets/icons/icon-facebook-blue.svg');
      }
    }
  }

  .separator {
    margin: $spacing-06-24 $spacing-00;
    position: relative;

    &__line {
      border-top: 0.1rem solid $color-divider;
      position: absolute;
      top: 49%;
      width: 100%;
      z-index: -1;
    }

    &__text {
      background: $color-surface-primary;
      color: $color-text-secondary;
      font-size: 1.6rem;
      font-style: italic;
      line-height: 2.3rem;
      padding: $spacing-00 $spacing-04-16;
    }
  }

  &--email-validation {
    .form {
      background-color: $color-divider;
      flex-direction: row;
      height: 7.2rem;
      justify-content: space-evenly;
      position: relative;

      &__field-wrapper {
        align-items: center;
        display: flex;
      }
    }

    .form-field {
      height: 3.6rem;
      width: 1.6rem;

      &.error {
        .form-field {
          &__input {
            background-color: $color-divider !important;
            border: none;
            color: initial !important;
          }

          &__error {
            left: 0;
            position: absolute;
            top: 90%;
            width: 100%;
          }
        }
      }

      &__inner {
        background-color: unset !important;
        border: none !important;
        height: 100%;
      }

      &__input {
        border: 0.1rem solid $color-text-primary !important;
        font-size: 2.4rem !important;
        height: 100%;
        padding: $spacing-00 !important;
        text-align: center;
        min-width: 2.5rem;

        &:not(:placeholder-shown) {
          border: none !important;
          background-color: transparent;
        }

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: $spacing-00;
        }

        &[type='number'] {
          -moz-appearance: textfield;
        }
      }
    }

    @media screen and (max-width: map-get($container-max-widths, lg)) {
      .sso-container {
        .separator {
          display: block;
        }
      }
    }

    @media screen and (max-width: map-get($container-max-widths, md)) {
      margin-bottom: $spacing-07-32;

      &--email-validation {
        .form {
          justify-content: space-around;
        }
      }
    }
  }
}
