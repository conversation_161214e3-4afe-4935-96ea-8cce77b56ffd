<?php

/**
 * Head<PERSON> type
 * @var string default | greeting
 */
$type = $args['type'];

/**
 * The users name
 * @var string
 */
$name = $args['name'] ?? null;

/**
 * User avatar URL.
 * @var string
 */
$avatar_url = $args['avatar_url'];
?>

<div class="auth-form__greeting-wrapper<?= !$name ? ' auth-form__greeting-wrapper--no-name' : '';?>">

	<?php if ($type === 'greeting' && $name) : ?>

		<img class="auth-form__logo rounded" src="<?= $avatar_url ?>" >
		<h3 class="auth-form__greeting"><?= esc_html__('Hey ', 'FORBES') . $name ?></h3>

	<?php else : ?>

		<img class="auth-form__logo" src="<?= get_template_directory_uri() . '/assets/images/forbes-initials-logo.svg' ?>">
		<h3><?= esc_html__('Account', 'FORBES'); ?></h3>

	<?php endif; ?>

</div>
