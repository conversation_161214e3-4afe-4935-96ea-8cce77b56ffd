document.addEventListener('DOMContentLoaded', () => {
  (function ($, ForbesNotifications, crmUser) {
    class Authentication {
      #includesHeader;
      #containerElement;

      #states;
      #currentState;

      #email;
      #userName;

      #passwordResetToken;

      #authForm;

      #redirectURL;

      /**
       * Initializes a new instance
       *
       * @param {string} containerID - The ID of the container element.
       * @param {boolean} includesHeader - Whether the form should include the header
       */
      constructor(containerID, includesHeader = true, redirectURL = null) {
        this.#includesHeader = includesHeader;
        this.#redirectURL = redirectURL;

        this.#containerElement = document.getElementById(containerID);

        this.#states = {
          emailCheck: 'email_check',
          login: 'login',
          registration: 'registration',
          emailValidation: 'email_validation',
          passwordResetBeforeEmail: 'password_reset_before_email',
          passwordResetAfterEmail: 'password_reset_after_email',
          passwordReset: 'password_reset',
        };

        this.#currentState = this.#states.emailCheck;

        this.#email = crmUser?.user?.email ?? null;
        this.#userName =
          crmUser?.user?.first_name && crmUser?.user?.last_name
            ? `${crmUser.user.first_name} ${crmUser.user.last_name}`
            : null;

        const queryString = window.location.search;
        const urlParams = new URLSearchParams(queryString);
        const stage = urlParams.get('stage');
        this.#passwordResetToken = urlParams.get('token');

        if (
          stage === this.#states.registration ||
          stage === this.#states.passwordResetBeforeEmail ||
          (stage === this.#states.passwordReset && this.#passwordResetToken)
        ) {
          this.#currentState = stage;
        }
      }

      /**
       * Initializes the function by performing the necessary setup and event listeners.
       */
      init() {
        this.transition(this.#currentState, true);

        document.addEventListener('authTransitionEnded', (e) => {
          this.resizeUsernameFontSize();
          this.addPasswordRevealFunctionality();
          this.setupFormFooterButtons();

          if (this.#currentState === this.#states.registration || this.#currentState === this.#states.passwordReset) {
            this.initLiveValidation();
          }

          if (this.#currentState === this.#states.emailValidation) {
            this.setupEmailValidationInputs();
            this.setupResendButton();
          }
        });
      }

      /**
       * Transitions to a new state in the authentication flow.
       *
       * @param {string} destination - The destination to transition to.
       * @param {boolean} [isFirstLoad=false] - Indicates whether this is the first page load.
       */
      transition(destination, isFirstLoad = false) {
        document.dispatchEvent(
          new CustomEvent('authTransitionStart', {
            bubbles: true,
            detail: {
              type: destination,
              isFirstLoad,
            },
          })
        );

        if (destination === this.#states.emailCheck) {
          this.reset();
        }

        $.ajax({
          url: ajaxUrl,
          type: 'POST',
          data: {
            action: 'get_form',
            type: destination,
            email: this.#email,
            userName: this.#userName,
            includesHeader: this.#includesHeader,
            redirectURL: this.#redirectURL,
          },
          dataType: 'json',
          success: (response) => {
            if (!isFirstLoad) {
              $(this.#containerElement).fadeOut('slow', () => {
                this.#containerElement.innerHTML = response.template;

                handleSuccess();
                this.clearURLParams();

                $(this.#containerElement).fadeIn('slow');
              });

              this.#currentState = destination;
            } else {
              this.#containerElement.innerHTML = response.template;

              handleSuccess();
            }
          },
          error: function (xhr) {
            console.error(xhr);
          },
        });

        const handleSuccess = () => {
          this.#authForm = document.getElementById('auth-form');

          this.#authForm && this.#authForm.addEventListener('submit', (event) => this.handleFormSubmission(event));

          document.dispatchEvent(
            new CustomEvent('authTransitionEnded', {
              bubbles: true,
            })
          );
        };
      }

      /**
       * Handles the submission of the authentication form.
       *
       * @param {Event} e - The form submission event.
       */
      handleFormSubmission(e) {
        e.preventDefault();

        const formData = new FormData(this.#authForm);

        const submitButton = this.#authForm.querySelector('.submit-button');

        submitButton && this.setSubmitButtonLoadingState(submitButton);

        if (['check_email', 'register_crm_user'].includes(this.#authForm.getAttribute('action'))) {
          this.#email = formData.get('email');
        } else if (!formData.has('email')) {
          formData.append('email', this.#email);
        }

        if (this.#passwordResetToken) {
          formData.append('token', this.#passwordResetToken);
        }

        const isValid = this.validateForm(formData, this.#authForm.getAttribute('action') !== 'login_crm_user');

        if (!isValid) {
          submitButton && this.setSubmitButtonLoadingState(submitButton);
          return;
        }

        formData.append('action', this.#authForm.getAttribute('action'));
        formData.set('password', formData.get('password') || formData.get('new_password') || '');

        $.ajax({
          // eslint-disable-next-line no-undef
          url: ajaxUrl,
          type: 'POST',
          data: formData,
          processData: false,
          contentType: false,
          cache: false,
          dataType: 'json',
          success: (response) => this.formSuccessHandler(response),
          error: (error) => this.formErrorHandler(error),
          complete: (jqXHR, textStatus) => {
            setTimeout(
              () => {
                submitButton && this.setSubmitButtonLoadingState(submitButton);
              },
              textStatus === 'success' ? 1500 : 0
            );
          },
        });
      }

      /**
       * Handles the success response of the form submission.
       *
       * @param {Object} response - The response object from the API.
       */
      formSuccessHandler(response) {
        const { code, user, message } = response;

        switch (code) {
          case 'user_found': {
            if (user && user.name) {
              this.#userName = user.name;
            }

            this.transition(this.#states.login);

            break;
          }

          case 'email_validated': {
            if (message) {
              ForbesNotifications.showNotifications([message]);
            }

            setTimeout(
              () => {
                this.transition(this.#states.login);
              },
              response.message ? 1500 : 0
            );
            break;
          }

          case 'password_reset_request_sent': {
            this.transition(this.#states.passwordResetAfterEmail);

            break;
          }

          case 'password_changed': {
            if (response.message) {
              ForbesNotifications.showNotifications([response.message]);
            }

            setTimeout(
              () => {
                this.transition(this.#states.login);
              },
              response.message ? 1500 : 0
            );
            break;
          }

          case 'user_logged_in': {
            if (message) {
              ForbesNotifications.showNotifications([message]);
            }

            setTimeout(
              () => {
                window.location.href = this.#redirectURL || vars.myAccountPageLink;
              },
              message ? 1500 : 0
            );
            break;
          }

          case 'user_registered': {
            this.transition(this.#states.emailValidation);
            break;
          }

          default: {
            submitButton && this.setSubmitButtonLoadingState(submitButton);

            console.error('Bad email check response!');
          }
        }
      }

      /**
       * Process and handle errors from the form.
       *
       * @param {Object} error - The error object.
       */
      formErrorHandler(error) {
        const { code, message = null, email = null } = error?.responseJSON?.data;

        if (code) {
          switch (code) {
            case 'user_not_found': {
              this.transition(this.#states.registration);
              break;
            }

            case 'code_expired':
            case 'code_doesnt_match': {
              this.raiseError({
                name: 'evdigit1',
                message,
              });

              setTimeout(() => {
                this.#authForm.reset();
                this.#authForm.querySelectorAll('input')[0].focus();
              }, 1000);
              break;
            }

            case 'email_not_confirmed': {
              this.transition(this.#states.emailValidation);
              break;
            }

            case 'email_taken': {
              this.#authForm.reset();
              this.raiseError({ name: 'email', message });
              break;
            }

            case 'invalid_email': {
              this.raiseError({ name: 'email', message });
              break;
            }

            case 'invalid_password': {
              this.raiseError({
                name: 'password',
                message,
              });
              break;
            }

            case 'invalid_credential': {
              this.raiseError({
                name: 'password',
                message,
              });
              break;
            }

            case 'auth_failed':
            case 'password_reset_request_failed':
            case 'password_reset_confirm_failed': {
              console.error(message);

              this.transition(this.#states.emailCheck);

              break;
            }

            case 'timeout': {
              ForbesNotifications.showNotifications([message]);

              setTimeout(() => {
                window.location.reload();
              }, 1500);

              break;
            }

            default: {
              console.error(error);

              if (message) {
                ForbesNotifications.showNotifications([message]);
              }

              setTimeout(
                () => {
                  window.location.reload();
                },
                message ? 0 : 1500
              );
            }
          }
        } else {
          console.error('Bad email check response!');
        }
      }

      /**
       * Sets the loading state of the form submit button.
       *
       * @param {HTMLElement} button - The submit button element.
       */
      setSubmitButtonLoadingState(button) {
        button.disabled = !button.disabled;
        button.classList.toggle('loading');
      }

      /**
       * Validates the form data.
       *
       * @param {Object} data - The form data to be validated.
       * @param {boolean} [shouldValidatePwd=true] - Whether to validate the password field or not. Defaults to true.
       * @return {boolean} Returns true if the form data is valid, otherwise false.
       */
      validateForm(data, shouldValidatePwd = true) {
        const wrapperElements = this.#authForm.querySelectorAll('.form-field');

        wrapperElements.forEach((wrapper) => {
          wrapper.classList.remove('error');
        });

        const errors = [];

        for (const [key, value] of data) {
          if (key === 'email' && !this.validateEmail(value)) {
            errors.push({
              name: 'email',
              message: vars.translations.emailError,
            });
          }

          if (
            shouldValidatePwd &&
            key === 'password' &&
            !this.validateNewPassword(value).every((error) => error.passed)
          ) {
            errors.push({
              name: 'password',
              message: vars.translations.passwordError,
            });
          }
        }

        errors.forEach((error) => {
          this.raiseError(error);
        });

        return errors.length === 0;
      }

      /**
       * Validates an email address.
       *
       * @param {string} email - The email address to be validated.
       * @return {boolean} Returns true if the email is valid, otherwise false.
       */
      validateEmail(email) {
        return email.match(/^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-.]+$/);
      }

      /**
       * Validates a new password by checking if it meets the following criteria:
       * 1. At least one lowercase letter
       * 2. At least one uppercase letter
       * 3. At least one number or special character
       * 4. At least 8 characters
       *
       * @param {string} newPassword - The new password to be validated.
       * @return {Array} An array of objects representing the validation results.
       * Each object has an 'id' property indicating the specific validation rule,
       * and a 'passed' property indicating whether the validation rule passed or not.
       */
      validateNewPassword(newPassword) {
        const errorList = [
          // 1. At least one lowercase letter
          {
            id: 'requiredLowerCase',
            passed: !!newPassword.match(/(?=.*[a-z])/),
          },
          // 2. At least one uppercase letter
          {
            id: 'requiredUpperCase',
            passed: !!newPassword.match(/(?=.*[A-Z])/),
          },
          // 3. At least one number or special character
          {
            id: 'requiredNumberOrSpecialCharacter',
            passed: !!newPassword.match(/(?=.*\d)|(?=.*[\W_])/),
          },
          // 4. At least 8 characters
          {
            id: 'requiredLength',
            passed: !!newPassword.match(/.{8,}/),
          },
        ];

        return errorList;
      }

      /**
       * Raises an error and updates the form UI accordingly.
       *
       * @param {Error} error - The error object containing the name and message of the error.
       */
      raiseError(error) {
        const inputElementWrapper = this.#authForm.querySelector(`input[name=${error.name}]`).closest('.form-field');

        if (inputElementWrapper) {
          inputElementWrapper.classList.add('error');

          const errorElement = inputElementWrapper.querySelector('.form-field__error');
          errorElement.textContent = error.message;
        }
      }

      /**
       * Resizes the font size of the username element to fit within its parent element.
       */
      resizeUsernameFontSize() {
        const username = document.querySelector('.auth-form__username');

        if (!username) return;

        requestAnimationFrame(() => {
          let size = parseInt(getComputedStyle(username).fontSize);

          const parentWidth = parseInt(getComputedStyle(username.parentElement).width);

          while (username.offsetWidth > parentWidth) {
            username.style.fontSize = size + 'px';
            size -= 1;
          }
        });
      }

      /**
       * Initializes the live validation feature for the authentication form.
       * This function adds an event listener to the password input field,
       * which triggers the validation process whenever the input value changes.
       * The validation results are then used to update the visual indicators
       * for each requirement in the validation container.
       *
       * @param {type} paramName - description of parameter
       * @return {type} description of return value
       */
      initLiveValidation() {
        const validationContainer = this.#authForm.querySelector('.form-field__live-validation');

        const passwordInput = this.#authForm.querySelector('input[type="password"]');

        passwordInput.addEventListener('input', (e) => {
          const requirementList = this.validateNewPassword(passwordInput.value);

          requirementList.forEach((requirement) => {
            const req = validationContainer.querySelector(`#error-${requirement.id}`);

            if (requirement.passed) {
              req.classList.add('passed');
            } else {
              req.classList.remove('passed');
            }
          });
        });
      }

      /**
       * Sets up email validation inputs.
       */
      setupEmailValidationInputs() {
        const inputs = Array.from(this.#authForm.querySelectorAll('.form-field__input'));

        if (!inputs.length) return;

        inputs[0].focus();

        // Iterate over each input element
        inputs.forEach((input, index) => {
          // Attach an 'input' event listener to each input element
          input.addEventListener('input', (event) => {
            const value = input.value;

            // Restrict the input value to only one character
            if (value.length > 1) {
              input.value = value.slice(1, 2);
            }

            // Get the next input element
            const nextInput = inputs[index + 1];

            // If the backspace key is pressed or the value is removed
            if (event.inputType === 'deleteContentBackward' || value === '') {
              // Get the previous input element
              const prevInput = inputs[index - 1];

              // If there is a previous input element, focus on it
              if (prevInput && !prevInput !== inputs[0]) {
                prevInput.focus();
              }
            }
            // If there is a next input element, focus on it
            else if (nextInput) {
              nextInput.focus();
            }
            // If there is no next input element and the form is valid, submit the form
            else if (this.#authForm.checkValidity()) {
              let event = new Event('submit', {
                cancelable: true,
              });
              this.#authForm.dispatchEvent(event);
              if (!event.defaultPrevented) {
                this.#authForm.submit();
              }
            }
            // If there is no next input element and the form is invalid, focus on the first invalid input element
            else {
              this.#authForm.querySelector(':invalid').focus();
            }
          });
        });
      }

      /**
       * Sets up the resend button functionality.
       *
       * This function initializes the resend button by adding a click event listener to it. When the button is clicked,
       * it disables the button, makes an AJAX POST request to the server to resend the verification code,
       * and shows a success message. After 30 seconds, the button is re-enabled.
       */
      setupResendButton() {
        const resendButton = document.getElementById('resend-email');

        if (!resendButton) return;

        resendButton.addEventListener('click', () => {
          resendButton.disabled = true;
          $.ajax({
            // eslint-disable-next-line no-undef
            url: ajaxUrl,
            type: 'POST',
            dataType: 'json',
            xhrFields: {
              withCredentials: true,
            },
            data: {
              action: 'send_verification_code',
              resend: true,
              email: this.#email,
            },
            success: function (response) {
              ForbesNotifications.showNotifications([response.message]);

              setTimeout(() => {
                resendButton.disabled = false;
              }, 30000);
            },
          });
        });
      }

      /**
       * Adds password reveal functionality to the form field.
       */
      addPasswordRevealFunctionality() {
        const passwordInput = this.#containerElement.querySelector('.form-field__input[type="password"]');

        const revealButton = this.#containerElement.querySelector(`button[data-for-field-id="${passwordInput?.id}"]`);

        if (!revealButton) return;

        passwordInput.addEventListener('focusin', () => {
          revealButton.classList.add('active');
        });

        passwordInput.addEventListener('focusout', () => {
          revealButton.classList.remove('active');
        });

        const icon = revealButton.querySelector('.icon');

        revealButton.addEventListener('mousedown', (e) => {
          e.preventDefault();
          passwordInput.focus();

          const newType = passwordInput.type === 'password' ? 'text' : 'password';

          passwordInput.type = newType;

          icon && icon.classList.toggle('icon--eye');
          icon && icon.classList.toggle('icon--eye-off');
        });
      }

      /**
       * Sets up the footer buttons.
       */
      setupFormFooterButtons() {
        const footerButtons = this.#containerElement.querySelectorAll('.links__link');

        footerButtons.forEach((button) => {
          button.addEventListener('click', () => {
            const destination = button.dataset.destination;
            if (destination) {
              this.transition(destination);
            }
          });
        });
      }

      /**
       * Resets the email, userName, and currentState properties of the object.
       */
      reset() {
        this.#email = null;
        this.#userName = null;
        this.#currentState = this.#states.emailCheck;
      }

      /**
       * Clears the URL parameters of the current page.
       */
      /**
       * Clears the URL parameters of the current page.
       */
      clearURLParams() {
        const url = window.location.href;

        const parsedUrl = new URL(url);

        const protocol = parsedUrl.protocol;
        const hostname = parsedUrl.hostname;
        const pathname = parsedUrl.pathname;
        const searchParams = parsedUrl.searchParams;

        // Preserve the query string with the key 'code'
        const updatedSearchParams = new URLSearchParams();
        searchParams.forEach((value, key) => {
          if (key === 'code') {
            updatedSearchParams.append(key, value);
          }
        });

        const updatedUrl = `${protocol}//${hostname}${pathname}?${updatedSearchParams}`;

        window.history.replaceState({}, '', updatedUrl);
      }
    }

    window.Authentication = Authentication;
  })(jQuery, window.ForbesNotifications, window.crm_user);
});
