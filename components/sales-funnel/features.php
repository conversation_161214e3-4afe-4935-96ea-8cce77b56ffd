<div class="section__wrapper">
    <?php
    // Check if the repeater field has rows of data
    if (have_rows('list_of_features', 'option')):

        // Loop through the rows of data
        while (have_rows('list_of_features', 'option')) : the_row();

            // Load sub field values
            $feature_code = get_sub_field('feature_code');
            $feature_name = get_sub_field('feature_name');
            $feature_description = get_sub_field('feature_description');
            $feature_icon = get_sub_field('feature_icon');
            $button_label = get_sub_field('button_label');
            $button_url = get_sub_field('button_url');

            // Instantiate the BenefitCard class and render it
            $benefitCard = new BenefitCard(
                id: $feature_code,
                subtitle: $feature_name,
                description: $feature_description,
                icon: $feature_icon,
                linkHref: $button_url,
                linkLabel: $button_label
            );
            echo $benefitCard->render();

        endwhile;

    else :

        // No rows found

    endif;
    ?>
</div>