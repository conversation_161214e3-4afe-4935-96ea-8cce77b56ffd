.follow-suggestion-box {
  border-bottom: 0.1rem solid $color-divider;
  padding-bottom: $spacing-06-24;

  &.hidden {
    display: none;
  }

  &__text {
    color: $color-text-secondary;
    margin-bottom: $spacing-04-16;
  }

  &__button-container {
    display: flex;
    gap: $spacing-02;
  }

  &__button {
    &--follow-all {
      display: flex;

      &:before {
        content: '';
        @include mask-properties;
        mask-image: url('assets/icons/icon-plus.svg');
        -webkit-mask-image: url('assets/icons/icon-plus.svg');
        mask-position: left 8% center;
        -webkit-mask-position: left 8% center;
        width: 1.6rem;
        height: 1.6rem;
        background-color: $color-button-secondary;
        margin-right: $spacing-01;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &__button-container {
      flex-direction: column;
    }

    &__button {
      width: 100%;
    }
  }
}
