.order-details-modal {
  @include modal;
  position: relative;

  &__title {
    margin-bottom: $spacing-01;
  }

  &__status,
  &__label {
    color: $color-text-secondary !important;
    font-weight: 500;
    line-height: 1.92rem;
  }

  &__label {
    margin-bottom: $spacing-01;
  }

  &__retry-button {
    white-space: nowrap;
  }

  &__data {
    font-weight: 500;
  }

  &__single-data {
    display: flex;
    flex-direction: column;
  }

  &__details {
    display: flex;
    flex-direction: column;
    gap: $spacing-04-16;
  }

  &__items-wrapper {
    margin: $spacing-06-24 -4rem $spacing-06-24;
    padding: $spacing-06-24;
    background-color: $color-surface-secondary;

    .order-details-modal__details {
      gap: $spacing-00;
    }

    .order-details-modal__label-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  &__item {
    margin: $spacing-02 $spacing-00;

    .order-details-modal__data {
      max-width: 50%;
    }

    &:last-child {
      margin-bottom: $spacing-00;
    }

    &:first-child {
      margin-top: $spacing-02;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: $spacing-04-16;
  }

  &__items,
  &__prices {
    display: flex;
    flex-direction: column;
  }

  &__prices,
  .prices-label {
    display: flex;
    justify-content: flex-end;
    text-align: right;
  }

  &__footer,
  &__codes-wrapper {
    border-top: 0.1rem solid $color-divider;
  }

  &__footer {
    padding: $spacing-06-24 $spacing-08-40 !important;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  &__invoice-wrapper,
  &__no-invoice-wrapper {
    width: 100%;
  }

  &__invoice-wrapper {
    display: flex;
    align-items: center;
    gap: $spacing-05-20;
  }

  &__codes-wrapper,
  &__invoice-wrapper,
  &__no-invoice-wrapper,
  &__footer {
    &.hidden {
      display: none;
    }
  }

  &__codes-wrapper {
    margin-top: $spacing-00;
    width: 100%;
    padding-bottom: $spacing-06-24;
  }

  &__codes {
    padding-top: $spacing-06-24;
  }

  &__invoice,
  &__codes {
    margin-left: $spacing-06-24;
    position: relative;

    &:before {
      content: '';
      @include mask-properties;
      mask-image: url('assets/icons/icon-invoice.svg');
      -webkit-mask-image: url('assets/icons/icon-invoice.svg');
      width: 1.6rem;
      height: 1.6rem;
      background-color: $color-text-primary;
      position: absolute;
      left: -2.4rem;
    }
  }

  &__invoice {
    transition: all 0.3s ease !important;

    @media (hover: hover) {
      &:hover {
        border-color: $color-text-brand;

        &:before {
          background-color: $color-text-brand;
        }
      }
    }
  }

  &__invoice-number {
    margin-left: $spacing-01;
  }

  &__no-invoice-wrapper {
    p,
    a {
      font-size: 1.6rem;
      font-weight: 500;
      color: $color-text-primary;
      font-family: $font-archivo;
      line-height: 1.92rem;
      transition: all 0.3s ease;
    }

    p {
      margin-bottom: $spacing-02;
    }
  }

  &__no-invoice-description {
    margin-top: $spacing-01;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__items-wrapper,
    &__address-details {
      flex-wrap: nowrap;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__title {
      font-size: 3.2rem;
    }

    &__header {
      padding-bottom: $spacing-08-40 !important;
    }

    &__items-wrapper,
    &__address-details {
      flex-wrap: wrap;
    }

    &__prices-wrapper {
      display: none;
    }

    &__footer {
      padding: $spacing-06-24 $spacing-04-16 !important;
    }

    &__no-invoice-wrapper {
      p,
      a {
        line-height: 1.9rem;
      }

      p {
        margin-bottom: $spacing-02;
      }
    }
  }
}
