.find-more-card {
  display: flex;
  align-items: center;
  gap: $spacing-02;
  cursor: pointer;

  @media (hover: hover) {
    &:hover {
      .find-more-card__title {
        color: $color-link-hover;
      }
    }
  }

  &__icon-wrapper {
    @include flexbox-properties;
    background-color: $color-surface-secondary;
    border-radius: 50%;
    height: 4.8rem;
    width: 4.8rem;
    min-height: 4.8rem;
    min-width: 4.8rem;
  }

  &__icon {
    background-color: $color-other-emerald-foreground;
    @include mask-properties;
    mask-image: url('assets/icons/icon-plus.svg');
    -webkit-mask-image: url('assets/icons/icon-plus.svg');
    height: 2.4rem;
    width: 2.4rem;
  }
}
