<?php

/**
 * Class for the Account Dropdown component.
 *
 * This component displays a dropdown menu for account navigation.
 * It retrieves menu items associated with the my account and renders them.
 *
 * @package Components\AccountDropdown
 */

class AccountDropdown {

    /**
     * Renders the HTML for the account navigation.
     *
     * @return void
     */
    public function render(): void {
        ?>
        <div class="account-dropdown">
            <div class="account-dropdown__header">
                <?php get_template_part('components/navigation/top-row/index', null, ['is_dropdown' => true]); ?>
            </div>
            <div class="account-dropdown__body" id="reactAccountDropdownBody">

            </div>
        </div>
        <?php
    }
}
