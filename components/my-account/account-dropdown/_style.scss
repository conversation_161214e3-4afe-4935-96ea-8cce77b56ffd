.navigation {
  .account-dropdown {
    width: 30rem;
    max-height: calc(100vh - 10rem);
    background-color: $color-surface-primary;
    padding: $spacing-06-24 $spacing-06-24 $spacing-00;
    box-shadow: $box-shadow-level-2;
    position: absolute;
    top: -99999rem;
    right: 2.4rem;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
    overflow-y: scroll;

    &::-webkit-scrollbar {
      display: none;
    }

    &.active {
      top: 5rem;
    }

    &.visible {
      opacity: 1;
    }

    &__header {
      display: none;
    }

    &__menu {
      border-bottom: 0.1rem solid $color-divider;
      margin-bottom: $spacing-05-20;

      &--icon {
        .account-dropdown__menu-item {
          &:before {
            content: '';
            @include mask-properties;
            width: 1.6rem;
            height: 1.6rem;
            margin-right: $spacing-01;
            background-color: $color-text-primary;
            margin-top: -0.2rem;
          }

          &.icon-bookmark:before {
            mask-image: url('assets/icons/icon-myacc-bookmark-active.svg');
            -webkit-mask-image: url('assets/icons/icon-myacc-bookmark-active.svg');
          }

          &.icon-shortcut:before {
            mask-image: url('assets/icons/icon-myacc-shortcut-active.svg');
            -webkit-mask-image: url('assets/icons/icon-myacc-shortcut-active.svg');
          }

          &.icon-mail:before {
            mask-image: url('assets/icons/icon-myacc-mail-active.svg');
            -webkit-mask-image: url('assets/icons/icon-myacc-mail-active.svg');
          }

          &.icon-diamond:before {
            mask-image: url('assets/icons/icon-myacc-diamond-active.svg');
            -webkit-mask-image: url('assets/icons/icon-myacc-diamond-active.svg');
          }
        }
      }
    }

    &__user-button {
      cursor: pointer;
      width: 2.4rem;
      height: 2.4rem;
      transition: all 0.3s ease;

      &.active {
        border-color: $color-text-brand;
      }

      #menu-user-avatar {
        border-radius: 50%;
      }

      @media (hover: hover) {
        &:hover {
          background-color: $color-text-brand;
        }
      }

      &--logged-in {
        mask-image: none;
        -webkit-mask-image: none;
        border-radius: 50%;
        color: $color-text-primary;
        width: auto;
        font-family: $font-archivo !important;
        font-size: 1rem;
        line-height: 1.1rem;
        height: 2rem;
        min-width: 2rem;
        max-width: 2rem;
        padding: $spacing-00;
        @include mask-properties;
        text-decoration: none;
      }
    }

    &__menu-link,
    &__logout-link {
      color: $color-text-primary;
      width: 100%;
      font-variant-numeric: lining-nums tabular-nums;
      line-height: 3.2rem;
      transition: color 0.3s ease;
    }

    &__logout-link {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .account-dropdown__menu-icon {
        mask-image: url('assets/icons/icon-logout.svg');
        -webkit-mask-image: url('assets/icons/icon-logout.svg');
        margin-right: $spacing-01;
      }
    }

    &__menu-icon {
      @include mask-properties;
      background-color: $color-text-primary;
      height: 1.6rem;
      width: 1.6rem;
      background-repeat: no-repeat;
      margin-right: $spacing-02;
    }

    &__menu-item {
      margin-bottom: $spacing-05-20;
      display: flex;
      align-items: center;

      &--logout {
        margin-bottom: $spacing-06-24;
      }

      &--active {
        .account-dropdown {
          &__menu-link,
          &__logout-link {
            text-decoration: underline;
          }
        }
      }

      @media (hover: hover) {
        &:hover {
          .account-dropdown {
            &__menu-link,
            &__logout-link {
              text-decoration: underline;
            }
          }
        }
      }
    }

    &__subscription-wrapper {
      padding: $spacing-04-16;
      margin-bottom: $spacing-06-24;

      &--upgrade {
        background-color: $color-surface-secondary;
      }

      &--premium {
        background-color: $color-surface-program-premium;
      }
    }

    &__subscription-label {
      margin-bottom: $spacing-01;
      font-weight: 600;
      font-size: 1.4rem;
      color: $color-text-primary;
      font-family: $font-archivo !important;
      line-height: 140%;
    }

    &__subscription-description {
      color: $color-text-secondary;
      font-size: 1.4rem;
      font-weight: 500;
      font-family: $font-archivo !important;
      line-height: 140%;
    }

    &__button {
      width: 100%;
      height: 4rem;
      padding: $spacing-02;
      font-size: 1.4rem;
      font-weight: 600;
      background-color: transparent;

      &--link {
        display: block;
        margin-top: $spacing-03-12;
        color: $color-text-primary !important;
        text-decoration: none;
      }

      @media (hover: hover) {
        &:hover {
          box-shadow: $box-shadow-level-3;
        }
      }

      &.button--icon-before {
        display: flex;
        align-items: center;

        &:before {
          content: '';
          @include mask-properties;
          width: 1.6rem;
          height: 1.6rem;
          margin-right: $spacing-01;
          background-color: $color-text-primary;
        }
      }

      &--upgrade {
        &:before {
          mask-image: url('assets/icons/icon-diamond.svg');
          -webkit-mask-image: url('assets/icons/icon-diamond.svg');
        }
      }

      &--extend {
        &:before {
          mask-image: url('assets/icons/icon-plus.svg');
          -webkit-mask-image: url('assets/icons/icon-plus.svg');
        }
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md)) {
  .navigation {
    &.admin-bar {
      .account-dropdown.active {
        top: 4.6rem;
        max-height: calc(100vh - 4.6rem);
      }
    }

    .account-dropdown {
      width: 100vw;
      max-height: 100vh;
      position: fixed;
      padding: $spacing-00;

      &.active {
        top: 0;
        left: 0;
      }

      &.visible {
        opacity: 1;
      }

      &__header {
        display: flex;
        justify-content: space-between;
        border-bottom: 0.1rem solid $color-divider;

        .navigation {
          &__top-row {
            padding-right: 15px !important;
            padding-left: 15px !important;
            width: 100%;
            height: 6.4rem;
          }
        }
      }

      &__body {
        padding: $spacing-07-32 $spacing-07-32 $spacing-00;
      }

      &__header-wrapper {
        display: flex;
      }

      &__icons {
        display: flex;
        margin-left: $spacing-04-16;
      }

      &__subscription-button {
        margin-right: $spacing-05-20;
      }

      &__user-button {
        margin-right: $spacing-06-24;
      }
    }
  }
}
