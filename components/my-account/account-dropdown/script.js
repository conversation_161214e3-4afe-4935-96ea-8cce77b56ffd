document.addEventListener('DOMContentLoaded', function () {
  jQuery(document).ready(function ($) {
    class AccountDropdown {
      constructor() {
        this.accountDropdown = document.querySelector('.account-dropdown');
        this.userButton = document.querySelector('.navigation__user-button');
        this.dropdownUserButton = document.querySelector('.navigation__user-button--closer');
        this.logoutButton = document.getElementById('logout');
        this.userObject = window.crm_user ?? null;
      }

      init() {
        if (!this.accountDropdown) return;

        this.initEvents();
      }

      initEvents() {
        const self = this;

        if (this.userButton) {
          this.userButton.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            self.toggleDropdown();
          });
        }

        if (this.dropdownUserButton) {
          this.dropdownUserButton.addEventListener('click', function (e) {
            self.closeDropdown();
          });
        }

        if (this.accountDropdown) {
          this.accountDropdown.addEventListener('mouseleave', function () {
            // Note: Removed temporarily since the dropdown logic changed
            // self.closeDropdown();
          });
        }

        document.addEventListener(
          'click',
          function () {
            self.closeDropdown();
          },
          false
        );

        const dropdownElementClassName = '.account-dropdown__menu-link';

        document.querySelectorAll(dropdownElementClassName).forEach((link) => {
          link.addEventListener('click', () => {
            self.closeDropdown();
          });
        });
      }

      toggleDropdown() {
        if (this.accountDropdown !== null) {
          this.accountDropdown.classList.toggle('active');
          this.accountDropdown.classList.toggle('visible');
        }
        if (this.userButton !== null) {
          this.userButton.classList.toggle('active');
        }
        if (this.dropdownUserButton !== null) {
          this.dropdownUserButton.classList.toggle('active');
        }
        if (window.innerWidth < 767) {
          document.documentElement.classList.toggle('no-scroll');
        }

        const elementHeight = this.accountDropdown ? this.accountDropdown.clientHeight : 0; // Height of the element

        // If element height is greater than or equal to window height
        if (elementHeight >= window.innerHeight) {
          this.accountDropdown.style.paddingBottom = '50px'; // Add padding bottom
        } else {
          this.accountDropdown.style.paddingBottom = '0'; // Reset padding
        }
      }

      closeDropdown() {
        if (this.accountDropdown !== null) {
          this.accountDropdown.classList.remove('active');
          this.accountDropdown.classList.remove('visible');
        }
        if (this.userButton !== null) {
          this.userButton.classList.remove('active');
        }
        if (this.dropdownUserButton !== null) {
          this.dropdownUserButton.classList.remove('active');
        }
        if (window.innerWidth < 767) {
          document.documentElement.classList.remove('no-scroll');
        }
      }
    }

    new AccountDropdown().init();
  });
});
