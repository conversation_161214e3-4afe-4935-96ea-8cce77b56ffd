/* My Account Styling */
.author-and-topic-card {
  cursor: pointer;
  display: flex;
  transition: all 0.3s ease;

  &.disabled {
    pointer-events: none;
    opacity: 0.6;
    cursor: not-allowed;
  }

  &--authors {
    .author-and-topic-card {
      &__name-wrapper {
        min-height: 6.4rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      &__avatar-wrapper {
        border-radius: 50%;
      }

      &__name {
        font-weight: 400;
        display: flex;
        flex-direction: column;

        &--last {
          font-weight: 600;
        }
      }
    }
  }

  @media (hover: hover) {
    &:hover {
      color: $color-text-brand;

      .author-and-topic-card {
        &__avatar {
          transform: scale(1.1);
        }

        &__name {
          color: $color-text-brand;
        }
      }
    }
  }

  &__avatar-wrapper {
    aspect-ratio: 1;
    height: 6.4rem;
    margin-right: $spacing-02;
    max-width: 6.4rem;
    min-width: 6.4rem;
    overflow: hidden;
  }

  &__avatar {
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
    width: 100%;
  }

  &__content {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
  }

  &__name {
    font-weight: 600;
    margin-bottom: $spacing-02;
    transition: all 0.3s ease;
  }

  &__label {
    color: $color-text-secondary !important;
    margin-bottom: $spacing-02;
  }

  &__button {
    cursor: pointer;
    font-size: 1.6rem;
    font-weight: 600;
    line-height: 2rem;
    padding: $spacing-00;
    text-decoration: underline;
    transition: color 0.3s ease;

    @media (hover: hover) {
      &:hover {
        color: $color-text-brand;
      }
    }
  }

  &--search-result {
    .author-and-topic-card {
      &__name-wrapper {
        flex-direction: row;
        min-height: 0;
        gap: $spacing-02;
        margin-bottom: $spacing-02;
      }

      &__label {
        margin-bottom: $spacing-03-12;
      }

      &__button {
        align-items: center;
        display: flex;

        &:before {
          content: '';
          @include mask-properties;
          background-color: $color-text-primary;
          height: 1.6rem;
          margin-right: $spacing-01;
          mask-image: url('assets/icons/icon-plus.svg');
          -webkit-mask-image: url('assets/icons/icon-plus.svg');
          transition: background-color 0.3s ease;
          width: 1.6rem;
        }

        @media (hover: hover) {
          &:hover {
            color: $color-text-brand;

            &:before {
              background-color: $color-text-brand;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    margin-bottom: $spacing-06-24;
  }
}

/* Single page styling for authors' extended cards */
.single .below-article__authors .author-and-topic-card {
  padding-top: $spacing-08-40;
  border-top: 0.1rem solid $color-divider;
  padding-bottom: $spacing-08-40;
  margin-bottom: $spacing-00;

  &__avatar-wrapper {
    height: 4.8rem;
    max-width: 4.8rem;
    min-width: 4.8rem;
  }

  &__name {
    margin-bottom: 0.25rem;
  }

  &__label {
    display: block;
    margin-bottom: $spacing-02;
  }

  &__button {
    color: $color-surface-primary;
    padding: $spacing-03-12 $spacing-04-16 $spacing-03-12 $spacing-07-32;
    background-color: $color-text-primary;
    position: relative;
    font-size: 1.4rem;
    font-family: $font-archivo;
    font-weight: 600;
    line-height: 1.68rem;
    text-decoration: none;

    &:before {
      content: '';
      position: absolute;
      left: 1.5rem;
      top: 2rem;
      transform: translateY(-50%);
      width: 1.6rem;
      height: 1.6rem;
      background-image: url('assets/icons/icon-plus-white.svg');
      background-size: cover;
    }
  }
}
