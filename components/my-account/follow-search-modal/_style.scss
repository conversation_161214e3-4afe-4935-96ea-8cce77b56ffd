@import '../../../atoms/modal/modal';

.follow-search-modal {
  @include modal;

  &__wrapper {
    padding-bottom: $spacing-00 !important;
  }

  &__content {
    padding-bottom: $spacing-00 !important;
  }

  &__search-input-wrapper {
    position: relative;

    &:before {
      content: '';
      background-color: $color-text-secondary;
      @include mask-properties;
      height: 2rem;
      left: 1.6rem;
      mask-image: url('assets/icons/icon-search.svg');
      -webkit-mask-image: url('assets/icons/icon-search.svg');
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 2rem;
      z-index: 1;
    }

    .clear-button {
      position: absolute;
      font-family: $font-archivo;
      text-decoration: underline;
      font-size: 1.6rem;
      font-weight: 600;
      line-height: 1.9rem;
      color: $color-text-primary;
      top: 1.4rem;
      right: 2.4rem;
      cursor: pointer;
      transition: all 0.3s ease;

      @media (hover: hover) {
        &:hover {
          color: $color-text-brand;
        }
      }
    }
  }

  &__search {
    padding-bottom: $spacing-07-32;
  }

  &__search-input {
    border: 0.2rem solid $color-divider;
    font-size: 1.6rem;
    background-color: $post-background-color;
    font-weight: 500;
    height: 4.8rem;
    padding-left: $spacing-08-40;
    position: relative;
    width: 100%;
  }

  &__search-results {
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .author-wrapper {
    margin-bottom: $spacing-06-24;
  }

  &--topic {
    .follow-search-modal {
      &__search-results {
        display: grid;
        grid-template-columns: 1fr 1fr;
        column-gap: $spacing-05-20;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    height: fit-content;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &__title {
      font-size: 2.6rem;
    }

    &__search-results {
      .author-and-topic-card:last-child {
        margin-bottom: $spacing-00;
      }
    }

    &--topic {
      .follow-search-modal {
        &__search-results {
          display: block;
          gap: $spacing-00;
        }
      }
    }
  }
}
