.settings-modal {
  &--form {
    .settings-modal {
      &__section-title {
        margin-bottom: $spacing-05-20;
      }

      &__content {
        display: flex;
        flex-wrap: wrap;
        column-gap: $spacing-05-20;
        padding-bottom: $spacing-00 !important;
        // modals with forms have 0 margin-bottom because field errors are displayed at the bottom of the form with hardcoded input heights

        &--margin {
          margin-bottom: $spacing-09-48;
        }
      }

      &__field-image {
        border-radius: 50%;
        height: 8rem;
        width: 8rem;
        margin-right: $spacing-04-16;
        object-fit: cover;
      }

      &__field {
        &--file {
          align-items: center;
          display: flex;
          margin-bottom: $spacing-09-48;

          .settings-modal {
            &__label {
              cursor: pointer;
              transition: color 0.3s ease;
              color: $color-text-primary;
              margin-bottom: $spacing-02;

              @media (hover: hover) {
                &:hover {
                  color: $color-text-brand;
                }
              }
            }
          }
        }

        &--half {
          flex: 1 0 calc(50% - 2rem);
        }

        &--full {
          flex: 1 0 100%;
        }
      }

      &__field-inner {
        display: flex;
        flex-direction: column;
      }

      &__field-description {
        color: $color-text-secondary;
        font-family: $font-archivo;
        font-size: 1.4rem;
        font-style: normal;
        font-weight: 500;
        line-height: 120%; /* 16.8px */
      }

      &__input {
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: $spacing-00;
        }

        &[type='number'] {
          -moz-appearance: textfield;
        }
      }

      @media screen and (max-width: map-get($container-min-widths, md )) {
        &__field {
          &--file {
            margin-bottom: $spacing-06-24;
          }

          &--half {
            flex: auto;
          }
        }
      }
    }
  }
}
