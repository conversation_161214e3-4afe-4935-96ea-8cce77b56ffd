.settings-modal {
  &--address {
    .settings-modal {
      position: relative;

      &__radio-title {
        color: $color-text-secondary !important;
        margin-bottom: $spacing-01 !important;
      }

      &__footer {
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: $color-surface-primary;
        margin-inline: -4rem;
        z-index: 999;
      }

      &__radio-input {
        display: none;
      }

      &__radio-wrapper {
        display: inline-flex;
        margin-bottom: $spacing-09-48;
        padding: $spacing-01;
        background-color: $color-surface-secondary;
        border: 0.1rem solid $color-divider;
      }

      &__radio {
        padding: $spacing-03-12 $spacing-04-16;
        color: $color-text-secondary;
        cursor: pointer;
        transition: all 0.3s ease;
        @include flexbox-properties;
        text-align: center;

        &:hover {
          color: $color-text-primary;
        }

        &:has(input:checked) {
          color: $color-surface-primary;
          background-color: $color-button-primary;
        }

        &.hidden {
          display: none;
        }

        &:has(input.disabled) {
          opacity: 0.64;
          cursor: not-allowed;
          pointer-events: none;
        }
      }

      &__row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: $spacing-08-40;

        &:last-child {
          margin-bottom: $spacing-00;
        }

        &.hidden {
          display: none;
        }
      }

      &__input {
        cursor: pointer;
        flex-grow: 1;
        transition: all 0.3s ease;
      }

      &__divider {
        margin-bottom: $spacing-06-24;
        border-bottom: 0.1rem solid $color-divider;
        margin-inline: -4rem;
      }

      &__country-selector-label {
        margin-bottom: $spacing-01;
      }

      &__country-selector-wrapper {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        width: 100%;
        min-height: 9.8rem;
        position: relative;

        &:hover {
          .settings-modal {
            &__country-selector-label {
              color: $color-text-primary;
            }

            &__country-selector {
              border-color: $color-text-primary;
            }

            &__country-selector-value {
              color: $color-text-primary;
            }

            &__country-selector-arrow {
              background-color: $color-text-primary;
            }
          }
        }

        .settings-modal {
          &__country-selector-dropdown {
            margin: $spacing-01 $spacing-00;
            opacity: 1;
            transition: opacity 0.3s ease;
            max-height: unset;
          }
        }
      }

      &__country-selector {
        height: 4.72rem;
        padding: $spacing-03-12 $spacing-06-24;
        border: 0.2rem solid $color-divider;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      &__country-selector-value {
        color: $color-text-secondary;
      }

      &__country-selector-arrow {
        width: 1.5rem;
        height: 0.75rem;
        @include mask-properties;
        mask-image: url('assets/icons/icon-chevron-up.svg');
        -webkit-mask-image: url('assets/icons/icon-chevron-up.svg');
        background-color: $color-text-secondary;
        transition: all 0.3s ease;
        transform: rotate(180deg);

        &.isOpen {
          transform: rotate(0deg);
        }
      }

      &__country-selector-dropdown {
        border: 0.2rem solid $color-text-primary;
        padding: $spacing-06-24;
        background-color: $color-surface-primary;
      }

      &__county-selector-options-wrapper {
        display: flex;
        gap: $spacing-07-32;
      }

      &__country-selector-option-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.disabled {
          opacity: 0.64;
          cursor: default;
          pointer-events: none;
        }

        &:last-child {
          margin-top: $spacing-07-32;
        }

        @media (hover: hover) {
          &:hover {
            .settings-modal {
              &__country-selector-option {
                color: $color-text-brand;
              }
            }
          }
        }
      }

      &__country-selector-divider {
        border-bottom: 0.1rem solid $color-divider;
        margin-block: $spacing-06-24;
      }

      &__country-selector-option {
        display: block;
        text-decoration: none;
        font-family: $font-archivo;
        font-size: 1.4rem;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      &__company-details {
        margin-top: $spacing-06-24;
      }
    }

    .delivery-field,
    .change-blocked-field {
      display: none;

      &.visible {
        display: block;
      }
    }

    .change-blocked-field {
      * {
        font-family: $font-archivo;
        font-size: 1.4rem;
        font-weight: 500;
        line-height: 1.7rem;
        color: $color-text-secondary;
      }
    }
  }

  &__content {
    p,
    a {
      color: $color-text-primary;
      font-weight: 500;
      font-family: $font-archivo;
      font-size: 1.6rem;
      line-height: 120%; /* 19.2px */

      &.input-error {
        color: $color-text-error;
        font-size: 1.4rem;
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  #settings-modal-address-footer {
    margin-inline: -1.5rem;
  }

  .settings-modal {
    &--address {
      .settings-modal {
        &__radio-wrapper {
          display: flex;
          margin-bottom: $spacing-06-24;
        }

        &__radio {
          flex: 0 0 33%;
        }

        &__row {
          flex-direction: column;
          gap: $spacing-04-16;

          .input-wrapper {
            width: 100%;
          }
        }

        &__country-selector-wrapper {
          min-width: 100%;
          max-width: 100%;
        }

        &__divider {
          margin-inline: $spacing-00;
        }
      }
    }
  }
}
