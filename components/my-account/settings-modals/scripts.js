document.addEventListener('DOMContentLoaded', () => {
  const container = document.querySelector('main');

  if (container) {
    const modals = document.querySelectorAll('dialog');

    modals.forEach((modal) => {
      modal.addEventListener('close', (e) => {
        // Remove the no-scroll class from the root element
        document.documentElement.classList.remove('no-scroll');

        // Find any forms inside the modal and reset them
        const form = modal.querySelector('form');
        if (form) {
          form.reset();

          // Hide or remove error messages with the class 'input-error'
          const errorMessages = form.querySelectorAll('.input-error');
          errorMessages.forEach((errorMsg) => {
            errorMsg.textContent = ''; // This will clear the content of the <p> element
          });
        }
      });
    });

    container.addEventListener('click', (e) => {
      const targetId = e.target.id;
      const modalId = `${targetId}-modal`;

      if (
        targetId === 'reset-password' ||
        targetId === 'delete-account' ||
        targetId === 'personal-info' ||
        targetId === 'addresses'
      ) {
        const modal = document.getElementById(modalId);

        if (modal) {
          document.documentElement.classList.add('no-scroll');
          modal.showModal();
        }
      } else if (
        targetId === 'reset_password_modal_cancel' ||
        targetId === 'delete_account_modal_ok' ||
        targetId === 'cancel_sub_modal_ok' ||
        targetId === 'address_modal_cancel' ||
        e.target.classList.contains('settings-modal__close-button') ||
        e.target.classList.contains('settings-modal')
      ) {
        const modal = e.target.closest('.settings-modal');

        if (modal) {
          modal.close();
        }
      }
    });
  }
});
