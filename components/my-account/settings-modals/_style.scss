@import './form-modal/style';
@import './address-modal/style';

.settings-modal {
  @include modal;

  &__company-toggle {
    display: flex;
    align-items: center;
    height: 3.2rem;
    margin-bottom: $spacing-04-16;
  }

  &__footer {
    padding: $spacing-04-16 $spacing-08-40 !important;
  }

  &__title {
    margin-right: $spacing-07-32;
  }

  &__button {
    cursor: pointer;
    transition: all 0.3s ease;
    height: 3.2rem;

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }

  &--security {
    .settings-modal {
      &__content {
        padding-top: $spacing-00 !important;
        margin-bottom: $spacing-06-24;
      }
    }
  }

  &--address {
    .modal {
      &__content {
        padding-bottom: 0 !important;
      }
    }
  }

  &--form,
  &--address {
    .settings-modal {
      &__form {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        height: 100%;
        flex: 1;
        overflow-x: scroll;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }
  }

  &--address,
  &--reset-password-modal {
    .settings-modal {
      &__footer {
        justify-content: flex-start !important;
        gap: $spacing-07-32;

        #address_modal_cancel,
        #reset_password_modal_submit {
          color: $color-text-error;
          transition: opacity 0.3s ease;

          @media (hover: hover) {
            &:hover {
              opacity: 0.8;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-min-widths, md )) {
    &__footer {
      justify-content: center !important;
    }
  }
}
