document.addEventListener('DOMContentLoaded', () => {
  const isTouchDevice = 'ontouchstart' in document.documentElement;
  const navMenu = document.querySelector('.my-account-nav__menu');

  if (!navMenu || !isTouchDevice || window.innerWidth > 768) return;

  const activeItem = navMenu.querySelector('.my-account-nav__menu-item--active');

  if (activeItem) {
    setTimeout(() => {
      activeItem.scrollIntoView({
        behavior: 'instant',
        block: 'start',
        inline: 'start',
      });
    }, 10);
  }
});
