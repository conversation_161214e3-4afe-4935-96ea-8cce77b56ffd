<?php
class SavedArticle {
	private int $id;
	private string $title;
	private string $tagName;
	private string $tagLink;
	private string $authorName;
	private string $authorLink;
	private string $publishDate;
	private string $readTime;
	private string $savedAt;
	private string $imageId;

	public function __construct(array $bookmark) {
		$this->id = array_keys($bookmark)[0];
		$this->title = get_the_title($this->id);
		$tagTerm = frontend_get_primary_tag($this->id);
		$this->tagName = $tagTerm ? $tagTerm->name : '';
		$this->tagLink = $tagTerm ? get_term_link($tagTerm, CATEGORY_TYPE) : '';
		$authorTerms = wp_get_post_terms($this->id, 'coauthor');
		$this->authorName = !is_wp_error($authorTerms) && isset($authorTerms[0]) ? $authorTerms[0]->name : '';
		$this->authorLink = !is_wp_error($authorTerms) && isset($authorTerms[0]) ? get_term_link($authorTerms[0]->term_id) : '';
		$this->publishDate = get_the_date('d.m.Y', $this->id);
		$this->readTime = get_post_read_time($this->id);
		$this->savedAt = date('d.m.Y', strtotime(array_values($bookmark)[0]));
		$this->imageId = has_post_thumbnail($this->id) ? get_the_post_thumbnail_url($this->id) : get_field('primaryImage', $this->id);
	}

	public function render() {
		ob_start(); ?>

		<div class="saved-article">

			<div class="saved-article__content-wrapper">

				<div class="saved-article__content-top">

					<?php if ($this->tagName && $this->tagLink) : ?>

						<a href="<?= $this->tagLink; ?>" class="saved-article__tag-link">
							<span class="saved-article__tag tag"><?= $this->tagName; ?></span>
						</a>

					<?php endif; ?>

					<?php if ($this->title) : ?>

						<a href="<?= get_the_permalink($this->id); ?>" class="saved-article__title-link">
							<h6 class="saved-article__title"><?= $this->title; ?></h6>
						</a>

					<?php endif; ?>

				</div>

				<div class="saved-article__content-bottom">

					<div class="saved-article__details">

						<?php if ($this->authorName && $this->authorLink) : ?>

							<a href="<?= $this->authorLink; ?>" class="saved-article__article-detail saved-article__article-detail--author link"><?= $this->authorName; ?></a>

						<?php endif; ?>

						<?php if ($this->publishDate) : ?>

							<span class="saved-article__article-detail saved-article__article-detail--date link"><?= $this->publishDate; ?></span>

						<?php endif; ?>

						<?php if ($this->readTime) : ?>

							<span class="saved-article__article-detail saved-article__article-detail--read-time link"><?= $this->readTime; ?></span>

						<?php endif; ?>

					</div>

					<div class="saved-article__info">

						<button class="saved-article__article-detail saved-article__article-detail--share">
							<span class="saved-article__article-detail saved-article__article-detail-text saved-article__article-detail saved-article__article-detail-text--share link">
								<?= esc_html__('Share', 'FORBES'); ?>
							</span>
						</button>

						<button class="saved-article__article-detail saved-article__article-detail--saved" data-id="<?= $this->id; ?>">
							<span class="saved-article__article-detail saved-article__article-detail-text saved-article__article-detail saved-article__article-detail-text--saved link">
								<?= esc_html__('Saved', 'FORBES') . ' ' . $this->savedAt; ?>
							</span>
						</button>


					</div>

				</div>

			</div>

			<div class="saved-article__image-wrapper">

				<?php if ($this->imageId) : ?>

					<?= wp_get_attachment_image($this->imageId, 'medium', false, array('class' => 'saved-article__image')) ?>

				<?php else : ?>

					<img class="saved-article__image" src="<?= get_template_directory_uri(); ?>/assets/images/forbes_placeholder.webp" alt="<?= $this->title; ?>">

				<?php endif; ?>

			</div>

		</div>

<?php return ob_get_clean();
	}
}
