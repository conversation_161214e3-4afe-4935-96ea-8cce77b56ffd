.order-row {
  cursor: pointer;
  height: 6.4rem;

  &.hidden {
    display: none;
  }

  &--canceled {
    .order-cell {
      &__label,
      &__data {
        color: $color-text-secondary;
        white-space: nowrap;
      }
    }

    .order-row {
      &__item-label {
        color: $color-text-secondary;
      }
    }
  }

  &__status {
    .order-cell__data {
      text-transform: lowercase;
    }
  }

  .order-cell {
    border-bottom: 0.1rem solid $color-divider;
    padding: $spacing-03-12 $spacing-07-32 $spacing-03-12 $spacing-00;
    width: 12.5%;

    &__label {
      display: none;
      font-size: 1.4rem;
    }

    &.order-row__items {
      padding-right: $spacing-00;
      width: 50%;
    }

    &__data {
      white-space: nowrap;
    }
  }

  &__items-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__item {
    max-width: 40rem;
    margin-right: $spacing-06-24;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  &__buttons {
    padding-right: $spacing-00;
    text-align: right;
  }

  &:last-child {
    .order-cell {
      border: none;
    }
  }

  &__buttons-wrapper {
    display: flex;
    justify-content: flex-end;

    .button {
      margin-right: $spacing-02;
      white-space: nowrap;

      &.order-row__invoice-button {
        &:before {
          content: '';
          @include mask-properties;
          mask-image: url('assets/icons/icon-invoice.svg');
          -webkit-mask-image: url('assets/icons/icon-invoice.svg');
          mask-position: left 8% center;
          -webkit-mask-position: left 8% center;
          width: 1.6rem;
          height: 1.6rem;
          background-color: $color-button-primary;
          margin-right: $spacing-01;
        }
      }

      &:last-child {
        margin-right: $spacing-00;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    display: block;
    margin-bottom: $spacing-06-24;
    padding-bottom: $spacing-06-24;
    border-bottom: 0.1rem solid $color-divider;
    height: auto;

    &:last-child {
      border-bottom: none;
      padding-bottom: $spacing-00;
      margin-bottom: $spacing-00;
    }

    .order-cell {
      display: flex;
      flex-direction: column;
      width: 100%;
      border-bottom: none;
      padding: $spacing-00 $spacing-00 $spacing-04-16;

      &__label {
        display: block;
      }

      &.order-row__items {
        width: 100%;
        padding-bottom: $spacing-00;
      }
    }

    &__items-wrapper {
      align-items: flex-start;
      flex-direction: column;
    }

    &__item {
      margin-right: $spacing-00;
      max-width: 100%;
    }

    &__buttons {
      width: 100%;
      margin-top: $spacing-02;
    }

    &__buttons-wrapper {
      flex-direction: column;

      .button {
        margin-top: $spacing-02;
        margin-right: $spacing-00;
      }
    }
  }
}
