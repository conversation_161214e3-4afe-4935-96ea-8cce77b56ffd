.single,
.page-single-list-item,
.page-list {
  .related-posts {
    padding-bottom: $spacing-12-80;

    a {
      display: inline-flex;
      text-decoration: none;
    }

    &__title {
      padding-bottom: $spacing-07-32;
    }

    .article-card {
      @media (hover: hover) {
        &:hover {
          .article-card__title {
            color: $color-text-brand;
          }
        }
      }

      &__content-wrapper {
        display: block;
      }

      &--featured {
        .article-card__title {
          font-size: 2.4rem;
          line-height: 2.88rem;
        }
      }

      &__title-wrapper {
        text-decoration: none;
        color: initial;
      }

      &__title {
        color: $color-text-primary;
        font-size: 1.8rem;
        font-weight: 600;
        line-height: 2.376rem;
      }

      .article-details {
        margin-bottom: $spacing-01;

        &__publish-time {
          &:after {
            content: none;
          }
        }
      }

      &__category {
        color: $color-text-brand;
        background-color: $post-background-color;
        font-family: $font-archivo;
        font-size: 1.2rem;
        font-style: normal;
        font-weight: 700;
        text-transform: none;
        line-height: 1.44rem;
        position: absolute;
        bottom: 0;
        left: 0;
        display: inline-block;
        padding: $spacing-02 $spacing-04-16 $spacing-02 $spacing-05-20;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 1.6rem;
          height: 0.2rem;
          background-color: $color-text-brand;
          transition: background-color 0.3s ease;
        }
      }

      &.premium {
        .article-card {
          &__badge {
            position: absolute;
            left: 0;
            bottom: 0;
            padding: $spacing-02 $spacing-04-16 $spacing-02 $spacing-04-16;
            display: flex;
            align-items: center;
          }

          &__badge-label {
            font-size: 1.2rem;
            line-height: 1.4rem;
            font-weight: 700;
            font-family: $font-archivo;
            color: $color-white;
            padding-left: $spacing-01;
            margin-right: $spacing-01;
          }

          &__badge-icon {
            height: 1.4rem;
            width: 1.4rem;
            object-fit: contain;
          }
        }
      }
    }

    &__top-section {
      display: flex;
      padding-bottom: $spacing-07-32;
      margin-bottom: $spacing-07-32;
      border-bottom: 0.1rem solid $color-divider;

      .line {
        display: none;
      }

      .article-card {
        &--featured {
          .article-card {
            &__excerpt {
              display: -webkit-box;
              margin-top: $spacing-03-12;
            }
          }
        }

        &--small {
          .article-card {
            &__image-wrapper {
              min-width: 16rem;
              max-width: 16rem;
              height: 11rem;
            }
          }
        }
      }
    }

    &__bottom-section {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      column-gap: $spacing-07-32;
      margin-bottom: $spacing-08-40;
      padding-bottom: $spacing-00;

      .article-card {
        padding-bottom: $spacing-07-32;
      }
    }

    .line {
      grid-column: 1 / -1;
      height: 0.1rem;
      background: $color-divider;
      margin: $spacing-07-32 $spacing-00;

      &:last-of-type {
        display: none;
      }
    }

    &__featured {
      flex: 1 1 100%;
      margin-right: $spacing-07-32;

      .article-card {
        &__title {
          padding-left: $spacing-00;
        }

        .article-details {
          padding-left: $spacing-00;
        }
      }
    }

    &__smalls {
      flex: 1 1 100%;
      display: flex;
      flex-flow: column;
      justify-content: space-between;

      &.fstart {
        justify-content: flex-start;
      }

      &.no-ad {
        justify-content: flex-start;
      }

      .article-card {
        &:not(:last-child) {
          margin-bottom: $spacing-06-24;
        }

        &__image-wrapper {
          a {
            height: 100%;
          }
        }

        &__image {
          object-fit: cover;
        }
      }
    }

    &__load-more-button {
      margin: auto;
    }

    &__native-ad {
      display: none;
      margin-bottom: $spacing-06-24;

      .article-card__category {
        text-transform: uppercase;
      }
    }

    &--list {
      padding-bottom: $spacing-00;

      .related-posts {
        &__top-section {
          margin-bottom: $spacing-00;
          padding-bottom: $spacing-00;
          border: none;
        }
      }
    }

    @media screen and (max-width: map-get($container-max-widths, xl)) {
      &__top-section {
        flex-flow: column;

        .line {
          display: block;
        }
      }

      &__featured {
        margin-right: $spacing-00;
      }

      &__smalls {
        gap: $spacing-02;
      }
    }

    @media screen and (max-width: map-get($container-max-widths, lg)) {
      &__title {
        padding-bottom: $spacing-06-24;
      }

      &__bottom-section {
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-07-32;

        // 2 separation lines are also counts as children
        .article-card:nth-child(n + 11) {
          display: none;
        }

        .line {
          display: none;
        }
      }

      &__smalls {
        gap: $spacing-00;
      }

      &--list {
        .article-card {
          &--small {
            .article-card {
              &__image-wrapper {
                a {
                  height: 100%;
                }
              }
            }
          }
        }
      }
    }

    @media screen and (max-width: map-get($container-max-widths, md)) {
      &__title {
        padding-bottom: $spacing-04-16;
      }

      &__top-section {
        padding-bottom: $spacing-00;
      }

      &__bottom-section {
        padding-bottom: $spacing-07-32;
        margin-top: $spacing-07-32;
        gap: $spacing-07-32;

        .article-card:nth-child(n + 11) {
          display: block;
        }
      }

      &__top-section,
      &__bottom-section {
        grid-template-columns: 1fr;
        grid-row-gap: $spacing-04-16;
        margin-bottom: $spacing-04-16;
      }

      &__featured {
        .article-card {
          &__title {
            -webkit-line-clamp: unset !important;
          }
          &__image-wrapper {
            width: 100%;
            margin-left: $spacing-00;
          }
        }
      }

      .line {
        margin: $spacing-04-16 $spacing-00;
      }

      &__smalls {
        .article-card {
          margin-bottom: $spacing-07-32;
        }
      }

      .article-card {
        &__category {
          display: block !important;
          padding-top: $spacing-02 !important;
          margin-bottom: $spacing-00 !important;
        }
      }

      &__load-more-button {
        margin-top: $spacing-08-40;
      }

      &__native-ad {
        margin-bottom: $spacing-00;
      }
    }
  }
}
