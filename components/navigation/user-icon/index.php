<?php

class UserIcon {
    public function __construct() {}

    public function render(): void {
		global $my_account_page_slug, $authentication_page_slug;

		$savedArticlesUrl = frontend_translate('saved-articles', 'route') ?? 'saved-articles';

	    $icon_url = get_template_directory_uri() . '/assets/icons/ds2024/icon-user.svg';
        $accountDropdown = new AccountDropdown(get_the_ID());
        $accountDropdown->render();

        echo <<<EOT
            <a href='/{$authentication_page_slug}' id='menu-user-avatar-link' class='navigation__user-button navigation__user-button--no-display-name'>
                <img id='menu-user-avatar' src='{$icon_url}' alt='User Icon'>
            </a>
            <script>
                if (window.crm_user) {
                    document.getElementById('menu-user-avatar-link').href = '/{$my_account_page_slug}/#/{$savedArticlesUrl}';
                }
            </script>
        EOT;
    }
}
