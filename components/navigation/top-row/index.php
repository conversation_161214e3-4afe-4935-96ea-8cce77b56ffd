<?php
$is_in_account_dropdown = $args['is_dropdown'] ?? false;

/**
 * The ACF fields for navigation (from an options menu).
 * @var array
 * */
$navigation = get_field('navigation', 'option');

/**
 * Subscription button label
 * @var string
 */
$subscription_label = $navigation['subscription_label'] ?? null;

/**
 * Subscription button label
 * @var string
 */
$subscription_link = $navigation['subscription_link'] ?? null;

/**
 * The label of the premium button
 * @var string
 */
$premium_label = $navigation['premium_label'] ?? null;

/**
 * The URL of the authentication page
 * @var string
 */
global $authentication_page_slug;

$user_icon = get_template_directory_uri() . '/assets/icons/ds2024/icon-user.svg';

?>

<div class="navigation__top-row h-d--flex h-justify-content--between">

	<?php render_site_logo(defined('CONTENT_TYPE') ? CONTENT_TYPE : null); ?>

	<div class="h-d--flex">

		<div class="navigation__icons">

			<?php if ($subscription_label && $subscription_link) : ?>

				<?php
					$button = new Button(
						text: $subscription_label,
						url: $subscription_link,
						size: 'medium',
						type: 'primary',
						disabled: false,
						isCloseButton: false,
						additionalClass: 'navigation__subscription-button'
					);
					echo $button->render();

				?>


			<?php endif; ?>

			<div class="navigation__icons-wrapper">

				<?php if (FEATURE_TOGGLE_MY_ACCOUNT): ?>
					<div class="navigation__dropdown-wrapper">

						<button id="navigation__user-button-li" style="display:none;"
								class="navigation__user-button navigation__user-button--no-display-name <?php
								if ( $is_in_account_dropdown ) : ?> navigation__user-button--closer <?php
								endif; ?>">
							<img id='menu-user-avatar' class="navigation__user-avatar" src='<?= $user_icon ?>'
								alt="<?= esc_html__( 'User', 'FORBES' ); ?>">
						</button>

						<a id="navigation__user-button-lo" style="display:none;" href="/<?php
						echo $authentication_page_slug; ?>"
						class="navigation__user-button navigation__user-button--logged-out"aria-label="<?= esc_html__( 'Login', 'FORBES' ); ?>">
							<div id='menu-user-avatar' class="navigation__user-avatar"></div>
						</a>

						<script>
							if (window.crm_user) {
								const loggedInAvatar = document.querySelectorAll("#navigation__user-button-li");

								if (loggedInAvatar) {
									loggedInAvatar.forEach((avatar) => {
										avatar.style.display = "block";
									});
								}
							} else {
								document.getElementById('navigation__user-button-lo').style.display = 'flex';
							}
						</script>

						<?php
						if ( ! $is_in_account_dropdown ) {
							$accountDropdown = new AccountDropdown();
							$accountDropdown->render();
						}
						?>
					</div>
				<?php endif; ?>

				<?php
				if ( ! $is_in_account_dropdown ) : ?>
					<button class="button-icon search-opener-btn" aria-label="Search">
						<i class="icon icon--search"></i>
					</button>
				<?php
				endif; ?>

				<button id="handheld-opener-btn" class="navigation__hamburger-icon button-icon" aria-label="Menu">
					<i class="icon icon--menu"></i>
				</button>

			</div>

		</div>

	</div>

</div>
