jQuery(function ($) {
  window.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
      document.documentElement.classList.remove('no-scroll');
    }
  });

  const locale = document.body.dataset.locale;
  /******************************************************************************
   * User management *
   ******************************************************************************/
  const crmUser = window.crm_user;

  if (crmUser) {
    const navBarUserElement = document.querySelector('.navigation__user-avatar');
    const drawerMenuUserElement = document.getElementById('drawer-menu-user-avatar');

    const avatarElements = [];

    navBarUserElement && avatarElements.push(navBarUserElement);
    drawerMenuUserElement && avatarElements.push(drawerMenuUserElement);

    const menuAvatarElements = document.querySelectorAll('#menu-user-avatar');
    const nameElement = document.getElementById('drawer-menu-user-name');

    const avatarUrl = crmUser.user_meta?.avatar_url || window.defaultUserAvatar;

    if (avatarElements.length) {
      avatarElements.forEach((element) => {
        element.src = avatarUrl;
        element.alt = `${crmUser.user.first_name ?? ''} ${crmUser.user.last_name ?? ''}`;
      });
    }

    if (menuAvatarElements.length) {
      menuAvatarElements.forEach((element) => {
        element.src = avatarUrl;
        element.alt = `${crmUser.user.first_name ?? ''} ${crmUser.user.last_name ?? ''}`;
      });
    }

    if (nameElement) {
      let name = crmUser.user.email ?? '';

      if (crmUser.user.first_name && crmUser.user.last_name) {
        name = `${crmUser.user.first_name} ${crmUser.user.last_name}`;
      }

      nameElement.innerHTML = name;
    }

    const { crm_user } = window;
    const { user_meta } = crm_user;

    if (!crm_user || !user_meta) {
      return;
    }
  }

  /******************************************************************************
   * Handheld menu opener
   ******************************************************************************/
  // Open handheld
  $('.navigation__hamburger-icon').on('click', () => {
    // Lock body scroll
    $('body').data('top-scroll', $(window).scrollTop());
    $('html').addClass('no-scroll');

    $('.drawer-handheld-menu-wrapper').addClass('open');
  });

  // Close handheld menu
  $('.drawer-handheld-menu-closer-btn, .handheld-overlay').on('click', () => {
    // Remove body scroll lock
    $('html').removeClass('no-scroll');

    if (window.innerWidth < 576) {
      $(window).scrollTop($('body').data('top-scroll'));
    }

    $('.drawer-handheld-menu-wrapper').removeClass('open');

    if ($('.submenu.open').length) {
      // Delay the transition to ensure smooth animation.
      // Adjust the delay time based on your transition duration.
      setTimeout(() => $('.submenu.open').removeClass('open'), 300);
    }
  });

  // Open submenu
  $('.submenu-opener').on('click', (e) => {
    e.preventDefault();

    const $submenu = $(e.target).closest('.drawer-handheld-menu__menu-item').find('.submenu');

    $submenu.toggleClass('open');
    $(e.target).toggleClass('open');
  });

  // Step back on handheld menu
  $('.drawer-handheld-menu-back-btn').on('click', () => {
    $('.submenu.open').removeClass('open');
  });

  /******************************************************************************
   * Scrolling appear-disappear effect
   ******************************************************************************/

  const $header = $('.navigation');
  let prevScroll = 0;
  let curScroll = window.pageYOffset;
  const $leaderboardAd = $('#leaderboard-ad');
  const $brandingContainer = $('.gam-branding');

  /*
	Set the top padding on the branding ad container placeholder
	 */
  function setTopPaddingBasedOnTopAd() {
    const $brandingContainer = $('.gam-branding');
    if ($brandingContainer.hasClass('active')) {
      const $activeBrandingTop = $('.gam-branding__top').filter((key, value) => $(value).css('display') !== 'none');
      if ($activeBrandingTop.length) {
        const $brandingPlaceholder = $('.gam-branding__placeholder');
        $brandingPlaceholder.css('padding-top', $activeBrandingTop.height());
      }
    }
  }

  /*
		Adjust start position on non HUN pages due to ad in the top of the page
	*/
  if (locale !== 'hu') {
    document.addEventListener('branding_done', setTopPaddingBasedOnTopAd);

    let timeout;
    window.addEventListener('resize', () => {
      clearTimeout(timeout);
      setTimeout(() => {
        setTopPaddingBasedOnTopAd();
      }, 300);
    });
  }

  /******************************************************************************/

  /******************************************************************************
   * Submenu box reveal
   ******************************************************************************/
  /**
   * Store the currently opened submenu for later use.
   */
  let openedSubMenuBox = null;

  /**
   * If we hover over a link and it has a submenu, open it by adding the "open" class
   * to the submenu box.
   */
  $('.navigation__primary-menu__menu-item')
    .children('.navigation__primary-menu__menu-item__link')
    .on('mouseenter', (e) => {
      const subMenuBox = $(e.target).next('.navigation__primary-menu__sub-menu-box');
      if (subMenuBox.length && !subMenuBox.hasClass('open')) {
        subMenuBox.addClass('open');
        if (openedSubMenuBox) {
          openedSubMenuBox.removeClass('open');
        }
        openedSubMenuBox = subMenuBox;
      }
    });

  /**
   * Close the submenu if we leave the menu element.
   */
  $('.navigation__wrapper').on('mouseleave', function (e) {
    if (
      openedSubMenuBox &&
      (e.pageY >= openedSubMenuBox.offset().top + openedSubMenuBox.outerHeight(true) ||
        e.pageY < $(this).offset().top ||
        e.pageX < $(this).offset().left ||
        e.pageX > $(this).offset().left + $(this).innerWidth())
    ) {
      openedSubMenuBox.removeClass('open');
      openedSubMenuBox = null;
    }
  });
  /******************************************************************************/

  /******************************************************************************
   * Set active class on current page's link *
   ******************************************************************************/
  setActiveLink();
  function setActiveLink() {
    const menuItems = $('.navigation__menu-item');
    const menuNavItems = $('.nav-hover-modal__subcategory');
    const currentPage = window.location.href;

    const active = $(menuItems).filter((i, item) => {
      return currentPage.includes($(item).find('a').attr('href'));
    });

    const activeNavItem = $(menuNavItems).filter((i, item) => {
      return currentPage.includes($(item).attr('href'));
    });

    const activeNavItemParent = $(activeNavItem).parent().parent().parent().parent();

    active && $(active).addClass('current');
    activeNavItem && $(activeNavItem).addClass('current');
    activeNavItem && $(activeNavItemParent).addClass('active-child');
  }

  /******************************************************************************
   * Mega Menu Hover functionality *
   ******************************************************************************/

  const menuItemsWithMegaMenu = [];
  const menuItemsNoMegaMenu = [];
  const megaMenus = $('.nav-hover-modal');

  $('.navigation__menu-item').each((index, item) => {
    const menuId = $(item).data('menu-id');
    if ($(`.nav-hover-modal[data-menu-id="${menuId}"]`).length > 0) {
      menuItemsWithMegaMenu.push($(item)[0]);
    } else {
      menuItemsNoMegaMenu.push($(item)[0]);
    }
  });

  function closeAllMegaMenus() {
    $(menuItemsWithMegaMenu).each((i, menu) => {
      const menuId = $(menu).data('menu-id');
      const modal = $(`.nav-hover-modal[data-menu-id="${menuId}"]`);
      modal.removeClass('active');
      modal.parent().removeClass('active');
    });
  }

  function hideElementsByScreenSize(modal) {
    const screenWidth = window.innerWidth;
    const specialList = modal.find('.nav-hover-modal__special-list');
    const subcategory = modal.find('.nav-hover-modal__subcategory');
    const magazineWrapper = modal.find('.nav-hover-modal__magazine-wrapper');

    magazineWrapper.show(); // Show magazineWrapper by default

    if (screenWidth < 1200 && specialList.width() > 0) {
      magazineWrapper.hide();
    }

    if (screenWidth < 992) {
      if (subcategory.length > 6) {
        magazineWrapper.hide();
        specialList.hide();
      }
    }
  }

  $(menuItemsWithMegaMenu).length &&
    $(menuItemsWithMegaMenu).on('mouseenter', (e) => {
      if (window.innerWidth >= 768) {
        closeAllMegaMenus();
        const menuId = $(e.currentTarget).data('menu-id');
        const modal = $(`.nav-hover-modal[data-menu-id="${menuId}"]`);
        modal.addClass('active');
        modal.parent().addClass('active');

        hideElementsByScreenSize(modal);
      }
    });

  megaMenus.length &&
    $header.on('mouseleave', (e) => {
      if (!$(e.relatedTarget).closest('.nav-hover-modal').length) {
        closeAllMegaMenus();
      }
    });
  megaMenus.length && megaMenus.on('mouseleave', closeAllMegaMenus);
  menuItemsNoMegaMenu.length && $(menuItemsNoMegaMenu).on('mouseenter', closeAllMegaMenus);

  function categoryMenuSplide() {
    const categoryMenu = $('.navigation__bottom-row');

    if (!categoryMenu.length) {
      return;
    }

    let navBottomRow;

    /**
     * Initializes and manages a responsive menu slider for category navigation elements.
     *
     * @param {jQuery} categoryMenu - The jQuery object representing the category menu element.
     * @return {void} This method does not return a value.
     */
    function responsiveCategoryMainMenu(categoryMenu) {
      // Only for resizing purposes
      if (!categoryMenu.hasClass('splide')) {
        categoryMenu.addClass('splide');
      }

      navBottomRow = new Splide('.navigation__bottom-row', {
        perPage: 1,
        perMove: 5,
        autoWidth: true,
        drag: 'free',
        snap: false,
        focus: 0,
        omitEnd: true,
        pagination: false,
        padding: { right: '4rem', left: '4rem' },
      });

      navBottomRow.on('mounted', function () {
        categoryMenu.find('.splide__track').css('overflow', 'clip visible');
      });

      navBottomRow.on('overflow', function (isOverflow) {
        if (!isOverflow) {
          categoryMenu.removeClass('splide');

          navBottomRow.destroy();
          navBottomRow = null;
        }
      });

      navBottomRow.mount();
    }

    responsiveCategoryMainMenu(categoryMenu);

    let resizeTimeout;
    $(window).on('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        responsiveCategoryMainMenu(categoryMenu);
      }, 300);
    });
  }

  categoryMenuSplide();

  /**
   * Border main nav functionality
   */
  function borderCategoryMainMenu() {
    const navBackgroundElement = document.querySelector('.navigation__background');
    const headerImageWrapper = document.querySelector('.header__image-wrapper');
    const background = document.querySelector('.background');

    if (!navBackgroundElement || (!headerImageWrapper && !background)) {
      return;
    }

    let borderUpdatedOnLoad = false;

    function isOverlapping(el1, el2) {
      const rect1 = el1.getBoundingClientRect();
      const rect2 = el2.getBoundingClientRect();
      return !(rect1.bottom <= rect2.top - 1 || rect1.bottom >= rect2.bottom);
    }

    function updateBorder(el1, el2, onLoad = false) {
      if (!borderUpdatedOnLoad && !onLoad) {
        return;
      }

      const overlap = isOverlapping(el1, el2);
      if (onLoad && overlap) {
        borderUpdatedOnLoad = true;
      }

      navBackgroundElement.classList.toggle('border-b', !overlap);
    }

    function handleScrollAndResizeEvents(el1, el2) {
      window.addEventListener('scroll', () => {
        requestAnimationFrame(() => updateBorder(el1, el2));
      });

      window.addEventListener('resize', () => {
        setTimeout(() => updateBorder(el1, el2), 300);
      });

      updateBorder(el1, el2, true);
    }

    handleScrollAndResizeEvents(navBackgroundElement, headerImageWrapper || background);
  }

  borderCategoryMainMenu();
});
