/**********************************************************************
 * Admin Bar Adjustments
 **********************************************************************/
body.admin-bar {
  .navigation {
    top: 3.2rem;
    .drawer-handheld-menu {
      top: 3.2rem;

      .submenus {
        .submenu {
          padding-bottom: $spacing-07-32;
        }
      }
    }

    @media screen and (max-width: map-get($container-max-widths, lg)) {
      top: 4.6rem;

      .drawer-handheld-menu {
        top: 4.6rem;
        padding-bottom: $spacing-09-48;

        .submenus {
          .submenu {
            padding-bottom: $spacing-09-48;
          }
        }
      }
    }
  }
}

body {
  &.page--red-background {
    .navigation,
    .googlead#leaderboard-ad {
      background-color: $color-surface-accent-rose;
    }
  }

  &.page--green-background {
    .navigation,
    .googlead#leaderboard-ad {
      background-color: $color-surface-accent-emerald;
    }
  }
}

/**********************************************************************/

.navigation {
  background-color: $color-surface-primary;
  position: sticky;
  top: 0;
  right: 0;
  z-index: 10;
  transition: transform 0.3s linear;

  &-wrapper {
    width: 100%;

    > .container:first-child {
      position: relative;
    }
  }

  &__background {
    position: absolute;
    top: 0;
    height: 100%;
    width: 100vw;
    left: calc((100% - 100vw) / 2);
    background-color: $color-surface-primary;
    z-index: -1;

    &.border-b {
      border-bottom: 0.1rem solid $color-divider;
    }
  }

  &.hide {
    transform: translateY(-100%);
  }

  &__bottom-row {
    position: relative;

    &.separator-off {
      border-bottom: none;
    }

    &:not(.is-overflow) {
      .splide__arrows {
        display: none;
      }
    }

    & .splide__list {
      flex-wrap: nowrap;
    }

    &.splide {
      margin-right: -$spacing-08-40;
      margin-left: -$spacing-08-40;
      margin-bottom: unset !important;

      .splide__arrow {
        height: $spacing-09-48;
        width: $spacing-08-40;
        transition: opacity 0.3s ease;

        &--prev {
          border-right: 0.1rem solid $color-divider !important;
          background-color: $color-white;
          opacity: 1;
          border-radius: unset;
          left: 0;

          @media (hover: hover) {
            &:hover {
              opacity: 1;
            }
          }

          .icon--chevron-left {
            display: block;
            width: $spacing-06-24;
            height: $spacing-06-24;

            transform: rotate(180deg);

            background-color: $color-icon-secondary;

            mask-image: url('assets/icons/icon-chevron-right.svg');
            -webkit-mask-image: url('assets/icons/icon-chevron-right.svg');
            @include mask-properties;
          }
        }

        &--next {
          border-left: 0.1rem solid $color-divider !important;
          background-color: $color-white;
          opacity: 1;
          border-radius: unset;
          right: 0;

          @media (hover: hover) {
            &:hover {
              opacity: 1;
            }
          }

          .icon--chevron-right {
            display: block;
            width: $spacing-06-24;
            height: $spacing-06-24;

            background-color: $color-icon-secondary;

            mask-image: url('assets/icons/icon-chevron-right.svg');
            -webkit-mask-image: url('assets/icons/icon-chevron-right.svg');
            @include mask-properties;
          }
        }

        &:disabled {
          opacity: 0;
          pointer-events: unset;
          cursor: unset;
        }

        &:before {
          display: none;
        }
      }
    }
  }

  .search-opener-btn {
    @include flexbox-properties;
  }

  &__top-row {
    height: fit-content;
    display: flex;
    justify-content: space-between;
    align-items: center !important;
    gap: $spacing-04-16;
    padding: $spacing-06-24 $spacing-00 $spacing-00 $spacing-00;

    #menu-user-avatar {
      min-width: 2rem;
      min-height: 2rem;
    }

    .icon {
      background-color: $color-icon-secondary;
      transition: background-color 0.3s ease;
    }
  }

  &__menu-item-link {
    display: block;
    color: $color-text-secondary;
    text-decoration: none;
  }

  &__icons {
    display: flex;
    position: relative;
    align-items: center;
    column-gap: $spacing-06-24;
  }

  &__icons-wrapper {
    display: flex;
    align-items: center;
    column-gap: $spacing-04-16;

    .button-icon {
      width: $spacing-08-40;
      height: $spacing-08-40;

      @media (hover: hover) {
        &:hover {
          .icon {
            background-color: $color-text-primary;
          }
        }
      }
    }
  }

  &__user-button {
    cursor: pointer;
    width: $spacing-08-40;
    height: $spacing-08-40;
    border-radius: 50%;

    display: flex;
    align-items: center;
    justify-content: center;

    border: 0.2rem solid transparent;
    transition: all 0.3s ease;

    @media (hover: hover) {
      &:hover {
        #menu-user-avatar {
          background-color: $color-text-primary;
        }
      }
    }

    #menu-user-avatar {
      border-radius: 50%;
      width: $spacing-06-24;
      height: $spacing-06-24;
      object-fit: cover;
    }

    &--logged-out {
      #menu-user-avatar {
        mask-image: url('assets/icons/ds2024/icon-user.svg');
        -webkit-mask-image: url('assets/icons/ds2024/icon-user.svg');
        @include mask-properties;
        background-color: $color-icon-secondary;
        transition: background-color 0.3s ease;
      }
    }

    &--logged-in {
      border-radius: 50%;
      color: $color-surface-primary;
      width: auto;
      font-family: $font-archivo !important;
      font-size: 1rem;
      line-height: 1.1rem;
      height: 2rem;
      min-width: 2rem;
      max-width: 2rem;
      padding: $spacing-00;
      display: flex;
      justify-content: center;
      align-items: center;
      text-decoration: none;
    }
  }

  &__bottom-row-inner {
    display: inline-flex;
    flex-wrap: wrap;
    margin-top: $spacing-00;
    margin-bottom: $spacing-06-24;
    gap: $spacing-06-24;
    align-items: center;

    &.splide__list {
      height: 6.8rem;
    }
  }

  &__menu-item {
    position: relative;

    & > .cta-link-tag,
    .cta-main {
      color: $color-text-primary;
      transition: color 0.3s ease;

      @media (hover: hover) {
        &:hover {
          color: $color-text-primary;
        }
      }
    }

    .highlight {
      position: relative;

      &:after {
        content: '';
        height: 0.7rem;
        width: 0.7rem;
        border-radius: 50%;
        background-color: $color-surface-brand-dark;
        position: absolute;
        right: -0.3rem;
        top: -0.6rem;
      }
    }

    &.current,
    &.active-child {
      .navigation__menu-item-link {
        color: $color-text-primary;
      }
    }

    &.active {
      .navigation__menu-item-link {
        color: $color-text-primary;
      }
    }

    a {
      white-space: nowrap;

      @media (hover: hover) {
        &:hover {
          color: $color-text-primary !important;
        }
      }
    }
  }

  .handheld-overlay {
    display: none;
    z-index: 3;
    @include blur-overlay;
  }

  .drawer-handheld-menu-wrapper {
    &.open {
      .drawer-handheld-menu {
        transform: translateX(0);
      }

      .handheld-overlay {
        display: block;
      }
    }
  }

  .drawer-handheld-menu__menu {
    .small-screen-search {
      display: none;
    }

    .search-opener-btn {
      @include flexbox-properties;

      .icon {
        background-color: $color-icon-primary;
      }
    }

    .navigation__hamburger-icon {
      .icon {
        background-color: $color-icon-secondary;
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, xl)) {
  .navigation {
    &__container {
      padding-right: $spacing-07-32;
      padding-left: $spacing-07-32;
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, lg)) {
  .navigation {
    &__top-row {
      border: none;
    }

    &__login-wrapper {
      height: calc(100vh - 4.6rem);
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md)) {
  .navigation {
    position: sticky;

    &.fixed {
      position: fixed;
    }
  }

  .navigation {
    &__top-row {
      padding-top: $spacing-06-24 !important;
      padding-bottom: $spacing-06-24 !important;

      .main-logo {
        max-width: 10.7rem;
        height: 3.2rem;
        flex-grow: 1;
      }
    }

    &__dropdown-wrapper {
      width: $spacing-08-40;
      height: $spacing-08-40;
      @include flexbox-properties;
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, sm)) {
  .navigation {
    &__icons,
    &__icons-wrapper {
      column-gap: $spacing-02;
    }
  }

  /**
   * Safari only
   */
  _::-webkit-full-page-media,
  _:future,
  .drawer-handheld-menu__footer {
    padding-bottom: $spacing-13-120;
  }
}

@media screen and (max-width: map-get($container-max-widths, sm)) {
  .drawer-handheld-menu__menu {
    .small-screen-search {
      display: block;
    }
    .search-opener-btn {
      margin-right: $spacing-04-16;

      i {
        width: 1.8rem;
        margin-right: $spacing-03-12;
      }
    }
  }
  .navigation {
    padding: $spacing-00;
    height: 6.4rem;
    display: flex;
    align-items: center;

    &__top-row {
      padding: $spacing-00 !important;
    }

    &__icons {
      .search-opener-btn {
        display: none;
      }
    }
  }
}

.navigation {
  .drawer-handheld-menu {
    .icon {
      background-color: $color-icon-secondary;
    }
  }

  &__subscription-button {
    &--mobile {
      display: none;
    }
  }

  &__dropdown-wrapper {
    position: relative;
  }

  &__bottom-row {
    ul {
      .navigation__menu-item {
        &:first-child {
          margin-left: $spacing-00;
        }

        &:last-child {
          margin-right: $spacing-00;
        }
      }
    }
  }
}

.drawer-handheld-menu {
  &.admin-bar {
    top: 3.2rem;
    height: calc(100vh - 3.2rem);
  }

  .magazine {
    &__image-wrapper {
      overflow: hidden;

      img {
        transition: all 0.3s ease;
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, lg)) {
  .navigation {
    &__top-row {
      padding-top: $spacing-06-24;
    }
  }

  .drawer-handheld-menu {
    &.admin-bar {
      top: 4.6rem;
      height: calc(100vh - 4.6rem);
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md)) {
  .navigation {
    &.sticky {
      &.admin-bar {
        top: 4.6rem;
      }
    }

    &__top-row {
      padding-top: $spacing-04-16;
      padding-bottom: $spacing-04-16;
    }

    &__bottom-row {
      display: none;
    }
  }
}
