<?php
/**
 * The ACF fields for navigation (from an options menu).
 * @var array
 * */
$navigation = get_field('navigation', 'option', false);

/**
 * Get the latest magazine ID using the custom function frontend_get_latest_magazine_id().
 * @var int|null
 */
$latest_magazine = Navigation\frontend_get_latest_magazine_id();

/**
 * The array of "menu_name" => "menu_id" pairs to determine which menu should we use in according to the $menu_name's value
 * @var int[]
 * */
$locations = get_nav_menu_locations();

/**
 * We deicde which menu should be loaded based on configuration of content type.
 * */
if (defined('CONTENT_TYPE') && CONTENT_TYPE === 'life') {
    $primary_menu = isset($locations['life-menu']) ? wp_get_nav_menu_object($locations['life-menu']) : null;
} else {
    $primary_menu = isset($locations['primary-menu']) ? wp_get_nav_menu_object($locations['primary-menu']) : null;
}


/**
 * Menu items of the primary menu.
 * @var array(WP_Post)
 */
$primary_menu_items = $primary_menu ? wp_get_nav_menu_items($primary_menu->term_id) : null;

/***************************************************************************************/
/** Checking tags for separator **/

/**
 * Get the primary tag associated with the current post using frontend_get_primary_tag().
 * @var WP_Term|null
 */
$primary_tag = frontend_get_primary_tag(get_the_ID());

/**
 * Extract the term ID from the primary tag or set to null if not available.
 * @var int|null
 */
$tag_id = $primary_tag?->term_id ?? null;

/**
 * Get all the terms associated with the current post under the CATEGORY_TYPE taxonomy.
 * @var array|null
 */
$all_tags = get_all_the_terms(get_the_ID());

/**
 * Get the 'brandvoice_categories' field from the options menu or set to null if not available.
 * @var array|null
 */
$brandvoice_categories = get_field('brandvoice_categories', 'option') ??  null;

/**
 * Check if the 'tag_id' is in the 'brandvoice_categories' array to determine if it's a brand voice post.
 * @var boolean
 */
$is_brandvoice = is_array($brandvoice_categories) ? in_array($tag_id, $brandvoice_categories) : false;

/**
 * Get the 'tag' field from the 'life' option or set to an empty array if not available.
 * @var array
 */
$life_tag = get_field('life', 'option')['tag'] ?? array();

/**
 * Check if the post has the 'life_tag' and is associated with 'CATEGORY_TYPE' to determine if it's a life post.
 * @var boolean
 */
$is_life = ! empty($life_tag) && has_term($life_tag, CATEGORY_TYPE, get_the_ID());

/**
 * If it's a life post, set 'tag_id' to the 'life_tag'.
 */
if ($is_life) {
	$tag_id = $life_tag;
}

/**
 * Get the 'breaking_news_tag' field from the options menu or set to null if not available.
 * @var int|null
 */
$breaking_news_tag_id = get_field('breaking_news_tag', 'option') ?? null;

/**
 * Check if the 'tag_id' matches the 'breaking_news_tag_id' to determine if it's a breaking news post.
 * @var boolean
 */
$is_breaking_news = $tag_id == $breaking_news_tag_id;

/**
 * Indicates whether the separator should be turned off in the header.
 * @var boolean
 */
$separator_off = !($is_brandvoice || $is_breaking_news || $is_life) && is_single();

$is_breadcrumbs = get_field('show_breadcrumbs') ?? false;
?>

<header class="navigation <?= is_admin_bar_showing() ? ' admin-bar' : ''; ?>
<?= $is_breadcrumbs ? ' tw:!relative tw:!top-0' : '' ?>
">

    <div class="navigation__background border-b"></div>

	<div class="navigation-wrapper">

		<div class="container">

			<?php
			get_template_part('components/navigation/top-row/index') ?>

			<div class="navigation__bottom-row splide <?= $separator_off ? 'separator-off' : '' ?>" aria-label="Splide">
				<div class="splide__arrows">
					<button class="splide__arrow splide__arrow--prev">
						<i class="icon icon--chevron-left"></i>
					</button>
					<button class="splide__arrow splide__arrow--next">
						<i class="icon icon--chevron-right"></i>
					</button>
				</div>

				<div class="navigation__bottom-row-wrapper splide__track">

					<ul class="navigation__bottom-row-inner splide__list">

						<?php
						foreach ($primary_menu_items as $key => $menu_item) : ?>

							<?php
							if (intval($menu_item->menu_item_parent) === 0) : ?>

								<li class="navigation__menu-item splide__slide" data-menu-id="<?= $menu_item->ID ?>">

									<a href="<?= $menu_item->url; ?>"
									   class="navigation__menu-item-link basic-14 <?= in_array(
										   'highlight',
										   $menu_item->classes,
									   ) ? 'highlight' : ''; ?>">
										<?= $menu_item->title; ?>
									</a>

								</li>

							<?php
							endif; ?>

						<?php
						endforeach; ?>

					</ul>

				</div>

			</div>

		</div>

		<div class="nav-hover-modals">
			<?php
			foreach ($primary_menu_items as $key => $menu_item) :
				get_template_part(
					'template-parts/nav-hover-modal/index',
					null,
					array('primary_menu_id' => $primary_menu->term_id, 'menu_item' => $menu_item)
				);
			endforeach; ?>
		</div>
	</div>

	<!-- Handheld menu -->
	<?php
	get_template_part('template-parts/drawer-nav-menus/index'); ?>

</header>

<?php
if ($is_breadcrumbs) :
	get_template_part('template-parts/breadcrumbs/index');
endif;
?>
