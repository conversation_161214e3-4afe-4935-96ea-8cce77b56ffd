<?php

namespace Navigation;

/**
 * Enqueue scripts.
 */
function frontend_navigation_scripts() {
	wp_enqueue_script('navigation-script', get_template_directory_uri() . '/minified-js/navigation/script.min.js', array(), filemtime(get_template_directory() . '/minified-js/navigation/script.min.js'), true);
}
add_action('wp_enqueue_scripts', 'Navigation\frontend_navigation_scripts');

/**
 * frontend_build_tree
 *
 * Builds a tree structure from a flat array recursively.
 *
 * @param  array         $elements  The flat array of elements passed as a reference.
 * @param  int           $parent_id
 * @return object[]|null $branch
 * */
function frontend_build_tree(array &$elements, $parent_id = 0) {
	$branch = array();
	foreach ($elements as &$element) {
		if ($element->menu_item_parent == $parent_id) {
			$children = frontend_build_tree($elements, $element->ID);
			if ($children)
				$element->menu_item_children = $children;

			$branch[$element->ID] = $element;
			unset($element);
		}
	}
	return $branch;
}

/**
 * frontend_nav_menu_2_tree
 *
 * Transform a navigational menu to it's tree structure.
 *
 * @param  string        $menud_id
 * @return object[]|null $tree
 * */
function frontend_nav_menu_2_tree($menu_id) {
	$items = wp_get_nav_menu_items($menu_id);
	return  $items ? frontend_build_tree($items, 0) : null;
}

/**
 * Queries for the latest post of type magazine, and returns its ID
 * @return int
 * */
function frontend_get_latest_magazine_id() {
	$args = array(
		'id'			 => 'latest_magazine',
		'posts_per_page' => 1,
		'orderby'		 => array(
			'date_clause' => 'DESC'
		),
		'post_type'		 => 'magazine',
		'post_status'	 => 'publish',
		'fields'		 => 'ids',
		'meta_query'	 => array(
			'relation'	 => 'AND',
			'date_clause' => array(
				'key' => 'issue_date',
				'compare' => 'EXISTS',
			)
		)
	);

	if ('cz' === COUNTRY) {
		$args['meta_query'][] = array(
			array(
				'key'	=> 'issueType',
				'value'	=> 'regular',
			)
		);
	}

	$loop = new \WP_Query($args);

	return $loop->have_posts() ? $loop->posts[0] : null;
}
