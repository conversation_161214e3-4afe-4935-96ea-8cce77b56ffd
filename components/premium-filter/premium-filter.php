<?php
add_action('wp_ajax_frontend_filter_premium_articles', 'frontend_filter_premium_articles');
add_action('wp_ajax_nopriv_frontend_filter_premium_articles', 'frontend_filter_premium_articles');

/**
 * Initializes a WP query by the passed parameters,
 * creates posts by the article card template and sends it back to the frontend
 */
function frontend_filter_premium_articles() {
	/**
	 * The query word
	 * @var string
	 */
	$currentMagazine = $_REQUEST['currentMagazine'] ?? null;
	$currentTags = $_REQUEST['currentTags'] ?? null;
	$premiumTypes = $_REQUEST['premiumTypes'] ?? null;
	$page = $_REQUEST['page'] ?? null;

	$premium_tag = get_field('premium_tag', 'option') ?? '';

	$args = array(
		'post_status'		=> 'publish',
		'posts_per_page'	=> 14,
		'orderby'			=> 'date',
		'order'				=> 'DESC',
		'paged'				=> $page,
		'tax_query'			=> array(
			'relation'		=> 'AND',
			array(
				'field'		=> 'term_id',
				'terms'		=> $premium_tag,
				'taxonomy'	=> CATEGORY_TYPE
			)
		),
		'meta_query'		=> array(
			'relation'		=> 'OR',
		)
	);

	if (isset($currentTags) && count($currentTags)) {
		$taxArray = array(
			'relation' => 'OR',
		);
		foreach ($currentTags as $tag) {
			$taxArray[] = array(
				'taxonomy'	=> CATEGORY_TYPE,
				'field'		=> 'term_id',
				'terms'		=> $tag,
			);
		}
		$args['tax_query'][] = $taxArray;
	}

	if ($currentMagazine) {
		$args['tax_query'][] = array(
			'taxonomy'	=> CATEGORY_TYPE,
			'field'		=> 'term_id',
			'terms'		=> $currentMagazine,
		);
	}

	if (!empty($premiumTypes)) {
		foreach ($premiumTypes as $type) {
			$args['meta_query'][]	= array(
				array(
					'key'		=> 'premium_type',
					'value'		=> $type
				)
			);
		}
	}

	$premium_posts_query = new WP_Query($args);

	$results = $premium_posts_query->posts;
	add_filter('paginate_links', function ($link) {
		if (is_paged()) {
			$link = str_replace('page/1/', '', $link);
		}
		return $link;
	});
	paginate_links();

	if ($results) {
		$articles = '';

		ob_start();
		get_template_part(
			'template-parts/pagination/index',
			null,
			[
				'posts_found' => $premium_posts_query->found_posts,
				'paged' => $page,
				'is_single_magazine' => true,
				'max_num_pages' => $premium_posts_query->max_num_pages,
				'pagination_string' => 'paged1'
			]
		);
		$pagination = ob_get_contents();
		ob_end_clean();


		/*
			Get the article card template for each found post and add it to the articles string
		*/
		foreach ($results as $id) {
			ob_start();
			get_template_part('template-parts/article-card/index', null, array('post_id' => $id, 'article_card_type' => 'archive'));
			$article = ob_get_contents();
			ob_end_clean();
			$articles = $articles . $article;
		}

		echo json_encode(array(
			'results'  	 		=> true,
			'articles' 	 		=> $articles,
			'pagination'		=> $pagination
		));
	} else {
		echo json_encode(array(
			'results'  	 		=> false,
			'articles' 	 		=> '',
			'pagination'		=> ''
		));
	}

	wp_die();
}

add_action('wp_ajax_frontend_filter_magazine_by_year', 'frontend_filter_magazine_by_year');
add_action('wp_ajax_nopriv_frontend_filter_magazine_by_year', 'frontend_filter_magazine_by_year');

function frontend_filter_magazine_by_year() {
	$year = $_REQUEST['year'];

	$magazine_args = array(
		'post_type' => 'magazine',
		'posts_per_page'	=> -1,
		'orderby'			=> 'date',
		'order'				=> 'DESC',
		'meta_query'		=> array(
			array(
				'key'       => 'issue_date',
				'value'     => '^' . $year,
				'compare'   => 'REGEXP',
			)
		),
	);

	$magazine_query = new WP_Query($magazine_args);

	$results = $magazine_query->posts;

	if ($results) {
		$magazines = '';

		/*
			Get the magazine card template for each found post and add it to the magazines string
		*/
		foreach ($results as $id) {
			ob_start();
			get_template_part('template-parts/premium-page/components/magazine-filter', null, ['magazine_id' => $id]);
			$magazine = ob_get_contents();
			ob_end_clean();
			$magazines = $magazines . $magazine;
		}

		echo json_encode(array(
			'results'  	 		 => true,
			'magazines' 	 	 => $magazines,
		));
	}
	wp_die();
}
