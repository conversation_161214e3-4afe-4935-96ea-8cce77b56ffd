<?php

/**
 * The ACF group for the footer
 * @var array
 * */
$footer = defined('CONTENT_TYPE') && CONTENT_TYPE === "life" ? get_field('life_footer', 'option') : get_field('footer', 'option');

/**
 * The links to be displayed in the footer
 * @var array(string)
 * */
$first_link_section = $footer['first_link_section'] ?? null;

/**
 * The links to be displayed in the footer
 * @var array(string)
 * */
$second_link_section = $footer['second_link_section'] ?? null;

/**
 * The links to be displayed in the footer
 * @var array(string)
 * */
$third_link_section = $footer['third_link_section'] ?? null;

/**
 * The ACF group for the social icons,
 * contains platform names and custom links
 * @var array
 * */
$social_media = $footer['social_media'] ?? null;

?>

<script>
	document.addEventListener('DOMContentLoaded', (event) => {
		var currentUrl = window.location.href;
		var footerLinks = document.querySelectorAll('.footer__link');

		footerLinks.forEach(function(link) {
			if (link.href === currentUrl) {
				link.classList.add('active');
			}
		});
	});
</script>

<footer class="footer">

	<div class="container">

		<div class="footer__wrapper">

			<div class="row">

				<div class="col-lg-2 col-md-3 col-12">

					<div class="footer__logo-wrapper">

					<?php render_site_logo(defined('CONTENT_TYPE') ? CONTENT_TYPE : null); ?>

					</div>

				</div>

				<div class="col-lg-5 col-md-6 col-12">

					<div class="footer__links-wrapper">

						<div class="footer__section">

							<?php if ($first_link_section['section_title']) : ?>

								<h4 class="h4-noto heading"><?= $first_link_section['section_title']; ?></h4>

									<ul class="footer__links-list footer__links-list--first">

										<?php foreach ($first_link_section['links'] as $link) : ?>

											<?php if ($link['link_text'] && ($link['link'] || $link['url_link'] || $link['tax_link'])) : ?>

												<?php
												$link_url = '';
												switch ($link['link_type']) {
													case 'url':
														$link_url = $link['url_link'];
														break;
													case 'page-link':
														$link_url = $link['link'];
														break;
													case 'tax':
														$link_url = !is_wp_error(get_term($link['tax_link'])) ? get_term_link(get_term($link['tax_link'])) : '#';
														break;
												}
												?>

												<li class="footer__link-wrapper">

													<a class="footer__link basic-14" href="<?= $link_url; ?>">

														<?= $link['link_text']; ?>

													</a>

												</li>

											<?php endif; ?>

										<?php endforeach; ?>

									</ul>

								<?php endif; ?>

						</div>

					</div>

				</div>

				<div class="col-lg-3 col-md-3 col-6">

					<div class="footer__links-wrapper">

						<div class="footer__section">

							<?php if ($second_link_section['section_title']) : ?>

								<h4 class="h4-noto heading"><?= $second_link_section['section_title']; ?></h4>

									<ul class="footer__links-list footer__links-list--second">

										<?php foreach ($second_link_section['links'] as $link) : ?>

											<?php if ($link['link_text'] && ($link['link'] || $link['url_link'] || $link['tax_link'])) : ?>

												<?php
												$link_url = '';
												switch ($link['link_type']) {
													case 'url':
														$link_url = $link['url_link'];
														break;
													case 'page-link':
														$link_url = $link['link'];
														break;
													case 'tax':
														$link_url = !is_wp_error(get_term($link['tax_link'])) ? get_term_link(get_term($link['tax_link'])) : '#';
														break;
												}
												?>

												<li class="footer__link-wrapper">

													<a class="footer__link basic-14" href="<?= $link_url; ?>">

														<?= $link['link_text']; ?>

													</a>

												</li>

											<?php endif; ?>

										<?php endforeach; ?>

									</ul>

								<?php endif; ?>

						</div>

					</div>

				</div>

				<div class="col-lg-2 col-md-9 col-6">

					<div class="footer__links-wrapper">

						<div class="footer__section">

							<?php if ($third_link_section['section_title']) : ?>

								<h4 class="h4-noto heading"><?= $third_link_section['section_title']; ?></h4>

									<ul class="footer__links-list footer__links-list--third">

										<?php foreach ($third_link_section['links'] as $link) : ?>

											<?php if ($link['link_text'] && ($link['link'] || $link['url_link'] || $link['tax_link'])) : ?>

												<?php
												$link_url = '';
												switch ($link['link_type']) {
													case 'url':
														$link_url = $link['url_link'];
														break;
													case 'page-link':
														$link_url = $link['link'];
														break;
													case 'tax':
														$link_url = !is_wp_error(get_term($link['tax_link'])) ? get_term_link(get_term($link['tax_link'])) : '#';
														break;
												}
												?>

												<li class="footer__link-wrapper">

													<a class="footer__link basic-14" href="<?= $link_url; ?>">

														<?= $link['link_text']; ?>

													</a>

												</li>

											<?php endif; ?>

										<?php endforeach; ?>

									</ul>

								<?php endif; ?>

						</div>

					</div>

				</div>

			</div>

            <?php if (COUNTRY === 'hu') : ?>
                <div class="footer__data-handling-wrapper" style="margin-top: 3.2rem;">

                    <a href="https://cdn.forbes.hu/uploads/2022/04/Fizetesi_tajekoztato-2.pdf" class="footer__image-wrapper" style="margin-bottom: 2.4rem; display: block;
                    width: 36rem; height: auto;">
                        <img width="0" src="https://cdn.forbes.hu/uploads/2022/04/simple-removebg-preview-1.webp?r=eyJ3Ijo1MDAsInEiOjk1LCJzIjoicG5nIn0%3D"
                             class="footer__simple-pay-image" alt="Simple Pay" decoding="async" style="width: 100%;"> </a>

                    <div class="footer__data-content-wrapper" style="display: flex; flex-direction: column;">

						<a href="https://cdn.forbes.hu/uploads/2025/07/2025.07.25._Adatkezelesi_Forbes_altalanos_javitott.pdf"
                           class="footer__data-handling cta-link-tag">
							Adatvédelmi és adatkezelési tájékoztató (aktuális) </a>

						<a href="https://cdn.forbes.hu/uploads/2025/07/AK_02_2025.07.22_sajtoadatkezeles-tajekozato-mediatartalom.pdf"
						   class="footer__data-handling cta-link-tag">
							Médiatartalom-szolgáltatói tevékenységre vonatkozó adatkezelési tájékoztató </a>

						<a href="https://cdn.forbes.hu/uploads/2025/07/altalanos_adatkezelesi_tajekoztato_Forbeshu_2022-1.pdf"
						   class="footer__data-handling cta-link-tag">
							Adatvédelmi és adatkezelési tájékoztató (2019-2025) </a>

						<span class="footer__data-registration-number cta-link-tag">Adatkezelési nyilvántartási szám: NAIH-78438/2014</span>

                    </div>

                </div>
            <?php endif; ?>

		</div>

	</div>

	<div class="footer__bottom">
		<div class="container">

			<div class="footer__bottom-wrapper">
				<?php
					$year = date('Y');

					if( COUNTRY === 'cz' ) :
				?>

					<p class="footer__copyright-text basic-14">Copyright © <?= $year; ?> MediaRey, SE </p>

                    <?php elseif (COUNTRY === 'sk') : ?>

					<p class="footer__copyright-text basic-14">© <?= $year; ?> Barecz & Conrad Media s.r.o. | Forbes Slovensko </p>

				<?php endif; ?>

                <?php if (COUNTRY === 'hu') : ?>
                    <p class="footer__copyright-text basic-14">Copyright © Mediarey Hungary Services Zrt. </p>
                <?php endif; ?>

				<?php if (is_array($social_media['social_icons']) && !empty($social_media['social_icons'])) : ?>

					<div class="footer__social-media-wrapper">

						<?php foreach ($social_media['social_icons'] as $icon) : ?>

							<a href="<?= $icon['url'] ?>" target="_blank" rel="noopener nofollow" class="footer__social-media basic-14"><?= $icon['social_media_platform'] ?></a>

						<?php endforeach; ?>

					</div>

				<?php endif; ?>
			</div>

		</div>
	</div>

</footer>
