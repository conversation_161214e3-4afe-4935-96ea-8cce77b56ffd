.footer {
  margin-top: auto;
  background-color: $color-surface-primary;

  &__logo-wrapper {
    height: 3.2rem;
    width: auto;
    max-width: fit-content;
  }

  &__wrapper {
    padding-block: $spacing-08-40;
    border-top: 0.1rem solid $color-divider;
  }

  h4.heading {
    margin-bottom: $spacing-06-24;
    color: $color-text-primary;
  }

  &__links-list {
    display: grid;
    grid-template-rows: repeat(7, 1fr);
    grid-auto-flow: column;
    grid-column-gap: $spacing-07-32;
    grid-row-gap: $spacing-02;
    max-height: none !important;

    &--first {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  &__link,
  &__social-media {
    display: block;
    color: $color-text-secondary;
    text-decoration: none;

    @media (hover: hover) {
      &:hover,
      &.active {
        color: $color-text-primary;
      }
    }
  }

  &__bottom {
    border-top: 0.1rem solid $color-divider;
  }

  &__bottom-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-block: $spacing-08-40;
  }

  &__copyright-text {
    color: $color-text-secondary;
    font-size: 1.4rem;
    line-height: 1.68rem;
  }

  &__social-media-wrapper {
    display: flex;
    gap: $spacing-06-24;
  }

  &__social-media {
    color: $color-text-secondary;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__logo-wrapper {
      margin-bottom: $spacing-07-32;
    }

    &__wrapper {
      .row {
        justify-content: flex-end;
      }
    }

    .main-logo {
      width: 10.7rem;
      max-width: 10.7rem;
      height: 3.2rem;
    }

    &__links-wrapper {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-07-32;
    }

    &__links-list {
      &--first {
        margin-bottom: $spacing-07-32;
      }
    }

    &__section {
      flex-grow: 1;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &__wrapper {
      .row {
        justify-content: flex-start;
      }
    }

    &__links-wrapper {
      display: flex;
    }

    &__bottom-wrapper {
      flex-direction: column;
      align-items: flex-start;
    }

    &__copyright-text {
      margin-bottom: $spacing-06-24;
    }

    &__social-media-wrapper {
      width: 100%;
      gap: unset;
      flex-wrap: wrap;
      gap: $spacing-04-16;
    }
  }
}
