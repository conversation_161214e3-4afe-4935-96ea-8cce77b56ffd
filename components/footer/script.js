document.addEventListener('DOMContentLoaded', () => {
  // The section titles that trigger the click events
  const triggers = Array.from(document.querySelectorAll('.footer__section-title'));

  // The wrapper elements containing the links
  const linksWrapper = Array.from(document.querySelectorAll('.footer__links-list'));

  // Flag to show if viewport width is of a smaller device
  let isMobile = window.innerWidth < 768;

  triggers.forEach((trigger) => {
    trigger.addEventListener('click', (e) => {
      // Exit if not on mobile
      if (!isMobile) return;

      // The links wrapper belonging to the clicked section
      const currentWrapper = e.target.nextElementSibling;

      // The height of the link elements combined
      const height = currentWrapper.querySelector('.footer__links-list-wrapper').offsetHeight + 30 + 'px';

      // Flag to show if wrapper is already open when clicked
      const alreadyActive = trigger.classList.contains('active');

      // Close each wrapper on click
      linksWrapper.forEach((wrapperElement) => {
        wrapperElement.style.maxHeight = 0;
      });

      triggers.forEach((trigger) => {
        trigger.classList.remove('active');
      });

      // Open the selected wrapper if it was not open before
      if (!alreadyActive) {
        trigger.classList.add('active');
        currentWrapper.style.maxHeight = height;
      }
    });
  });

  /*
		Recalculate viewport width and reset wrapper states based on it
	 */
  let timeout;
  window.addEventListener('resize', () => {
    clearTimeout(timeout);

    timeout = setTimeout(() => {
      isMobile = window.innerWidth < 768;

      if (!isMobile) {
        linksWrapper.forEach((wrapper) => {
          wrapper.classList.remove('active');
          wrapper.style.maxHeight = 'unset';
        });

        triggers.forEach((trigger) => {
          trigger.classList.remove('active');
        });
      } else {
        linksWrapper.forEach((wrapper) => {
          wrapper.style.maxHeight = 0;
        });
      }
    }, 250);
  });
});
