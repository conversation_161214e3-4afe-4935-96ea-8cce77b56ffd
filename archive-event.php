<?php
/**
 * Get pinned events
 * @var array
 */
$pinned_events = get_field('events_archive', 'option')['pin_events'];
$pinned_events_ids = array();

if ($pinned_events) {

	foreach ($pinned_events as $event) {
		if($event['event'] != false ){
			$pinned_events_ids[] = $event['event'];
		}

	}

}

get_header(); ?>

<main class="events-archive">

    <?php if ('hu' === COUNTRY) : ?>

        <?php get_template_part('template-parts/leaderboard-ad/index', null, array('page' => 'archive')) ?>

    <?php endif; ?>

	<?php if(!empty($pinned_events_ids) && count($pinned_events_ids) > 1) : ?>
		<div class="events-archive__header <?php if(!empty($pinned_events_ids) && count($pinned_events_ids) > 1) : ?> events-archive__header--background <?php endif; ?>">
			<div class="container">
                <?php $upcomingTitle = new Heading(__('Featured events', 'FORBES'), 'h2', null, '700', 'noto-serif');
                echo $upcomingTitle->render(); ?>

				<div class="row archive-template__pinned-events">

					<?php foreach( $pinned_events_ids as $id ) : ?>

						<div class="col-lg-6">
							<?php get_template_part('template-parts/event-and-special-card/index', null, array('event_id' => $id)); ?>
						</div>

					<?php endforeach; ?>

				</div>

			</div>

		</div>
	<?php endif; ?>

	<div class="container">
		<?php if (have_posts()) : ?>
            <?php $upcomingTitle = new Heading(__('Upcoming events', 'FORBES'), 'h2', null, '700', 'noto-serif');
            echo $upcomingTitle->render(); ?>

			<div class="row">

				<div class="col-12<?= 'cz' !== COUNTRY ? ' col-md-8' : ''; ?>">

					<div class="events-archive__grid">

						<?php $index = 0; ?>

						<?php while (have_posts()) : the_post(); ?>

							<?php get_template_part('template-parts/event-and-special-card/index'); ?>

							<?php if (1 === $index && 'hu' === COUNTRY) : ?>

								<?php get_template_part('template-parts/leaderboard-ad/index', null, array('page' => 'archive', 'mobile' => true)); ?>

							<?php endif; ?>

							<?php $index++; ?>

						<?php endwhile; ?>

					</div>

				</div>

				<?php if ('cz' !== COUNTRY) : ?>

					<div class="col-12 col-md-4">

						<div class="events-archive__ad-container events-archive__ad-container--desktop googlead-container">
							<div id="category-ad-desktop" class="googlead"></div>
						</div>

					</div>

				<?php endif; ?>


			</div>

			<div class="row">

				<div class="col-12">

					<?php get_template_part('template-parts/pagination/index', null, array('posts_found' => $wp_query->found_posts, 'paged' => get_query_var('paged') ? get_query_var('paged') : 1, 'max_num_pages' => $wp_query->max_num_pages)); ?>

					<?php wp_reset_query(); ?>

				</div>

			</div>

		<?php endif; ?>

		<?php

		$starting_date_field_name = 'cz' === COUNTRY ? 'eventStartDate' : 'starting_date';

		$args = array(
			'posts_per_page'  => -1,
			'post_type'       => 'event',
			'post_status'     => 'publish',
			'orderby'         => 'meta_value',
			'order'           => 'DESC',
			'post__not_in' 		=> count($pinned_events_ids) > 1 ? $pinned_events_ids : array(),
			'meta_key'        => $starting_date_field_name,
			'meta_query'      => array(
				array(
					'key'     => $starting_date_field_name,
					'value'   => date('Ymd'),
					'type'    => 'DATE',
					'compare' => '<'
				)
			)
		);

		$query = new WP_Query($args);


		if($query->have_posts()) : ?>
            <?php $previousTitle = new Heading(__('Previous events', 'FORBES'), 'h2', null, '700', 'noto-serif');
            echo $previousTitle->render(); ?>

			<div class="events-archive__grid <?= 'cz' !== COUNTRY ? ' col-md-8' : ''; ?>">

				<?php while($query->have_posts()) : $query->the_post();
					get_template_part('template-parts/event-and-special-card/index');
				endwhile; ?>

			</div>

		<?php endif; ?>
	</div>


</main>

<?php get_footer(); ?>
