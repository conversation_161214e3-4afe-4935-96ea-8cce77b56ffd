{"require": {"flyntwp/acf-field-group-composer": "^1.0", "google/apiclient": "^2.12.1", "predis/predis": "^1.1", "sentry/sdk": "^3.3", "mailchimp/marketing": "^3.0", "sendinblue/api-v3-sdk": "^8.4"}, "scripts": {"pre-autoload-dump": "Google\\Task\\Composer::cleanup"}, "extra": {"google/apiclient-services": ["Sheets"], "installer-paths": {"vendor/{$vendor}/{$name}/": ["type:wordpress-muplugin", "type:wordpress-plugin", "type:wordpress-theme"]}}, "config": {"allow-plugins": {"composer/installers": true, "php-http/discovery": true}}, "require-dev": {"php-stubs/acf-pro-stubs": "^6.0"}}