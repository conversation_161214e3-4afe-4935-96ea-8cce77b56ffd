<?php

/**
 * Template Name: Print Landing Page
 */

/**
 * Get Magazine Post Data
*/
$magazines = get_magazine_post_data();

/**
 * Get the title of the header
 */
$title = get_field('header_title');

get_header();

?>

<script>
	window.Magazines = <?php echo json_encode($magazines); ?>
</script>

<main class="print-page">

	<div class="print-page__header">

		<div class="container">

			<?php if($title) : ?>

				<?php $subtitle = new Tag(text: 'Print'); echo $subtitle->render(); ?>

				<?php $pageTitle = new Heading($title, 'h2', null, '700', 'archivo'); echo $pageTitle->render(); ?>

			<?php endif; ?>


		</div>

	</div>

	<div class="reactPrintArticles"></div>
</main>

<?php get_footer(); ?>
