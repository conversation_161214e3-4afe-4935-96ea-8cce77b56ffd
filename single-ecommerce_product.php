<?php

$product = ForbesEcommerceSync\ProductQuery::byId(get_the_ID());

if (!$product) {
	// TODO: Redirect to brands page?
	wp_redirect(get_home_url());
	exit;
}

$product->content = wp_kses_post(html_entity_decode($product->content, ENT_QUOTES));

$recommendedProducts = (new ForbesEcommerceSync\ProductQuery())
	->setBrand($product->brand)
	->setPerPage(4)
	->addExcludeId($product->id)
	->fetchAll();

if ( ! empty($recommendedProducts)) {
    foreach ($recommendedProducts as $recommendedProduct) {
        $recommendedProduct->content = wp_kses_post(html_entity_decode($recommendedProduct->content, ENT_QUOTES));
    }
}
?>

<?php get_header(); ?>

<main class="ecommerce-single-products">
	<div class="container">
		<div class="row">
			<div class="col-12">
				<div
					id="reactEcommerceDetailPage"
                    data-product-detail="<?php echo esc_attr(wp_json_encode($product)); ?>"
                    data-recommended-products="<?php echo esc_attr(wp_json_encode($recommendedProducts)); ?>"
				></div>
			</div>
		</div>
	</div>
</main>

<?php get_footer(); ?>
