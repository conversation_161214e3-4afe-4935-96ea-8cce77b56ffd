.PHONY: update_db update_admin_email setup setup_shell config_set plugins wpms_settings install finalize_setup show-login-info create-pages

update_db:
	@echo "Updating database..."
	@mysql -u root -proot local -e "UPDATE wp_options SET option_value = 'twentytwentytwo' WHERE option_name IN ('template', 'stylesheet');"

update_admin_email:
	@echo "Updating admin email..."
	@mysql -u root -proot local -e "UPDATE wp_options SET option_value = '<EMAIL>' WHERE option_name = 'admin_email';"

setup: update_db update_admin_email config_set plugins wpms_settings install finalize_setup create_pages

config_set:
	@read -p "Enter your local development url like forbes.local without http: " local_url; \
	wp config set WP_HOME "http://$$local_url" --type=constant; \
	wp config set WP_SITEURL "http://$$local_url" --type=constant; \
	wp config set WP_DEBUG true --raw; \
	wp config set DOMAIN "$$local_url" --type=constant; \
	wp config set COUNTRY 'cz' --type=constant; \
	wp config set CATEGORY_TYPE 'stitek' --type=constant; \
	wp config set TAG_TYPE 'post_tag' --type=constant; \
	wp config set APP_ENV 'local' --type=constant; \
	wp config set REDIS_HOST 'localhost' --type=constant; \
	wp config set DN_REMP_CRM_HOST 'https://stage-predplatne.forbes.cz/' --type=constant; \
	wp config set DN_REMP_CRM_TOKEN '982cf6edeac4fff43dcd496b74766930' --type=constant \
	wp config set REACT_URL "http://$$local_url" --type=constant

plugins:
	wp plugin install plugins/* --activate --force; \

wpms_settings:
	wp config set WPMS_ON true --raw; \
	wp config set WPMS_LICENSE_KEY ''; \
	wp config set WPMS_MAIL_FROM '<EMAIL>'; \
	wp config set WPMS_MAIL_FROM_FORCE true --raw; \
	wp config set WPMS_MAILER 'mailgun'; \
	wp config set WPMS_MAILGUN_API_KEY '**************************************************'; \
	wp config set WPMS_MAILGUN_DOMAIN 'm.splendex.io'; \
	wp config set WPMS_MAILGUN_REGION 'EU'

install:
	npm install
	npm run build
	composer install

create_pages:
	@read -p "You have to set up a page called My Account with the page template My Account first. If you have set it up please enter the My Account Page ID to continue: " my_account_id; \
	echo "Creating Pages..."; \
	wp post create --post_type=page --post_title="Saved Articles" --post_status=publish --post_parent=$$my_account_id --post_content="" --page_template="page-saved-articles.php"; \
	wp post create --post_type=page --post_title="Followed Authors & Topics" --post_status=publish --post_parent=$$my_account_id --post_content="" --page_template="page-followed-content.php"; \
	wp post create --post_type=page --post_title="My Newsletters" --post_status=publish --post_parent=$$my_account_id --post_content="" --page_template="page-my-newsletters.php"; \
	wp post create --post_type=page --post_title="My Orders" --post_status=publish --post_parent=$$my_account_id --post_content="" --page_template="page-my-orders.php"; \
	wp post create --post_type=page --post_title="Subscriptions" --post_status=publish --post_parent=$$my_account_id --post_content="" --page_template="page-subscriptions.php"; \
	wp post create --post_type=page --post_title="Settings" --post_status=publish --post_parent=$$my_account_id --post_content="" --page_template="page-settings.php"; \

	echo "Pages created successfully."

finalize_setup:
	wp theme activate frontend
	@RESULT=$$(wp user <NAME_EMAIL> --role=administrator --user_pass=dev 2>&1); \
	if echo "$$RESULT" | grep -q "already exists"; then \
		echo "dev user with password dev already exists."; \
	else \
		echo "$$RESULT"; \
	fi

	@echo "Congratulations 🎉 You can now login with username: dev and password: dev. Happy coding!"
	@echo "         _____"
	@echo "        /     \\"
	@echo "       |  0 0  |"
	@echo "       |   ^   |"
	@echo "       |  \\__/ |"
	@echo "        \\_____/"