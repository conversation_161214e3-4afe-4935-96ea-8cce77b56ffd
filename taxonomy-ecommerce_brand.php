<?php

$termSlug = get_query_var('term');
$brand = ForbesEcommerceSync\Brand::getBySlug($termSlug);

if (!$brand) {
	// TODO: Redirect to brands page?
	wp_redirect(get_home_url());
	exit;
}

// Získanie page parametra z URL
$currentPage = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$itemsPerPage = 20;

// Výpočet celkového počtu produktov na načítanie (page * itemsPerPage)
$totalItemsToLoad = $currentPage * $itemsPerPage;

$productsQuery = (new ForbesEcommerceSync\ProductQuery())
	->setBrand($brand)
	->setPerPage($totalItemsToLoad);

if (!empty($_GET['category'])) {
	$category = ForbesEcommerceSync\Category::getBySlug($_GET['category']);
	$productsQuery->setCategory($category);
}

$products = $productsQuery->fetchAll();

if ( ! empty($products)) {
    foreach ($products as $product) {
        $product->content = wp_kses_post(html_entity_decode($product->content, ENT_QUOTES));
    }
}

$categories = array_values(array_filter(
	ForbesEcommerceSync\Category::getTopLevel(),
	fn($category): bool => (new ForbesEcommerceSync\ProductQuery())
		->setCategory($category)
		->setBrand($brand)
		->exists()
));

$brandDetailData = [
	'name' => $brand->name,
    'description' => wp_kses_post(html_entity_decode($brand->description)),
];

$brandLogo = [
	'url' => $brand->logoUrl,
	'alt' => $brand->name,
];

?>

<?php get_header(); ?>

<main class="brands-single">
	<div class="container">
		<div class="row">
			<div class="col-12">
				<div
					id="reactEcommerceBrandDetailPage"
                    data-brand-detail="<?php echo esc_attr(wp_json_encode($brandDetailData)); ?>"
                    data-products="<?php echo esc_attr(wp_json_encode($products)); ?>"
                    data-brand-logo="<?php echo esc_attr(wp_json_encode($brandLogo)); ?>"
                    data-brand-id="<?php echo esc_attr(wp_json_encode($brand->id)); ?>"
                    data-categories="<?php echo esc_attr(wp_json_encode($categories)); ?>"
                    data-current-page="<?php echo esc_attr($currentPage); ?>"
				></div>
			</div>
		</div>
	</div>
</main>

<?php get_footer(); ?>
