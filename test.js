const shuffle = (array) => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
};

// Create Int8Array with length 1M, fill it with numbers 0 and 1 randomly.
const arr = new Int8Array(10000000);

for (let i = 0; i < arr.length; i++) {
  arr[i] = Math.random() < 0.5 ? 0 : 1;
}

// Take 14537 random samples from the array.
const sample = shuffle(arr).slice(0, 14537);

// Compute the distribution of 0s and 1s in the sample.
const distribution = { 0: 0, 1: 0 };

sample.forEach((value) => {
  distribution[value]++;
});

// Log the distribution.
console.log('0:', distribution[0]);
console.log('1:', distribution[1]);
