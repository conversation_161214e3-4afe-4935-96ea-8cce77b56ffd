import UniversalProduct from '../../../react/src/payment/components/v2/UniversalProduct';

export default {
  title: 'React/Payment/UniversalProduct',
  component: UniversalProduct,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },
  args: {},
};

export const Default = {
  args: {
    title: '<PERSON>kud nen<PERSON> konec',
    price: '27,70 €',
    author: '<PERSON><PERSON>',
    description:
      'Pro jedny velk<PERSON> hrdina, pro druhé chladnokrevný zločinec. V českých novodobých dějinách patrně nelze nalézt další postavu vzbuzující tak rozporuplné názory a vášně.',
    image: 'https://picsum.photos/330',
    button: 'https://forbes.com',
    buttonText: 'Primary button',
    button2: 'https://forbes.com',
    buttonText2: 'Secondary button',
  },
};
