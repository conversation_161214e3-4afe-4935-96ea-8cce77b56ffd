import UnlockArticleButton from '../../../../react/src/articles/components/unlock-article/UnlockArticleButton';

export default {
  title: 'React/Article/UnlockArticle',
  component: UnlockArticleButton,
  parameters: {
    layout: 'centered',
    deepControls: {
      enabled: true,
    },
  },
  decorators: [
    (Story, { allArgs: { canShare = true, usedShares = 0, shareLimit = 5, hasSeenHelp = false } = {} }) => {
      window.crm_user = {
        article_unlocks: {
          time_interval_count: usedShares,
          time_interval_limit: shareLimit,
        },
        user_meta: {
          has_seen_unlock_article_help: hasSeenHelp ? '1' : '0',
        },
        access: canShare ? ['share'] : [],
      };

      window.navigator.share = (data) =>
        window.alert(`Called navigator.share with parameters: ${JSON.stringify(data)}`);

      const key = `${canShare}-${usedShares}-${shareLimit}-${hasSeenHelp}`;

      return (
        <div className="tw:flex tw:justify-center" key={key}>
          {Story()}
        </div>
      );
    },
  ],
};

export const Interactive = {
  args: {
    articleData: {
      title: 'Morávkovo pražské entrée. Jeho fond kupuje za osm miliard Myslbek a OC Flora',
      image: 'https://picsum.photos/128/128',
      url: 'https://stage.forbes.sk/cez-spinave-ruky-k-milionom-firma-zenit-vyraba-legendarnu-solvinu-a-v-zavode-ma-aj-peklo/',
    },
    canShare: true,
    hasSeenHelp: false,
    usedShares: 0,
    shareLimit: 5,
  },
};
