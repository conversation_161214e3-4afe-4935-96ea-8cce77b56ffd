import UnlockedArticleDialog from '../../../../react/src/articles/components/unlocked-article/UnlockedArticleDialog';

export default {
  title: 'React/Article/UnlockedArticle',
  component: UnlockedArticleDialog,
  args: {
    opened: true,
    onClose: () => {},
  },
  argTypes: {
    onClose: { table: { disable: true } },
  },
  decorators: [
    (Story) => {
      window.crm_user = {};
      return Story();
    },
  ],
};

export const LoggedIn = {
  args: {
    title: 'Morávkovo pražské entrée. Jeho fond kupuje za osm miliard Myslbek a OC Flora',
    imageLink: 'https://picsum.photos/128/128',
    unlockedBy: '<PERSON>',
  },
};
