import Magazine from '../../../react/src/print/components/Magazine';

export default {
  title: 'React/Print/Magazine',
  component: Magazine,
  parameters: {
    layout: 'fullscreen',
    deepControls: {
      enabled: true,
    },
  },
  args: {},
};

export const Default = {
  args: {
    issue: {
      id: 1,
      title: 'Mrs. <PERSON><PERSON><PERSON>',
      description:
        'Apríl už tradične v slovenskom Forbese patrí TOP ženám biznisu. Aj tentokrát sme sa pozreli na príbehy najúspešnejších podnikateliek Slovenska. V aprílovom vydaní tiež na vás čaká sekcia o realitách, v ktorej prinášame zaujímavé rozhovory o tom, čo sa deje na slovenskom trhu s nehnuteľnosťami a nechýba ani pohľad do zákulisia ich predaja. <a href="https://example.com">Pozrite si, čo si môžete prečítať v januárovom vydaní</a>',
      content:
        'Apríl už tradične v slovenskom Forbese patrí TOP ženám biznisu. Aj tentokrát sme sa pozreli na príbehy najúspešnejších podnikateliek Slovenska.<div style="height: 0.75em"></div>V aprílovom vydaní tiež na vás čaká sekcia o realitách, v ktorej prinášame zaujímavé rozhovory o tom, čo sa deje na slovenskom trhu s nehnuteľnosťami a nechýba ani pohľad do zákulisia ich predaja.<div style="height: 0.75em"></div><a href="https://example.com">Pozrite si, čo si môžete prečítať v januárovom vydaní</a>',
      formattedDate: 'apríl 2025',
      issueYear: 2025,
      featuredImage: 'https://cdn.forbes.sk/uploads/2025/05/image.jpg',
      shopUrl: 'https://example.com',
    },
  },
};
