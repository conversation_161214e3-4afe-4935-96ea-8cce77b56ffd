import Audioplayer from '../../../react/src/components/audioplayer/Audioplayer';

export default {
  title: 'React/Audioplayer',
  component: Audioplayer,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  args: {
    audioGenerating: false, // Ensure default value is set
  },
};

export const FullLength = {
  args: {
    audioGenerating: false,
    source: 'https://cdn.forbes.sk/uploads/2025/05/SoundHelix-Song-1.mp3',
    thumbnail: 'https://picsum.photos/64/64',
  },
};

export const Truncated = {
  args: {
    audioGenerating: false,
    source: 'https://cdn.forbes.cz/uploads/2025/05/test-elevenlabs-3.mp3',
    suffix: 'https://cdn.forbes.sk/uploads/2025/05/sk-locker-suffix.mp3',
    thumbnail: 'https://picsum.photos/64/64',
  },
};

export const Generating = {
  args: {
    audioGenerating: true,
    thumbnail: 'https://picsum.photos/64/64',
  },
};

export const WithMembershipText = {
  args: {
    audioGenerating: false,
    source: 'https://cdn.forbes.sk/uploads/2025/05/SoundHelix-Song-1.mp3',
    thumbnail: 'https://picsum.photos/64/64',
    hasAudioAccess: false,
    forceShowMembershipText: true, // Force show for Storybook regardless of locale
  },
};
