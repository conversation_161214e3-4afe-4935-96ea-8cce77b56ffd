import React from 'react';
import BenefitCardDiscount from '../../../react/src/benefits/BenefitCardDiscount';

export default {
  title: 'React/Benefits/BenefitCardDiscount',
  component: BenefitCardDiscount,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },
};

export const Default = {
  args: {
    imageSrc: 'https://picsum.photos/200/300',
    title: 'K<PERSON>žný svet',
    chip: 'Zľava',
    chipColor: 'stone',
    description: 'Využite 30 % zľavu na knihy, ktoré obohatia vašu knižnicu. Použite Váš promo kód pri platbe.',
    copyText: '<PERSON><PERSON><PERSON> promo kód',
    copyCode: 'FRBS30EVNTS1234',
  },
};
