import Benefit from '../../../react/src/benefits/Benefit';

export default {
  title: 'React/Benefits/Benefit',
  component: Benefit,
  parameters: {
    layout: 'fullscreen',
    deepControls: {
      enabled: true,
    },
  },
  args: {
    web: false,
    digi: false,
    print: false,
    paper: false,
    mobile: false,
    share: false,
    minimalads: false,
    audio: false,
    books: false,
    events: false,
    webinars: false,
    newsletter: false,
  },
  decorators: [
    (Story, { allArgs: access }) => {
      const userAccessTypes = [
        access.web ? 'web' : null,
        access.digi ? 'digi' : null,
        access.print ? 'print' : null,
        access.paper ? 'paper' : null,
        access.mobile ? 'mobile' : null,
        access.share ? 'share' : null,
        access.minimalads ? 'minimalads' : null,
        access.audio ? 'audio' : null,
        access.books ? 'books' : null,
        access.events ? 'events' : null,
        access.webinars ? 'webinars' : null,
        access.newsletter ? 'newsletter' : null,
      ].filter(Boolean);

      window.crm_user = {
        article_unlocks: {
          time_interval_count: 3,
          time_interval_limit: 20,
        },
        access: userAccessTypes,
      };

      const key = userAccessTypes.join('-');

      return (
        <div className="tw:pt-[24px]" key={key}>
          {Story()}
        </div>
      );
    },
  ],
};

export const DigitalSK = {
  args: {
    web: true,
    share: true,
    minimalads: true,
    audio: true,
  },
};

export const PrintPlusDigitalSK = {
  args: {
    web: true,
    digi: true,
    print: true,
    paper: true,
    share: true,
    minimalads: true,
    audio: true,
  },
};

export const ForbesClubSK = {
  args: {
    web: true,
    digi: true,
    print: true,
    paper: true,
    mobile: true,
    share: true,
    minimalads: true,
    audio: true,
    books: true,
    events: true,
    webinars: true,
    newsletter: true,
  },
};
