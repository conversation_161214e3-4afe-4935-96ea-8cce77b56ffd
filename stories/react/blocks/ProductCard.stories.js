import ProductCard from '../../../react/src/home/<USER>/ProductCard';

export default {
  title: 'React/Blocks/ProductCard',
  component: ProductCard,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    deepControls: {
      enabled: true,
    },
  },
  args: {},
  argTypes: {
    hideBrand: {
      control: 'boolean',
    },
    disablePagination: {
      control: 'boolean',
    },
    useSingleImage: {
      control: 'boolean',
    },
    isLinkVisibleOnList: {
      control: 'boolean',
    },
    showSecondaryButton: {
      control: 'boolean',
    },
    secondaryButtonText: {
      control: 'text',
    },
  },
};

const baseMockProduct = {
  id: 1,
  title: 'Premium Leather Wallet',
  content: 'Luxurious handcrafted leather wallet with multiple card slots and premium finish.',
  priceAmount: '89.99',
  priceAmountNum: '89.99',
  priceCurrency: 'EUR',
  permalink: '/product/premium-leather-wallet',
  url: 'https://example.com/product/premium-leather-wallet',
  urlDomain: 'example.com',
  images: [
    { url: 'https://placehold.co/300x300/8B4513/FFFFFF?text=Wallet', type: 'main' },
    { url: 'https://placehold.co/300x300/654321/FFFFFF?text=Wallet+2', type: null },
    { url: 'https://placehold.co/300x300/8B4513/FFFFFF?text=Wallet+3', type: null },
  ],
  brand: {
    name: 'Premium Brand',
    id: 1,
    description: 'Luxury accessories brand',
    url: '/brand/premium-brand',
    parent: null,
  },
  categories: [
    {
      name: 'Accessories',
      id: 1,
      description: 'Fashion accessories',
      url: '/category/accessories',
      parent: null,
    },
  ],
  genders: [
    {
      name: 'Unisex',
      id: 1,
      description: 'For everyone',
      url: '/gender/unisex',
      parent: null,
    },
  ],
  labels: [
    { name: 'New', label: 'New', variant: 'success' },
    { name: 'Premium', label: 'Premium', variant: 'primary' },
  ],
  vendor: {
    name: 'Luxury Goods Co.',
    id: 1,
    description: 'Premium vendor',
    url: '/vendor/luxury-goods',
    parent: null,
  },
  isLinkVisibleOnList: true,
  attributes: [
    { name: 'Material', value: 'Genuine Leather' },
    { name: 'Color', value: 'Brown' },
  ],
};

export const Default = {
  args: {
    ...baseMockProduct,
  },
};

export const WithSingleImage = {
  args: {
    ...baseMockProduct,
    title: 'Smart Watch Pro',
    priceAmountNum: '299.99',
    priceAmount: '299.99',
    images: [{ url: 'https://placehold.co/300x300/2F4F4F/FFFFFF?text=Watch', type: 'main' }],
    brand: {
      name: 'TechBrand',
      id: 2,
      description: 'Technology brand',
      url: '/brand/techbrand',
      parent: null,
    },
    labels: [{ name: 'Bestseller', label: 'Bestseller', variant: 'warning' }],
    useSingleImage: true,
  },
};

export const WithoutBrand = {
  args: {
    ...baseMockProduct,
    title: 'Designer Sunglasses',
    priceAmountNum: '159.99',
    priceAmount: '159.99',
    images: [{ url: 'https://placehold.co/300x300/000000/FFFFFF?text=Sunglasses', type: 'main' }],
    hideBrand: true,
    labels: [{ name: 'Limited Edition', label: 'Limited Edition', variant: 'danger' }],
  },
};

export const WithoutExternalLink = {
  args: {
    ...baseMockProduct,
    title: 'Wireless Headphones',
    priceAmountNum: '199.99',
    priceAmount: '199.99',
    images: [{ url: 'https://placehold.co/300x300/4169E1/FFFFFF?text=Headphones', type: 'main' }],
    brand: {
      name: 'AudioTech',
      id: 4,
      description: 'Audio technology brand',
      url: '/brand/audiotech',
      parent: null,
    },
    isLinkVisibleOnList: false,
    labels: [],
  },
};

export const WithDisabledPagination = {
  args: {
    ...baseMockProduct,
    title: 'Gaming Keyboard',
    priceAmountNum: '129.99',
    priceAmount: '129.99',
    images: [
      { url: 'https://placehold.co/300x300/FF6B6B/FFFFFF?text=Keyboard', type: 'main' },
      { url: 'https://placehold.co/300x300/4ECDC4/FFFFFF?text=Keyboard+2', type: null },
      { url: 'https://placehold.co/300x300/45B7D1/FFFFFF?text=Keyboard+3', type: null },
      { url: 'https://placehold.co/300x300/96CEB4/FFFFFF?text=Keyboard+4', type: null },
    ],
    brand: {
      name: 'GamerTech',
      id: 5,
      description: 'Gaming peripherals brand',
      url: '/brand/gamertech',
      parent: null,
    },
    disablePagination: true,
    labels: [{ name: 'Gaming', label: 'Gaming', variant: 'info' }],
  },
};

export const WithVendorOnly = {
  args: {
    ...baseMockProduct,
    title: 'Organic Coffee Beans',
    priceAmountNum: '24.99',
    priceAmount: '24.99',
    images: [{ url: 'https://placehold.co/300x300/8B4513/FFFFFF?text=Coffee', type: 'main' }],
    brand: null,
    vendor: {
      name: 'Organic Foods Ltd.',
      id: 6,
      description: 'Organic food supplier',
      url: '/vendor/organic-foods',
      parent: null,
    },
    labels: [
      { name: 'Organic', label: 'Organic', variant: 'success' },
      { name: 'Fair Trade', label: 'Fair Trade', variant: 'primary' },
    ],
  },
};

export const HighPriceProduct = {
  args: {
    ...baseMockProduct,
    title: 'Luxury Diamond Ring',
    priceAmountNum: '2999.99',
    priceAmount: '2999.99',
    images: [
      { url: 'https://placehold.co/300x300/FFD700/000000?text=Diamond+Ring', type: 'main' },
      { url: 'https://placehold.co/300x300/C0C0C0/000000?text=Ring+Detail', type: null },
    ],
    brand: {
      name: 'Luxury Jewels',
      id: 7,
      description: 'Premium jewelry brand',
      url: '/brand/luxury-jewels',
      parent: null,
    },
    vendor: {
      name: 'Diamond Merchants',
      id: 7,
      description: 'Certified diamond dealer',
      url: '/vendor/diamond-merchants',
      parent: null,
    },
    labels: [
      { name: 'Luxury', label: 'Luxury', variant: 'warning' },
      { name: 'Certified', label: 'Certified', variant: 'success' },
    ],
    urlDomain: 'luxury-jewels.com',
  },
};

export const WithSecondaryButton = {
  args: {
    ...baseMockProduct,
    title: 'Wireless Bluetooth Speaker',
    priceAmountNum: '79.99',
    priceAmount: '79.99',
    images: [{ url: 'https://placehold.co/300x300/FF6B35/FFFFFF?text=Speaker', type: 'main' }],
    brand: {
      name: 'AudioTech',
      id: 8,
      description: 'Audio technology brand',
      url: '/brand/audiotech',
      parent: null,
    },
    labels: [{ name: 'Bestseller', label: 'Bestseller', variant: 'success' }],
    showSecondaryButton: true,
    secondaryButtonText: 'Přejít na produkt',
  },
};

export const WithSecondaryButtonDefaultText = {
  args: {
    ...baseMockProduct,
    title: 'Smart Fitness Tracker',
    priceAmountNum: '149.99',
    priceAmount: '149.99',
    images: [{ url: 'https://placehold.co/300x300/4ECDC4/FFFFFF?text=Fitness+Tracker', type: 'main' }],
    brand: {
      name: 'FitTech',
      id: 9,
      description: 'Fitness technology brand',
      url: '/brand/fittech',
      parent: null,
    },
    labels: [{ name: 'New', label: 'New', variant: 'primary' }],
    showSecondaryButton: true,
    // No secondaryButtonText provided - should use translation fallback
  },
};