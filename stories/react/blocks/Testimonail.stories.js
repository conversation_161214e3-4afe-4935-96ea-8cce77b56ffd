import Testimonial from '../../../react/src/blocks/Testimonial';

export default {
  title: 'React/Blocks/Testimonial',
  component: Testimonial,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    deepControls: {
      enabled: true,
    },
  },
  args: {},
};

export const Default = {
  args: {
    testimonials: [
      {
        logoSrc: 'https://placehold.co/48x48',
        logoAlt: 'Logo 1',
        name: '<PERSON>',
        position: 'CEO, Company A',
        description:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        imageSrc: 'https://placehold.co/48x48',
        imageAlt: 'Image 1',
      },
      {
        logoSrc: 'https://placehold.co/48x48',
        logoAlt: 'Logo 2',
        name: '<PERSON>',
        position: 'CTO, Company B',
        description:
          'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
        imageSrc: 'https://placehold.co/48x48',
        imageAlt: 'Image 2',
      },
      {
        logoSrc: 'https://placehold.co/48x48',
        logoAlt: 'Logo 3',
        name: '<PERSON> <PERSON>',
        position: 'CFO, Company C',
        description:
          'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
        imageSrc: 'https://placehold.co/48x48',
        imageAlt: 'Image 3',
      },
    ],
  },
};
