import EventCart from '../../../react/src/components/event-cart/EventCart';

export default {
  title: 'React/Blocks/EventCart',
  component: EventCart,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    deepControls: {
      enabled: true,
    },
  },
  args: {},
  argTypes: {
    backLinkText: {
      control: 'text',
    },
    checkoutButtonText: {
      control: 'text',
    },
    quantityLabel: {
      control: 'text',
    },
    totalLabel: {
      control: 'text',
    },
    totalToPayLabel: {
      control: 'text',
    },
    unitLabel: {
      control: 'text',
    },
    isCheckoutDisabled: {
      control: 'boolean',
    },
  },
};

const mockEventItems = [
  {
    id: '1',
    eventName: 'Forbes Business Fest',
    ticketType: 'Early Bird (Dospelý)',
    price: 89.0,
    currency: '€',
    quantity: 0,
  },
  {
    id: '2',
    eventName: 'Forbes Business Fest',
    ticketType: 'Early Bird (Študent)',
    price: 39.0,
    currency: '€',
    quantity: 0,
  },
];

const mockEventItemsWithQuantity = [
  {
    id: '1',
    eventName: 'Forbes Business Fest',
    ticketType: 'Early Bird (Dospelý)',
    price: 89.0,
    currency: '€',
    quantity: 2,
  },
  {
    id: '2',
    eventName: 'Forbes Business Fest',
    ticketType: 'Early Bird (Študent)',
    price: 39.0,
    currency: '€',
    quantity: 1,
  },
];

const singleEventItem = [
  {
    id: '1',
    eventName: 'Forbes Business Fest',
    ticketType: 'Early Bird (Dospelý)',
    price: 89.0,
    currency: '€',
    quantity: 1,
  },
];

// Mock handlers for Storybook
const mockHandlers = {
  onBackClick: () => {
    console.log('Back button clicked');
  },
  onCheckoutClick: (cartItems) => {
    console.log('Checkout button clicked with cart items:', cartItems);
  },
};

export const Default = {
  args: {
    items: mockEventItems,
    ...mockHandlers,
  },
};

export const WithQuantities = {
  args: {
    items: mockEventItemsWithQuantity,
    ...mockHandlers,
  },
};

export const SingleItem = {
  args: {
    items: singleEventItem,
    ...mockHandlers,
  },
};

export const CheckoutDisabled = {
  args: {
    items: mockEventItemsWithQuantity,
    isCheckoutDisabled: true,
    ...mockHandlers,
  },
};

export const EmptyCart = {
  args: {
    items: [],
    ...mockHandlers,
  },
};

export const CustomLabels = {
  args: {
    items: mockEventItemsWithQuantity,
    backLinkText: 'Back to Event Website',
    checkoutButtonText: 'Proceed to Order',
    quantityLabel: 'Quantity',
    totalLabel: 'Total',
    totalToPayLabel: 'Total to Pay',
    unitLabel: 'pcs',
    ...mockHandlers,
  },
};

export const DifferentCurrency = {
  args: {
    items: [
      {
        id: '1',
        eventName: 'Forbes Business Conference',
        ticketType: 'VIP Pass',
        price: 299.99,
        currency: 'USD',
        quantity: 1,
      },
      {
        id: '2',
        eventName: 'Forbes Business Conference',
        ticketType: 'Regular Pass',
        price: 149.99,
        currency: 'USD',
        quantity: 2,
      },
    ],
    ...mockHandlers,
  },
};

export const LongEventNames = {
  args: {
    items: [
      {
        id: '1',
        eventName: 'Forbes Business Fest 2024 - Premium International Conference',
        ticketType: 'Early Bird Premium Package (Adult with Networking)',
        price: 189.0,
        currency: '€',
        quantity: 1,
      },
      {
        id: '2',
        eventName: 'Forbes Business Fest 2024 - Premium International Conference',
        ticketType: 'Student Discount Package (Limited Seats Available)',
        price: 79.0,
        currency: '€',
        quantity: 1,
      },
    ],
    ...mockHandlers,
  },
};
