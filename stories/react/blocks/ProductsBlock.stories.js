import ProductsBlock from '../../../react/src/e-commerce/components/ProductsBlock';

export default {
  title: 'React/Blocks/ProductsBlock',
  component: ProductsBlock,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    deepControls: {
      enabled: true,
    },
  },
  args: {},
  argTypes: {
    mobileView: {
      control: 'select',
      options: ['swiper', 'block'],
    },
    grid: {
      control: 'select',
      options: ['single', 'double', 'triple', 'quad'],
    },
    advertisementPosition: {
      control: 'number',
    },
  },
};

const mockProducts = [
  {
    id: 1,
    title: 'Premium Leather Wallet',
    content: 'Luxurious handcrafted leather wallet with multiple card slots and premium finish.',
    priceAmount: '89.99',
    priceAmountNum: '89.99',
    priceCurrency: 'EUR',
    permalink: '/product/premium-leather-wallet',
    url: 'https://example.com/product/premium-leather-wallet',
    urlDomain: 'example.com',
    images: [
      { url: 'https://placehold.co/300x300/8B4513/FFFFFF?text=Wallet', type: 'main' },
      { url: 'https://placehold.co/300x300/654321/FFFFFF?text=Wallet+2', type: null },
    ],
    brand: {
      name: 'Premium Brand',
      id: 1,
      description: 'Luxury accessories brand',
      url: '/brand/premium-brand',
      parent: null,
    },
    categories: [
      {
        name: 'Accessories',
        id: 1,
        description: 'Fashion accessories',
        url: '/category/accessories',
        parent: null,
      },
    ],
    genders: [
      {
        name: 'Unisex',
        id: 1,
        description: 'For everyone',
        url: '/gender/unisex',
        parent: null,
      },
    ],
    labels: [
      { label: 'New', variant: 'success' },
      { label: 'Premium', variant: 'primary' },
    ],
    vendor: {
      name: 'Luxury Goods Co.',
      id: 1,
      description: 'Premium vendor',
      url: '/vendor/luxury-goods',
      parent: null,
    },
    isLinkVisibleOnList: true,
    attributes: [
      { name: 'Material', value: 'Genuine Leather' },
      { name: 'Color', value: 'Brown' },
    ],
  },
  {
    id: 2,
    title: 'Smart Watch Pro',
    content: 'Advanced smartwatch with health monitoring, GPS, and premium design.',
    priceAmount: '299.99',
    priceAmountNum: '299.99',
    priceCurrency: 'EUR',
    permalink: '/product/smart-watch-pro',
    url: 'https://example.com/product/smart-watch-pro',
    urlDomain: 'example.com',
    images: [
      { url: 'https://placehold.co/300x300/2F4F4F/FFFFFF?text=Watch', type: 'main' },
    ],
    brand: {
      name: 'TechBrand',
      id: 2,
      description: 'Technology brand',
      url: '/brand/techbrand',
      parent: null,
    },
    categories: [
      {
        name: 'Electronics',
        id: 2,
        description: 'Electronic devices',
        url: '/category/electronics',
        parent: null,
      },
    ],
    labels: [
      { label: 'Bestseller', variant: 'warning' },
    ],
    isLinkVisibleOnList: true,
  },
  {
    id: 3,
    title: 'Designer Sunglasses',
    content: 'Stylish designer sunglasses with UV protection and premium frame.',
    priceAmount: '159.99',
    priceAmountNum: '159.99',
    priceCurrency: 'EUR',
    permalink: '/product/designer-sunglasses',
    url: 'https://example.com/product/designer-sunglasses',
    urlDomain: 'example.com',
    images: [
      { url: 'https://placehold.co/300x300/000000/FFFFFF?text=Sunglasses', type: 'main' },
    ],
    brand: {
      name: 'Fashion House',
      id: 3,
      description: 'Designer fashion brand',
      url: '/brand/fashion-house',
      parent: null,
    },
    categories: [
      {
        name: 'Accessories',
        id: 1,
        description: 'Fashion accessories',
        url: '/category/accessories',
        parent: null,
      },
    ],
    labels: [
      { label: 'Limited Edition', variant: 'danger' },
    ],
    isLinkVisibleOnList: true,
  },
  {
    id: 4,
    title: 'Wireless Headphones',
    content: 'High-quality wireless headphones with noise cancellation and long battery life.',
    priceAmount: '199.99',
    priceAmountNum: '199.99',
    priceCurrency: 'EUR',
    permalink: '/product/wireless-headphones',
    url: 'https://example.com/product/wireless-headphones',
    urlDomain: 'example.com',
    images: [
      { url: 'https://placehold.co/300x300/4169E1/FFFFFF?text=Headphones', type: 'main' },
    ],
    brand: {
      name: 'AudioTech',
      id: 4,
      description: 'Audio technology brand',
      url: '/brand/audiotech',
      parent: null,
    },
    categories: [
      {
        name: 'Electronics',
        id: 2,
        description: 'Electronic devices',
        url: '/category/electronics',
        parent: null,
      },
    ],
    isLinkVisibleOnList: true,
  },
];

export const Default = {
  args: {
    products: mockProducts,
    sectionHeader: {
      title: 'Featured Products',
      linkLabel: 'View All Products',
      url: '/products',
      blockId: 'featured-products',
    },
    button: {
      label: 'Load More Products',
      url: '/products',
    },
    grid: 'quad',
    mobileView: 'block',
  },
};

export const WithSwiper = {
  args: {
    products: mockProducts,
    sectionHeader: {
      title: 'Mobile Swiper View',
      linkLabel: 'View All',
      url: '/products',
      blockId: 'mobile-swiper-products',
    },
    mobileView: 'swiper',
    grid: 'quad',
  },
};

export const TripleGrid = {
  args: {
    products: mockProducts,
    sectionHeader: {
      title: 'Triple Grid Layout',
      linkLabel: 'See More',
      url: '/products',
      blockId: 'triple-grid-products',
    },
    grid: 'triple',
    mobileView: 'block',
  },
};

export const WithAdvertisement = {
  args: {
    products: mockProducts,
    sectionHeader: {
      title: 'Products with Advertisement',
      linkLabel: 'View All',
      url: '/products',
      blockId: 'products-with-ad',
    },
    advertisementPosition: 2,
    grid: 'quad',
    mobileView: 'block',
  },
};

export const WithClickableButton = {
  args: {
    products: mockProducts.slice(0, 2),
    sectionHeader: {
      title: 'Products with Action Button',
      linkLabel: 'View All',
      url: '/products',
      blockId: 'products-with-button',
    },
    button: {
      label: 'Load More',
      onClick: () => alert('Load more clicked!'),
      loading: false,
    },
    grid: 'double',
    mobileView: 'block',
  },
};

export const LoadingButton = {
  args: {
    products: mockProducts.slice(0, 2),
    sectionHeader: {
      title: 'Loading State',
      linkLabel: 'View All',
      url: '/products',
      blockId: 'products-loading',
    },
    button: {
      label: 'Loading...',
      onClick: () => {},
      loading: true,
    },
    grid: 'double',
    mobileView: 'block',
  },
};

export const WithSecondaryButtons = {
  args: {
    products: mockProducts.map((product, index) => ({
      ...product,
      showSecondaryButton: true,
      secondaryButtonText: index === 0 ? 'View Details' : index === 1 ? 'Learn More' : undefined,
    })),
    sectionHeader: {
      title: 'Products with Secondary Buttons',
      linkLabel: 'View All',
      url: '/products',
      blockId: 'products-with-secondary-buttons',
    },
    grid: 'triple',
    mobileView: 'block',
  },
};

export const MixedSecondaryButtons = {
  args: {
    products: mockProducts.map((product, index) => ({
      ...product,
      showSecondaryButton: index % 2 === 0, // Show button on every other product
      secondaryButtonText: index === 0 ? 'Custom Text' : undefined,
    })),
    sectionHeader: {
      title: 'Mixed Secondary Button States',
      linkLabel: 'View All',
      url: '/products',
      blockId: 'products-mixed-buttons',
    },
    grid: 'quad',
    mobileView: 'block',
  },
};