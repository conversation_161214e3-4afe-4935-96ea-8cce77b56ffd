import NewsletterUnsubscribe from '../../../react/src/newsletter/components/NewsletterUnsubscribe';

export default {
  title: 'React/Newsletter/NewsletterUnsubscribe',
  component: NewsletterUnsubscribe,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    onUnsubscribe: { action: 'unsubscribed' },
  },
};

export const Default = {
  args: {
    onUnsubscribe: (reason) => {
      console.log('Unsubscribe reason:', reason);
      alert(`Unsubscribed with reason: ${reason}`);
    },
  },
};

export const WithoutCallback = {
  args: {},
};
