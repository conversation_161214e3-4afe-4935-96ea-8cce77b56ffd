import os
import re
import sys
import requests
from datetime import datetime

jira_base_url = os.environ['JIRA_BASE_URL']
jira_user_email = os.environ['JIRA_USER_EMAIL']
jira_api_token = os.environ['JIRA_API_TOKEN']
slack_webhook_url = os.environ['SLACK_WEBHOOK_URL']

# Function to send a Slack message
def send_slack_message(message):
    payload = {"text": message, "channel": "monitoring"}
    response = requests.post(slack_webhook_url, json=payload)
    response.raise_for_status()

# Fetch the branch name
branch_name = os.environ['CI_COMMIT_REF_NAME']

# Bypass validation if the branch name contains "hotfix"
if "hotfix" in branch_name:
    print("Bypassing worklog validation for hotfix branch.")
    sys.exit(0)

# Extract the JIRA issue key from the branch name
issue_key = re.search(r"[A-Z]+-\d+", branch_name)
if not issue_key:
    # Fetch the GitLab user who attempted to create the merge request
    gitlab_user = os.environ['GITLAB_USER_EMAIL']

    # Get the current timestamp
    timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')

    # Send a Slack message to the monitoring channel
    message = f"{timestamp} - {gitlab_user} attempted to create a merge request without a valid JIRA issue key in the branch name."
    send_slack_message(message)

    print("JIRA issue key not found in the branch name.")
    sys.exit(1)

issue_key = issue_key.group(0)

# Fetch worklogs for the JIRA issue
auth = (jira_user_email, jira_api_token)
worklog_url = f"{jira_base_url}/rest/api/3/issue/{issue_key}/worklog"
response = requests.get(worklog_url, auth=auth)
response.raise_for_status()

worklogs = response.json()['worklogs']

if not worklogs:
    # Fetch the GitLab user who attempted to create the merge request
    gitlab_user = os.environ['GITLAB_USER_EMAIL']

    # Get the current timestamp
    timestamp = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')

    # Send a Slack message to the monitoring channel
    message = f"{timestamp} - {gitlab_user} attempted to create a merge request without worklogs for issue {issue_key}."
    send_slack_message(message)

    print(f"No worklogs found for issue {issue_key}. Merge request not allowed.")
    sys.exit(1)

print(f"Worklogs found for issue {issue_key}. Merge request allowed.")
