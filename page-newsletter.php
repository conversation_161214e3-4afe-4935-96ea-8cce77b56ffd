<?php

/**
 * Template Name: Newsletter
 */

/**
 * The title of the header
 * @var string
 */
$header_title = get_field('header_title') ?? '';

/**
 * The quote text from a person
 * @var string
 */
$quote = get_field('quote') ?? '';

/**
 * The quote author datas
 * @var string
 */
$quote_author = get_field('quote_author') ?? '';

/**
 * The image of the author
 * @var int
 */
$quote_author_image = get_field('quote_author_image') ?? '';

/**
 * The image of the header
 * @var int
 */
$main_image = get_field('header_main_image') ?? '';

get_header();
?>

<main class="page-newsletter">

	<div class="container">
		<?php the_content(); ?>
	</div>

	<div id="next-section" class="reactPageNewsletterContent"></div>

</main>

<?php get_footer(); ?>
