stages:
  - build
  - cache

variables:
  SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar'
  GIT_DEPTH: '0'

Run Build:
  stage: build
  image: node:12
  script:
    - npm install
    - npm run build
    - npx gulp build
  artifacts:
    paths:
      - style.css
      - editor.css
      - blocks.css
      - lists.css
      - single.css
      - my-account.css
      - editor-blocks.css
      - blocks-non-critical.css
      - style-non-critical.css
      - minified-js/
    expire_in: 2 days
  only:
    - master
    - develop
s3-cache:
  before_script:
    - apt-get update -qq
    - apt-get install -qq awscli
    - aws configure set aws_access_key_id $S3_UPLOADS_KEY
    - aws configure set aws_secret_access_key $S3_UPLOADS_SECRET
  stage: cache
  script:
    - FILE_NAME=$CI_PROJECT_NAME-$CI_COMMIT_BRANCH.tar.gz
    - tar --exclude-vcs -czf /tmp/$FILE_NAME .
    - aws s3 cp /tmp/$FILE_NAME s3://forbes-modules/$CI_PROJECT_NAMESPACE/$FILE_NAME
  only:
    - master
    - develop
