<?php
$current_term = get_queried_object();

$premium_stitek = get_field('premium_tag', 'option') ?? 0;

$is_premium = intval($premium_stitek) === $current_term->term_id;

$premium_page = get_field('premium_page_link', 'option');

$linked_term_id = MagazineTax\check_term_linkage($current_term->term_id);

if ($is_premium && $premium_page) {
	echo "<script> window.location.replace('" . $premium_page . "') </script>";
	die();
} elseif ($linked_term_id) {
	echo "<script> window.location.replace('" . get_the_permalink($linked_term_id) . "') </script>";
	die();
} else {
	get_header();
	?>

	<main class="tag-archive">

		<?php get_template_part('template-parts/archive-template/index', null, ['term' => $current_term]);?>

	</main>

	<?php
	get_footer();
}
