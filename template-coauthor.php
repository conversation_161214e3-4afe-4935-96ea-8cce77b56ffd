<?php

/**
 * Template Name: Authors Page
 */

get_header();

/**
 * The ACf fields for the author pages
 * @var array
 */
$author_pages = get_field('author_pages', 'option');

/**
 * The titles for the authors page
 * @var string
 * @var string
 * @var string
 * @var string
 */
$forbes_authors_title = $author_pages['forbes_authors_title'] ?? '';
$contributors_title = $author_pages['contributors_title'] ?? '';
$brandvoice_title = $author_pages['brandvoice_title'] ?? '';
$life_title = $author_pages['life_title'] ?? '';

/**
 * The labels for the authors page
 * @var string
 * @var string
 * @var string
 * @var string
 */
$forbes_authors_label = $author_pages['forbes_authors_label'] ?? '';
$contributors_label = $author_pages['contributors_label'] ?? '';
$brandvoice_authors_label = $author_pages['brandvoice_authors_label'] ?? '';
$life_authors_label = $author_pages['life_authors_label'] ?? '';

/**
 * Get all coauthors
 * @var array
 */
$all_coauthor = frontend_get_coauthors();

?>

<script>
	window.Coauthors = <?php echo json_encode($all_coauthor); ?>;
	window.AuthorPageLabels = {
		forbes_authors_title: <?php echo json_encode($forbes_authors_title); ?>,
		contributors_title: <?php echo json_encode($contributors_title); ?>,
		brandvoice_title: <?php echo json_encode($brandvoice_title); ?>,
		life_title: <?php echo json_encode($life_title); ?>,
		forbes_authors_label: <?php echo json_encode($forbes_authors_label); ?>,
		contributors_label: <?php echo json_encode($contributors_label); ?>,
		brandvoice_authors_label: <?php echo json_encode($brandvoice_authors_label); ?>,
		life_authors_label: <?php echo json_encode($life_authors_label); ?>
	};
</script>

<main class="page-authors reactPageAuthors"></main>

<?php get_footer(); ?>
