const gulp = require('gulp');
const uglify = require('gulp-uglify');
const rename = require('gulp-rename');
const browserSync = require('browser-sync').create();

const source = [
  'style.js',
  'assets/js/**/*.js',
  'assets/js/*.js',
  'blocks.js',
  'components/**/*.js',
  'blocks/acf-blocks/***/**/*.js',
  'blocks/acf-blocks/****/***/**/*.js',
  'template-parts/**/*.js',
  'atoms/**/*.js',
];

gulp.task('minify-script', function () {
  return gulp
    .src(source)
    .pipe(uglify())
    .pipe(rename({ suffix: '.min' })) // Add .min suffix to the file name
    .pipe(gulp.dest('minified-js'))
    .pipe(browserSync.stream()); // Inject changes without a full browser reload.
});

// Setup server and watch files
gulp.task('watch', function () {
  gulp.watch(source, gulp.series('minify-script'));

  // If you also want to watch other files (e.g., HTML or PHP) to reload the browser:
  gulp.watch(['path/to/html-or-php-files/*']).on('change', browserSync.reload);
});

gulp.task('default', gulp.series('minify-script', 'watch'));

gulp.task('build', gulp.series('minify-script'));
