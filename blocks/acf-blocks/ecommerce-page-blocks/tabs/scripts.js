document.addEventListener('DOMContentLoaded', () => {
  const staticElement = document.querySelector('.reactEcommerceTabs');
  if (!staticElement) return;

  const html = document.documentElement;
  html.style.scrollPaddingTop = window.innerWidth <= 768 ? '120px' : '260px';

  /*
   * Handle the scroll event
   */
  const handleScroll = () => {
    const navigation = document.querySelector('.navigation');
    const navigationHeight = navigation?.offsetHeight;
    const wpAdminBarHeight = document.getElementById('wpadminbar')?.offsetHeight;
    const elementTop = staticElement.getBoundingClientRect().top;
    const tabsWrapper = staticElement.querySelector('.tabs-wrapper');

    if (!tabsWrapper) return;

    // Calculate the height
    const height = wpAdminBarHeight ? navigationHeight + wpAdminBarHeight : navigationHeight;

    // Add the fixed and container classes
    if (elementTop <= height) {
      tabsWrapper.classList.add('fixed');
      tabsWrapper.classList.add('container');

      if (navigation?.classList.contains('hide')) {
        tabsWrapper.style.top = wpAdminBarHeight ? `${wpAdminBarHeight}px` : '0px';
        return;
      }

      // Set the top position
      tabsWrapper.style.top = `${height}px`;
    } else {
      // Remove the fixed and container classes
      tabsWrapper.classList.remove('fixed');
      tabsWrapper.classList.remove('container');
      tabsWrapper.style.top = '0';
    }
  };

  window.addEventListener('scroll', handleScroll);

  // Cleanup
  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
});
