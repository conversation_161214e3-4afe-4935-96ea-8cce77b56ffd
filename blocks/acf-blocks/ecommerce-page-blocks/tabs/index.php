<?php

/**
 * The tabs of the block
 * @var array|false $tabs
 */
$tabs = get_field('ecommerce_tabs');

if (!is_array($tabs)) {
	$tabs = [];
}

$structured_tabs = [];

foreach ($tabs as $tab) {
	$structured_tabs[] = [
		'label' => $tab['title'],
		'link' => $tab['anchor'],
	];
}

?>

<div
	class="reactEcommerceTabs"
	data-tabs="<?php echo esc_attr(json_encode($structured_tabs)); ?>"
	data-use-hash-routing="true"
>
	<div
		style="display: none"
		class="forbes-gutenberg-block-placeholder"
	>
		<span
			class="dashicon dashicons dashicons-editor-ul"
			context="list-view"
		></span>
		<?php echo esc_html__('Ecommerce: Tabs Block', 'FORBES'); ?>
	</div>
</div>