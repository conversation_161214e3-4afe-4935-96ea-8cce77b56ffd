<?php


/**
 * The tiles of the block
 * @var array
 */
$tiles = get_field('ecommerce_tiles');

/**
 * The size of the block
 * @var string
 */
$tilesSize = get_field('size');

/**
 * The top section of the block
 * @var array
 */
$topSection = get_field('top_section');


$sectionHeader = [
	'title' => $topSection['section_title'],
	'isExternal' => false,
	'url' => $topSection['button_url'],
	'linkLabel' => $topSection['button_label'],
	'hasTitleLink' => true,
];

foreach ($tiles as &$tile) {
	$tile['image'] = [
		'url' => wp_get_attachment_url($tile['image']),
		'id' => $tile['image'],
		'alt' => get_post_meta($tile['image'], '_wp_attachment_image_alt', true),
	];
}

unset($tile);
?>

<div
	class="reactEcommerceTiles"
	data-tiles="<?php echo htmlspecialchars(json_encode($tiles)); ?>"
	data-type="<?php echo $tilesSize; ?>"
	data-section-header="<?php echo htmlspecialchars(json_encode($sectionHeader)); ?>"
>
	<div
		style="display: none"
		class="forbes-gutenberg-block-placeholder"
	>
		<span
			class="dashicon dashicons dashicons-grid-view"
			context="list-view"
		></span>
		<?php echo esc_html__('Ecommerce: Tiles Block', 'FORBES'); ?>
	</div>
</div>