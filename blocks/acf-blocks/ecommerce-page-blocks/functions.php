<?php
add_action('acf/init', 'forbes_init_ecommerce_page_block_types');
function forbes_init_ecommerce_page_block_types()
{

  // Check function exists.
  if (function_exists('acf_register_block_type')) {

    /*
			ACF BLOCKS FOR THE ECOMMERCE PAGE
		*/

    acf_register_block_type(array(
      'name'              => 'ecommerce-tiles-block',
      'title'             => esc_html__('Ecommerce: Tiles Block', 'FORBES'),
      'description'       => esc_html__('Tiles for the Ecommerce Page', 'FORBES'),
      'render_template'   => get_template_directory() . '/blocks/acf-blocks/ecommerce-page-blocks/tiles/index.php',
      'category'          => 'common',
      'icon'              => 'grid-view',
      'mode'        => 'preview',
      'supports'      => array(
        'mode'      => false
      ),
      'keywords'          => array(esc_html__('ecommerce', 'FORBES'), esc_html__('tiles', 'FORBES')),
      'align'        => 'center',
    ));

    acf_register_block_type(array(
      'name'              => 'ecommerce-products-block',
      'title'             => esc_html__('Ecommerce: Products Block', 'FORBES'),
      'description'       => esc_html__('Products for the Ecommerce Page', 'FORBES'),
      'render_template'   => get_template_directory() . '/blocks/acf-blocks/ecommerce-page-blocks/products/index.php',
      'category'          => 'common',
      'icon'              => 'products',
      'mode'        => 'preview',
      'supports'      => array(
        'mode'      => false
      ),
      'keywords'          => array(esc_html__('ecommerce', 'FORBES'), esc_html__('products', 'FORBES')),
      'align'        => 'center',
    ));

    acf_register_block_type(array(
      'name'              => 'ecommerce-tabs-block',
      'title'             => esc_html__('Ecommerce: Tabs Block', 'FORBES'),
      'description'       => esc_html__('Tabs for the Ecommerce Page', 'FORBES'),
      'render_template'   => get_template_directory() . '/blocks/acf-blocks/ecommerce-page-blocks/tabs/index.php',
      'category'          => 'common',
      'icon'              => 'editor-ul',
      'mode'        => 'preview',
      'supports'      => array(
        'mode'      => false
      ),
      'keywords'          => array(esc_html__('ecommerce', 'FORBES'), esc_html__('tabs', 'FORBES')),
      'align'        => 'center',
      'enqueue_assets'  => function () {
        if (!wp_script_is('sticky-tabs-ecommerce', 'enqueued')) {
          wp_enqueue_script('sticky-tabs-ecommerce', get_template_directory_uri() . '/minified-js/ecommerce-page-blocks/tabs/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/ecommerce-page-blocks/tabs/scripts.min.js'), true);
        }
      },
    ));
  }
}
