<?php

/**
 * The top section of the block
 * @var array
 */
$topSection = get_field('top_section');


/**
 * The bottom button of the block
 * @var array
 */
$bottomButton = get_field('bottom_button');

/**
 * Boolean to show advertisement
 * @var boolean
 */
$showAdvertisement = get_field('show_advertisement');

/**
 * The advertisement position of the block
 * @var number
 */
$advertisementPosition = get_field('advertisement_position');

/**
 * The mobile view of the block
 * @var string
 */
$mobileView = get_field('mobile_view');

/**
 * The HTML ID of the block
 * @var string $htmlId
 */
$htmlId = get_field('html_id');

/**
 * How many products to show
 * @var number
 */
$productQuantity = get_field('product_quantity') ?? 8;


$categoryId = get_field('product_category') ?? -1;
$category = ForbesEcommerceSync\Category::getById($categoryId);

$products = (new ForbesEcommerceSync\ProductQuery())
	->setPerPage($productQuantity)
	->setCategory($category) // This can be null
    ->setSortBy('rand') // Set random ordering
	->fetchAll();

$topProductIds = get_field('top_products') ?: [];
$topProducts = ! empty($topProductIds)
	? ForbesEcommerceSync\ProductQuery::byMultipleIds($topProductIds)
	: [];

$products = array_unique(array_merge($topProducts, $products), SORT_REGULAR);

if (count($products) > $productQuantity) {
	$products = array_slice($products, 0, $productQuantity);
}

$sectionHeader = [
	'title' => $topSection['section_title'],
	'isExternal' => false,
	'url' => $topSection['button_url'],
	'linkLabel' => $topSection['button_label'],
	'hasTitleLink' => true,
];

$bottomButton = [
	'label' => $bottomButton['button_label'],
	'url' => $bottomButton['button_url'],
];


?>

<?php if ($htmlId): ?>
	<div
		id="<?php echo $htmlId; ?>"
		style="translateY(-150px);"
	></div>
<?php endif; ?>
<div
	class="reactEcommerceProducts"
	data-section-header="<?php echo htmlspecialchars(json_encode($sectionHeader)); ?>"
	data-bottom-button="<?php echo htmlspecialchars(json_encode($bottomButton)); ?>"
	<?php if ($showAdvertisement): ?>
		data-advertisement-position="<?php echo htmlspecialchars(json_encode($advertisementPosition)); ?>"
	<?php endif; ?>
	data-mobile-view="<?php echo $mobileView; ?>"
	data-products="<?php echo htmlspecialchars(json_encode($products)); ?>"
>
	<div
		style="display: none"
		class="forbes-gutenberg-block-placeholder"
	>
		<span
			class="dashicon dashicons dashicons-products"
			context="list-view"
		></span>
		<?php echo esc_html__('Ecommerce: Products Block', 'FORBES'); ?>
	</div>
</div>
