.accordion-extended {
  width: 100%;

  & .accordions {

    &__item-wrapper {
      cursor: initial;
      padding: $spacing-00;
      border-top: 0.1rem solid var(--wp--preset--color--grey);
      position: relative;
      transition: background-color 0.3s ease;
      border-bottom: 0.1rem solid $color-divider;

      &:only-child {
        border-top: 0.1rem solid $color-divider;
      }

      &:last-child:not(:only-child) {
        border-bottom: none;
      }

      @media (hover: hover) {
        &:hover {
          background-color: var(--wp--preset--color--tertiary);
        }
      }

      &.open {
        .accordions__plus-sign {
          &:before {
            transform: rotate(90deg);
          }
        }
      }
    }
  }
}
