<?php

/**
 * The title of the block
 */
$title = get_field('title');

/**
 * The padding of the block
 */
$padding = get_field('padding');

/**
 * The font of the block
 */
$font_size = get_field('font');

/**
 * Whether the block is on a onepage
 * @var boolean
 */
$is_onepage_block = is_page_template('page-onepage.php');

$allowed_blocks = array('acf/accordion-extended-row-block');

$classes = array(
    ! empty($padding) ? 'accordions--padding' : '',
    ! empty($font_size) ? 'accordions--font-size' : '',
);
?>

<div class="accordion-extended <?= esc_attr(implode(' ', $classes)) ?>">

    <?php if ( ! empty($title)) : ?>

        <h3 class="accordions__title"><?= $title ?></h3>

    <?php endif; ?>

    <div class="accordions__items-wrapper">
        <InnerBlocks allowedBlocks="<?= esc_attr(wp_json_encode($allowed_blocks)); ?>"/>
    </div>
</div>
