jQuery(function ($) {
  const initAccordion = () => {
    const $accordionContainer = $('.accordion-extended');
    if (!$accordionContainer.length) return;

    // The wrapper elements around the questions
    const $accordionItems = $accordionContainer.find('.accordions__item-wrapper');
    
    /**
     * Create the opening effect, and toggle the open class
     */
    $accordionItems.each((index, item) => {
      const $item = $(item);
      const $questionWrapper = $item.find('.accordions__question-wrapper');
      const $answerWrapper = $item.find('.accordions__answer-wrapper');
      const $answer = $item.find('.accordions__answer');
      const $question = $item.children('.accordions__question');

      $questionWrapper.on('click', () => {
        const isOpen = $item.hasClass('open');

        if (!isOpen) {
          // Calculate the open height of the answer area.
          const answerHeight = $answer.outerHeight() + 10;

          // Open the accordion.
          $answerWrapper.css({
            'padding-bottom': 20,
            'max-height': answerHeight + 30,
          });
          $item.addClass('open');
          $question.css('color', 'var(--wp--preset--color--primary)');
        } else {
          // Close the accordion.
          $answerWrapper.css({
            'padding-bottom': 0,
            'max-height': 0,
          });
          $item.removeClass('open');
          $question.css('color', 'var(--wp--preset--color--dark-blue)');
        }
      });
    });
  };

  const isAdmin = $('body').hasClass('wp-admin');

  if (isAdmin && wp) {
    const { select, subscribe } = wp.data;
    const closeListener = subscribe(() => {
      const isReady = select('core/editor').__unstableIsEditorReady();
      if (isReady) {
        closeListener();

        initAccordion();
      }
    });
  } else {
    initAccordion();
  }
});
