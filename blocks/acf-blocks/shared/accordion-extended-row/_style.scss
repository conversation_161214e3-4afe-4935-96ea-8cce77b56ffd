.accordion-extended {
  &.accordions {
    &--padding {
      .accordions__question-wrapper {
        padding: $spacing-00 $spacing-04-16;
      }
    }

    &--font-size {
      .accordions__question {
        font-size: 2rem;
      }
    }
  }

  & .accordions {
    &__question-wrapper {
      display: flex;
      justify-content: space-between;
      padding: 1.4rem $spacing-00;
      cursor: pointer;
      align-items: center;
      height: $spacing-11-64;
    }

    &__plus-sign {
      position: relative;
      width: 1.2rem;
      height: 1.2rem;
      min-width: 1.2rem;
      margin-top: $spacing-00;

      &:before,
      &:after {
        content: '';
        position: absolute;
        background-color: $color-icon-primary;
        transition: transform 0.25s ease-out;
      }

      /* Vertical line */
      &:before {
        top: 0;
        left: 50%;
        width: 0.2rem;
        height: 100%;
        transform: translateX(-50%);
      }

      /* horizontal line */
      &:after {
        top: 50%;
        left: 0;
        width: 100%;
        height: 0.2rem;
        transform: translateY(-50%);
      }
    }

    &__question {
      font-family: $font-archivo;
      margin: $spacing-00;
      text-transform: none !important;
      font-size: 1.6rem;
      line-height: 1.4;
      font-weight: 600;
      color: $color-link-default;
      transition: all 0.3s ease;
    }

    &__answer-wrapper {
      max-height: 0;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    &__answer {
      margin-top: 0.4rem;
      margin-bottom: $spacing-00;
    }

    @media screen and (max-width: map-get($container-max-widths, lg)) {
      &__question-wrapper {
        &:after {
          top: 2.8rem;
          right: 1.6rem;
        }
      }

      &__question {
        padding-right: $spacing-03-12;
      }

      &__answer {
        margin-top: $spacing-04-16;
      }
    }

    @media screen and (max-width: map-get($container-max-widths, md)) {
      padding: $spacing-00 $spacing-00 $spacing-05-20;

      &__questions-wrapper {
        width: 100%;
      }
    }
  }
}

.page-one-page {
  .accordions__answer {
    & > * {
      margin: $spacing-00 $spacing-00 $spacing-08-40;
    }
  }
}
