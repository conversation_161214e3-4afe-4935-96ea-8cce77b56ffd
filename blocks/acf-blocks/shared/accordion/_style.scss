.accordions {
  padding: $spacing-11-64$spacing-00;
  margin-left: 13.014%;
  margin-right: 13.014%;
  width: auto;

  &__title {
    font-size: 4.8rem;
    line-height: 6.6rem;
    margin-bottom: $spacing-02;
    font-family: $font-archivo !important;
  }

  &__item-wrapper {
    cursor: pointer;
    padding: $spacing-05-20 $spacing-00;
    border-top: 0.1rem solid var(--wp--preset--color--grey);
    position: relative;
    transition: background-color 0.3s ease;
    border-bottom: 0.1rem solid $color-divider;

    &:last-child {
      border-bottom: none;
    }

    @media (hover: hover) {
      &:hover {
        background-color: var(--wp--preset--color--tertiary);
      }
    }

    &.open {
      .accordions__plus-sign {
        &:before {
          transform: rotate(90deg);
        }
      }
    }
  }

  &__question-wrapper {
    display: flex;
    justify-content: space-between;
  }

  &__plus-sign {
    position: relative;
    width: 1.4rem;
    height: 1.4rem;
    min-width: 1.4rem;
    margin-top: $spacing-01;

    &:before,
    &:after {
      content: '';
      position: absolute;
      background-color: $color-text-secondary;
      transition: transform 0.25s ease-out;
    }

    /* Vertical line */
    &:before {
      top: 0;
      left: 50%;
      width: 0.2rem;
      height: 100%;
      transform: translateX(-50%);
    }

    /* horizontal line */
    &:after {
      top: 50%;
      left: 0;
      width: 100%;
      height: 0.2rem;
      transform: translateY(-50%);
    }
  }

  &__question {
    margin: $spacing-00;
    text-transform: none !important;
    font-size: 1.6rem;
    line-height: 2.4rem;
    color: $color-text-primary;
    transition: all 0.3s ease;
  }

  &__answer-wrapper {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  &__answer {
    margin-top: $spacing-06-24;
    margin-bottom: $spacing-00;
    font-weight: 300;
    font-size: 1.6rem;
    line-height: 2.4rem;
  }

  &.one-page-block {
    padding-block: $spacing-00;

    .accordions {
      &__item-wrapper {
        &.open {
          .accordions {
            &__question {
              color: $color-text-brand;
            }
          }
        }
        @media (hover: hover) {
          &:hover {
            .accordions {
              &__question {
                color: $color-text-brand;
              }
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl)) {
    margin-left: $spacing-00;
    margin-right: $spacing-00;
    width: 100%;
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    &__title {
      font-size: 3rem;
      line-height: 4rem;
      margin-bottom: $spacing-01;
    }

    &__question-wrapper {
      &:after {
        top: 2.8rem;
        right: 1.6rem;
      }
    }

    &__question {
      padding-right: $spacing-03-12;
    }

    &__answer {
      margin-top: $spacing-04-16;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    padding: $spacing-00 $spacing-00 $spacing-05-20;

    &.one-page-block {
      padding-block: $spacing-00;
    }

    &__questions-wrapper {
      width: 100%;
    }
  }
}
