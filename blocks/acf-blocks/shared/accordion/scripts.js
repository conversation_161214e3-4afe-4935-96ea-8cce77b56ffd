jQuery(function ($) {
  const initAccordion = () => {
    if (!$('.accordions').length) return;

    // The wrapper elements around the questions
    const $questionWrappers = $('.accordions__item-wrapper');

    /**
     * Create the opening effect, and toggle the open class
     */
    $questionWrappers.length &&
      $questionWrappers.each((i, questionWrapper) => {
        $(questionWrapper).on('click', () => {
          const answerWrapper = $(questionWrapper).find('.accordions__answer-wrapper');
          const answer = $(questionWrapper).find('.accordions__answer');
          const height = $(answer).outerHeight() + 10;
          const question = $(questionWrapper).children('.accordions__question');
          const wasOpen = $(questionWrapper).hasClass('open');

          $questionWrappers.removeClass('open');
          $('.accordions__answer-wrapper').css('max-height', 0);

          if (!wasOpen) {
            $(questionWrapper).toggleClass('open');
          }

          if ($(questionWrapper).hasClass('open')) {
            $(answerWrapper).css('max-height', height + 30);
            question.css('color', 'var(--wp--preset--color--primary)');
          } else {
            $(answerWrapper).css('max-height', 0);
            question.css('color', 'var(--wp--preset--color--dark-blue)');
          }
        });
      });
  };

  const isAdmin = $('body').hasClass('wp-admin');

  if (isAdmin && wp) {
    const { select, subscribe } = wp.data;
    const closeListener = subscribe(() => {
      const isReady = select('core/editor').__unstableIsEditorReady();
      if (isReady) {
        closeListener();

        initAccordion();
      }
    });
  } else {
    initAccordion();
  }
});
