@mixin carousel-title-truncation() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: wrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.carousel-section {
  .hide-bookmark {
    .article-meta__button--bookmark {
      display: none;
    }
  }

  .article-meta__button--bookmark {
    height: 1.7rem;
  }

  &.background {
    position: relative;
    background-color: $color-surface-secondary;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      right: 100%;
      width: 100vw;
      height: 100%;
      background-color: $color-surface-secondary;
      z-index: -1;
    }

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 100%;
      width: 100vw;
      height: 100%;
      background-color: $color-surface-secondary;
      z-index: -1;
    }

    .tag--background {
      background-color: $color-surface-secondary;
    }
  }
}

.carousel {
  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }

  .jusitfy-end {
    justify-content: flex-end;
  }

  .w-full {
    width: 100%;
  }

  .pt-6 {
    padding-top: 2.4rem;
  }

  .relative {
    position: relative;
  }

  .hide-controls-mobile {
    @media screen and (max-width: map-get($container-max-widths, md)) {
      .splide__arrows,
      .splide__controls-wrapper {
        display: none;
      }
    }
  }

  &--wrapper {
    margin-bottom: 0 !important;
  }

  .splide__slide {
    margin-right: 2.4rem;
  }

  .splide--line {
    .splide__pagination {
      display: none;
    }
  }

  .splide__arrows {
    position: unset !important;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    inset: 0;
    pointer-events: none;

    button {
      position: relative !important;
      width: 3.2rem !important;
      height: 3.2rem !important;
      transform: unset !important;
      transition-duration: 0.12s;
      transition-timing-function: ease-out;
      pointer-events: auto;

      &.splide__arrow--next {
        right: 0;
        background: linear-gradient(
          90deg,
          rgba(var(--surface-primary-rgb), 0) 0%,
          var(--post-background-color, var(--color-surface-primary)) 80%
        );
      }

      &.splide__arrow--prev {
        left: 0;
        background: linear-gradient(
          90deg,
          var(--post-background-color, var(--color-surface-primary)) 0%,
          rgba(var(--surface-primary-rgb), 0) 80%
        );

        &:before {
          transform: translateY(-50%) rotate(180deg);
        }
      }

      &:hover {
        margin-bottom: 0.2rem;

        &::before {
          background-color: $color-button-primary !important;
        }
      }

      &:disabled {
        opacity: 0.64 !important;

        &::before {
          background-color: $color-button-secondary-disabled !important;
        }
      }

      &::before {
        content: '';
        width: 1.6rem !important;
        height: 1.6rem !important;
        background-color: $color-icon-secondary !important;
        mask-image: url('assets/icons/icon-24-arrow-right.svg');
        -webkit-mask-image: url('assets/icons/icon-24-arrow-right.svg');
        left: 0 !important;
        right: 0 !important;
        margin: auto !important;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 2;
      }

      svg {
        display: none !important;
      }
    }

    &.dots {
      button {
        background-color: $color-divider;
        border-radius: 100%;
      }
    }

    &.in-carousel {
      position: absolute !important;
      display: initial;

      button {
        position: absolute !important;
        transform: translateY(-50%) !important;
        background-color: $color-icon-primary !important;

        &::before {
          background-color: $color-surface-primary !important;
        }

        &.splide__arrow--prev {
          left: 1.6rem !important;
        }

        &.splide__arrow--next {
          right: 1.6rem !important;
        }

        &:disabled {
          background-color: $color-button-primary-disabled !important;

          &::before {
            background-color: $color-surface-primary !important;
          }
        }

        &:hover {
          transform: translateY(calc(-50% - 0.2rem)) !important;

          &::before {
            background-color: $color-surface-primary !important;
            opacity: 1;
          }
        }
      }
    }
  }
}

.splide__controls {
  height: 40px;
  gap: 2.4rem;

  .splide__pagination-wrapper {
    width: 100%;

    .splide__pagination-custom {
      position: relative;
      width: 100%;
      height: 0.2rem;
      background-color: $color-divider;
      margin: 0 !important;

      .splide__pagination-line {
        height: 100%;
        background-color: $color-text-primary;
        transition: width 0.7s ease;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        list-style: none;
      }
    }

    &.dots {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;

      .splide__pagination {
        width: auto;
        position: relative;
        bottom: 0 !important;
        margin: auto;
        display: flex;
        padding: 1.2rem 1.6rem;
        flex-direction: row;
        align-items: center;
        flex-wrap: nowrap;
        background-color: $color-divider;
        border-radius: 20px;

        li {
          width: 1.4rem;
          height: 1.4rem;
          background-color: transparent;
          display: flex;
          align-items: center;
          justify-content: center;

          button {
            width: 0.8rem;
            height: 0.8rem;
            border-radius: 100%;
            position: relative;
            background-color: $color-button-secondary-disabled;
            opacity: 1;

            &:hover {
              background-color: $color-button-primary-disabled !important;
            }

            &.is-prev {
            }

            &.is-active,
            &.is-active ~ li button {
              background-color: $color-text-primary !important;
              transform: none !important;
            }

            &:before {
              width: 1.4rem;
              left: -0.3rem;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md)) {
  .carousel-section {
    .splide__track {
      margin-inline: -1.6rem !important;
      padding-inline: 1.6rem !important;
    }
  }

  .splide__controls {
    .splide__pagination-wrapper {
      &.dots {
        .splide__pagination {
          margin-left: unset;
        }

        &.dots-arrow {
          .splide__pagination {
            margin-left: auto;
          }
        }
      }
    }
  }
}
