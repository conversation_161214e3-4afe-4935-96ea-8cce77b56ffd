<?php

if (empty($args)) {
    return;
}

/**
 * Params:
 * - id
 * - permalink
 * - thumbnail
 * - authorName
 * - authorImageId
 * - authorLink
 * - title
 * - datetime
 */

?>

<div class="comment-card splide__slide">

    <div class="comment-card__wrapper">

        <a href="<?= $args['authorLink'] ?>"
           class="comment-card__image-wrapper">

            <img src="<?= get_the_author_image_url($args['authorId']) ?>"
                 srcset="<?= get_the_author_image_url($args['authorId'], 'comment_card_desktop2x') ?> 2x"
                 alt="<?= $args['authorName'] ?>" class="comment-card__image">

        </a>

        <div class="comment-card__content-wrapper">

            <?php
            if ($args['authorName']) : ?>

                <a href="<?= $args['authorLink'] ?>" class="comment-card__tag-wrapper">
                    <?php
                    $tag = new Tag(text: $args['authorName']);
                    echo $tag->render();
                    ?>
                </a>

            <?php
            endif; ?>

            <a href="<?= $args['permalink'] ?>" class="comment-card__title-wrapper">
                <h4 class="comment-card__title basic-16"><?= $args['title'] ?></h4>
            </a>

        </div>

    </div>

</div>
