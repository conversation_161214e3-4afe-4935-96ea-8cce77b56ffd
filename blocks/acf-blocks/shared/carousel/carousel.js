document.addEventListener('DOMContentLoaded', function () {
  (function ($) {
    if (!$('.carousel').length) return;

    const newsSliders = $('.carousel--wrapper');

    const initSliders = () => {
      newsSliders.each((index, slider) => {
        const type = $(slider).data('type');
        const scrollBehavior = $(slider).data('scroll-behavior');
        const infinite = $(slider).data('infinite');
        const typeControls = $(slider).data('type-controls');
        const contentLayout = $(slider).data('content-layout');
        let perPage = 4;

        const isSingle = $(slider).parent().hasClass('carousel--single');

        if (isSingle) {
          perPage = 3;
        }

        if (contentLayout.includes('articles')) {
          perPage = parseInt(contentLayout.slice(-1));

          if (isSingle && perPage === 4) {
            perPage = 3;
          }
        }

        if (['comment_day', 'most_read', 'podcast'].includes(contentLayout)) {
          perPage = 3;
        }

        const splide = new Splide(slider, {
          type: infinite ? 'loop' : 'slide',
          drag: scrollBehavior === 'natural' ? 'free' : true,
          flickMaxPages: scrollBehavior === 'discrete' ? 1 : 3,
          flickPower: scrollBehavior === 'discrete' ? 25 : 300,
          perPage: perPage,
          perMove: 1,
          pagination: true,
          arrows: true,
          focus: 'number',
          omitEnd: true,
          speed: 300,
          breakpoints: {
            992: {
              perPage: 2.5,
              arrows: true,
              pagination: true,
            },
            768: {
              perPage: 1.5,
              arrows: true,
            },
          },
          classes:
            type === 'b'
              ? {
                  arrows: 'splide__arrows splide__arrows--gray',
                  arrow: 'splide__arrow splide__arrow--bn',
                  pagination: 'splide__pagination splide__pagination--bn',
                }
              : {},
        });

        splide.on('mounted move scrolled', () => {
          const activeIndex = splide.index;

          if (typeControls === 'line') {
            const customPaginationElement = $(slider).find('.splide__pagination-custom');

            if (!customPaginationElement.length) return;

            const maxPages = splide.Components.Pagination.items.length;
            const activeElement = customPaginationElement.find('.splide__pagination-line');

            const widthPercentage = ((activeIndex + 1) / maxPages) * 100;

            activeElement.css('width', `${widthPercentage}%`);
          }
        });

        splide.mount();

        window.splideSliders = window.splideSliders || [];
        window.splideSliders.push(splide);
      });
    };

    const isAdmin = $('body').hasClass('wp-admin');

    if (isAdmin && wp) {
      const { select, subscribe } = wp.data;
      const closeListener = subscribe(() => {
        const isReady = select('core/editor').__unstableIsEditorReady();
        if (isReady) {
          closeListener();

          initSliders();
        }
      });
    } else {
      initSliders();
    }
  })(jQuery);
});
