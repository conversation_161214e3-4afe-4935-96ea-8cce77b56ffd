<?php

require_once __DIR__ . '/CarouselCardController.php';
require_once __DIR__ . '/breaking-news-card/BreakingNewsCardController.php';
require_once __DIR__ . '/comment-day-card/CommentDayCardController.php';
require_once __DIR__ . '/event-card/EventCardController.php';
require_once __DIR__ . '/most-read-card/MostReadCardController.php';
require_once __DIR__ . '/opinions-card/OpinionsCardController.php';
require_once __DIR__ . '/podcast-card/PodcastCardController.php';
require_once __DIR__ . '/articles-card/ArticlesCardController.php';

if (empty($block)) {
    return;
}

$carouselFields = get_fields();

$content        = $carouselFields['content'];
$contentType    = $content['content_type'];
$contentFilters = $content['filters_' . $contentType];
$contentPinned = $content['pinned_posts'];
$contentLayout  = $content['content_layout'];

$customSettings = $carouselFields['custom_carousel_settings'] ?? [];
if ( ! $customSettings) {
    $customSettings = array();
}

$behavior         = $carouselFields['behavior'];
$behaviorLimit    = $behavior['number_of_items'];
$behaviorScroll   = $behavior['scroll_behavior'];
$behaviorInfinite = $behavior['infinite'];

$appearance                   = $carouselFields['appearance'];
$appearanceTypeControls = $appearance['type_of_controls'];
$appearancePadding            = $appearance['padding'];
$appearanceHideControlsMobile = $appearance['hide_controls_on_mobile_breakpoint'];
$appearanceBackgroundColor = $appearance['background_color'];

$taxQuery = array();
foreach ($contentFilters as $key => $value) {
    if ($key === 'filter_insertion') {
        if ($value === 'life') {
            $taxQuery[] = array(
                'taxonomy' => 'life_category',
                'operator' => 'EXISTS',
            );
        }

        continue;
    }
    if ($value) {
        $taxonomy   = strtolower(str_replace('filter_', '', $key));
        $taxQuery[] = array(
            'taxonomy' => $taxonomy,
            'field'    => 'id',
            'terms'    => $value,
        );
    }
}

// TODO: Refactor this to use pre_get_posts propably for all event queries
if ($contentType === 'event') {
    $starting_date_field_name = 'cz' === COUNTRY ? 'eventStartDate' : 'starting_date';
    $eventQuery               = array(
        'orderby'    => 'meta_value',
        'order'      => 'ASC',
        'meta_key'   => $starting_date_field_name,
        'meta_query' => array(
            array(
                'key'     => $starting_date_field_name,
                'value'   => date('Ymd'),
                'type'    => 'DATE',
                'compare' => '>='
            )
        )
    );
}

$args = array(
    'post_type'      => $contentType,
    'numberposts' => $behaviorLimit,
    'tax_query'      => $taxQuery,
);

if (isset($eventQuery)) {
    $args = array_merge($args, $eventQuery);
}

$carouselPosts = get_posts($args);

if ($contentPinned) {
    foreach ($contentPinned as $pinnedPost) {
        $index                 = $pinnedPost['poradie'] - 1;
        $carouselPosts[$index] = get_post($pinnedPost['post']);
    }
}

?>
    <section class="carousel-section <?= $appearanceBackgroundColor ? 'background' : '' ?>" style="padding: <?= $appearancePadding ?>px 0;">
        <?php
        $firstTaxQuery = array_filter($taxQuery, static function ($item) {
            return ! empty($item['terms']);
        });
        get_template_part('template-parts/home-page/category-header/index', null, ['category' => ! empty($firstTaxQuery) ? $firstTaxQuery[0]['terms'][0] : null]); ?>

        <div class="carousel <?= is_single() ? 'carousel--single' : '' ?>">
            <div id="carousel" class="carousel--wrapper splide
            <?= $appearanceTypeControls === 'line' ? 'splide--line' : '' ?>
            <?= $appearanceHideControlsMobile ? 'hide-controls-mobile' : '' ?>" aria-label=""
                 data-type="b"
                 data-scroll-behavior="<?= $behaviorScroll ?>"
                 data-infinite="<?= $behaviorInfinite ?>"
                 data-type-controls="<?= $appearanceTypeControls ?>"
                 data-content-layout="<?= $contentLayout ?>"
            >
                <div class="splide__track">
                    <div class="splide__list">
                        <?php
                        foreach ($carouselPosts as $key => $post) {
                            $controller = match ($contentLayout) {
                                'comment_day' => new CommentDayCardController($post, $key),
                                'event' => new EventCardController($post, $key),
                                'most_read' => new MostReadCardController($post, $key),
                                'opinions' => new OpinionsCardController($post, $key),
                                'podcast' => new PodcastCardController($post, $key),
                                'articles_1_4', 'articles_1_3', 'articles_1_2' => new ArticlesCardController($post, $key, $customSettings),
                                default => new BreakingNewsCardController($post, $key),
                            };

                            $controller->render();
                        }
                        ?>
                    </div>

                    <?php
                    if ($appearanceTypeControls === 'dots_arrows') { ?>
                        <div class="splide__arrows dots in-carousel"></div>
                        <?php
                    } ?>
                </div>

                <div class="splide__controls-wrapper w-full pt-6">
                    <div class="splide__controls relative flex items-center jusitfy-end w-full">
                        <div class="splide__pagination-wrapper <?= $appearanceTypeControls === 'line' ? '' : 'dots' ?> <?= $appearanceTypeControls === 'dots_arrows' ? 'dots-arrow' : ''
                        ?>">
                            <ul class="<?= $appearanceTypeControls === 'line' ? 'splide__pagination-custom' : 'splide__pagination' ?>">
                                <?= $appearanceTypeControls === 'line' ? '<li class="splide__pagination-line"></li>' : '' ?>
                            </ul>
                        </div>
                        <?php
                        if ($appearanceTypeControls !== 'dots_arrows') { ?>
                            <div class="splide__arrows <?= $appearanceTypeControls !== 'line' ? 'dots' : '' ?>"></div>
                            <?php
                        } ?>
                    </div>
                </div>

            </div>
        </div>
    </section>

<?php

// Enqueue the carousel script
// Check fit if carousel-js is already enqueued
if ( ! wp_script_is('carousel-js')) {
    wp_enqueue_script(
        'carousel-js',
        get_template_directory_uri() . '/blocks/acf-blocks/shared/carousel/carousel.js'
        ,
        array('jquery'),
        null,
        true
    );
}
