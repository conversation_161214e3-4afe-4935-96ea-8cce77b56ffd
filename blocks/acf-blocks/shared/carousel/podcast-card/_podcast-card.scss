.carousel-section {
  .podcast-card {
    width: 350px !important;

    &:hover {
      .podcast-card__title {
        text-decoration: underline;
      }
    }

    &__image-wrapper {
      width: 100%;
      height: 100%;
      max-width: 10rem;
      max-height: 10rem;
      flex-shrink: 0;

      img {
        border-radius: 0.8rem;
      }
    }

    &__details {
      gap: 1.6rem;

      time {
        color: $color-text-secondary !important;
        font-variant-numeric: lining-nums tabular-nums !important;
        font-family: Archivo !important;
        font-size: 1.4rem !important;
        font-style: normal !important;
        font-weight: 600 !important;
        line-height: 1.4 !important;
      }
    }

    &__tag-wrapper {
      margin-bottom: 0.4rem;

      .tag__text {
        line-height: 1.2 !important;
      }
    }

    &__title {
      display: -webkit-box !important;
      -webkit-box-orient: vertical !important;
      -webkit-line-clamp: 3 !important;

      overflow: hidden !important;
      color: $color-link-default !important;
      font-variant-numeric: lining-nums tabular-nums !important;
      text-overflow: ellipsis !important;
      font-family: $font-archivo !important;
      font-size: 1.6rem !important;
      font-style: normal !important;
      font-weight: 600 !important;
      line-height: 1.4 !important;

      margin-bottom: 0.4rem;

      font-variation-settings: unset !important;
    }

    &__length {
      display: flex;
      align-items: center;
      gap: 0.4rem;

      color: $color-text-secondary !important;
      font-variant-numeric: lining-nums tabular-nums !important;
      font-family: $font-archivo !important;
      font-size: 1.4rem !important;
      font-style: normal !important;
      font-weight: 600 !important;
      line-height: 1.4 !important;
    }
  }
}
