<?php

abstract class CarouselCardController
{
    private WP_Post $post;

    private int|string $index;

    private array $customSettings;

    abstract protected function getTemplate(): string;

    abstract protected function getParams(\WP_Post $post, int|string $index): array;

    private function getSharedParams(\WP_Post $post, int|string $index): array
    {
        return [
            'index'          => $index + 1,
            'id'             => $post->ID,
            'permalink'      => get_permalink($post),
            'thumbnail'      => get_the_post_thumbnail_url($post),
            'authorId'       => get_the_main_author($post->ID)->term_id,
            'authorName'     => get_the_main_author($post->ID)->name,
            'authorImageId'  => $this->getAuthorImageId($post),
            'authorLink'     => get_the_main_author($post->ID)->archive_link,
            'title'          => $post->post_title,
            'datetime'       => get_the_date('Y-m-d\TH:i', $post),
            'terms'          => get_the_terms($post, CATEGORY_TYPE),
            'primaryTerm'    => $this->getPrimaryTerm($post),
            'customSettings' => $this->customSettings,
        ];
    }

    final public function render(): void
    {
        $template = $this->getTemplate();
        $params   = $this->getAllParams();

        get_template_part("blocks/acf-blocks/shared/carousel/$template/$template", $template, $params);
    }

    private function getAllParams(): array
    {
        return array_merge(
            $this->getParams($this->post, $this->index),
            $this->getSharedParams($this->post, $this->index)
        );
    }

    public function __construct(\WP_Post $post, int|string $index, $customSettings = [])
    {
        $this->post  = $post;
        $this->index = $index;
        $this->customSettings = $customSettings;
    }

    protected function getPrimaryTerm(WP_Post $post): ?WP_Term
    {
        return frontend_get_primary_tag($post->ID);
    }

    protected function getAuthorImageId($post)
    {
        $main_author = get_the_main_author($post->ID);

        return (static function () use ($main_author) {
            $image_id = get_field('profileImage', 'coauthor_' . $main_author->term_id);

            if ( ! $image_id) {
                $image_id = get_field('profile_picture', 'user_' . $main_author->term_id);
            }

            return $image_id;
        })();
    }
}
