<?php

if (empty($args)) {
    return;
}

/**
 * The post id used for the article card
 * @var int
 */
$id = $args['post_id'] ?? null;

$customSettings        = $args['customSettings'] ?? [];
$customShowTitle       = $customSettings['show_title'] ?? true;
$customShowReadingTime = $customSettings['show_reading_time'] ?? true;
$customShowAuthor      = $customSettings['show_author'] ?? true;
$customShowCategory    = $customSettings['show_category'] ?? true;
$customShowDate        = $customSettings['show_date'] ?? true;
$customShowBookmark    = $customSettings['show_bookmark'] ?? true;

/**
 * The category object of the posts to show
 * @var object
 * */
$main_term = $id ? frontend_get_primary_tag($id) : null;

/**
 * The category ID of the posts to show
 * @var int
 */
$main_term_id = $main_term ? $main_term->term_id : null;

/**
 * The image of the posts to show (only guide page)
 * @var string
 */
$post_image = $args['post_image'] ?? null;

/**
 * The URL for the placeholder image (in case no featured image is set)
 * @var string
 */
$placeholder_image_url = get_field('thumbnail_placeholder', 'option') ?? '';

$badge = false;
if ($main_term_id) {
    $badge = get_field('toggle_badge', CATEGORY_TYPE . '_' . $main_term_id);
}

/**
 * The type of the card's visual represenation (small, normal, large, featured)
 * @var string
 */
$article_card_type = $args['article_card_type'] ?? 'normal';

/**
 * The premium tag ID
 * @var string
 */
$premium_tag_id = get_field('premium_tag', 'option') ?: '';

$image_size = 'article_card_desktop_default';
switch ($article_card_type) {
    case 'small':
        $image_size = 'article_card_desktop_small';
        break;
    case 'large':
    case 'archive':
    case 'featured':
        $image_size = 'article_card_desktop_featured';
        break;
    case 'featured-large':
        $image_size = 'article_card_desktop_featured_large';
        break;
    default:
        $image_size = 'article_card_desktop_default';
        break;
}

if ($id) {
    $post_thumbnail_url   = get_the_post_thumbnail_url($id, $image_size);
    $post_thumbnail2x_url = get_the_post_thumbnail_url($id, $image_size . '2x');
    $lifeTag = get_field('life', 'option')['tag'] ?? array();

    $article_data = array(
        'post_id'            => $id,
        'post_category'      => $main_term ? $main_term->name : null,
        'post_category_id'   => $main_term_id ?? null,
        'post_category_link' => $main_term ? get_term_link($main_term_id) : null,
        'post_title'         => get_the_title($id),
        'post_link'          => get_field('redirect_url', $id) ?: get_the_permalink($id),
        'post_excerpt'       => get_post_excerpt($id),
        'post_image'         => $post_image ?: $post_thumbnail_url ?: $placeholder_image_url,
        'post_image_2x'      => $post_image ?: $post_thumbnail2x_url,
        'post_read_time'     => get_post_read_time($id),
        'badge'              => $badge,
        'index'              => $args['most_read'] ?? null,
        'is_premium' => ! empty($premium_tag_id) && has_term($premium_tag_id, CATEGORY_TYPE, $id),
        'is_life'    => ! empty($lifeTag) && has_term($lifeTag, CATEGORY_TYPE, $id),
    );
}

if ('cz' === COUNTRY) {
    if (!has_post_thumbnail($id)) {
        $primaryImageId = get_field('primaryImage', $id);

        $article_data['post_image'] = $primaryImageId
            ? wp_get_attachment_image_url($primaryImageId, $image_size)
            : $placeholder_image_url;

        $article_data['post_image_2x'] = $primaryImageId
            ? wp_get_attachment_image_url($primaryImageId, $image_size . '2x')
            : $article_data['post_image'];
    }
}

/**
 * Whether the card is in the search results
 * @var boolean
 */
$search = $args['search'] ?? false;

/**
 * The location of the article card
 * @var string
 */
$location = $args['location'] ?? '';

/**
 * Show tag instead of category
 * @var boolean
 */
$show_tag = $args['show_tag'] ?? false;

/**
 * Hide the read time on the card
 * @var boolean
 */
$hide_read_time = $args['hide_read-time'] ?? false;

/**
 * Show excert on the article card
 * @var boolean
 */
$show_excerpt = $args['show_excerpt'] ?? false;

/**
 * Whether the card is in a slider
 * @var boolean
 */
$is_slider = $args['slider'] ?? false;

/**
 * The design of the card
 * @var string
 * */
$design = get_field('article_card_design', 'option');

/**
 * The author's name and link of the article
 * @var string
 */
$author      = get_the_main_author($id) ?? null;
$author_name = get_the_main_author($id)->name ?? null;
$author_link = get_the_main_author($id)->archive_link ?? null;

/**
 * Get custom additional classes
 */
$classes = (function () use ($article_card_type, $search, $args) {
    $additional_classes = '';

    if ( ! in_array($article_card_type, ['normal', 'archive'])) {
        $additional_classes .= ' article-card--' . $article_card_type;
    }

    if ($search) {
        $additional_classes .= ' article-card--search';
    }

    if (isset($args['most_read'])) {
        $additional_classes .= ' splide__slide';
    }

    return $additional_classes;
})();

/**
 * Get the badge toggle for the specified category term
 * @var array|false
 */
$badge_toggle             = get_field('badge', CATEGORY_TYPE . '_' . $main_term_id) ?? false;
$contains_remp_lock_block = has_block('remp-paywall/lock', $id);

/**
 * Get the URL of the badge icon from the badge toggle, or null
 * @var string|null
 */
$label_icon = null;
if (is_array($badge_toggle) && isset($badge_toggle['icon'])) {
    $label_icon = wp_get_attachment_url($badge_toggle['icon']) ?? null;
}
if ($contains_remp_lock_block) {
    $label_icon = get_template_directory_uri() . '/assets/icons/ds2024/icon-diamond-empty.svg';
}

/**
 * Get the label from the badge toggle, or the post category
 * @var string
 */
$label = $article_data['post_category'];
if (is_array($badge_toggle) && isset($badge_toggle['label'])) {
    $label = $badge_toggle['label'];
}

$x                  = $args['x'] ?? null;
$MAX_PRIORITY_INDEX = 3;

$priority_fetch = $x !== null && $x < $MAX_PRIORITY_INDEX ? 'fetchpriority="high"' : 'loading="lazy"';

?>

<div class="article-card splide__slide <?php
echo $classes; ?>">

    <div class="article-card__image-wrapper">

        <a href="<?php
        echo $article_data['post_link']; ?>" class="article-card__image">
            <img
				src="<?= esc_attr($article_data['post_image']); ?>"
				alt="<?= esc_attr($article_data['post_title']); ?>"
				<?php if (!empty($article_data['post_image_2x'])) : ?>
					srcset="<?= esc_attr($article_data['post_image_2x']); ?> 2x"
				<?php endif; ?>
                <?= $priority_fetch; ?>
			>
        </a>

        <?php
        if ($customShowCategory) : ?>
            <div class="article-card__tag-wrapper article-card__tag-wrapper--image">
            <?php
            $tag = new Tag(
                text: $label,
                icon: $label_icon,
                hasBackground: true,
                url: $article_data['post_category_link'],
            );
            echo $tag->render();
            ?>
        </div>
        <?php
        endif; ?>

    </div>

    <div class="article-card__content-wrapper<?php
    is_singular('guide') ? " h-d--flex h-flex--column-reverse" : ''; ?>">

        <?php
        if ($customShowTitle) : ?>
        <div class="article-card__title-wrapper">
            <div class="article-card__tag-wrapper article-card__tag-wrapper--content">
                <?php
                $tag = new Tag(
                    text: $label,
                    icon: $label_icon,
                    hasBackground: true,
                    url: $article_data['post_category_link'],
                );
                echo $tag->render();
                ?>
            </div>

            <?php
            if ($article_data['post_link'] && $article_data['post_title']) : ?>

                <a href="<?php
                echo $article_data['post_link']; ?>" class="article-card__title-link">
                    <?php
                    if ($article_card_type === 'featured-large'): ?>
                        <h4 class="h4-noto heading"><?php
                            echo $article_data['post_title']; ?></h4>
                    <?php
                    else: ?>
                        <h6 class="h6-noto heading"><?php
                            echo $article_data['post_title']; ?></h6>
                    <?php
                    endif; ?>
                </a>

            <?php
            endif; ?>
        </div>
        <?php
        endif; ?>

        <?php
        if ((in_array($article_card_type, ['featured-large']) || $show_excerpt === true)) : ?>

            <a class="article-card__excerpt article-excerpt basic-16<?php
            if (in_array($article_card_type, ['featured-large'])) : ?> article-excerpt--large <?php
            else : ?> article-excerpt--small <?php
            endif; ?>" href="<?php
            echo $article_data['post_link']; ?>"><?php
                echo strip_tags($article_data['post_excerpt']); ?></a>

        <?php
        endif; ?>

        <?php
        if ($article_card_type !== 'large') : ?>

            <div class="article-meta__wrapper <?= ! $customShowBookmark ? ' hide-bookmark' : '' ?>
<?= ( ! $customShowBookmark && ! $customShowDate && ! $customShowReadingTime && ! $customShowAuthor) ? ' hidden' : '' ?>
">
                <?php
                $authors     = get_all_the_authors($article_data['post_id']);
                $articleData = get_article_data($article_data['post_id']);

                if ( ! $customShowReadingTime) :
                    $articleData['readTime'] = false;
                endif;

                if ( ! $customShowDate) :
                    $articleData['publishDate'] = false;
                endif;

                if ( ! $customShowAuthor) :
                    $authors = false;
                endif;
                ?>

                <div class="reactArticleMeta" data-authors="<?php
                echo htmlspecialchars(json_encode($authors), ENT_QUOTES, 'UTF-8'); ?>" data-article="<?php
                echo htmlspecialchars(json_encode($articleData), ENT_QUOTES, 'UTF-8'); ?>"></div>
            </div>

        <?php
        endif; ?>


        <?php
        if ($article_card_type === 'large') : ?>

            <div class="article-card__details-wrapper">

                <?php
                if ($article_data['post_category_link'] && $article_data['post_category'] && $article_data['post_read_time']) : ?>

                    <?php
                    get_template_part(
                        'template-parts/article-details/index',
                        null,
                        ['id' => $id, 'search' => $search, 'hide_read_time' => $hide_read_time, 'article_data' => $article_data, 'design' => $design]
                    ); ?>

                <?php
                endif; ?>

            </div>

        <?php
        endif; ?>

    </div>

</div>
