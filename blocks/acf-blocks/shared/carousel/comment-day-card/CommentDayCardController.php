<?php


class CommentDayCardController extends CarouselCardController
{
    protected function getTemplate(): string
    {
        return 'comment-day-card';
    }

    protected function getParams(WP_Post $post, int|string $index): array
    {
        return [
            'labelIcon'   => $this->getLabelIcon($post),
        ];
    }

    private function getBadge(WP_Post $post): ?string
    {
        $badge = get_field('toggle_badge', CATEGORY_TYPE . '_' . $this->getPrimaryTerm($post)->term_id);

        return $badge ?: null;
    }

    private function getLabelIcon(WP_Post $post): string|null|bool
    {
        $label_icon               = null;
        $badge_toggle             = $this->getBadgeToggle($post);
        $contains_remp_lock_block = has_block('remp-paywall/lock', $post);


        if (is_array($badge_toggle) && isset($badge_toggle['icon'])) {
            $label_icon = wp_get_attachment_url($badge_toggle['icon']) ?? null;
        }
        if ($contains_remp_lock_block) {
            $label_icon = get_template_directory_uri() . '/assets/icons/ds2024/icon-diamond-empty.svg';
        }

        return $label_icon;
    }

    private function getBadgeToggle(WP_Post $post): mixed
    {
        $badge_toggle = get_field('badge', CATEGORY_TYPE . '_' . $this->getPrimaryTerm($post)->term_id) ?? false;

        return $badge_toggle ?: null;
    }
}
