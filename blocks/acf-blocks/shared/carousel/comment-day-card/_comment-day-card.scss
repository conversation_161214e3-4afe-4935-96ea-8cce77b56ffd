.comment-day-card {
  max-width: 28.8rem !important;
  width: 100% !important;

  &__wrapper {
    position: relative;
    display: flex;
    width: 100%;
    padding: 1.6rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.8rem;
    text-decoration: unset;
    background-color: $color-surface-primary;

    transition-property: all;
    transition-duration: 160ms;
    transition-timing-function: ease-in-out;
  }

  &__top {
    display: flex;
    align-items: center;
    gap: 0.8rem;

    .tag {
      padding-right: 0;
      padding-bottom: 0;
      padding-top: 0;
      height: 0;
      margin: 0 !important;

      &__text {
        line-height: 1.2;
      }
    }
  }

  &__date {
    font-size: 1.2rem;
    font-weight: 600;
    color: $color-text-secondary;
  }

  &__middle {
    text-decoration: none !important;

    &:hover {
      .comment-day-card__title {
        text-decoration: underline !important;
      }
    }
  }

  &__title {
    @include carousel-title-truncation;
    color: $color-text-primary;

    font-family: $font-noto-serif;
    font-size: 1.8rem;
    font-style: normal;
    font-weight: 600;
    line-height: 1.32;
  }

  &__bottom {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    text-decoration: none !important;

    &:hover {
      .comment-day-card__author-name {
        text-decoration: underline !important;
      }
    }
  }

  &__image {
    width: 3.2rem;
    height: 3.2rem;
    object-fit: cover;
    border-radius: 100%;
  }

  &__author-name {
    font-size: 1.4rem;
    font-weight: 600;
    color: $color-link-default;
    text-decoration: none !important;
  }
}
