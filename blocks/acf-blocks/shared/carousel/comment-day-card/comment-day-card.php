<?php

if (empty($args)) {
    return;
}

/**
 * Params:
 * - id
 * - permalink
 * - thumbnail
 * - authorName
 * - authorImageId
 * - authorLink
 * - title
 * - datetime
 * - terms
 * - primaryTerm
 * - badge
 * - badgeToggle
 * - labelIcon
 */

?>
<div class="comment-day-card splide__slide">
    <div class="comment-day-card__wrapper">

        <div class="comment-day-card__top">
            <?php
            $tag = new Tag(
                text: frontend_get_primary_tag($args['id'])->name ?? $args['terms'][0]->name,
                icon: $args['labelIcon'],
                url: get_term_link(frontend_get_primary_tag($args['id']) ?? $args['terms'][0]),
                hasBackground: true,
            );
            echo $tag->render();
            ?>

            <time class="comment-day-card__date" datetime="<?php
            echo get_the_date('Y-m-d\TH:i', $args['id']); ?>"><?php
                echo get_the_date('j. n. Y', $args['id']); ?></time>
        </div>

        <a href="<?= $args['permalink'] ?>" class="comment-day-card__middle">
            <h6 class="comment-day-card__title"><?= $args['title'] ?></h6>
        </a>

        <a href="<?= $args['authorLink'] ?>" class="comment-day-card__bottom">
            <img src="<?= get_the_author_image_url($args['authorId']) ?>"
                 srcset="<?= get_the_author_image_url(
                     $args['authorId'],
                     'comment_card_desktop2x'
                 ) ?> 2x"
                 alt="<?= $args['authorName'] ?>" class="comment-day-card__image">

            <div class="comment-day-card__author">
                <span class="comment-day-card__author-name"><?= $args['authorName'] ?></span>
            </div>
        </a>

    </div>
</div>
