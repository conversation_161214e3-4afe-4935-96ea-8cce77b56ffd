<?php

add_action('acf/init', 'forbes_init_shared_block_types');

function forbes_init_shared_block_types(): void
{
    // Check function exists.
    if (function_exists('acf_register_block_type')) {
        // register the CAROUSEL block
        acf_register_block_type(array(
            'name'            => 'carousel-block',
            'title'           => esc_html__('Carousel', 'FORBES'),
            'description'     => '',
            'render_template' => get_template_directory() . '/blocks/acf-blocks/shared/carousel/carousel.php',
            'category'        => 'common',
            'icon'            => 'admin-post',
            'mode'            => 'preview',
            'keywords'        => array(esc_html__('carousel', 'FORBES')),
            'align'           => 'center',
        ));

        // register the ACCORDION block
        acf_register_block_type(array(
            'name'            => 'accordion-extended-block',
            'title'           => esc_html__('Accordion', 'FORBES'),
            'description'     => '',
            'render_template' => get_template_directory() . '/blocks/acf-blocks/shared/accordion-extended/index.php',
            'category'        => 'common',
            'icon'            => 'admin-post',
            'mode'            => 'preview',
            'keywords'        => array(esc_html__('accordion', 'FORBES')),
            'enqueue_script'  => get_template_directory_uri() . '/blocks/acf-blocks/shared/accordion-extended/scripts.js',
            'align'           => 'center',
            'supports'        => array(
                'jsx'   => true,
                'align' => true,
                'mode'  => true,
            ),
        ));

        // register the ACCORDION ROW block
        acf_register_block_type(array(
            'name'            => 'accordion-extended-row-block',
            'title'           => esc_html__('Accordion Row', 'FORBES'),
            'description'     => '',
            'render_template' => get_template_directory() . '/blocks/acf-blocks/shared/accordion-extended-row/index.php',
            'category'        => 'common',
            'icon'            => 'admin-post',
            'mode'            => 'preview',
            'keywords'        => array(esc_html__('accordion', 'FORBES')),
            'align'           => 'center',
            'supports'        => array(
                'jsx'   => true,
                'align' => true,
                'mode'  => true,
            ),
        ));

	    // register the SPACING block
	    acf_register_block_type(array(
		    'name'            => 'spacing-block',
		    'title'           => esc_html__('Spacing Responsive', 'FORBES'),
		    'description'     => esc_html__('Add custom spacing between blocks for mobile and desktop', 'FORBES'),
		    'render_template' => get_template_directory() . '/blocks/acf-blocks/shared/spacing/spacing.php',
		    'category'        => 'layout',
		    'icon'            => 'editor-spacing',
		    'mode'            => 'preview',
		    'keywords'        => array(esc_html__('spacing', 'FORBES'), esc_html__('gap', 'FORBES')),
		    'align'           => 'full',
		    'supports'        => array(
			    'align' => array('full', 'wide'),
		    ),
	    ));
    }
}
