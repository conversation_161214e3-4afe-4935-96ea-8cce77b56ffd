jQuery(function ($) {
  const galleries = Array.from(document.querySelectorAll('.gallery-with-link'));

  // Exit script is no gallery block is present
  if (!galleries.length) return;

  // If we are on the HU site
  const isHun = $('body').data('locale') === 'hu';

  let originalURL = window.location.href;

  class Gallery {
    constructor(gallery, index) {
      this.scrollPos = 0;
      this.index = index;
      this.gallery = gallery;
      this.numberOfImages = this.gallery.dataset.photos;
      this.renderedImages = Array.from(this.gallery.querySelectorAll('.gallery-with-link__image-wrapper'));
      this.remainingImages = this.gallery.querySelectorAll('.remaining-images');
      this.modal = this.gallery.querySelector('.overlay');
      this.backButton = this.modal ? this.modal.querySelector('.overlay__back-button') : null;

      if (this.index > 0) {
        const splitUrlString = this.gallery.dataset.urlString.split('=');
        splitUrlString[0] += '_' + this.index + '=';
        this.urlString = splitUrlString[0];

        const slides = $(this.gallery).find('.swipebox-gallery-block');
        $(slides).each((i, slide) => {
          $(slide).addClass('swipebox-gallery-block_' + this.index);
        });
      } else {
        this.urlString = this.gallery.dataset.urlString;
      }

      this.initSwipebox();
      this.setRemainingImages();

      let timeout;
      window.addEventListener('resize', () => {
        clearTimeout(timeout);
        setTimeout(() => {
          this.setRemainingImages();
        }, 300);
      });

      if (window.location.href.includes(this.urlString)) {
        this.openGalleryIfRequestedInUrl();
      }

      /*
				Opening the modal on click of any image
			*/
      if (this.modal) {
        // eslint-disable-next-line no-undef
        const macyInstance = Macy({
          container: this.modal.querySelector('.overlay__grid'),
          columns: 3,
          margin: {
            x: 30,
            y: 20,
          },
          breakAt: {
            576: 1,
          },
          trueOrder: true,
        });

        let timeout;
        window.addEventListener('resize', () => {
          clearTimeout(timeout);
          setTimeout(() => {
            macyInstance.recalculate();
          }, 300);
        });

        this.renderedImages.forEach((image) => {
          image.addEventListener('click', () => this.openModal());
        });

        this.backButton.addEventListener('click', () => this.closeModal());
      }
    }

    /*
		Initialize the swipebox lib on the gallery's images
		*/
    initSwipebox() {
      const className = this.index > 0 ? '.swipebox-gallery-block_' + this.index : '.swipebox-gallery-block';

      $(`${className}`).swipebox(className, {
        removeBarsOnMobile: false,
        nextSlide: (index) => {
          isHun && this.toggleAdVisibility(index);
          this.changeURL(index + 1);
        },
        prevSlide: (index) => {
          isHun && this.toggleAdVisibility(index);
          this.changeURL(index + 1);
        },
        afterOpen: (index) => {
          this.changeURL(index + 1);
        },
        afterClose: () => {
          isHun && this.toggleAdVisibility(0);
          this.changeURL(-1);
        },
      });
    }

    /*
		Set the number of hidden images based on the amount shown
		*/
    setRemainingImages() {
      this.remainingImages.forEach((images) => {
        images.innerHTML = window.innerWidth < 768 ? this.numberOfImages - 2 : this.numberOfImages - 3;
      });
    }

    /*
		Show/hide the ad at 4th place in the slider
		*/
    toggleAdVisibility(index) {
      if (index === 3) {
        setTimeout(() => {
          $(this.gallery).find('.gallery-with-link__ad-container').addClass('visible');
          if (window.innerWidth < 992) {
            $('main.single').append('<div class="gallery-with-link__swipe"></div>');
          }
        }, 200);
      } else {
        $(this.gallery).find('.gallery-with-link__ad-container').removeClass('visible');
        $(this.gallery).find('.gallery-with-link__swipe').remove();
      }
    }

    changeURL(index) {
      if (index === -1) {
        history.replaceState(null, null, originalURL);
      } else {
        history.replaceState(null, null, originalURL + this.urlString + index);
        isHun && pp_gemius_hit('.RCaAetGtwMU4f.XJ8G_.6eirjvUCgMIItJwuDNdxRj.a7');
      }
    }

    openModal() {
      setTimeout(() => {
        document.documentElement.classList.add('no-scroll');
      }, 500);

      document.querySelector('main').animate([{ zIndex: 0 }, { zIndex: 998 }], {
        duration: 500,
        fill: 'forwards',
      });
      this.modal.style.display = 'block';
      this.modal.animate(
        [
          { zIndex: -1, opacity: 0 },
          { zIndex: 998, opacity: 0 },
          { zIndex: 998, opacity: 1 },
        ],
        {
          duration: 500,
          fill: 'forwards',
        }
      );
      this.scrollPos = $(window).scrollTop();
    }

    closeModal() {
      document.querySelector('main').style.zIndex = 0;
      document.documentElement.classList.remove('no-scroll');
      $('html, body').animate(
        {
          scrollTop: this.scrollPos,
        },
        1
      );
      this.modal.animate(
        [
          { zIndex: 998, opacity: 1 },
          { zIndex: 998, opacity: 0 },
          { zIndex: -1, opacity: 0 },
        ],
        {
          duration: 500,
          fill: 'forwards',
        }
      );
      setTimeout(() => {
        this.modal.style.display = 'none';
      }, 500);
    }

    openGalleryIfRequestedInUrl() {
      originalURL = window.location.href.split(this.urlString)[0];
      let index =
        window.location.href.split(this.urlString)[1] < 4
          ? window.location.href.split(this.urlString)[1]
          : window.location.href.split(this.urlString)[1] - 1;
      if (!isHun) {
        index = parseInt(window.location.href.split(this.urlString)[1]);
      }
      history.replaceState(null, null, originalURL);

      if (this.modal) {
        this.openModal();
        this.modal.querySelectorAll('.overlay__image-wrapper')[index - 1].click();
      } else {
        this.gallery.querySelectorAll('.gallery-with-link__image')[0].click();
      }
    }
  }

  galleries.forEach((gallery, index) => {
    const galleryInited = new Gallery(gallery, index);
  });
});
