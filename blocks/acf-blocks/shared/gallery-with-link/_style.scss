.gallery-with-link {
  margin-top: $spacing-09-48;
  margin-bottom: $spacing-09-48;

  &__images {
    display: grid;
    grid-template-columns: 1fr 1fr 16rem;
    grid-template-rows: 15.5rem;
    grid-column-gap: $spacing-02;
    margin-bottom: $spacing-01;

    &.no-modal {
      .gallery-with-link__image-wrapper:nth-child(n + 4) {
        display: none;
      }
    }
  }

  &__image-wrapper {
    position: relative;
    cursor: pointer;
  }

  &__image {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }

  &__ad-container {
    display: none;
    touch-action: none;

    &.visible {
      @include flexbox-properties;
      position: fixed;
      width: auto !important;
      margin-left: $spacing-00 !important;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 10000 !important;
    }

    .googlead {
      width: 64rem;
    }
  }

  .overlay {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: $color-surface-primary;
    overflow: scroll;
    padding-bottom: $spacing-05-20;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;

    .container {
      padding-block: $spacing-00;
    }

    &.admin-bar {
      padding-top: $spacing-07-32;
    }

    &__header {
      display: flex;
      align-items: center;
      padding: $spacing-05-20 $spacing-00;
      border-bottom: 0.1rem solid $color-divider;
      margin-bottom: $spacing-06-24;
    }

    .main-logo {
      display: block;
      height: 3.2rem;
      width: 10.7rem;
      max-width: 10.7rem;
      margin-right: $spacing-06-24;
    }

    &__article-title {
      margin: $spacing-00;
    }

    &__title-wrapper {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-07-32;
    }

    &__back-button {
      width: 1.8rem;
      height: 1.6rem;
      margin-right: 1.1rem;
      @include mask-properties;
      mask-image: url('assets/icons/icon-back-button-amber.svg');
      -webkit-mask-image: url('assets/icons/icon-back-button-amber.svg');
      background-color: $color-text-brand;
      cursor: pointer;
    }

    &__title {
      margin: $spacing-00;
    }

    &__counter,
    &__photo-label {
      color: $color-text-secondary;
    }

    &__image {
      width: 100%;
      max-height: 100%;
      height: auto;
      object-fit: cover;
      cursor: pointer;
    }

    &__ad-column {
      padding-left: $spacing-11-64;
    }

    &__ad-wrapper {
      position: sticky;
      top: 0.8rem;
      left: 0;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    .overlay {
      &__ad-column {
        padding-left: $spacing-01;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-top: $spacing-07-32;
    margin-bottom: $spacing-07-32;

    .overlay {
      padding-bottom: $spacing-09-48;

      &.admin-bar {
        padding-top: $spacing-09-48;
      }

      .main-logo {
        width: 8rem;
      }

      &__grid {
        margin: $spacing-00;
      }

      &__image-wrapper {
        max-width: 100%;
      }

      &__image {
        padding: $spacing-00;
      }
    }

    &__ad-container {
      &.visible {
        width: 30rem !important;
      }
    }

    &__images {
      grid-template-columns: 0.6fr 0.4fr;
    }

    &__image-wrapper {
      &.last--mobile {
        .gallery-with-link__last-image {
          display: flex;
        }
        &:after {
          content: '';
        }
      }

      &.last {
        display: none !important;

        &:after {
          content: unset;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, sm )) {
    .overlay {
      &__header {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        padding: $spacing-04-16 $spacing-00;
        margin-bottom: $spacing-04-16;
      }

      &__logo-wrapper {
        margin-bottom: $spacing-04-16;
      }

      &__title-wrapper {
        margin-bottom: $spacing-06-24;
      }

      &__image {
        height: 100%;
      }
    }
  }

  @media screen and (max-width: 375px) {
    &__images {
      grid-template-rows: 115px;
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, lg )) {
  .single-post {
    .gallery-with-link {
      &__swipe {
        position: fixed;
        top: 6rem;
        left: 0;
        width: 100vw;
        height: calc(100vh - 6rem);
        z-index: 999999;
      }

      .overlay {
        &.admin-bar {
          padding-top: $spacing-09-48;
        }
        &__header {
          flex-direction: column;
          align-items: flex-start;
        }

        &__logo-wrapper {
          margin-bottom: $spacing-04-16;
        }

        &__ad-column {
          display: none;
        }
      }
    }
  }
}
