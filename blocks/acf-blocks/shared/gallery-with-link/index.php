<?php

/**
 * All the images in the gallery
 * @var array
 */
$all_images = !empty(get_field('images')) ? get_field('images') : null;

/**
 * Whether the modal should be opened first or the slideshow directly
 * @var boolean
 */
$modal = get_field('modal');

/**
 * The number of images in the gallery
 * @var int
 */
$total_images = $all_images ? count($all_images) : null;

/**
 * The id of the gallery
 * @var string
 */
$rel = 'forbes_' . rand(1, 600);
?>

<?php if ($all_images) : ?>

	<div class="gallery-with-link one-page-block" data-ishun="<?= 'hu' === COUNTRY ? 'true' : 'false'; ?>" data-photos="<?= $total_images; ?>" data-id="<?= $rel; ?>" data-url-string="<?= esc_html__('?gallery=', 'FORBES'); ?>">

		<div class="gallery-with-link__images <?php if (!$modal) : echo 'no-modal';
												endif; ?>">

			<?php foreach ($all_images as $key => $image) : ?>

				<?php if ($modal) : ?>

					<div class='gallery-with-link__image-wrapper'>

						<?= wp_get_attachment_image($image, 'medium', false, array('class' => 'gallery-with-link__image', 'alt' => get_post_meta($image, '_wp_attachment_image_alt', TRUE))) ?>

						<?php if (1 === $key || 2 === $key) : ?>

							<?php get_template_part('blocks/acf-blocks/shared/gallery-with-link/last-image/index', null, ['remaining' => count($all_images) - 3, 'position' => 1 === $key ? 'mobile' : 'desktop']); ?>

						<?php endif; ?>

					</div>

				<?php else : ?>

					<?php if ('hu' === COUNTRY && 3 === $key) : ?>

						<a href="#gallery-ad" class="gallery-with-link__ad-container swipebox-gallery-block" title="" rel="<?= $rel; ?>" data-rel="<?= $rel; ?>" style="display: none"></a>
						<div id="gallery-ad" class="gallery-with-link__ad-container" style="display: none"></div>

					<?php endif; ?>

					<a href="<?= wp_get_attachment_image_url($image, 'full', false) ?>" class="gallery-with-link__image-wrapper swipebox-gallery-block" rel="<?= $rel; ?>" data-rel="<?= $rel; ?>" title="<?= wp_get_attachment_caption($image); ?>">
						<?= wp_get_attachment_image($image, 'medium', false, array('class' => 'gallery-with-link__image', 'alt' => get_the_title() . '_' . $key)) ?>

						<?php if (1 === $key || 2 === $key) : ?>

							<?php get_template_part('blocks/acf-blocks/shared/gallery-with-link/last-image/index', null, ['remaining' => count($all_images) - 3, 'position' => 1 === $key ? 'mobile' : 'desktop']); ?>

						<?php endif; ?>
					</a>

				<?php endif; ?>

			<?php endforeach; ?>

		</div>

		<div class="gallery-with-link__caption">

			<?php if (get_field('caption')) : ?>

				<figcaption class="wp-element-caption">
					<?= get_field('caption') . ' (' . $total_images . esc_html__(' photos', 'FORBES') . ')'; ?>
				</figcaption>

			<?php endif; ?>

		</div>

		<?php if ($modal) : ?>

			<div class="overlay <?php if (is_admin_bar_showing()) : echo 'admin-bar';
								endif; ?>">

				<div class="container">

					<div class="overlay__header">

						<?php render_site_logo(defined('CONTENT_TYPE') ? CONTENT_TYPE : null); ?>

						<p class="overlay__article-title"><?= get_the_title(); ?></p>

					</div>

					<div class="overlay__title-wrapper">

						<span class="overlay__back-button"></span>
						<h3 class="overlay__title">
							<span><?= esc_html__('Gallery', 'FORBES') ?></span>
							<span class="overlay__counter"><?= $total_images; ?></span>
							<span class="overlay__photo-label"><?= esc_html__('photos', 'FORBES') ?></span>
						</h3>

					</div>

					<div class="overlay__grid">

						<?php foreach ($all_images as $key => $image) : ?>

							<?php if ('hu' === COUNTRY && 3 === $key) : ?>

								<a href="#gallery-ad-placeholder" class="swipebox-gallery-block" title="" rel="<?= $rel; ?>" data-rel="<?= $rel; ?>" style="display: none"></a>
								<div id="gallery-ad-placeholder" style="display: none"></div>

							<?php endif; ?>

							<a href="<?= wp_get_attachment_image_url($image, 'full', false) ?>" class="overlay__image-wrapper swipebox-gallery-block" rel="<?= $rel; ?>" data-rel="<?= $rel; ?>" title="<?= wp_get_attachment_caption($image); ?>">
								<?= wp_get_attachment_image($image, 'medium', false, array('class' => 'overlay__image', 'alt' => get_the_title() . '_' . $key)) ?>
							</a>

						<?php endforeach; ?>

					</div>

				</div>

			</div>

			<div class="gallery-with-link__ad-container googlead-container">

				<div id="gallery-ad" class="googlead"></div>

			</div>

		<?php endif; ?>

	</div>

<?php endif; ?>
