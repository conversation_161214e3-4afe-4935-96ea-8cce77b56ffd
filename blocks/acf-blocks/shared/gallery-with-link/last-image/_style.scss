.last-image {
  z-index: 2;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  @include flexbox-properties;
  flex-direction: column;
  padding: $spacing-04-16;

  &__overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: $color-black;
    opacity: 0.6;
    z-index: -1;
  }

  @media (hover: hover) {
    &:hover {
      .last-image__opener-text {
        color: $color-text-brand;
      }

      .last-image__icon {
        background-color: $color-text-brand;
      }
    }
  }

  &__icon {
    width: 2.4rem;
    height: 2.4rem !important;
    @include mask-properties;
    mask-image: url('assets/icons/icon-gallery-open.svg');
    -webkit-mask-image: url('assets/icons/icon-gallery-open.svg');
    background-color: $color-text-brand;
    transition: background-color 0.3s ease;
  }

  &__opener-text {
    font-family: $font-noto-serif;
    color: $color-text-brand;
    font-weight: 400;
    font-size: 1.6rem;
    line-height: 3.2rem;
    text-align: center;
    text-decoration: underline;
    transition: color 0.3s ease;
  }

  &--mobile {
    display: none;
  }

  &--three {
    display: none;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    display: none;

    &--mobile {
      display: flex;
    }
  }
}
