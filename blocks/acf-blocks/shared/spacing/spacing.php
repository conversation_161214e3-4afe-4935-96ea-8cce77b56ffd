<?php
/**
 * Spacing Block Template.
 *
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during backend preview render.
 * @param int $post_id The post ID the block is rendering content against.
 */

// Get field values
$mobile_spacing = get_field('mobile_spacing') ?: 16;
$desktop_spacing = get_field('desktop_spacing') ?: 56;

// Create a unique ID for the block
$id = 'spacing-block';
if (isset($block) && isset($block['id'])) {
    $id = 'spacing-' . $block['id'];

    // Check for anchor value
    if (!empty($block['anchor'])) {
        $id = $block['anchor'];
    }

    // Create class attribute allowing for custom "className" and "align" values
    $class_name = 'spacing-block';
    if (!empty($block['className'])) {
        $class_name .= ' ' . $block['className'];
    }
    if (!empty($block['align'])) {
        $class_name .= ' align' . $block['align'];
    }
} else {
    $class_name = 'spacing-block';
}
?>

<section id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($class_name); ?>">
  <div class="tw:md:hidden" style="height: <?php echo esc_attr($mobile_spacing); ?>px;">
  </div>
  <div class="tw:hidden tw:md:block tw:lg:hidden" style="height: <?php echo esc_attr($mobile_spacing); ?>px;">
  </div>
  <div class="tw:hidden tw:lg:block" style="height: <?php echo esc_attr($desktop_spacing); ?>px;">
  </div>
</section>
