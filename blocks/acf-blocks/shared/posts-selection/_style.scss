html.dark-mode {
  .posts-selection {
    .article-card {
      &__title {
        color: $color-text-secondary;
      }
    }
  }
}

.posts-selection {
  margin-bottom: $spacing-10-56;

  &__title {
    max-width: 60%;
    margin-bottom: $spacing-06-24;
    font-size: 3.2rem;
    font-weight: 400;
    line-height: 4.36rem;
    color: $color-text-primary;
  }

  &__articles-grid {
    row-gap: 3rem;

    &--small {
      .article-card {
        &__excerpt {
          font-size: 1.4rem !important;
          line-height: 2.2rem !important;
        }
      }
    }

    &--large {
      .article-card {
        display: flex;

        &__image-wrapper,
        &__content-wrapper {
          flex-basis: 50%;
        }

        &__content-wrapper {
          margin-left: $spacing-03-12;
        }

        &__details-wrapper {
          margin-top: $spacing-00;
          margin-bottom: $spacing-02;
        }

        &__title {
          font-size: 3.2rem;
          line-height: 4.2rem;
          margin-bottom: $spacing-02;
        }

        &__excerpt {
          font-size: 1.6rem;
          line-height: 2.4rem;
        }
      }
    }
  }

  .article-card {
    &__details-wrapper {
      margin-top: $spacing-02;
      margin-bottom: $spacing-00;
    }

    &__grey-line {
      height: 1.4rem;
      width: 100%;
      background-color: $color-surface-secondary;
      margin-bottom: $spacing-02;

      &.short {
        width: 80%;
      }
    }

    &__bottom-text {
      color: $color-text-secondary;
      font-size: 1.4rem;
      line-height: 1.7rem;
    }

    &--last-item {
      &__details-wrapper {
        margin-bottom: $spacing-02;
      }

      &__image-wrapper {
        background-color: $color-surface-secondary;
        margin-bottom: $spacing-02;
        aspect-ratio: 16 / 9;
        margin-top: $spacing-00;
      }

      .article-card__image-wrapper {
        display: flex !important;
      }
    }

    &__last-item-title {
      margin-bottom: $spacing-01;
      font-weight: 400;
      font-size: 1.8rem;
      line-height: 2.5rem;
      color: $color-text-secondary;
    }

    &__excerpt {
      margin-top: $spacing-02;
      font-size: 1.6rem;
      line-height: 2.4rem;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 5;
      -webkit-box-orient: vertical;
      color: $color-text-secondary;
    }

    .article-details {
      &__publish-time {
        font-family: $font-noto-serif !important;
        font-weight: 500 !important;

        &:after {
          display: none;
        }
      }
    }
  }

  &--onepage {
    margin-bottom: $spacing-11-64;

    .posts-selection {
      &__articles-grid {
        &--small {
          .posts-selection {
            &__article-wrapper {
              &:nth-child(2) {
                padding-top: $spacing-06-24;
              }

              &:nth-child(3) {
                padding-top: $spacing-09-48;
              }
            }
          }
        }

        &--medium {
          .posts-selection {
            &__article-wrapper {
              &:nth-child(2) {
                padding-top: $spacing-06-24;
              }
            }
          }
        }

        &--medium,
        &--large {
          .article-card {
            &__content-wrapper {
              display: flex;
              flex-direction: column;
            }

            &__title-wrapper {
              order: 1;
            }

            &__title {
              font-size: 3.2rem;
              line-height: 4.3rem;
              font-weight: 500 !important;
            }

            &__details-wrapper {
              order: 2;
              margin-top: $spacing-01;
            }

            &__excerpt {
              order: 3;
              margin-top: $spacing-00;
            }
          }
        }

        &--large {
          .article-card {
            &__image-wrapper {
              margin-bottom: $spacing-00;
            }
            &__details-wrapper {
              margin-top: $spacing-00;
            }
          }

          .posts-selection {
            &__article-wrapper {
              position: relative;

              &--with-button {
                .article-card {
                  &__content-wrapper {
                    padding-bottom: $spacing-09-48;
                  }
                }
              }
            }

            &__button {
              position: absolute;
              bottom: 0;
              left: calc(50% + 0.6rem);
            }
          }
        }
      }

      &__button {
        margin-top: $spacing-03-12 !important;
      }
    }

    .article-card {
      &__details-wrapper {
        margin-bottom: $spacing-02;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__grid--one-column {
      .article-card {
        display: block;

        &__title {
          font-size: 1.8rem;
          line-height: 2.5rem;
        }

        &__excerpt {
          font-size: 1.4rem;
          line-height: 2rem;
        }
      }
    }

    &--onepage {
      .posts-selection {
        &__articles-grid {
          &--medium,
          &--large {
            .article-card {
              &__title {
                font-size: 2.2rem;
                line-height: 3.2rem;
              }
            }
          }

          &--large {
            .posts-selection {
              &__button {
                position: relative;
                left: unset;
                bottom: unset;
              }

              &--with-button {
                .article-card {
                  &__content-wrapper {
                    padding-bottom: $spacing-00;
                  }
                }
              }
            }

            .article-card {
              &__title {
                font-size: 2.4rem;
                line-height: 3.2rem;
              }
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-bottom: $spacing-08-40;

    &__title {
      margin-bottom: $spacing-04-16;
      font-size: 2.4rem;
      line-height: 3.3rem;
    }

    &__articles-grid {
      row-gap: 4rem;
    }

    &__article-wrapper {
      padding-top: $spacing-00 !important;
    }

    &--onepage {
      .posts-selection {
        &__articles-grid {
          &--large {
            .article-card {
              display: block;

              &__content-wrapper {
                margin-top: $spacing-02;
                margin-left: $spacing-00;
              }
            }
          }
        }
      }
    }
  }
}
