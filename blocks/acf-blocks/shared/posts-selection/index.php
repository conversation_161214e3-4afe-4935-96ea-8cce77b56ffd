<?php

/**
 * The title of the section
 * @var string
 * */
$title = get_field('title') ?? null;

/**
 * The icon of the last item card
 * @var array
 * */
$last_item = get_field('last_item') ?? null;

/**
 * Size of the article cards
 */
$size = get_field('size');

/**
 * Whether the block is on a onepage
 * @var boolean
 */
$is_onepage = str_contains(get_page_template(), 'page-onepage.php') || str_contains(get_the_title(), 'One Page');

/**
 * Show description of article
 */
$show_excerpt = $is_onepage && $size === 'small' ? false : get_field('show_excerpt');

/**
 * The posts of the block
 * @var array
 */
$posts = get_field('posts');

$last_item_enabled = false;

if ($last_item) {

	/**
	 * The enabled state of the last item
	 * @var boolean
	 */
	$last_item_enabled = $last_item['enable'] ?? false;

	/**
	 * The text of the last item card
	 * @var string
	 * */
	$text = $last_item['text'] ?? '';

	/**
	 * The bottom text of the last item card
	 * @var string
	 * */
	$bottom_text = $last_item['bottom_text'] ?? '';
}

$read_article_button = __('Read the article', 'FORBES');

switch ($size) {
	case 'small':
		$col = 'col-lg-4 col-12';
		break;
	case 'medium':
		$col = 'col-lg-6 col-12';
		break;
	case 'large':
		$col = 'col-lg-12 col-12';
		break;
}
?>

<div class="posts-selection<?= $is_onepage ? ' posts-selection--onepage' : ''; ?> one-page-block">

	<?php if ($title) : ?>
		<h3 class="posts-selection__title"><?php echo $title; ?></h3>
	<?php endif; ?>

	<?php if (!empty($posts)) : ?>

		<div class="posts-selection__articles-grid posts-selection__articles-grid--<?php echo $size; ?> row">

			<?php foreach ($posts as $post_obj) : ?>

				<div class="posts-selection__article-wrapper<?= $is_onepage && $post_obj['show_button'] ? ' posts-selection__article-wrapper--with-button' : ''?> <?php echo $col; ?>">

					<?php get_template_part('template-parts/article-card/index', null, array('post_id' => $post_obj['post'], 'post_image' => $post_obj['post_image'], 'show_excerpt' => $show_excerpt, 'article_card_type' => $size === 'small' ? 'normal' : 'large')); ?>

					<?php if ($is_onepage && $post_obj['show_button']) : ?>

						<a href="<?= $post_obj['custom_link'] ?: get_the_permalink($post_obj['post']); ?>" class="posts-selection__button button button--medium button--primary"><?php echo $post_obj['button_text'] ? $post_obj['button_text'] : $read_article_button; ?></a>

					<?php endif; ?>

				</div>

			<?php endforeach; ?>

			<?php if ($last_item_enabled) : ?>

				<div class="article-card article-card--last-item">

					<div class="article-card--last-item__image-wrapper h-d--flex h-align-items--end">

						<div class="article-card__content-wrapper h-d--flex h-flex--column-reverse ">

							<div class="article-card__details-wrapper">

								<h4 class="article-card__last-item-title"><?php echo $text; ?></h4>

							</div>

						</div>

					</div>

					<div class="article-card__grey-line"></div>
					<div class="article-card__grey-line short"></div>
					<div class="article-card__last-item-bottom"></div>
					<h4 class="article-card__bottom-text"><?php echo $bottom_text; ?></h4>
				</div>

			<?php endif; ?>

		</div>

	<?php endif; ?>
</div>

