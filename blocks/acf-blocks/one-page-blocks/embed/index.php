<?php

/**
 * The background color of the block
 * @var string
 */
$bg_color = get_field('bg_color');

/**
 * 	The background color of the block in dark mode
 * @var string
 * */
$bg_color_dark = get_field('bg_color_dark');

/**
 * The content of the block
 * @var string
 */
$content = get_field('content');

/**
 * The buttons of the block
 * @var array
 * */
$buttons = get_field('buttons');

$allowed_blocks = [
	'core/embed',
	'core/html'
]
?>

<?php if (is_admin()) : ?>

	<p class="one-page-embed__admin-note callout"><?= esc_html__('Press the plus button to add embed block', 'FORBES'); ?></p>

<?php endif; ?>

<div class="one-page-embed one-page-block">

	<style>
		.one-page-embed {
			--one-page-embed-bg-color: <?= $bg_color ?? null; ?>;
			--one-page-embed-bg-color-dark: <?= $bg_color_dark ?? null; ?>;
		}
	</style>

	<div class="one-page-embed__embed-wrapper">
		<InnerBlocks allowedBlocks="<?= esc_attr(wp_json_encode($allowed_blocks)); ?>" />
	</div>

	<div class="one-page-embed__text-content-wrapper">

		<?php if ($content) : ?>

			<div class="one-page-embed__text-content">
				<?= $content; ?>
			</div>

		<?php endif; ?>

		<?php if (!empty($buttons)) : ?>

			<div class="one-page-embed__buttons-container">

				<?php foreach ($buttons as $index => $button) : ?>

					<?php if ($button['label'] && $button['link']) : ?>

						<a href="<?= $button['link']; ?>" class="one-page-embed__button button button--medium <?= $index === 0 ? 'button--primary' : 'button--secondary'; ?>" <?= $button['new_tab'] ? " target='_blank' rel='noopener'" : ''; ?>><?= $button['label']; ?></a>

					<?php endif; ?>

				<?php endforeach; ?>

			</div>

		<?php endif; ?>

	</div>

</div>
