html.dark-mode {
  .one-page-embed {
    background-color: var(--one-page-embed-bg-color-dark, $color-surface-secondary);
  }
}

.one-page-embed {
  padding: $spacing-08-40;
  max-width: 81rem;
  margin: $spacing-00 auto $spacing-11-64;
  background-color: var(--one-page-embed-bg-color, $color-surface-secondary);

  &__admin-note {
    margin-left: $spacing-04-16 !important;
    font-style: italic;
  }

  &__embed-wrapper {
    margin-bottom: $spacing-06-24;

    & > *,
    iframe {
      margin-bottom: $spacing-00 !important;
    }
  }

  &__text-content {
    & > p {
      font-size: 1.8rem;
      line-height: 2.8rem;
      color: $color-text-secondary;
    }
  }

  &__buttons-container {
    margin-top: $spacing-06-24;
    display: flex;
    gap: 0.8rem;
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .one-page-embed {
    margin-inline: -1.6rem;
    padding: $spacing-04-16;

    &__embed-wrapper {
      margin-bottom: $spacing-04-16;
    }

    &__buttons-container {
      margin-top: $spacing-04-16;
    }

    &__text-content {
      & > p {
        font-size: 1.6rem;
        line-height: 2.4rem;
      }
    }
  }
}
