html.dark-mode {
  .products-selection {
    .last-item {
      &__image {
        &--dark {
          display: block;
        }
        &--light {
          display: none;
        }
      }
    }
  }
}

.products-selection {
  margin-bottom: $spacing-11-64;
  position: relative;

  &__title {
    display: flex;
    align-items: center;
    font-size: 4.8rem;
    font-weight: 400;
    margin-bottom: $spacing-04-16;
    color: $color-text-primary;

    .products-selection__icon {
      width: 4.8rem;
      height: 4.8rem;
      margin-right: $spacing-02;
    }
  }

  &__description {
    margin-bottom: $spacing-05-20;
    max-width: 63.5rem;

    p,
    a {
      font-size: 1.8rem;
      font-weight: 400;
      text-decoration: none;
      line-height: 2.5rem;
      margin-bottom: $spacing-00;
    }

    p {
      color: $color-text-secondary;
    }
  }

  .last-item {
    height: 100%;
    min-height: 36rem;
    width: 26.5rem;
    position: relative;
    padding: $spacing-04-16;
    flex-direction: column;
    background-color: $color-surface-secondary;
    display: flex !important;
    @include flexbox-properties;

    &__image {
      margin-bottom: $spacing-05-20;
      height: 4.8rem;
      width: 4.8rem;
      margin: $spacing-00 auto $spacing-04-16;
      object-fit: contain;

      &--dark {
        display: none;
      }
      &--light {
        display: block;
      }
    }

    &__title {
      margin-bottom: $spacing-01;
      font-weight: 400;
      font-size: 2.4rem;
      line-height: 3.2rem;
      color: $color-text-secondary;
      font-family: $font-noto-serif;
      text-align: center;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    padding-bottom: $spacing-12-80;

    &__title {
      font-size: 3.2rem;
      margin-bottom: $spacing-04-16;
    }

    &__description {
      margin-bottom: $spacing-03-12;

      p,
      a {
        font-size: 1.6rem;
      }
    }
  }
}

.branding-active {
  .product-card {
    width: 23rem;
  }
}
