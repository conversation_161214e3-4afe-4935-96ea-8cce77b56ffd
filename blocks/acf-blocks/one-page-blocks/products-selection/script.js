jQuery(function ($) {
  $(document).ready(function () {
    $('.products-selection a').on('click', function (event) {
      if (this.hash !== '') {
        event.preventDefault();
        const hash = this.hash;

        $('html, body').animate(
          {
            scrollTop: $(hash).offset().top - 100,
          },
          800,
          function () {
            window.location.hash = hash;
          }
        );
      }
    });
  });

  $('.product-selection__slider').each(function (index, element) {
    new Splide(element, {
      perPage: 4,
      perMove: 1,
      pagination: true,
      arrows: true,
      autoplay: false,
      gap: '1.6rem',
      breakpoints: {
        1200: {
          perPage: 3,
        },
        992: {
          perPage: 2,
        },
        765: {
          perPage: 1.5,
          gap: '3rem',
        },
      },
      classes: {
        prev: 'slick-prev',
        next: 'slick-next',
      },
    }).mount();
  });
});
