<?php

/**
 * The title of the block
 * @var string
 * */
$title = get_field('title');

/**
 * The icon of the title
 * @var string
 * */
$icon = get_field('icon');

/**
 * The description of the block
 * @var string
 * */
$description = get_field('description');

/**
 * The background color of the product cards
 * @var string
 * */
$product_background_color = get_field('background_color');

/**
 * The background color of the product cards on dark mode
 * @var string
 * */
$product_dark_background_color = get_field('dark_background_color');

/**
 * The icon of the last item card
 * @var array
 * */
$last_item = get_field('last_item');

/**
 * The text of the last item card
 * @var string
 * */
$text = $last_item['text'];

/**
 * The products for listing
 * @var array
 */
$products = get_field('products');
?>

<div class="products-selection">

    <?php if ($title) : ?>
        <h3 class="products-selection__title">
            <?php
            if ($icon) {
                echo wp_get_attachment_image($icon, 'thumbnail', "", ["class" => "products-selection__icon h-object-fit--contain"]);
            }
            echo $title;
            ?>
        </h3>

    <?php endif; ?>

    <?php if ($description) : ?>

        <div class="products-selection__description"><?= $description; ?></div>

    <?php endif; ?>

    <?php if (!empty($products)) : ?>

        <div class="product-selection__slider splide splide-redesign">

			<div class="splide__track">
				<ul class="splide__list">
					<?php foreach ($products as $product) : ?>
						<li class="splide__slide"><?php get_template_part('template-parts/product-card/index', null, array('post_id' => $product['product'], 'background_color' => $product_background_color, 'dark_background_color' => $product_dark_background_color)); ?></li>
					<?php endforeach; ?>
				</ul>
			</div>

            <?php if ($last_item['enable']) : ?>
                <div class="last-item">

                    <div class="last-item__image-wrapper">
                        <img src="<?= get_template_directory_uri() . '/assets/icons/guide-plus-circle.svg"' ?>" alt="" class="last-item__image last-item__image--light">
                        <img src="<?= get_template_directory_uri() . '/assets/icons/guide-plus-circle--dark.svg"' ?>" alt="" class="last-item__image last-item__image--dark">
                    </div>

                    <?php if ($text) : ?>
                        <h4 class="last-item__title"><?= $text; ?></h4>
                    <?php endif; ?>

                </div>

            <?php endif; ?>

        </div>

    <?php endif; ?>

</div>

