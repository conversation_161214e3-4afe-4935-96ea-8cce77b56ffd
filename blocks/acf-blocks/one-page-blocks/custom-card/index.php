<?php

/**
 * Whether the cards should be in a carousel
 * @var bool
 */
$is_carousel = get_field('is_carousel');

/**
 * The type of the cards
 * @var string
 */
$type = get_field('type');

/**
 * The size of the cards
 * @var string
 */
$size = $is_carousel ? 's' : get_field('size');

/**
 * The cards
 * @var array
 */
$cards = get_field('cards');
?>

<?php if (!empty($cards)) : ?>

	<div class="one-page-custom-cards<?= $is_carousel ? ' splide' : ''; ?> one-page-block">

		<?php if ($is_carousel) : ?>

			<div class="splide__arrows splide__arrows--onepage">
				<div class="splide__arrow splide__arrow--prev">
					<button class="splide__arrow-button" aria-label="Previous slide" type="button"></button>
				</div>
				<div class="splide__arrow splide__arrow--next">
					<button class="splide__arrow-button" aria-label="Next slide" type="button"></button>
				</div>
			</div>

		<?php endif; ?>

		<div class="one-page-custom-cards__track<?= $is_carousel ? ' splide__track' : ''; ?>">

			<div class="one-page-custom-cards__list<?= $is_carousel ? ' splide__list' : ''; ?><?= " one-page-custom-cards__list--{$size}"; ?>">

				<?php foreach ($cards as $card) : ?>

					<?php get_template_part('template-parts/onepage-custom-card/index', null, ['card' => $card, 'is_carousel' => $is_carousel, 'type' => $type, 'size' => $size]); ?>

				<?php endforeach; ?>

			</div>

		</div>

	</div>

<?php endif; ?>
