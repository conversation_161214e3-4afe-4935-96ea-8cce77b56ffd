.one-page-custom-cards {
  &__track.splide__track {
    padding-bottom: $spacing-07-32;
  }

  .splide {
    &__pagination {
      display: flex !important;
      margin-bottom: $spacing-00;

      &__page {
        transform: none !important;
        width: 0.6rem;
        height: 0.2rem;
        background: $color-divider !important;
        opacity: 1 !important;
        border-radius: 0.2rem !important;

        &.is-active {
          background-color: $color-surface-brand-dark !important;
          width: 1.6rem;
        }
      }

      li {
        margin-bottom: $spacing-00 !important;

        &:before {
          content: none !important;
        }
      }
    }

    &__arrow {
      @include mask-properties;
      mask-image: url('assets/icons/icon-arrow.svg');
      -webkit-mask-image: url('assets/icons/icon-arrow.svg');
      background-color: $color-surface-primary;

      &--prev {
        transform: rotate(180deg);
      }
    }
  }

  &__list {
    &:not(.splide__list) {
      display: grid;
      gap: 3rem;
    }

    &--s:not(.splide__list) {
      grid-template-columns: repeat(3, 1fr);

      .custom-card:nth-child(3n + 2) {
        margin-top: $spacing-04-16;
      }

      .custom-card:nth-child(3n) {
        padding-top: $spacing-07-32;
      }
    }

    &--m:not(.splide__list) {
      grid-template-columns: repeat(2, 1fr);

      .custom-card:nth-child(even) {
        padding-top: $spacing-07-32;
      }
    }

    &--l:not(.splide__list) {
      grid-template-columns: 1fr;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    .splide {
      &__list {
        .custom-card {
          width: 90% !important;
        }
      }
    }

    &__list:not(.splide__list) {
      grid-template-columns: 1fr;

      .custom-card {
        padding-top: 0 !important;

        &--color {
          padding-top: $spacing-06-24;
        }
      }
    }
  }
}
