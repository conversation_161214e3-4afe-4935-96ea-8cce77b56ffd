document.addEventListener('DOMContentLoaded', function () {
  jQuery(function ($) {
    const initSlider = () => {
      const $sliders = $('.one-page-custom-cards.splide');

      if (!$sliders.length) return;

      $sliders.each(function () {
        const splide = new Splide(this, {
          type: 'loop',
          perPage: 3,
          perMove: 1,
          gap: '3rem',
          pagination: true,
          arrows: true,
          focus: 'number',
          infinite: true,
          omitEnd: true,
          speed: 300,
          breakpoints: {
            768: {
              perPage: 2,
              gap: '1.5rem',
            },
            576: {
              perPage: 1,
            },
          },
          classes: {
            pagination:
              'splide__pagination splide__pagination--onepage splide__pagination splide__pagination--onepage-ccard',
            page: 'splide__pagination__page splide__pagination__page--onepage',
          },
        }).mount();

        window.splideSliders = window.splideSliders || [];
        window.splideSliders.push(splide);
      });
    };

    const isAdmin = $('body').hasClass('wp-admin');

    if (isAdmin && wp) {
      const { select, subscribe } = wp.data;
      const closeListener = subscribe(() => {
        const isReady = select('core/editor').__unstableIsEditorReady();
        if (isReady) {
          closeListener();

          initSlider();
        }
      });
    } else {
      initSlider();
    }
  });
});
