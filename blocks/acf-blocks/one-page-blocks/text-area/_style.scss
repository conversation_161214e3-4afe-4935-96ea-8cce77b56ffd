.one-page-textarea {
  background-color: var(--one-page-text-area-bg-color, $color-surface-secondary);
  margin: $spacing-00 auto $spacing-11-64;
  padding: $spacing-11-64 $spacing-05-20;
  position: relative;

  &--no-bg {
    padding-block: $spacing-00;
  }

  &__bg-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;

    &--mobile {
      display: none;
    }
  }

  &__bg-image {
    position: absolute;
    height: 100%;
    width: 100%;
    inset: 0;
    z-index: 0;
    object-fit: cover;

    &--mobile {
      display: none;
    }
  }

  &__content {
    max-width: 73rem;
    margin: auto;
    position: relative;
    z-index: 1;

    p {
      color: var(--one-page-text-area-color, $color-text-primary);
    }

    &.small-text {
      p {
        font-size: 1.6rem;
        line-height: 2.4rem;
      }
    }

    &.large-text {
      p {
        font-size: 2.4rem;
        line-height: 4rem;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-inline: -1.6rem;
    padding: $spacing-08-40 $spacing-04-16;
    margin-bottom: $spacing-07-32;

    &--no-bg {
      padding-block: $spacing-00;
    }

    &__bg-video {
      &.has-mobile {
        display: none;
      }

      &--mobile {
        display: block;
      }
    }

    &__bg-image {
      &.has-mobile {
        display: none;
      }

      &--mobile {
        display: block;
      }
    }
  }
}

html.dark-mode {
  .one-page-textarea {
    background-color: var(--one-page-text-area-dark-bg-color, $color-surface-secondary);

    &__content {
      p {
        color: var(--one-page-text-area-dark-color, $color-text-primary);
      }
    }
  }
}
