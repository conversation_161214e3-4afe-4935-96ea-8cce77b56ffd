<?php
/**
 * The ID of the block
 * @var string
 */
$block_id = uniqid('onepage-textarea-');

/**
 * The text content of the block
 * @var string
 */
$content = get_field('content');

/**
 * The text color of the block
 * @var string
 */
$text_color = get_field('text_color') ?: null;

/**
 * The text color of the block in dark mode
 * @var string
 */
$text_color_dark = get_field('text_color_dark') ?: null;

/**
 * The text size of the block
 * @var string
 */
$text_size = get_field('text_size');

/**
 * The type of the background of the block
 * @var string
 */
$bg_type = get_field('bg_type');

/**
 * The background color of the block
 * @var string
 */
$bg_color = $bg_type === 'color' ? get_field('bg_color') : null;

/**
 * The background color of the block in dark mode
 * @var string
 */
$bg_color_dark = $bg_type === 'color' ? get_field('bg_color_dark') : null;

/**
 * The background image of the block
 * @var string
 */
$bg_image = $bg_type === 'image' ? get_field('bg_image') : null;

/**
 * The background image of the block for mobile sizes
 * @var string
 */
$bg_image_mobile = $bg_type === 'image' ? get_field('bg_image_mobile') : null;

/**
 * The background video of the block
 * @var string
 */
$bg_video = $bg_type === 'video' ? get_field('bg_video') : null;

/**
 * The background video of the block for mobile sizes
 * @var string
 */
$bg_video_mobile = $bg_type === 'video' ? get_field('bg_video_mobile') : null;
?>

<div id="<?= $block_id; ?>" class="one-page-textarea one-page-block <?= $bg_type === 'color' && !$bg_color ? 'one-page-textarea--no-bg' : ''; ?>" style="<?= $bg_color ? 'background-color: ' . $bg_color . ';' : ''; ?>">

	<style>
		<?= '#' . $block_id; ?> {
			--one-page-text-area-bg-color: <?= $bg_color ?? null; ?>;
			--one-page-text-area-dark-bg-color: <?= $bg_color_dark ?? null; ?>;
			--one-page-text-area-color: <?= $text_color ?? null; ?>;
			--one-page-text-area-dark-color: <?= $text_color_dark ?? null; ?>;
		}
	</style>

	<?php if ($bg_video) : ?>

		<video class="one-page-textarea__bg-video<?= $bg_video_mobile ? ' has-mobile' : ''; ?>" autoplay muted loop>
			<source src="<?= wp_get_attachment_url($bg_video); ?>" type="video/mp4">
		</video>

	<?php endif; ?>

	<?php if ($bg_video_mobile) : ?>

		<video class="one-page-textarea__bg-video one-page-textarea__bg-video--mobile" autoplay muted loop>
			<source src="<?= wp_get_attachment_url($bg_video_mobile); ?>" type="video/mp4">
		</video>

	<?php endif; ?>

	<?php if ($bg_image) : ?>

		<?= wp_get_attachment_image($bg_image, 'medium_large', false, ['class' => 'one-page-textarea__bg-image' . ($bg_image_mobile ? ' has-mobile' : '')]) ?>

	<?php endif; ?>

	<?php if ($bg_image_mobile) : ?>

		<?= wp_get_attachment_image($bg_image_mobile, 'medium', false, ['class' => 'one-page-textarea__bg-image one-page-textarea__bg-image--mobile']) ?>

	<?php endif; ?>

	<?php if ($content) : ?>

		<div class="one-page-textarea__content<?= $text_size === 's' ? ' small-text' : ''; ?><?= $text_size === 'l' ? ' large-text' : ''; ?>">
			<?= $content; ?>
		</div>

	<?php endif; ?>

</div>
