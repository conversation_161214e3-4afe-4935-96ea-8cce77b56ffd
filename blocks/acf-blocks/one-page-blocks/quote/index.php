<?php

$quote = get_field('quote');
$author = get_field('author');
$author_title = get_field('author_title');
$author_image = get_field('auhtor_image');
$background_color = get_field('background_color');

$is_small = get_field('small_text');

?>

<div class="one-page-quote one-page-block background-<?= $background_color; ?> ">

	<?php if ( get_field('text') ) : ?>
		<div class="one-page-quote__text <?= $is_small ? 'small-text' : ''; ?>"><?= get_field('text'); ?></div>
	<?php endif; ?>

	<div class="one-page-quote__author-wrapper h-d--flex ">
		<?php if($author_image) : ?>
			<img class="one-page-quote__author-image" src="<?= wp_get_attachment_url($author_image, 'thumbnail'); ?>" alt="">
		<?php endif; ?>
		<div class="one-page-quote__author-info">
			<?php if ( $author ) : ?>
				<h4 class="one-page-quote__author-name<?= $is_small ? ' small-text' : ''; ?>"> <?= $author ?></h4>
			<?php endif; ?>

			<?php if ( $author_title ) : ?>
				<p class="one-page-quote__author-title callout<?= $is_small ? ' small-text' : ''; ?>"><?= $author_title ?></p>
			<?php endif; ?>

		</div>
	</div>

</div>

