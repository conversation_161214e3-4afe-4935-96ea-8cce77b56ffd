.one-page-quote {
  border-left: 0.2rem solid $color-surface-brand-dark;
  margin: auto;
  margin-bottom: $spacing-11-64;
  max-width: 73rem;
  padding: $spacing-08-40;

  &.background-colored {
    background-color: $color-surface-program-premium;
  }

  &__text {
    font-weight: 500;
    margin-bottom: $spacing-08-40;

    &:not(.small-text) {
      p {
        font-size: 3.2rem;
        font-weight: 500;
        line-height: 4.4rem;
      }
    }
  }

  &__author-image {
    border-radius: 50%;
    height: 6.4rem;
    margin-right: $spacing-02;
    object-fit: cover;
    width: 6.4rem;
  }

  &__author-name {
    margin-bottom: $spacing-01;

    &.small-text {
      font-size: 1.8rem;
      list-style: 2.4rem;
    }
  }

  &__author-title {
    color: $color-text-secondary;

    &.small-text {
      font-size: 1.4rem;
      list-style: 1.6rem;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    &__text {
      p {
        font-size: 2.4rem;
        line-height: 3.2rem;
      }
    }

    &__author-name {
      font-size: 2rem;
      line-height: 2.8rem;
    }

    &__author-title {
      font-size: 1.4rem;
      line-height: 2rem;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin: $spacing-00;
    padding: $spacing-06-24 $spacing-04-16;

    &__text {
      margin-bottom: $spacing-06-24;

      p:not(.small-text) {
        font-size: 2.2rem;
        line-height: 3.2rem;
      }
    }

    &__author-image {
      height: 4.8rem;
      width: 4.8rem;
    }

    &__author-name {
      font-size: 2rem;
      line-height: 2.8rem;
    }

    &__author-title {
      font-size: 1.4rem;
      line-height: 2rem;
    }
  }
}
