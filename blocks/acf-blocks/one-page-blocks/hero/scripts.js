document.addEventListener('DOMContentLoaded', function () {
  jQuery(function ($) {
    const initSlider = () => {
      const $sliderElements = $('.one-page-hero');

      if (!$sliderElements.length) return;

      $sliderElements.each((index, sliderElement) => {
        if ($(sliderElement).find('.one-page-hero__element').length < 2) {
          $(sliderElement).find('.splide__arrows').remove();
          return;
        }

        $(sliderElement).addClass('splide');
        $(sliderElement).find('.one-page-hero__track').addClass('splide__track');
        $(sliderElement).find('.one-page-hero__list').addClass('splide__list');
        $(sliderElement).find('.one-page-hero__element').addClass('splide__slide');

        const splide = new Splide(sliderElement, {
          type: 'loop',
          perPage: 1,
          perMove: 1,
          focus: 'number',
          speed: 300,
          classes: {
            pagination: 'splide__pagination splide__pagination--onepage',
            page: 'splide__pagination__page splide__pagination__page--onepage',
          },
          autoplay: true,
          interval: 5000,
          pauseOnFocus: false,
        });

        const colorSplideElements = (index = 0) => {
          const currentSlideElement = splide.Components.Elements.slides[index];

          if (!currentSlideElement) return;

          const color = $(currentSlideElement).data('textcolor');

          color && $(sliderElement).find('.splide__arrow-button, .splide__pagination').css('backgroundColor', color);
        };

        splide.on('pagination:mounted', () => colorSplideElements());
        splide.on('move', (newIndex) => colorSplideElements(newIndex));

        splide.mount();

        window.splideSliders = window.splideSliders || [];
        window.splideSliders.push(splide);
      });
    };

    const isAdmin = $('body').hasClass('wp-admin');

    if (isAdmin && wp) {
      const { select, subscribe } = wp.data;
      const closeListener = subscribe(() => {
        const isReady = select('core/editor').__unstableIsEditorReady();
        if (isReady) {
          closeListener();

          initSlider();
        }
      });
    } else {
      initSlider();
    }
  });
});
