<?php

/**
 * The slides
 * @var array
 */
$slides = get_field('slides');
?>

<?php if (!empty($slides)) : ?>

	<div id="one-page-hero-slider" class="one-page-hero one-page-block">

		<div class="splide__arrows splide__arrows--onepage">
			<div class="splide__arrow splide__arrow--prev">
				<button class="splide__arrow-button" aria-label="Previous slide" type="button"></button>
			</div>
			<div class="splide__arrow splide__arrow--next">
				<button class="splide__arrow-button" aria-label="Next slide" type="button"></button>
			</div>
		</div>

		<div class="one-page-hero__track">

			<div class="one-page-hero__list">

				<?php foreach ($slides as $slide) : ?>

					<?php
					/**
					 * Whether the background image has an overlay element, for better readabilty of text
					 * @var boolean
					 */
					$overlay = $slide['overlay'];

					/**
					 * The vertical alignment of text
					 * @var string
					 */
					$vertical_alignment = $slide['text_alignment_vertical'];

					/**
					 * The horizontal alignment of text
					 * @var string
					 */
					$horizontal_alignment = $slide['text_alignment_horizontal'];

					/**
					 * The background image for desktop size screens
					 * @var string
					 */
					$background = $slide['background'];

					/**
					 * The background image for mobile devices
					 * @var string
					 */
					$background_mobile = $slide['background_mobile'] ? $slide['background_mobile'] : $background;

					/**
					 * The color of the text
					 * @var string
					 */
					$text_color = $slide['text_color'] ?? '#FFFFFF';

					if (count($slides) === 1) {
					?>
						<style>
							.one-page-hero {
								--onepage-hero-text-color: <?= $text_color ?>;
							}
						</style>
					<?php
					}
					?>

					<div class="one-page-hero__element one-page-block horizontal-<?= $horizontal_alignment ?> vertical-<?= $vertical_alignment ?>" data-textcolor="<?= $text_color; ?>">

						<div class="one-page-hero__background justDesktop <?= $overlay ? 'overlay' : '' ?>" style="background-image: <?php if ($overlay) : ?>linear-gradient(0deg, rgba(0,0,0, <?= $vertical_alignment == 'bottom' ? '1' : '0' ?>) 0%, rgba(0,0,0,0.8) <?= $vertical_alignment == 'top' ? '75%' : ($vertical_alignment == 'middle' ? '50%' : '25%') ?>, rgba(0,0,0, <?= $vertical_alignment == 'top' ? '1' : '0' ?>) 100%),<?php endif; ?> url(<?= wp_get_attachment_url($background, 'large'); ?>)"></div>

						<div class="one-page-hero__background justPhone <?= $overlay ? 'overlay' : '' ?>" style="background-image: <?php if ($overlay) : ?>linear-gradient(0deg, rgba(0,0,0, <?= $vertical_alignment == 'bottom' ? '1' : '0' ?>) 0%, rgba(0,0,0,0.8) <?= $vertical_alignment == 'top' ? '75%' : ($vertical_alignment == 'middle' ? '50%' : '25%') ?>, rgba(0,0,0, <?= $vertical_alignment == 'top' ? '1' : '0' ?>) 100%),<?php endif; ?> url(<?= wp_get_attachment_url($background_mobile, 'large'); ?>)"></div>

						<?php if ($slide['title']) : ?>
							<h1 class="one-page-hero__title" style="color: <?= $text_color; ?>"><?= $slide['title']; ?></h1>
						<?php endif; ?>

						<?php if ($slide['text']) : ?>
							<p class="one-page-hero__text" style="color: <?= $text_color; ?>"><?= $slide['text']; ?></p>
						<?php endif; ?>


						<?php if (($slide['button_1_link'] && $slide['button_1_text']) || ($slide['button_2_link'] && $slide['button_2_text'])) : ?>

							<div class="one-page-hero__button-container">

								<?php if ($slide['button_1_link'] && $slide['button_1_text']) : ?>
									<a class="one-page-hero__button-1 button button--primary button--medium" style="background-color: <?= $text_color; ?>;" href="<?= $slide['button_1_link']; ?>" target="_blank"><?= $slide['button_1_text'] ?></a>
								<?php endif; ?>

								<?php if ($slide['button_2_link'] && $slide['button_2_text']) : ?>
									<a class="one-page-hero__button-2 button button--secondary button--medium" style="color: <?= $text_color; ?>; border-color: <?= $text_color; ?>;" href="<?= $slide['button_2_link']; ?>" target="_blank"><?= $slide['button_2_text'] ?></a>
								<?php endif; ?>

							</div>

						<?php endif; ?>

					</div>

				<?php endforeach; ?>

			</div>

		</div>

	</div>

<?php endif; ?>
