.one-page-hero {
  margin-bottom: $spacing-11-64;
  position: relative;

  &__element {
    aspect-ratio: 16 / 9;
    display: flex;
    flex-direction: column;
    padding: $spacing-11-64 $spacing-12-80;
    position: relative;

    &.horizontal {
      &-left {
        text-align: left;

        .one-page-hero {
          &__button-container {
            justify-content: flex-start;
          }
        }
      }

      &-middle {
        text-align: center;

        .one-page-hero {
          &__button-container {
            justify-content: center;
          }
        }
      }
    }

    &.vertical {
      &-top {
        justify-content: flex-start;
      }

      &-bottom {
        justify-content: flex-end;
      }

      &-middle {
        justify-content: center;
      }
    }
  }

  &__background {
    background-position: center !important;
    background-size: cover !important;
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 0;
  }

  &__title {
    color: $color-white;
    font-weight: 700;
    margin-bottom: $spacing-06-24;
    z-index: 1;
  }

  &__text {
    color: $color-white;
    line-height: 2.8rem;
    z-index: 1;
  }

  &__button-container {
    display: flex;
    justify-content: center;
    gap: 0.8rem;
    z-index: 1;
    margin-top: $spacing-06-24;
  }

  &__button-1 {
    background-color: $color-white;
    color: $color-black;
    transition: opacity 0.3s ease;

    @media (hover: hover) {
      &:hover {
        background-color: $color-white;
        color: $color-black;
        opacity: 0.8;
      }
    }
  }

  &__button-2 {
    color: $color-white;
    border-color: $color-white;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__element {
      padding: $spacing-08-40;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    width: 100vw;
    margin: $spacing-00 -1.5rem $spacing-08-40 !important;

    &__element {
      aspect-ratio: auto;
      height: 61.8rem;
      padding: $spacing-04-16 $spacing-04-16 $spacing-08-40 $spacing-04-16;
    }
  }
}
