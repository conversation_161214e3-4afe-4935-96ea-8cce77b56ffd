<?php

/**
 * The quote
 * @var string
 * */
$quote = get_field('quote');

/**
 * The quotee image
 * @var string
 * */
$quotee_image = get_field('quotee_image');

/**
 * The quotee name
 * @var string
 * */
$quotee_name = get_field('quotee_name');

/**
 * The quotee information
 * @var string
 * */
$quotee_info = get_field('quotee_info');
?>

<div class="quote-details">

	<?php if ($quote) : ?>

		<div class="quote-details__quote"><?= $quote; ?></div>

	<?php endif; ?>

	<div class="quote-details__quotee-details-wrapper">
		<?php
		if ($quotee_image) {
			echo wp_get_attachment_image($quotee_image, 'thumbnail', "", ["class" => "quote-details__quotee-image"]);
		};
		?>

		<div class="quote-details__quotee-details">

			<span class="quote-details__quotee-name"><?= $quotee_name; ?></span>

			<span class="quote-details__quotee-info"><?= $quotee_info; ?></span>

		</div>

	</div>

</div>
