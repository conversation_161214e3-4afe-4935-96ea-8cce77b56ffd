.quote-details {
  background-color: $color-surface-secondary;
  max-width: 73rem;
  margin: $spacing-00 auto $spacing-11-64;
  padding: $spacing-11-64;

  &__quote {
    margin-bottom: $spacing-06-24;

    p {
      font-size: 4rem;
      font-weight: 400;
      color: $color-surface-invert;
      line-height: 4.8rem;
    }
  }

  &__quotee-details-wrapper {
    display: flex;
    align-items: center;
  }

  &__quotee-image {
    width: 5.5rem;
    height: 5.5rem;
    margin-right: $spacing-02;
    object-fit: contain;
  }

  &__quotee-details {
    display: flex;
    flex-direction: column;
  }

  &__quotee-name {
    font-size: 1.6rem;
    font-weight: 600;
    color: $color-surface-invert;
    font-family: $font-noto-serif;
  }

  &__quotee-info {
    font-size: 1.6rem;
    font-weight: 400;
    color: $color-text-secondary;
    font-family: $font-noto-serif;
    font-style: italic;
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .quote-details {
    margin: 3.2rem 0;
    padding: 2.4rem;

    &__quote {
      margin-bottom: 2rem;

      p {
        font-size: 3.2rem;
        line-height: 4.4rem;
      }
    }

    &__quotee-image {
      width: 5.3rem;
      height: 5.3rem;
    }

    &__quotee-info {
      padding-right: 2rem;
    }
  }
}
