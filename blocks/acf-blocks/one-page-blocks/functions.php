<?php
add_action('acf/init', 'forbes_init_one_page_block_types');
function forbes_init_one_page_block_types() {
	if (function_exists('acf_register_block_type')) {

		/*
			ACF BLOCKS FOR THE ONE PAGE
		*/

		// register the QUOTE WITH DETAILS
		acf_register_block_type(array(
			'name'              => 'quote-details',
			'title'             => esc_html__('One Page: Quote with details', 'FORBES'),
			'description'       => esc_html__('Quote with image, name and information', 'FORBES'),
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/one-page-blocks/quote-details/index.php',
			'category'          => 'common',
			'icon'              => 'editor-quote',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('quote', 'FORBES'), esc_html__('details', 'FORBES')),
			'align'				=> 'center',
		));

		// register the PRODUCT SELECTION
		acf_register_block_type(array(
			'name'              => 'products-selection',
			'title'             => esc_html__('One Page: Products Selection', 'FORBES'),
			'description'       => esc_html__('Choose three products manually', 'FORBES'),
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/one-page-blocks/products-selection/index.php',
			'category'          => 'common',
			'icon'              => 'admin-post',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('products', 'FORBES'), esc_html__('selection', 'FORBES'), esc_html__('related', 'FORBES')),
			'align'				=> 'center',
			'enqueue_assets'	=> function () {
				wp_enqueue_script('products-selection-script', get_template_directory_uri() . '/minified-js/one-page-blocks/products-selection/script.min.js', ['jquery'], filemtime(get_template_directory() . '/minified-js/one-page-blocks/products-selection/script.min.js'), true);
			}
		));

		acf_register_block_type(array(
			'name'              => 'one-page-hero',
			'title'             => esc_html__('One Page: Hero', 'FORBES'),
			'description'       => esc_html__('Hero block for OnePager', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/hero/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('hero', 'FORBES'), esc_html__('one', 'FORBES')),
			'align'				=> 'center',
			'enqueue_assets'	=> function () {
				wp_enqueue_script('one-page-hero-script', get_template_directory_uri() . '/minified-js/one-page-blocks/hero/scripts.min.js', array('splide-script', 'wp-element'), filemtime(get_template_directory() . '/minified-js/one-page-blocks/hero/scripts.min.js'), true);
			}
		));

		acf_register_block_type(array(
			'name'              => 'one-page-posts-selection',
			'title'             => esc_html__('One Page: Posts Selection', 'FORBES'),
			'description'       => esc_html__('Choose three posts manually', 'FORBES'),
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/shared/posts-selection/index.php',
			'category'          => 'common',
			'icon'              => 'admin-post',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('posts', 'FORBES'), esc_html__('selection', 'FORBES'), esc_html__('related', 'FORBES'), esc_html__('one-page', 'FORBES'), esc_html__('one', 'FORBES')),
			'align'				=> 'center',
		));

		acf_register_block_type(array(
			'name'              => 'one-page-quote',
			'title'             => esc_html__('One Page: Quote', 'FORBES'),
			'description'       => esc_html__('Quote block for OnePager', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/quote/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('quote', 'FORBES'), esc_html__('one', 'FORBES')),
			'align'				=> 'center',
		));

		acf_register_block_type(array(
			'name'              => 'one-page-partners',
			'title'             => esc_html__('One Page: Partners', 'FORBES'),
			'description'       => esc_html__('Partners block for OnePager', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/partners/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('Partner', 'FORBES'), esc_html__('one', 'FORBES')),
			'align'				=> 'center',
		));

		acf_register_block_type(array(
			'name'              => 'one-page-separator',
			'title'             => esc_html__('One Page: Separator', 'FORBES'),
			'description'       => esc_html__('Block Separator for OnePager', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/separator/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('Separator', 'FORBES'), esc_html__('one', 'FORBES')),
			'align'				=> 'center',
			'supports'			=> array('mode' => false)
		));

		acf_register_block_type(array(
			'name'              => 'one-page-gallery-with-link',
			'title'             => esc_html__('One Page: Gallery With Link', 'FORBES'),
			'description'       => esc_html__('Gallery with 3 images and a link to the rest of the collection', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/shared/gallery-with-link/index.php',
			'category'          => 'common',
			'icon'              => 'format-gallery',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('gallery', 'FORBES'), esc_html__('one', 'FORBES')),
			'align'				=> 'center',
			'enqueue_assets'	=> function () {
				wp_enqueue_script('one-page-gallery-with-link-script', get_template_directory_uri() . '/minified-js/shared/gallery-with-link/script.min.js', array(), filemtime(get_template_directory() . '/minified-js/shared/gallery-with-link/script.min.js'), true);
			}
		));

		acf_register_block_type(array(
			'name'              => 'one-page-podcast',
			'title'             => esc_html__('One Page: Podcast', 'FORBES'),
			'description'       => esc_html__('Block with spotify embed and description of podcast', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/podcast/index.php',
			'category'          => 'common',
			'icon'              => 'format-audio',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('podcast', 'FORBES'), esc_html__('one', 'FORBES')),
			'align'				=> 'center',
			'enqueue_assets'	=> function () {
				if (!wp_script_is('spotify-embed', 'enqueued')) {
					wp_enqueue_script('spotify-embed', 'https://open.spotify.com/embed-podcast/iframe-api/v1', null, null, true);
				}
				wp_enqueue_script('one-page-podcast-script', get_template_directory_uri() . '/minified-js/podcast-block/scripts.min.js', array(), filemtime(get_template_directory() . '/minified-js/podcast-block/scripts.min.js'), true);
			}
		));

		acf_register_block_type(array(
			'name'              => 'one-page-custom-card',
			'title'             => esc_html__('One Page: Custom Card', 'FORBES'),
			'description'       => esc_html__('Block with custom content in a card layout', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/custom-card/index.php',
			'category'          => 'common',
			'icon'              => 'admin-page',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('custom', 'FORBES'), esc_html__('card', 'FORBES')),
			'align'				=> 'center',
			'enqueue_assets'	=> function () {
				wp_enqueue_script('one-page-custom-cards-script', get_template_directory_uri() . '/minified-js/one-page-blocks/custom-card/scripts.min.js', array(), filemtime(get_template_directory() . '/minified-js/one-page-blocks/custom-card/scripts.min.js'), true);
			}
		));

		acf_register_block_type(array(
			'name'              => 'one-page-textarea',
			'title'             => esc_html__('One Page: Text Area', 'FORBES'),
			'description'       => esc_html__('Block with textarea and customizeable background', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/text-area/index.php',
			'category'          => 'common',
			'icon'              => 'text',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('text', 'FORBES'), esc_html__('area', 'FORBES')),
			'align'				=> 'center',
		));

		acf_register_block_type(array(
			'name'              => 'one-page-text-inline-photo',
			'title'             => esc_html__('One Page: Text and Inline Photo', 'FORBES'),
			'description'       => esc_html__('Block with a large image, title and text', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/text-and-inline-photo/index.php',
			'category'          => 'common',
			'icon'              => 'table-col-after',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('text', 'FORBES'), esc_html__('inline', 'FORBES'), esc_html__('photo', 'FORBES')),
			'align'				=> 'center',
		));

		acf_register_block_type(array(
			'name'              => 'one-page-accordion',
			'title'             => esc_html__('One Page: Accordion', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/shared/accordion/index.php',
			'category'          => 'common',
			'icon'              => 'feedback',
			'keywords'          => array(esc_html__('accordion', 'FORBES')),
			'align'				=> 'center',
			'enqueue_assets' => function () {
				wp_enqueue_script('faq-script', get_template_directory_uri() . '/minified-js/shared/accordion/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/shared/accordion/scripts.min.js'), true);
			},
		));

		acf_register_block_type(array(
			'name'              => 'one-page-embed',
			'title'             => esc_html__('One Page: Embed', 'FORBES'),
			'description'       => esc_html__('Embed block for one page, please insert an embed or a custom html block inside', 'FORBES'),
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/one-page-blocks/embed/index.php',
			'category'          => 'common',
			'icon'              => 'format-video',
			'keywords'          => array(esc_html__('embed', 'FORBES')),
			'align'				=> 'center',
			'supports'          => array(
				'jsx' => true
			),
		));

		acf_register_block_type(array(
			'name'              => 'one-page-banner',
			'title'             => esc_html__('One Page: Banner', 'FORBES'),
			'description'       => esc_html__('Banner with title, content, buttons, image and customizeable background', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/banner/index.php',
			'category'          => 'common',
			'icon'              => 'editor-insertmore',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('banner', 'FORBES')),
			'align'				=> 'center',
		));

		acf_register_block_type(array(
			'name'              => 'one-page-social-icons',
			'title'             => esc_html__('One Page: Social Icons', 'FORBES'),
			'description'       => esc_html__('Social buttons with custom links', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/social-icons/index.php',
			'category'          => 'common',
			'icon'              => 'share',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('social', 'FORBES'), esc_html__('icon', 'FORBES')),
			'align'				=> 'center',
		));

		acf_register_block_type(array(
			'name'              => 'one-page-video',
			'title'             => esc_html__('One Page: Video', 'FORBES'),
			'description'       => esc_html__('Video player', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/one-page-blocks/video/index.php',
			'category'          => 'common',
			'icon'              => 'format-video',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('video', 'FORBES')),
			'align'				=> 'center',
			'supports'          => array(
				'jsx' => true
			),
		));
	}
}
