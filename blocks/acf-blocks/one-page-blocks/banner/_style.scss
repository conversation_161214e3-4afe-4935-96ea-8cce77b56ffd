html.dark-mode {
  .one-page-banner {
    background-color: var(--one-page-banner-bg-color-dark, $color-surface-secondary);

    &__bg-image {
      display: none;

      &--dark {
        display: block;
      }
    }
  }
}

.one-page-banner {
  background-color: var(--one-page-banner-bg-color, $color-surface-secondary);
  display: flex;
  gap: 4.8rem;
  padding: $spacing-05-20 $spacing-08-40;
  position: relative;

  &--medium {
    gap: 2.4rem;
    margin: auto;
    max-width: 73rem;

    .one-page-banner {
      &__image-wrapper {
        max-height: 20rem;
      }

      &__image {
        object-fit: contain;
        top: 3rem;
      }
    }
  }

  &__bg-image-wrapper {
    height: 100%;
    left: 0;
    top: 0;
    position: absolute;
    width: 100%;
    z-index: 0;
  }

  &__bg-image {
    height: 100%;
    left: 0;
    top: 0;
    position: absolute;
    width: 100%;

    &--dark {
      display: none;
    }
  }

  &__image-wrapper {
    flex: 1;
    position: relative;
    z-index: 1;
  }

  &__image {
    height: 100%;
    inset: 0;
    object-fit: cover;
    position: absolute;
    width: 100%;
  }

  &__content-wrapper {
    flex: 2;
    padding: $spacing-07-32 $spacing-00;
    z-index: 1;
  }

  &__title {
    margin-bottom: $spacing-04-16;
  }

  &__content {
    margin-bottom: $spacing-04-16;
  }

  &__buttons-wrapper {
    display: flex;
    gap: 0.8rem;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    flex-direction: column;
    gap: 1.6rem;
    padding: $spacing-06-24;

    &__image-wrapper {
      flex: none;
      min-height: 20rem;
      max-height: 20rem;
    }

    &__image {
      object-fit: contain;
    }

    &__content-wrapper {
      flex: none;
      padding: $spacing-00;
    }

    &__buttons-wrapper {
      justify-content: space-between;

      .button {
        flex: 1;
      }
    }
  }
}
