<?php
/**
 * The ID of the block
 * @var string
 */
$id = uniqid('onepage-banner-');

/**
 * The size of the block
 * @var string
 */
$size = get_field('size');

/**
 * The image of the block
 * @var string
 */
$image = get_field('image');

/**
 * The title of the block
 * @var string
 * */
$title = get_field('title');

/**
 * The content of the block
 * @var string
 * */
$content = get_field('content');

/**
 * The buttons of the block
 * @var array
 */
$buttons = get_field('buttons');

/**
 * The background type of the block
 * @var string
 */
$bg_type = get_field('bg_type');

/**
 * The background color of the block
 * @var string
 * */
$bg_color = $bg_type === 'color' ? get_field('bg_color') : null;

/**
 * The background color of the block in dark mode
 * @var string
 * */
$bg_color_dark = $bg_type === 'color' ? get_field('bg_color_dark') : null;

/**
 * The background image of the block
 * @var string
 * */
$bg_image = $bg_type === 'image' ? get_field('bg_image') : null;

/**
 * The background image of the block in dark mode
 * @var string
 * */
$bg_image_dark = $bg_type === 'image' ? get_field('bg_image_dark') : null;
?>

<style>
	<?= '#' . $id; ?> {
		--one-page-banner-bg-color: <?= $bg_color ?? null; ?>;
		--one-page-banner-bg-color-dark: <?= $bg_color_dark ?? null; ?>;
	}
</style>

<div id="<?= $id; ?>" class="one-page-banner one-page-banner--<?= $size; ?> one-page-block">

	<?php if ($bg_image && $bg_image_dark) : ?>

		<div class="one-page-banner__bg-image-wrapper">
			<?= wp_get_attachment_image($bg_image, 'full', false, ['class' => 'one-page-banner__bg-image']); ?>
			<?= wp_get_attachment_image($bg_image_dark, 'full', false, ['class' => 'one-page-banner__bg-image one-page-banner__bg-image--dark']); ?>
		</div>

	<?php endif; ?>

	<?php if ($image) : ?>

		<div class="one-page-banner__image-wrapper">

			<?= wp_get_attachment_image($image, 'large', false, ['class' => 'one-page-banner__image']); ?>

		</div>

	<?php endif; ?>

	<div class="one-page-banner__content-wrapper">

		<?php if ($title) : ?>

			<h3 class="one-page-banner__title"><?= $title; ?></h3>

		<?php endif; ?>

		<?php if ($content) : ?>

			<div class="one-page-banner__content"><?= $content; ?></div>

		<?php endif; ?>

		<?php if (!empty($buttons)) : ?>

			<div class="one-page-banner__buttons-wrapper">

				<?php foreach ($buttons as $key => $button) : ?>

					<a href="<?= $button['link']; ?>" <?= $button['new_tab'] ? ' target="_blank" rel="noopener nofollow"' : ''; ?> class="one-page-banner__button button button-<?= $key === 0 ? 'primary' : 'secondary'; ?> button--large"><?= $button['label']; ?></a>

				<?php endforeach; ?>

			</div>

		<?php endif; ?>

	</div>

</div>
