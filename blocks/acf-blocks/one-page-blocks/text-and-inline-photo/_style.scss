.one-page-text-inline-photo {
  display: flex;
  gap: 3.2rem;
  min-height: 61.8rem;

  &--r {
    flex-direction: row-reverse;
  }

  &__image-wrapper {
    flex: 1;
    position: relative;
  }

  &__image {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__content-wrapper {
    flex: 1;
  }

  &__title {
    margin-bottom: $spacing-02;
  }

  &__buttons-wrapper {
    margin-top: $spacing-06-24;
    display: flex;
    gap: 0.8rem;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-inline: -1.6rem;
    min-height: 0;
    flex-direction: column;
    gap: 0.8rem;

    &__image-wrapper {
      height: auto;
    }

    &__content-wrapper {
      padding-inline: $spacing-04-16;
    }

    &__image {
      position: relative;
      height: 41.8rem;
    }

    &__buttons-wrapper {
      margin-top: $spacing-02;
    }
  }
}
