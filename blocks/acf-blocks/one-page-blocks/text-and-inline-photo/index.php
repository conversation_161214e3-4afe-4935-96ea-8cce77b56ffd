<?php

/**
 * The alignment of the image in the block
 * @var string
 */
$alignment = get_field('alignment');

/**
 * The image ID of the block
 * @var string
 * */
$image_id = get_field('image');

/**
 * The title of the block
 * @var string
 * */
$title = get_field('title');

/**
 * The description of the block
 * @var string
 * */
$content = get_field('content');

/**
 * The buttons of the block
 * @var array
 */
$buttons = get_field('buttons');
?>

<div class="one-page-text-inline-photo one-page-block<?= " one-page-text-inline-photo--{$alignment}"; ?>">

	<div class="one-page-text-inline-photo__image-wrapper">

		<?php if ($image_id) : ?>

			<?= wp_get_attachment_image($image_id, 'large', false, ['class' => 'one-page-text-inline-photo__image']) ?>

		<?php endif; ?>

	</div>

	<div class="one-page-text-inline-photo__content-wrapper">

		<?php if ($title) : ?>

			<h3 class="one-page-text-inline-photo__title"><?= $title; ?></h3>

		<?php endif; ?>

		<?php if ($content) : ?>

			<div class="one-page-text-inline-photo__content"><?= $content; ?></div>

		<?php endif; ?>

		<?php if (!empty($buttons)) : ?>

			<div class="one-page-text-inline-photo__buttons-wrapper">

				<?php foreach ($buttons as $key => $button) : ?>

					<?php if ($button['link'] && $button['label']) : ?>

						<a href="<?= $button['link']; ?>" class="one-page-text-inline-photo__button button button--medium<?= $key === 0 ? ' button--primary' : ' button--secondary' ?>">
							<?= $button['label']; ?>
						</a>

					<?php endif; ?>

				<?php endforeach; ?>

			<?php endif; ?>

			</div>

	</div>

</div>
