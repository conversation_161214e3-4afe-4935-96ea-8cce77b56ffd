.one-page-social-icons {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;

  &--center {
    justify-content: center;
  }

  &--right {
    justify-content: flex-end;
  }

  &__inner-container {
    display: flex;
    align-items: center;
    gap: 2.4rem;
    padding: $spacing-07-32;
  }

  &__icon {
    background-color: $color-text-secondary;
    height: 1.8rem;
    @include mask-properties;
    transition: all 0.3s ease;
    width: 1.8rem;

    @media (hover: hover) {
      &:hover {
        transform: scale(1.2);
      }
    }

    &--fb {
      mask-image: url('assets/icons/icon-onepage-facebook.svg');
      -webkit-mask-image: url('assets/icons/icon-onepage-facebook.svg');
    }

    &--tw {
      mask-image: url('assets/icons/icon-onepage-twitter.svg');
      -webkit-mask-image: url('assets/icons/icon-onepage-twitter.svg');
    }

    &--insta {
      mask-image: url('assets/icons/icon-onepage-instagram.svg');
      -webkit-mask-image: url('assets/icons/icon-onepage-instagram.svg');
    }

    &--tk {
      mask-image: url('assets/icons/icon-onepage-tiktok.svg');
      -webkit-mask-image: url('assets/icons/icon-onepage-tiktok.svg');
    }

    &--li {
      mask-image: url('assets/icons/icon-onepage-linkedin.svg');
      -webkit-mask-image: url('assets/icons/icon-onepage-linkedin.svg');
    }

    &--yt {
      mask-image: url('assets/icons/icon-onepage-youtube.svg');
      -webkit-mask-image: url('assets/icons/icon-onepage-youtube.svg');
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    justify-content: center;
  }
}
