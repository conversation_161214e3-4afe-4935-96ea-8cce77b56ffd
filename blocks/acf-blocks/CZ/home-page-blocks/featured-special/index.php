<?php

/**
 * Toggle whether to display only the background
 * @var boolean
 */
$only_background = get_field('only_background') ?? false;

/**
 * The background type of the block
 * @var string
 */
$background_type = get_field('background_type') ?? 'image';

/**
 * The video file of the special block
 * @var string
 */
$background_video = get_field('video_file') ?? null;

/**
 * The poster image of the special block if the background type is video
 * @var string
 */
$poster_image = get_field('poster_image') ?? null;

/**
 * The background of the box for desktop browsers
 * @var string
 */
$background_desktop = get_field('desktop_image') ?? null;

/**
 * The background of the box for mobile browsers
 * @var string
 */
$background_mobile = get_field('mobile_image') ? get_field('mobile_image') : get_field('desktop_image');

/**
 * The alignment of the content in the block
 * @var string
 */
$alignment = get_field('alignment') ?? 'left';

/**
 * The logo of the special block
 * @var string
 */
$logo = get_field('logo') ?? null;

/**
 * The title of the block
 * @var string
 */
$title = get_field('title') ?? null;

/**
 * The description of the special block
 * @var string
 */
$description = get_field('description') ?? null;

/**
 * The label of the button
 * @var string
 */
$button_text = get_field('button_text') ?? null;

/**
 * The custom link of the block
 * @var string
 */
$link = get_field('link') ?? null;
?>

<div class="home-featured-special <?php if (!$only_background) : ?>home-featured-special--with-content<?php endif; ?>">
    <?php
    if ($link && $only_background) {
        echo '<a href="' . esc_url($link) . '" class="home-featured-special__wrapper home-featured-special__wrapper--background home-featured-special__wrapper--' . esc_attr($alignment) . '">';
    } else {
        echo '<div class="home-featured-special__wrapper home-featured-special__wrapper--' . esc_attr($alignment) . '">';
    }
    ?>

    <?php if ($background_type === 'image') : ?>
        <?php if ($background_desktop) : ?>
			<?= wp_get_attachment_image( $background_desktop,
				'home_featured_special',
				false,
				[
					'class'  => 'home-featured-special__background justDesktop',
					'alt'    => esc_attr( $title ),
					'srcset' => wp_get_attachment_image_url( $background_desktop, 'home_featured_special2x' ) . ' 2x',
				] ) ?>
        <?php endif; ?>

        <?php if ($background_mobile) : ?>
			<?= wp_get_attachment_image( $background_mobile,
				'home_featured_special_tablet',
				false,
				[
					'class'  => 'home-featured-special__background justPhone',
					'alt'    => esc_attr( $title ),
					'srcset' => wp_get_attachment_image_url( $background_mobile,
							'home_featured_special_tablet2x' ) . ' 2x',
				] ) ?>
        <?php endif; ?>
    <?php else : ?>
        <?php if ($background_video) : ?>
            <div class="home-featured-special__video-wrapper">
                <div class="home-featured-special__video">
                    <?php
                    $video_id = $background_video;
                    $video_url = wp_get_attachment_url($video_id);
                    ?>

                    <video class="home-featured-special__background-video" playsinline autoplay="autoplay" loop="loop" muted="true" width="100%" poster="<?php echo $poster_image ? esc_url(wp_get_attachment_url($poster_image)) : ''; ?>" alt="<?php echo $poster_image ? esc_attr($image['alt']) : ''; ?>">
                        <source src="<?php echo esc_url($video_url); ?>" type="video/mp4">
                    </video>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <?php if (!$only_background) : ?>
        <div class="home-featured-special__content-wrapper">
            <div class="home-featured-special__content-layer"></div>

           <?php if ($logo) : ?>
				<div class="home-featured-special__logo-wrapper" style="mask-image: url('<?php echo esc_url(wp_get_attachment_url($logo)); ?>'); -webkit-mask-image: url('<?php echo esc_url(wp_get_attachment_url($logo)); ?>');">
				</div>
			<?php endif; ?>


            <?php if ($title) : ?>
                <h2 class="home-featured-special__title"><?= esc_html($title); ?></h2>
            <?php endif; ?>

            <?php if ($description) : ?>
                <p class="home-featured-special__description">
                    <?= wp_kses_post(strip_tags($description)); ?>
                </p>
            <?php endif; ?>

            <?php if ($button_text && $link) :
                $button = new Button($button_text, $link, 'medium', 'primary', false, false, '', '');
                echo $button->render();
            endif;
            ?>
        </div>
    <?php endif; ?>

    <?php
    if ($link && !$button_text) {
        echo '</a>';
    } else {
        echo '</div>';
    }
    ?>
</div>

