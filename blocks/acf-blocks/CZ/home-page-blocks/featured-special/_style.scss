.home-featured-special {
  margin-bottom: $spacing-08-40;
  overflow: hidden;

  &--with-content {
    .home-featured-special {
      &__background,
      &__background-video {
        width: 75rem;
      }

      &__wrapper {
        &--right {
          .home-featured-special__background,
          .home-featured-special__background-video {
            left: 0;
          }
        }

        &--left {
          .home-featured-special__background,
          .home-featured-special__background-video {
            right: 0;
            left: auto;
          }
        }
      }
    }
  }

  &__wrapper {
    position: relative;
    width: 100%;
    text-decoration: none;
    color: initial;
    display: flex;
    align-items: center;
    aspect-ratio: auto 1110 / 324;

    &--background {
      img {
        object-fit: contain;
      }
    }

    &--right {
      flex-direction: row-reverse;

      .home-featured-special {
        &__content-layer {
          background: linear-gradient(270deg, $color-text-primary 0%, $banner-layer 100%);
          left: -18rem;
        }
      }
    }
  }

  &__video-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }

  &__background,
  &__background-video {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: top center;
    z-index: 0;
    transition: all 0.3s ease;
  }

  &__background-video {
    object-position: center;
  }

  &__content-wrapper {
    z-index: 1;
    position: relative;
    max-width: 36rem;
    padding: $spacing-06-24;
    height: 100%;
    min-height: inherit;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: $color-text-primary;
  }

  &__content-layer {
    position: absolute;
    top: 0;
    right: -18rem;
    height: 100%;
    z-index: 0;
    width: 18rem;
    z-index: -1;
    background: linear-gradient(90deg, $color-text-primary 0%, $banner-layer 100%);
  }

  &__logo-wrapper {
    max-width: 6.4rem;
    max-height: 6.5rem;
    width: 6.4rem;
    height: 6.4rem;
    @include mask-properties;
    background-color: $color-surface-primary;
  }

  &__title {
    color: $color-white;
    font-size: 2.4rem;
    line-height: 3.36rem;
    font-weight: 700;
    margin-bottom: $spacing-02;
    max-width: 31rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
  }

  &__description {
    color: $color-white;
    font-size: 1.4rem;
    font-weight: 500;
    line-height: 1.96rem;
    font-family: $font-archivo;
    max-width: 31rem;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
  }

  .button {
    margin-top: $spacing-04-16;
    background-color: $color-surface-primary;
    color: $color-button-primary;
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    margin-bottom: $spacing-07-32;

    &--with-content {
      .home-featured-special {
        &__background,
        &__background-video {
          width: 100%;
        }
      }
    }

    &__wrapper {
      aspect-ratio: auto 768 / 224;
    }

    &__content-layer {
      left: 0;
      right: 0;
      top: -37.5rem;
      height: 37.5rem;
      width: 100%;
      background: linear-gradient(0deg, $color-text-primary 0%, $banner-layer 100%);
    }

    &__logo-wrapper {
      display: none;
    }

    &__content-wrapper {
      width: 100%;
      max-width: 100%;
      height: auto;
      min-height: auto;
    }

    &__title {
      font-size: 2.8rem;
      line-height: 3.36rem;
    }

    .button {
      width: 100%;
    }
  }
}
