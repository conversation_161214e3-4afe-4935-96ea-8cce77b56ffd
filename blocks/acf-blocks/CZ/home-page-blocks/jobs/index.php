<?php
/**
 * Jobs page link
 * @var string
 */
$jobs_link = get_field('jobs_page_link') !== '' ? get_field('jobs_page_link') : get_field('jobs_page_url');

$args = array(
	'posts_per_page' => 5,
	'post_type'		 => 'job',
	'post_status'	 => 'publish',
	'orderby'		 => 'date',
	'order'			 => 'DESC',
);

$query = new WP_Query($args);
?>

<div class="home-jobs">

	<div class="home-jobs__header">

		<?php $newsletterBlockTitle = new Heading(esc_html__('Forbes Jobs', 'FORBES'), 'h4', null, '700', 'archivo'); echo $newsletterBlockTitle->render(); ?>
		<?php
			$link = new Link(esc_html__('All job offers', 'FORBES'), '/newsletter', 'small', true, null,  get_template_directory_uri() . '/assets/icons/icon-arrow-right-black.svg', 'after', '');
			echo $link->render();
		?>
	</div>

	<?php if( $query->have_posts() ):?>

		<div class="home-jobs__list">

			<?php while( $query->have_posts() ): $query->the_post();?>

				<?php $jobCard = new JobCard(get_the_ID());
				$jobCard->render();
				?>

			<?php endwhile;?>

		</div>

	<?php endif;?>

</div>
