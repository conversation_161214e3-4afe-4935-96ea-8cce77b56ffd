.home-special-list {
  padding: $spacing-06-24;
  background-color: $color-surface-secondary;

  &__title {
    text-decoration: none;
    color: $color-text-primary;
    font-size: 2.4rem;
    line-height: 3.4rem;
    font-weight: 700;
    margin-bottom: $spacing-06-24;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    width: 100vw;
    margin-left: calc((100% - 100vw) / 2);

    &__title {
      font-size: 2rem;
      line-height: 2.8rem;
      margin-bottom: $spacing-04-16;
    }
  }
}
