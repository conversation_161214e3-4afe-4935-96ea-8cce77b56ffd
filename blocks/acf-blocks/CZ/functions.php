<?php
add_action('acf/init', 'forbes_init_cz_blocks');
function forbes_init_cz_blocks() {

	// Check function exists.
	if (function_exists('acf_register_block_type')) {

		/*
		ACF BLOCKS FOR CZ SITE
		*/

		// register the SPECIAL LIST
		acf_register_block_type(array(
			'name'              => 'special-list',
			'title'             => esc_html__('Home: Special List', 'FORBES'),
			'description'       => esc_html__('Top 5 of the chosen list', 'FORBES'),
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/CZ/home-page-blocks/special-list/index.php',
			'category'          => 'common',
			'icon'              => 'businessperson',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('special', 'FORBES'), esc_html__('list', 'FORBES')),
			'align'				=> 'center',
		));

		// register the FEATURED SPECIAL
		acf_register_block_type(array(
			'name'              => 'featured-special',
			'title'             => esc_html__('Home: Featured Special', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/CZ/home-page-blocks/featured-special/index.php',
			'category'          => 'common',
			'icon'              => 'site',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('featured', 'FORBES'), esc_html__('special', 'FORBES')),
			'align'				=> 'center',
		));

		// register the ROADBLOCK AD
		acf_register_block_type(array(
			'name'              => 'roadblock-ad',
			'title'             => esc_html__('Single: Roadblock Ad', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/CZ/single-page-blocks/roadblock-ad/index.php',
			'category'          => 'common',
			'icon'              => 'site',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('roadblock', 'FORBES'), esc_html__('ad', 'FORBES'), esc_html__('advertisement', 'FORBES')),
			'align'				=> 'center',
		));

		// register the INTERSCROLLER AD
		acf_register_block_type(array(
			'name'              => 'interscroller-ad',
			'title'             => esc_html__('Single: InterScroller Ad', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/CZ/single-page-blocks/interscroller-ad/index.php',
			'category'          => 'common',
			'icon'              => 'site',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('interscroller', 'FORBES'), esc_html__('ad', 'FORBES'), esc_html__('advertisement', 'FORBES')),
			'align'				=> 'center',
		));

		// register the OUTSTREAM AD
		acf_register_block_type(array(
			'name'              => 'outstream-ad',
			'title'             => esc_html__('Single: Outstream Ad', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/CZ/single-page-blocks/outstream-ad/index.php',
			'category'          => 'common',
			'icon'              => 'site',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('outstream', 'FORBES'), esc_html__('ad', 'FORBES'), esc_html__('advertisement', 'FORBES')),
			'align'				=> 'center',
		));

		// register the LIFE DETAILS
		acf_register_block_type(array(
			'name'              => 'life-details',
			'title'             => esc_html__('Single: Life Details', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/CZ/single-page-blocks/life-details/index.php',
			'category'          => 'common',
			'icon'              => 'food',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('life', 'FORBES'), esc_html__('detail', 'FORBES')),
			'align'				=> 'center',
		));

		// register the MAGAZINE REVIEW
		acf_register_block_type(array(
			'name'              => 'magazine_review',
			'title'             => esc_html__('Magazine: Review', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/CZ/single-page-blocks/roadblock-ad/index.php',
			'category'          => 'common',
			'icon'              => 'editor-spellcheck',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('magazine', 'FORBES'), esc_html__('review', 'FORBES')),
			'align'				=> 'center',
		));
	}
}
