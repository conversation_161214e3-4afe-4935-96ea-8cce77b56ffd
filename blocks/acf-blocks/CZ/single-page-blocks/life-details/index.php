<?php

/**
 * The items to list
 * @var array
 */
$items = get_field('items');
?>

<?php if (!empty($items)) : ?>

	<div class="life-details">

		<?php foreach ($items as $item) : ?>

			<div class="life-details__detail-wrapper">

				<?php if ($item['icon']) : ?>

					<?= wp_get_attachment_image( $item['icon'], 'thumbnail', true, ['class' => 'life-details__detail-icon', 'alt' => esc_html__('Icon', 'FORBES')] );?>

				<?php endif; ?>

				<?php if ($item['label']) : ?>

					<span class="life-details__detail-label callout"><?= $item['label']; ?></span>

				<?php endif; ?>

			</div>

		<?php endforeach; ?>

	</div>

<?php endif; ?>
