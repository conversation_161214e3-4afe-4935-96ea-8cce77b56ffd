.life-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: $spacing-06-24;
  border-top: 0.1rem solid $color-divider;
  margin-left: 13.014%;
  margin-right: 13.014%;
  margin-bottom: $spacing-06-24;
  width: auto;

  &__detail-wrapper {
    @include flexbox-properties;
    margin-right: $spacing-04-16;

    &:last-child {
      margin-right: $spacing-00;
    }
  }

  &__detail-icon {
    width: 2.4rem;
    height: 2.4rem;
    object-fit: contain;
    margin-right: $spacing-03-12;
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    margin-left: $spacing-00;
    margin-right: $spacing-00;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    flex-direction: column;
    align-items: flex-start;
    padding-bottom: $spacing-06-24;
    border-bottom: 0.1rem solid $color-divider;

    &__detail-wrapper {
      margin-right: $spacing-00;
      margin-bottom: $spacing-04-16;

      &:last-child {
        margin-bottom: $spacing-00;
      }
    }
  }
}
