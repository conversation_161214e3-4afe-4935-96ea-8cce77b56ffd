.property-widget {
  background-color: #f3f4f6;
  padding: 4rem;

  &.widget-type-4 {
    .property-widget__table {
      &--mobile {
        display: none;
      }
    }
  }

  h4,
  table {
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 2.4rem !important;
  }

  table {
    border-collapse: collapse;
    width: 100% !important;
  }

  td {
    font-size: 1.8rem;
    padding: 0.8rem 2.4rem;
    text-align: right;

    &:first-child {
      text-align: left;
    }
  }

  tr:nth-child(odd) {
    background-color: $background-0;
  }

  &__bottom-text {
    font-size: 1.2rem !important;
    margin-top: 0 !important;
    line-height: 1.9rem !important;
    margin-right: 2.4rem;

    a {
      font-size: 1.2rem !important;
      line-height: 1.9rem !important;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    padding: 2.4rem;
    margin-left: -1.5rem;
    margin-right: -1.5rem;

    &.widget-type-4 {
      .property-widget__table {
        &--desktop {
          display: none;
        }

        &--mobile {
          display: table;

          span:first-child {
            font-weight: 500;
          }
        }
      }
    }

    span {
      margin: 0.4rem 0;
    }

    h4 {
      margin-bottom: 1.6rem !important;
    }

    td {
      padding: 0.8rem 1.6rem;
      font-size: 1.6rem;
    }

    &__bottom-text {
      margin-right: 0;
    }
  }
}
