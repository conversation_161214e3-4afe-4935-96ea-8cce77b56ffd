<?php

$widget_type = get_field('widget_type');

$data = \PropertyWidget\PropertyData::get_data();

$mobile_structure = false;

if($widget_type == 1) {
	$title = $data[3][1];
	$current_data = [$data[4], $data[5], $data[6]];
} else if($widget_type == 2) {
	$title = $data[9][1];
	$current_data = [$data[10], $data[11], $data[12], $data[13]];
} else if($widget_type == 3) {
	$title = $data[16][1];
	$current_data = [$data[17], $data[18], $data[19]];
} else {
	$title = $data[22][1];
	$current_data = [$data[23], $data[24], $data[25], $data[26]];
	$mobile_structure = true;
	$mobile_rows = [
		[[$data[24][1], null], [$data[23][2], $data[24][2]], [$data[23][3], $data[24][3]]],
		[[$data[25][1], null], [$data[23][2], $data[25][2]], [$data[23][3], $data[25][3]]],
		[[$data[26][1], null], [$data[23][2], $data[26][2]], [$data[23][3], $data[26][3]]]
	];
}

?>

<div class="property-widget widget-type-<?= $widget_type ?>">
	<h4><?= $title ?></h4>

	<table class="property-widget__table--desktop">

	<?php
	foreach($current_data as $row) {
		echo '<tr>';
		if (is_array($row) && !empty($row)) {
			foreach($row as $cell) {
				if($cell) {
					echo '<td>' . $cell . '</td>';
				}
			}
		}
		echo '</tr>';
	}

	if($mobile_structure) {

	?>

	<table class="property-widget__table--mobile">

	<?php

		foreach($mobile_rows as $main_row) {
			echo '<tr><td>';
			foreach($main_row as $sub_row) {
				if($sub_row) {
					echo '<div class="h-d--flex h-justify-content--between">';
					foreach($sub_row as $cell) {
						if($cell) {
							echo '<span>' . $cell . '</span>';
						}
					}
					echo '</div>';
				}
			}
			echo '</tr></td>';
		}

	?>
	</table>

	<?php

	}
	?>
	</table>



	<div class="h-d--flex h-align-items--start h-flex--md--wrap">
		<p class="property-widget__bottom-text">Údaje vychází z dat portálu <a href="https://flatzone.cz" target="_blank">Flatzone.cz</a>, který mapuje trh novostaveb. Jedná se o redakční spolupráci. Údaje jsou aktuální v čase a vychází z dat portálu.</p>
		<img class="property-widget__logo" src="<?= get_template_directory_uri() . '/assets/images/flatzone-logo.svg' ?>" alt="">
	</div>
</div>
