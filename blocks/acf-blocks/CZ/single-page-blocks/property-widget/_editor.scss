.property-widget {
  background-color: $color-surface-secondary;
  padding: 40px;

  &.widget-type-4 {
    .property-widget__table {
      &--mobile {
        display: none;
      }
    }
  }

  h4,
  table {
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 24px !important;
  }

  table {
    border-collapse: collapse;
    width: 100% !important;
  }

  td {
    font-size: 18px;
    padding: 8px 24px;
    text-align: right;

    &:first-child {
      text-align: left;
    }
  }

  tr:nth-child(odd) {
    background-color: $color-surface-primary;
  }

  &__bottom-text {
    font-size: 12px !important;
    margin-top: 0 !important;
    line-height: 19px !important;
    margin-right: 24px;

    a {
      font-size: 12px !important;
      line-height: 19px !important;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    padding: 24px;
    margin-left: -15px;
    margin-right: -15px;

    &.widget-type-4 {
      .property-widget__table {
        &--desktop {
          display: none;
        }

        &--mobile {
          display: table;

          span:first-child {
            font-weight: 500;
          }
        }
      }
    }

    span {
      margin: 4px 0;
    }

    h4 {
      margin-bottom: 16px !important;
    }

    td {
      padding: 8px 16px;
      font-size: 16px;
    }

    &__bottom-text {
      margin-right: 0;
    }
  }
}
