<?php

/**
 * Block Name: Limited Edition
 *
 * This is the template that displays the limited edition block.
 *
 * @package FORBES
 */

if ( ! defined('ABSPATH')) {
	exit; // Exit if accessed directly.
}

$anchor        = get_field('field_limited_edition_anchor');
$title         = get_field('field_limited_edition_title');
$description   = get_field('field_limited_edition_description');
$limited_offer = get_field('field_limited_edition_limited_offer');

?>

<div class="reactLimitedEdition <?php
echo esc_attr($block['className'] ?? ''); ?>"
	<?php
	if ( ! empty($anchor)) : ?>
		id="<?php
		echo esc_attr($anchor); ?>"
	<?php
	endif; ?>
	 data-title="<?php
	 echo esc_attr(wp_json_encode($title)); ?>"
	 data-description="<?php
	 echo esc_attr(wp_json_encode($description)); ?>"
	 data-limited-offer="<?php
	 echo esc_attr(wp_json_encode(acf_array_camel_case($limited_offer))); ?>"
></div>