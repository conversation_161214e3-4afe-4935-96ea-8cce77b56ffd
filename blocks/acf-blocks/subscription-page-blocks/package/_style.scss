.subscription-package {
  @include flexbox-properties;
  flex-direction: column;
  padding: $spacing-11-64;
  margin-bottom: $spacing-09-48;
  background-color: $color-surface-secondary;

  &__title {
    margin-bottom: $spacing-09-48 !important;
    color: $color-text-secondary;
    font-family: $font-archivo !important;
    text-transform: uppercase;
  }

  &__grid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    grid-gap: $spacing-04-16;
  }

  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    &:after {
      content: '';
      position: absolute;
      right: -0.8rem;
      top: 50%;
      transform: translateY(-50%);
      height: 65%;
      border-right: 0.2rem solid $color-divider;
    }

    &:last-child {
      &:after {
        content: none;
      }
    }
  }

  &__item-image {
    height: 7.2rem;
    width: 5.9rem;
    object-fit: contain;
    margin-bottom: $spacing-06-24;
  }

  &__item-title {
    color: $color-text-secondary;
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    padding: $spacing-07-32;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-bottom: $spacing-07-32;
    padding: $spacing-04-16;

    &__title {
      margin-bottom: $spacing-07-32;
    }

    &__grid {
      grid-row-gap: $spacing-07-32;
    }

    &__item {
      &:after {
        right: -1rem;
      }

      &:nth-child(2) {
        &:after {
          content: none;
        }
      }
    }
  }
}
