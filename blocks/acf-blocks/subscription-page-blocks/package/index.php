<?php

/**
 * The title of the block
 * @var string
 */
$title = get_field('title');

/**
 * The title of the block
 * @var string
 */
$title = get_field('title');

/**
 * The items to be listed
 * @var array
 */
$items = get_field('items');
?>


<?php if (isset($items) && !empty($items)) : ?>

    <div class="subscription-package">

        <?php if (isset($title)) : ?>

            <h4 class="subscription-package__title"><?= $title; ?></h4>

        <?php endif; ?>

        <div class="subscription-package__grid">

            <?php foreach ($items as $item) : ?>

                <div class="subscription-package__item">

                    <?php if (isset($item['image'])) : ?>

                        <?= wp_get_attachment_image($item['image'], 'thumbnail', false, ['class' => 'subscription-package__item-image', 'alt' => $item['title']]); ?>

                    <?php endif; ?>

                    <?php if (isset($item['title'])) : ?>

                        <span class="subscription-package__item-title minititle"><?= $item['title']; ?></span>

                    <?php endif; ?>

                </div>

            <?php endforeach; ?>

        </div>

    </div>

<?php endif; ?>
