<?php
add_action('acf/init', 'forbes_init_subscription_page_block_types');
function forbes_init_subscription_page_block_types() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

		/*
		ACF BLOCKS FOR SUBSCRIPTION PAGES
		*/


		// register the ABOUT
        acf_register_block_type(array(
            'name'              => 'subscription-about',
            'title'             => esc_html__('Subscription: About', 'FORBES'),
            'description'       => '',
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/subscription-page-blocks/about/index.php',
            'category'          => 'common',
            'icon'              => 'align-right',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('about', 'FORBES') ),
			'align'				=> 'center',
        ));

		// register the WHY SUBSCRIBE
        acf_register_block_type(array(
            'name'              => 'subscription-why-subscribe',
            'title'             => esc_html__('Subscription: Why Subscribe?', 'FORBES'),
            'description'       => '',
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/subscription-page-blocks/why-subscribe/index.php',
            'category'          => 'common',
            'icon'              => 'welcome-learn-more',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('why', 'FORBES'), esc_html__('subscribe', 'FORBES') ),
			'align'				=> 'center',
        ));

		// register the PACKAGE
        acf_register_block_type(array(
            'name'              => 'subscription-package',
            'title'             => esc_html__('Subscription: Package', 'FORBES'),
            'description'       => '',
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/subscription-page-blocks/package/index.php',
            'category'          => 'common',
            'icon'              => 'filter',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('package', 'FORBES') ),
			'align'				=> 'center',
        ));

		// register the ADVICE
        acf_register_block_type(array(
            'name'              => 'subscription-advice',
            'title'             => esc_html__('Subscription: Advice', 'FORBES'),
            'description'       => '',
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/subscription-page-blocks/advice/index.php',
            'category'          => 'common',
            'icon'              => 'status',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('advice', 'FORBES') ),
			'align'				=> 'center',
        ));

		// ******************************************************************************************************** //
		// New subscription blocks

		// register the ADVICE
        acf_register_block_type(array(
            'name'              => 'subscription-header',
            'title'             => esc_html__('Subscription: Header', 'FORBES'),
            'description'       => '',
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/subscription-page-blocks/header/index.php',
            'category'          => 'common',
            'icon'              => 'status',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('header', 'FORBES'), esc_html__('subscribe', 'FORBES') ),
			'align'				=> 'center',
        ));

		// register the WHY SUBSCRIBE
        acf_register_block_type(array(
            'name'              => 'subscription-why-subscribe-2',
            'title'             => esc_html__('Subscription: Why Subscribe? New', 'FORBES'),
            'description'       => '',
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/subscription-page-blocks/why-subscribe-2/index.php',
            'category'          => 'common',
            'icon'              => 'welcome-learn-more',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('why', 'FORBES'), esc_html__('subscribe', 'FORBES') ),
			'align'				=> 'center',
        ));

		// register the WHY SUBSCRIBE
        acf_register_block_type(array(
            'name'              => 'subscription-slider',
            'title'             => esc_html__('Subscription: Slider', 'FORBES'),
            'description'       => '',
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/subscription-page-blocks/slider/index.php',
            'category'          => 'common',
            'icon'              => 'welcome-learn-more',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('slider', 'FORBES'), esc_html__('subscribe', 'FORBES') ),
			'align'				=> 'center',
			'enqueue_assets'	=> function() {
				wp_enqueue_script( 'sub-slider-script', get_template_directory_uri() . '/minified-js/subscription-page-blocks/slider/scripts.min.js', array('jquery'), filemtime(get_template_directory(  ) . '/minified-js/subscription-page-blocks/slider/scripts.min.js'), true );
			}
        ));

		acf_register_block_type(array(
			'name'              => 'subscription-faq',
			'title'             => esc_html__('Subscription: FAQ', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory(  ) . '/blocks/acf-blocks/shared/accordion/index.php',
			'category'          => 'common',
			'icon'              => 'feedback',
			'keywords'          => array( esc_html__('faq', 'FORBES'), esc_html__('subscribe', 'FORBES') ),
			'align'				=> 'center',
			'enqueue_assets' => function(){
				wp_enqueue_script('faq-script', get_template_directory_uri() . '/minified-js/shared/accordion/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/shared/accordion/scripts.min.js'), true );
			},
		));

		acf_register_block_type(array(
			'name'              => 'subscription-cta',
			'title'             => esc_html__('Subscription: CTA', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory(  ) . '/blocks/acf-blocks/subscription-page-blocks/cta/index.php',
			'category'          => 'common',
			'icon'              => 'feedback',
			'keywords'          => array( esc_html__('cta', 'FORBES'), esc_html__('subscribe', 'FORBES') ),
			'align'				=> 'center',
		));

		acf_register_block_type(array(
			'name'              => 'subscription-popups',
			'title'             => esc_html__('Subscription: Popups', 'FORBES'),
			'description'       => '',
			'render_template'   => get_stylesheet_directory(  ) . '/blocks/acf-blocks/subscription-page-blocks/popups/index.php',
			'category'          => 'common',
			'icon'              => 'feedback',
			'keywords'          => array( esc_html__('popups', 'FORBES'), esc_html__('subscribe', 'FORBES') ),
			'align'				=> 'center',
			'enqueue_assets' => function(){
				wp_enqueue_script('popups-script', get_template_directory_uri() . '/minified-js/subscription-page-blocks/popups/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/subscription-page-blocks/popups/scripts.min.js'), true );
			},
		));

	    acf_register_block_type(array(
		    'name'            => 'subscriptions',
		    'title'           => esc_html__('Offer', 'FORBES'),
		    'description'     => '',
		    'render_template' => get_stylesheet_directory(
		                         ) . '/blocks/acf-blocks/subscription-page-blocks/subscriptions/index.php',
		    'category'        => 'common',
		    'align'           => 'center',
		    'mode'            => 'edit',
	    ));

	    acf_register_block_type(array(
		    'name'            => 'limited-edition',
		    'title'           => esc_html__('Limited Edition', 'FORBES'),
		    'description'     => '',
		    'render_template' => get_stylesheet_directory(
		                         ) . '/blocks/acf-blocks/subscription-page-blocks/limited-edition/index.php',
		    'category'        => 'common',
		    'align'           => 'center',
		    'mode'            => 'edit',
	    ));

	    acf_register_block_type(array(
		    'name'            => 'other-subscriptions',
		    'title'           => esc_html__('Other Subscriptions', 'FORBES'),
		    'description'     => '',
		    'render_template' => get_stylesheet_directory(
		                         ) . '/blocks/acf-blocks/subscription-page-blocks/other-subscriptions/index.php',
		    'category'        => 'common',
		    'align'           => 'center',
		    'mode'            => 'edit',
	    ));

	    acf_register_block_type(array(
		    'name'            => 'testimonial',
		    'title'           => esc_html__('Testimonial', 'FORBES'),
		    'description'     => '',
		    'render_template' => get_stylesheet_directory(
		                         ) . '/blocks/acf-blocks/subscription-page-blocks/testimonial/index.php',
		    'category'        => 'common',
		    'align'           => 'center',
		    'mode'            => 'edit',
	    ));

	    acf_register_block_type(array(
		    'name'            => 'subscription-form',
		    'title'           => esc_html__('Subscription Form', 'FORBES'),
		    'description'     => '',
		    'render_template' => get_stylesheet_directory(
		                         ) . '/blocks/acf-blocks/subscription-page-blocks/subscription-form/index.php',
		    'category'        => 'common',
		    'align'           => 'center',
		    'mode'            => 'edit'
	    ));
    }
}
