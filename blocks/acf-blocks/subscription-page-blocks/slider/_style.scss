.subscription-slider {
  position: relative;
  margin: $spacing-11-64 $spacing-00;

  &__blur {
    padding: $spacing-11-64 $spacing-13-120;
    display: flex !important;
    flex-direction: column;
    justify-content: space-between;
    min-height: 55.2rem !important;
    height: 100% !important;
  }

  &__quote-mark {
    margin-bottom: $spacing-04-16;
    height: 2.7rem;
  }

  &__item {
    position: relative;
    background-size: cover;
    background-position: left;

    &.dark {
      .subscription-slider__item__name,
      .subscription-slider__item__text {
        color: $color-white;
      }

      .subscription-slider__item__title {
        color: $color-divider;
      }
    }

    &__mobile-image {
      display: none !important;
    }

    &__text {
      font-family: $font-noto-serif !important;
    }

    &__text {
      font-family: $font-archivo !important;
    }

    &__name,
    &__text {
      font-size: 2.4rem;
      line-height: 3.2rem;
      color: $color-surface-invert;
      max-width: 60%;
    }

    &__name {
      font-weight: 500;
      white-space: nowrap;
      font-family: $font-noto-serif !important;
    }

    &__title {
      margin-top: 0.8rem;
      color: $color-text-secondary;
      padding-bottom: 6.4rem;
      font-family: $font-archivo;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl)) {
    &__blur {
      min-height: 42.2rem !important;
      padding: $spacing-07-32 $spacing-04-16;
    }

    &__item {
      &__title {
        margin-top: $spacing-00;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin: $spacing-07-32 $spacing-00;

    &__blur {
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
      min-height: auto !important;
    }

    &__item {
      background-position: center;

      &__text {
        margin-bottom: $spacing-07-32;
      }

      &__name,
      &__text {
        max-width: 100%;
      }

      &__mobile-image {
        display: block !important;
        margin-right: $spacing-02;
        border-radius: 50%;
        height: 5.6rem;
        width: 5.6rem;
      }

      &__title {
        margin-top: $spacing-00;
        padding-bottom: $spacing-00;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 28rem;
      }
    }
  }
}
