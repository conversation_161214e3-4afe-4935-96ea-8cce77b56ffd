<div id="subscription-slider" class="splide subscription-slider splide-redesign">
    <div class="splide__track">
        <ul class="splide__list">

            <?php if ( have_rows('items') ) : ?>
                <?php while( have_rows('items') ) : the_row();
                    $background_image = wp_get_attachment_image_url(get_sub_field('background_image'), 'large');
                    $theme = get_sub_field('lightdark_style');
                ?>

                    <li class="splide__slide">
                        <div class="subscription-slider__item <?= $theme ?>" style="background-image: url(<?= $background_image ?>)">
                            <div class="subscription-slider__blur">
                                <div>
                                    <img src="<?php echo get_template_directory_uri(). '/assets/icons/icon-quote.svg' ?>" alt="" class="subscription-slider__quote-mark h-object-fit--contain">

                                    <p class="subscription-slider__item__text">
                                        <?php the_sub_field('text'); ?>
                                    </p>
                                </div>
                                <div class="h-d--flex">
                                    <?=  wp_get_attachment_image( get_sub_field('mobile_image'), 'full', "", ["class" => "subscription-slider__item__mobile-image h-object-fit--contain"]  ); ?>
                                    <div>
                                        <p class="subscription-slider__item__name">
                                            <?php the_sub_field('name'); ?>
                                        </p>
                                        <p class="subscription-slider__item__title">
                                            <?php the_sub_field('title'); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>

                <?php endwhile; ?>
            <?php endif; ?>

        </ul>
    </div>
</div>