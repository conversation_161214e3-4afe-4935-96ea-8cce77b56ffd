.subscription-cta {
  position: relative;
  margin: $spacing-11-64 $spacing-00;

  &__title {
    margin-bottom: $spacing-06-24;
    width: 60%;
    font-family: $font-archivo !important;
    font-weight: 600;
  }

  &__content {
    padding: $spacing-11-64 $spacing-00;
    padding-left: 13.014%;
    width: auto;
    background: linear-gradient(
      90deg,
      $color-surface-secondary 0%,
      $color-surface-secondary 62%,
      rgba(0, 212, 255, 0) 100%
    );
    position: relative;
    z-index: 1;
  }

  &__button {
    display: inline-flex;

    &::after {
      background-color: $color-divider;
    }
  }

  img,
  &__image {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    object-fit: cover;
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    &__content {
      padding-left: $spacing-04-16;
      padding-right: $spacing-04-16;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    margin: $spacing-07-32 $spacing-00;

    &__title {
      width: 100%;
      margin-bottom: $spacing-04-16;
    }

    &__content {
      padding-top: 26.4rem;
      background: linear-gradient(
        0deg,
        $color-surface-secondary 0%,
        $color-surface-secondary 42%,
        rgba(0, 212, 255, 0) 100%
      );
      padding-bottom: $spacing-04-16;
    }

    &__button {
      display: flex;
    }

    img,
    &__image {
      left: 0;
      bottom: auto;
      height: auto;
      width: 100%;
    }
  }
}
