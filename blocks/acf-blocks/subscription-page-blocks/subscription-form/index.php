<?php

$inModal         = get_field('in_modal');
$modalIdentifier = get_field('form_id');
$title           = get_field('title');
$description     = get_field('description');
$contacts        = get_field('contacts');
$formEmails      = get_field('form_emails');

?>

<div class="reactSubscriptionForm <?php
echo esc_attr($block['className'] ?? ''); ?>"
	 data-in-modal="<?php
	 echo esc_attr($inModal ? 'true' : 'false'); ?>"
	 data-modal-identifier="<?php
	 echo esc_attr($modalIdentifier); ?>"
	 data-title="<?php
	 echo esc_html(wp_json_encode($title)); ?>"
	 data-description="<?php
	 echo esc_html(wp_json_encode($description)); ?>"
	 data-contacts='<?php
	 echo esc_attr(wp_json_encode($contacts)); ?>'
	 data-form-emails='<?php
	 echo esc_attr(wp_json_encode($formEmails)); ?>'
></div>
