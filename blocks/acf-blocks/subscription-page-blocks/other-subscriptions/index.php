<?php

/**
 * Block Name: Other Subscriptions
 *
 * This is the template that displays the other subscriptions block.
 *
 * @package FORBES
 */

if ( ! defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

$inverted_colors = get_field('field_other_subscriptions_inverted_colors');
$title           = get_field('field_other_subscriptions_title');
$description     = get_field('field_other_subscriptions_description');
$button_url      = get_field('field_other_subscriptions_button_url');
$button_text     = get_field('field_other_subscriptions_button_text');
$other_subscriptions_cards = get_field('field_other_subscriptions_other_subscriptions_cards');

// Fix: apply acf_array_camel_case to each card if it's an array of objects
$other_subscriptions_cards_camel = [];
if (is_array($other_subscriptions_cards)) {
	foreach ($other_subscriptions_cards as $card) {
		$other_subscriptions_cards_camel[] = acf_array_camel_case($card);
	}
}

?>

<div class="reactOtherSubscriptions <?php
echo esc_attr($block['className'] ?? ''); ?>"
	 data-inverted-colors="<?php
	 echo esc_attr($inverted_colors ? 'true' : 'false'); ?>"
     data-title="<?php echo esc_attr(wp_json_encode($title)); ?>"
	 data-description="<?php
	 echo esc_attr(wp_json_encode($description)); ?>"
	 data-button-url="<?php
	 echo esc_attr($button_url); ?>"
	 data-button-text="<?php
	 echo esc_attr($button_text); ?>"
	 data-other-subscriptions-cards="<?php
	 echo esc_attr(wp_json_encode($other_subscriptions_cards_camel)); ?>"
></div>
