.subscription-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-11-64;
  background-color: $color-surface-secondary;

  &__subtitle {
    font-size: 1.4rem;
    line-height: 1.6rem;
    margin-bottom: $spacing-04-16;
    color: $color-text-brand;
    font-family: $font-archivo !important;
  }

  &__title {
    font-size: 7.2rem;
    line-height: 8rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: $spacing-07-32;
    padding: $spacing-00 $spacing-13-120;
    font-family: $font-noto-serif !important;
  }

  &__text {
    font-size: 2.4rem;
    line-height: 3.2rem;
    margin-bottom: $spacing-07-32;
    text-align: center;
    color: $color-text-secondary;
    padding: $spacing-00 $spacing-13-120;
    font-family: $font-archivo;
  }

  &__button {
    display: inline-block;
    margin-bottom: $spacing-07-32;
  }

  &__separator {
    border-bottom: 0.1rem solid $color-divider;
    margin-bottom: $spacing-07-32;
    width: 75%;
  }
  &__bottom-image {
    height: 4.8rem;
    object-fit: contain;
    object-position: right;
    flex: 50%;
    aspect-ratio: auto;
    width: auto;
  }

  &__bottom-text {
    font-size: 1.4rem;
    line-height: 1.8rem;
    color: $color-text-secondary;
    margin-left: $spacing-04-16;
    flex: 50%;
    max-width: 26rem;
    font-family: $font-archivo;
  }

  @media screen and (max-width: map-get($container-max-widths, xl)) {
    padding: $spacing-06-24 $spacing-04-16;

    &__subtitle {
      margin-bottom: $spacing-02;
    }

    &__title {
      font-size: 4rem;
      line-height: 4.8rem;
      padding: $spacing-00;
      margin-bottom: $spacing-06-24;
    }

    &__text {
      padding: $spacing-00;
      margin-bottom: $spacing-06-24;
      font-size: 1.8rem;
      line-height: 2.4rem;
    }

    &__button {
      margin-bottom: $spacing-06-24;
    }

    &__bottom-text,
    &__bottom-image {
      flex: 100%;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &__bottom-image {
      margin-bottom: $spacing-04-16;
      max-height: 4.8rem;
      object-position: center;
    }

    &__bottom-text {
      text-align: center;
    }

    &__separator {
      width: 100%;
    }
  }
}
