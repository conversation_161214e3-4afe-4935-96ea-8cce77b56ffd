<?php

/**
 * Block Name: Subscriptions
 *
 * This is the template that displays the subscriptions block.
 *
 * @package FORBES
 */

if ( ! defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

$badge                      = get_field('field_subscriptions_badge');
$title                      = get_field('field_subscriptions_title');
$description                = get_field('field_subscriptions_description');
$background_color           = get_field('field_subscriptions_background_color');
$type                       = get_field('field_subscriptions_type');
$only_for_logged_in_users   = get_field('field_subscriptions_only_for_logged_in_users');
$logged_out_text            = get_field('field_subscriptions_logged_out_text');
$only_for_segments          = get_field('field_subscriptions_only_for_segments');
$not_in_segment_text        = get_field('field_subscriptions_not_in_segment_text');
$not_in_segment_button_text = get_field('field_subscriptions_not_in_segment_button_text');
$not_in_segment_button_link = get_field('field_subscriptions_not_in_segment_button_link');
$link                       = get_field('field_subscriptions_link');
$link_text                  = get_field('field_subscriptions_link_text');
$subscriptions_products     = get_field('field_subscriptions_products') ?: [];
$extra_pricing              = get_field('field_subscriptions_extra_pricing');
$extra_product              = get_field('field_subscriptions_extra_product');

$rempClient = new RempClient();
$subscriptions_products_mapped = [];
$extra_product_mapped = [];

if ( ! empty($subscriptions_products)) {
	$response = $rempClient->setPayload([
		'ids' => implode(
			',',
			array_map(
				function ($product) {
					return $product['id'];
				},
				$subscriptions_products
			)
		),
	])->get('subscriptions/types');

	if ($response->status !== "ok") {
		return;
	}

	$subscriptions_products_mapped = array_map(
		function ($product) use ($subscriptions_products) {
			return [
				'id'                => $product->id,
				'isFavorite'        => $product->meta->is_favorite === 'true',
				'favoriteText'      => $product->meta->favorite_text,
				'name'              => $product->meta->subscription_name,
				'buttonVariant'     => $product->meta->button_variant,
				'buttonText'        => $product->meta->button_text,
				'buttonLink'        => $product->meta->button_link,
				'description'       => $product->meta->description,
				'included'          => array_filter(
					array_map(
						function ($i) use ($product) {
							if (isset($product->meta->{'info_' . $i}) || isset($product->meta->{'info_' . $i . '_hover'})) {
								return [
									'text'  => $product->meta->{'info_' . $i} ?? null,
									'hover' => $product->meta->{'info_' . $i . '_hover'} ?? null
								];
							}

							return null;
						},
						range(1, 30)
					)
				),
				'price'             => $product->meta->show_price_lower ?: $product->price,
				'currency'          => $product->currency,
				'pricePeriod'       => $product->meta->price_period,
				'icon'              => $product->meta->icon,
				'benefitTitleIcon'  => $product->meta->benefit_title_icon,
				'benefitTitle'      => $product->meta->benefit_title,
				'elevated'          => $product->meta->elevated === 'true',
				'socialProof'       => current(
										   array_filter(
											   $subscriptions_products,
											   fn($p) => (int)$p['id'] === $product->id
										   )
									   )['social_proof'] ?? false,
				'socialProofImages' => current(
										   array_filter(
											   $subscriptions_products,
											   fn($p) => (int)$p['id'] === $product->id
										   )
									   )['social_proof_images'] ?? null,
				'socialProofText'   => current(
										   array_filter(
											   $subscriptions_products,
											   fn($p) => (int)$p['id'] === $product->id
										   )
									   )['social_proof_text'] ?? null,
				'precision' => $product->meta->precision ?? 2,
			];
		},
		$response->subscriptionTypes
	);

	$extra_product_response = $rempClient->setPayload([
		'ids' => $extra_product['id'],
	])->get('subscriptions/types');

	$extra_product_mapped = [
		'id'          => $extra_product_response->subscriptionTypes[0]->id,
		'bgImageSrc'  => $extra_product_response->subscriptionTypes[0]->meta->bg_image_src,
		'description' => $extra_product_response->subscriptionTypes[0]->meta->description,
		'name'        => $extra_product_response->subscriptionTypes[0]->meta->subscription_name,
		'buttonText'  => $extra_product_response->subscriptionTypes[0]->meta->button_text,
		'buttonLink'  => $extra_product_response->subscriptionTypes[0]->meta->button_link,
		'price'     => $extra_product_response->subscriptionTypes[0]->meta->show_price_lower ?? '',
		'currency'    => $extra_product_response->subscriptionTypes[0]->currency,
		'pricePeriod' => $extra_product_response->subscriptionTypes[0]->meta->price_period,
		'precision' => $extra_product_response->subscriptionTypes[0]->meta->precision ?? 2,
	];
}

?>

<div class="reactOffer <?php
echo esc_attr($block['className'] ?? ''); ?>"
	 data-badge="<?php
	 echo esc_attr(wp_json_encode($badge)); ?>"
	 data-title="<?php
	 echo esc_attr(wp_json_encode($title)); ?>"
	 data-description="<?php
	 echo esc_attr(wp_json_encode($description)); ?>"
	 data-background-color="<?php
	 echo esc_attr(wp_json_encode($background_color)); ?>"
	 data-type="<?php
	 echo esc_attr(wp_json_encode($type)); ?>"
	 data-only-for-logged-in-users="<?php
	 echo esc_attr(wp_json_encode($only_for_logged_in_users)); ?>"
	 data-logged-out-text="<?php
	 echo esc_attr(wp_json_encode($logged_out_text)); ?>"
	 data-only-for-segments="<?php
	 echo esc_attr(wp_json_encode($only_for_segments)); ?>"
	 data-not-in-segment-text="<?php
	 echo esc_attr(wp_json_encode($not_in_segment_text)); ?>"
	 data-not-in-segment-button-text="<?php
	 echo esc_attr(wp_json_encode($not_in_segment_button_text)); ?>"
	 data-not-in-segment-button-link="<?php
	 echo esc_attr(wp_json_encode($not_in_segment_button_link)); ?>"
	 data-link="<?php
	 echo esc_attr(wp_json_encode($link)); ?>"
	 data-link-text="<?php
	 echo esc_attr(wp_json_encode($link_text)); ?>"
	 data-subscriptions-products="<?php
	 echo esc_attr(wp_json_encode($subscriptions_products_mapped)); ?>"
	 data-extra-pricing="<?php
	 echo esc_attr(wp_json_encode($extra_pricing)); ?>"
	 data-extra-product="<?php
	 echo esc_attr(wp_json_encode($extra_product_mapped)); ?>"
></div>
