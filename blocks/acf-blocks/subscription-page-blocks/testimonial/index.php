<?php

/**
 * Block Name: Testimonial
 *
 * This is the template that displays the testimonial block.
 *
 * @package FORBES
 */

if ( ! defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

$testimonials = get_field('field_testimonial_testimonials');

$testimonials_camel = [];
if (is_array($testimonials)) {
	foreach ($testimonials as $testimonial) {
		$testimonials_camel[] = acf_array_camel_case($testimonial);
	}
}

?>

<div class="reactTestimonial <?php
echo esc_attr($block['className'] ?? ''); ?>"
	 data-testimonials="<?php
	 echo esc_attr(wp_json_encode($testimonials_camel)); ?>"
></div>