.subscription-why-subscribe-2-sk {
  margin: $spacing-11-64 $spacing-00;
  margin-left: 13.014%;
  width: auto;

  &__title {
    margin-bottom: $spacing-07-32;
    font-weight: 600;
    font-family: $font-noto-serif !important;
  }

  &__item-wrapper {
    gap: $spacing-07-32;
    flex-wrap: wrap;
  }

  &__item {
    max-width: 25.5rem;

    &__image {
      height: 12.8rem;
      width: 12.8rem;
    }

    &__text {
      font-size: 2.4rem;
      line-height: 3.2rem;
      font-family: $font-noto-serif !important;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl)) {
    &__item {
      max-width: 22.5rem;
    }

    &__item-wrapper {
      justify-content: space-around;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin-left: $spacing-00;
    width: 100%;
    margin: $spacing-07-32 $spacing-00;

    &__title {
      font-size: 3rem;
      line-height: 4rem;
    }

    &__item {
      flex: 45%;

      &__text {
        font-size: 1.8rem;
        line-height: 2.4rem;
      }
    }
  }
}
