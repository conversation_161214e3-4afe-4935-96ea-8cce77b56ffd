<?php
$title = get_field('title');
$items = get_field('items');
?>

<div class="subscription-popups">

	<h3 class="subscription-popups__title"><?= $title ?></h3>
	<?php if($items) : foreach ($items as $key => $item) :
	$opener = $item['opener'];
	$opener_title = $opener['title'];
	$opener_text = $opener['text'];
	$popup = $item['popup'];
	$popup_title = $popup['title'];
	$popup_text = $popup['text'];
	$popup_products = $popup['products'];
	$popup_button_text = $popup['button_text'];
	$popup_button_url = $popup['button_url'];
	?>
		<div class="subscription-popups__opener" data-key="<?= $key ?>">
			<p class="subscription-popups__opener__title after-icon after-icon--arrow h-d--flex h-align-items--center"><?= $opener_title ?></p>
			<p class="subscription-popups__opener__text"><?= $opener_text ?></p>
		</div>
		<div class="subscription-popups__popup popup-<?= $key ?>" style="display: none;">

			<div class="subscription-popups__popup__content <?= $popup_products ? 'large' : '' ?>">
			<div class="subscription-popups__popup__header <?= $popup_products ? 'large' : '' ?>">
				<p class="subscription-popups__popup__title"><?= $popup_title ?></p>

				<button class="button-icon popup-closer-btn">
					<i class="icon icon--cross"></i>
				</button>
			</div>
				<div class="subscription-popups__popup__text"><?= $popup_text ?></div>

			<?php if($popup_products) : foreach ($popup_products as $key => $product) : ?>


			<div class="subscription-popups__popup__product-wrapper">
				<div class="subscription-popups__popup__product">
					<div class="subscription-popups__popup__product__image-wrapper">
						<?= wp_get_attachment_image( get_field('image' , $product['product']->ID), 'large', false, array('class' => 'subscription-popups__popup__product__image', 'alt' => '') ); ?>
					</div>
					<div class="subscription-popups__popup__product__text">
						<div class="subscription-popups__popup__product__header">
							<a href="<?= get_field('website_url' , $product['product']->ID) ?>" class="subscription-popups__popup__product__name"><?= $product['product']->post_title ?></a>
							<p class="subscription-popups__popup__product__price"><?= get_field('price' , $product['product']->ID) ?></p>
						</div>
						<div class="subscription-popups__popup__product__description"><?= get_field('description' , $product['product']->ID) ?></div>
					</div>
				</div>
			</div>

			<?php endforeach; endif; ?>

			<?php if($popup_button_text && $popup_button_url) : ?>
				<?php
					$button = new Button($popup_button_text, $popup_button_url, 'medium', 'primary', false, false, 'subscription-popups__popup__button', '');
					echo $button->render();
				?>
			<?php endif; ?>
			</div>

		</div>
	<?php endforeach; endif; ?>

</div>
