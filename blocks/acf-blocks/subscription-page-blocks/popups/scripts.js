jQuery(function ($) {
  $(document).ready(() => {
    const currentUrl = new URL(window.location.href);
    const currentPopupKey = currentUrl.searchParams.get('p');
    if (currentPopupKey) {
      openPopup(currentPopupKey);
    }
  });

  $('.subscription-popups__opener').click(function (e) {
    const key = $(this).data('key');
    openPopup(key);
  });

  $('.popup-closer-btn').click(() => {
    closePopup();
  });

  $('.subscription-popups__popup').click((e) => {
    const isOuterDiv = e.target.classList.contains('subscription-popups__popup');
    if (isOuterDiv) {
      closePopup();
    }
  });

  const openPopup = (key) => {
    $(`.subscription-popups__popup`).hide();
    $('body').addClass('no-scroll');
    $(`.popup-${key}`).show();

    window.history.pushState({}, '', `?p=${key}`);
  };

  const closePopup = () => {
    $('body').removeClass('no-scroll');
    $(`.subscription-popups__popup`).hide();
    $('main').css('overflow', 'initial');

    window.history.pushState({}, '', '?');
  };
});
