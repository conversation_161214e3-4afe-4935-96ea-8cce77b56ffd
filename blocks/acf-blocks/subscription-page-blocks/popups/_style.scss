.subscription-popups {
  padding: $spacing-08-40;
  margin-left: 13.014%;
  margin-right: 13.014%;
  width: auto;
  border: 0.1rem solid $color-divider;
  margin-bottom: $spacing-10-56;

  &__title {
    font-size: 1.6rem;
    font-weight: 700;
  }

  &__opener {
    border-bottom: 0.1rem solid $color-divider;
    padding: $spacing-06-24 $spacing-00 $spacing-04-16;
    cursor: pointer;

    &:nth-last-child(2) {
      border-bottom: none;
      padding-bottom: $spacing-00;
    }

    &__title {
      font-size: 1.8rem;
      line-height: 2.8rem;
      color: $color-text-brand;
      margin-bottom: $spacing-01;
    }

    &__text {
      font-size: 1.6rem;
      line-height: 2.4rem;
      color: $color-text-secondary;
    }
  }

  &__popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 4;
    overflow-y: scroll;

    a:not(.button) {
      background-color: transparent !important;
      color: $color-text-brand;
      padding: $spacing-00 !important;
      width: 100%;
      display: inline;
    }

    &__content {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: $color-surface-primary;
      padding: $spacing-08-40;
      z-index: 5;
      width: 73rem;
      overflow-y: scroll;
    }

    &__header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: $spacing-06-24;

      .icon {
        background-color: $color-text-secondary;
      }
    }

    &__title {
      font-size: 2.6rem;
      line-height: 3.6rem;
    }

    &__text {
      margin-bottom: $spacing-04-16;

      * {
        font-size: 1.6rem;
        line-height: 2.4rem;
        margin-bottom: $spacing-04-16;
        color: $color-text-secondary;

        a {
          color: $color-text-brand;
          text-decoration: underline !important;
          margin-bottom: $spacing-00;
          display: inline;
        }
      }
    }

    &__button {
      display: inline-block;
    }

    &__product-wrapper {
      border-bottom: 0.1rem solid $color-divider;

      &:last-child {
        border-bottom: none;
      }
    }
    &__product {
      display: flex;
      flex-wrap: nowrap;
      padding-top: $spacing-06-24;
      padding-bottom: $spacing-04-16;
      width: 100%;

      &__image {
        max-width: 8.4rem;
        width: 100%;
        height: auto;
        object-fit: contain;
        margin-right: $spacing-02;
        object-position: top;
      }

      &__text {
        width: 100%;
      }

      &__header {
        display: flex;
        justify-content: space-between;
      }

      &__name {
        font-size: 1.8rem;
        line-height: 2.8rem;
        color: $color-text-brand;
        text-decoration: none;
      }

      &__price {
        color: $color-text-primary;
        font-weight: 500;
        white-space: nowrap;
      }

      &__description {
        margin-right: $spacing-04-16;

        * {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          font-size: 1.6rem;
          line-height: 2.6rem;
          color: $color-text-secondary;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl)) {
    &__popup {
      &__content {
        width: 80%;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin-left: $spacing-00;
    margin-right: $spacing-00;
    width: 100%;
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    padding: $spacing-04-16;

    &__popup {
      height: 100%;
      overflow-y: scroll;

      &__header {
        align-items: flex-start;
        margin-bottom: $spacing-04-16;

        &.large {
          margin-top: $spacing-07-32;
        }

        button {
          margin-top: $spacing-01;
        }
      }

      &__button {
        width: 100%;
        text-align: center;
      }

      &__title {
        font-size: 2rem;
        line-height: 2.8rem;
      }

      &__product {
        flex-direction: column;
        padding-top: $spacing-03-12;
        padding-bottom: $spacing-02;

        &__image-wrapper {
          align-self: center;
          background-color: $color-surface-secondary;
          width: 100%;
          display: flex;
          justify-content: center;
          margin-bottom: $spacing-02;
          max-height: 8rem;
        }

        &__image {
          object-position: center;
          margin-right: $spacing-00;
          width: auto;
          max-height: 100%;
        }

        &__price {
          font-size: 1.8rem;
        }
      }

      &__content {
        padding: $spacing-04-16;
        width: 100%;

        &.large {
          height: 100%;
        }
      }

      &__text {
        margin-bottom: $spacing-04-16;
      }
    }
  }
}
