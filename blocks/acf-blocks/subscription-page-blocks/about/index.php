<?php
/**
 * The description of the block
 * @var string
 */
$description = get_field('description');

/**
 * The button label of the block
 * @var string
 */
$button_label = get_field('button_label');

/**
 * The image of the block
 * @var string
 */
$image = get_field('image');
?>

<div class="subscription-about">

	<div class="subscription-about__background"></div>

	<div class="subscription-about__wrapper">

		<div class="row">

			<div class="col-12 col-md-6">

				<div class="subscription-about__content-wrapper">

					<h1 class="subscription-about__title"><?= get_the_title($post_id);?></h1>

					<div class="subscription-about__separator"></div>

					<?php if( isset( $description ) ):?>

						<div class="subscription-about__description">

							<?= $description;?>

						</div>

					<?php endif;?>

					<?php if( isset( $button_label ) ):?>

						<button id="navigation-button" class="button button--medium button--primary"><?= $button_label;?></button>

					<?php endif;?>

				</div>

			</div>

			<div class="col-6">

				<?php if( isset( $image ) ):?>

					<div class="subscription-about__image-wrapper">

						<?= wp_get_attachment_image( $image, 'medium', false, array('class' => 'subscription-about__image', 'alt' => 'Forbes Logo') );?>

					</div>

				<?php endif;?>

			</div>

		</div>

	</div>

</div>
