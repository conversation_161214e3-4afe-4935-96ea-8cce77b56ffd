.subscription-about {
  position: relative;
  padding: $spacing-09-48 $spacing-00;
  margin-bottom: $spacing-09-48;

  &__background {
    position: absolute;
    top: 0;
    height: 100%;
    width: 100vw;
    margin-left: calc((100% - 100vw) / 2);
    background-color: $color-surface-secondary;
  }

  &__title {
    margin-bottom: $spacing-04-16 !important;
  }

  &__separator {
    width: 5.2rem;
    border-bottom: 0.1rem solid $color-divider;
    margin-bottom: $spacing-04-16;
  }

  &__description {
    color: $color-text-secondary;
    margin-bottom: $spacing-07-32;
  }

  &__image-wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
  }

  &__image {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__image-wrapper {
      left: 0;
    }

    &__image {
      object-fit: contain;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    padding: $spacing-07-32 $spacing-00;
    margin-bottom: $spacing-07-32;

    &__image-wrapper {
      display: none;
    }
  }
}
