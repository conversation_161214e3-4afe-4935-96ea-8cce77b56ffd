<?php
add_action('acf/init', 'forbes_init_life_home_page_block_types');
function forbes_init_life_home_page_block_types() {

	// Check function exists.
	if (function_exists('acf_register_block_type')) {

		/*
			ACF BLOCKS FOR THE HOME PAGE
		*/

		acf_register_block_type(array(
			'name'              => 'life-home-podcasts-block',
			'title'             => esc_html__('Podcast block for the (Life) Home Page', 'FORBES'),
			'description'       => esc_html__('Carousel with the breaking news for the HOME PAGE', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/life-home-page-blocks/podcasts/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'supports'			=> array(
				'mode'			=> false
			),
			'keywords'          => array(esc_html__('life', 'FORBES'), esc_html__('home', 'FORBES')),
			'align'				=> 'center',
		));


		acf_register_block_type(array(
			'name'              => 'life-home-products-block',
			'title'             => esc_html__('Product Carousel block for the (Life) Home Page', 'FORBES'),
			'description'       => esc_html__('Carousel with the breaking news for the HOME PAGE', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/life-home-page-blocks/product-carousel/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'supports'			=> array(
				'mode'			=> false
			),
			'keywords'          => array(esc_html__('life', 'FORBES'), esc_html__('home', 'FORBES')),
			'align'				=> 'center',
		));

	}
};
