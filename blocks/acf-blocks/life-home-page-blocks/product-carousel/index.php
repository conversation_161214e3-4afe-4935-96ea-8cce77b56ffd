<?php

$productIds = get_field('products') ?? [];
$header = get_field('header') ?? [];

if (!is_array($productIds)) {
	$productIds = [];
}

$products = ForbesEcommerceSync\ProductQuery::byMultipleIds($productIds);

$header = [
	'title' => $header['title'] ?? '',
	'linkLabel' => $header['link_label'] ?? '',
	'url' => $header['url'] ?? '',
	'icon' => $header['title_icon'] ?? 'none',
];

?>

<div
	class="reactLifeProductCarousel"
	data-react-json-prop-products="<?php echo esc_attr(json_encode($products)); ?>"
	data-react-json-prop-header="<?php echo esc_attr(json_encode($header)); ?>"
>
	<div
		style="display: none"
		class="forbes-gutenberg-block-placeholder"
	>
		<span
			class="dashicon dashicons dashicons-editor-ul"
			context="list-view"
		></span>
		<?php echo esc_html__('Product Carousel block for the (Life) Home Page', 'FORBES'); ?>
	</div>
</div>