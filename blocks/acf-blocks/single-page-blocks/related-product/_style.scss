html.dark-mode {
  .related-product-block {
    &__image {
      &--light {
        display: none;
      }

      &--dark {
        display: block;
      }
    }

    &__category {
      background-color: $color-divider;
    }

    &__button {
      &:hover {
        color: $color-divider !important;
      }
    }
  }
}

.related-product-block {
  margin-top: $spacing-07-32;
  margin-bottom: $spacing-09-48;

  &__image {
    &--light {
      display: block;
    }

    &--dark {
      display: none;
    }
  }

  &__image-wrapper {
    margin-bottom: $spacing-06-24;
    position: relative;

    img {
      width: 100%;
      height: auto;
      max-height: 65rem;
      object-fit: cover;
    }
  }

  &__category {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: $spacing-04-16 $spacing-03-12;
    background-color: $color-white;
    color: $color-text-brand;
    font-weight: 700;
    font-size: 1.2rem;
    text-transform: uppercase;
    font-family: $font-archivo;
  }

  &__inner {
    margin-bottom: $spacing-04-16;
    display: flex;
    justify-content: space-between;
    align-items: baseline;
  }

  &__title {
    font-size: 2.6rem;
    font-weight: 500;
    color: $color-text-primary;
    margin-right: $spacing-02;
    width: 50%;
  }

  &__price {
    color: $color-text-secondary;
    font-size: 2.6rem;
    font-weight: 500;
    width: 50%;
    text-align: right;
  }

  &__description {
    margin-bottom: $spacing-04-16;

    p,
    a {
      color: $color-text-primary;
      line-height: 3.2rem;
      font-size: 1.8rem;
      font-weight: 400;
      margin-bottom: $spacing-00;
    }
  }

  &__button-wrapper {
    padding: $spacing-03-12 $spacing-00;
  }

  &__button {
    &:hover {
      color: $color-white !important;
      background-color: $color-text-primary;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-top: $spacing-02;
    margin-bottom: $spacing-07-32;

    &__image-wrapper {
      margin-bottom: $spacing-04-16;
    }

    &__inner {
      margin-bottom: $spacing-02;
    }

    &__title,
    &__price {
      font-size: 2rem;
    }

    &__description {
      p,
      a {
        font-size: 1.6rem;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__image-wrapper {
      img {
        max-height: 40rem;
      }
    }

    &__inner {
      flex-direction: column;
      align-items: flex-start;

      .related-product-block__title {
        margin-bottom: $spacing-01;
        width: 100%;
      }

      .related-product-block__price {
        text-align: left;
        width: 100%;
      }
    }
  }
}
