<?php

/**
 * Selected products array (id)
 * @var array|false $products
 */
$products = get_field('products');
$show_secondary_button = get_field('show_secondary_button');

if (!$products) {
	return;
}

$products_objects = [];

foreach ($products as $product) {
	$product_object = ForbesEcommerceSync\ProductQuery::byId($product['product']);

	if ($product_object) {
        if ( ! empty($product['product_image'])) {
            foreach ($product_object->images as &$image) {
                if ($image['type'] === 'main') {
                    $image['url'] = $product['product_image'];
                    break;
                }
            }
            unset($image);
        }

        // Add secondary button setting to product object
        $product_object->showSecondaryButton = $show_secondary_button;

		$products_objects[] = $product_object;
	}
}

foreach ($products_objects as $product_object) {
	$product_object->images = array_filter($product_object->images, function ($image) {
		return $image['type'] === 'main';
	});
}
?>

<div class="related-product-block">
	<div
		class="reactEcommerceArticleProducts"
		data-products="<?php echo esc_attr(json_encode($products_objects)); ?>"
	>
		<div
			style="display: none"
			class="forbes-gutenberg-block-placeholder"
		>
			<span
				class="dashicon dashicons dashicons-text-page"
				context="list-view"
			></span>
			<?php echo esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Related Product', 'FORBES') ?>
		</div>
	</div>
</div>
