<?php
add_action('acf/init', 'forbes_init_single_page_block_types');
function forbes_init_single_page_block_types() {

	// Check function exists.
	if (function_exists('acf_register_block_type')) {

		/*
		ACF BLOCKS FOR SINGLE PAGES
		*/

		// register the IN ARTICLE RELATED POSTS
		acf_register_block_type(array(
			'name'              => 'single-in-article-related-posts',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('In Article Related Posts', 'FORBES'),
			'description'       => esc_html__('Interactive slideshow of 3 or more related articles', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/single-page-blocks/in-article-related-posts/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('in-article', 'FORBES'), esc_html__('article', 'FORBES'), esc_html__('relative', 'FORBES'), esc_html__('post', 'FORBES'), esc_html__('posts', 'FORBES')),
			'align'				=> 'center',
			'enqueue_assets'	=> function () {
				wp_enqueue_script('iarp_script', get_template_directory_uri() . '/minified-js/single-page-blocks/in-article-related-posts/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/single-page-blocks/in-article-related-posts/scripts.min.js'), true);
			}
		));

		// register the GALLERY WITH LINK
		acf_register_block_type(array(
			'name'              => 'single-gallery-with-link',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Gallery With Link', 'FORBES'),
			'description'       => esc_html__('Gallery with 3 images and a link to the rest of the collection', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/shared/gallery-with-link/index.php',
			'category'          => 'common',
			'icon'              => 'format-gallery',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('gallery', 'FORBES')),
			'align'				=> 'center',
			'enqueue_assets'	=> function () {
				wp_enqueue_script('single-gallery-with-link-script', get_template_directory_uri() . '/minified-js/shared/gallery-with-link/script.min.js', array(), filemtime(get_template_directory() . '/minified-js/shared/gallery-with-link/script.min.js'), false);
			}
		));

		// register the RELATED MAGAZINE
		acf_register_block_type(array(
			'name'              => 'single-related-magazine',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Related Magazine', 'FORBES'),
			'description'       => esc_html__('Inserts a block with a chosen magazine with a link to it'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/single-page-blocks/related-magazine/index.php',
			'category'          => 'common',
			'icon'              => 'text-page',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('related', 'FORBES'), esc_html__('magazine', 'FORBES')),
			'align'				=> 'center',
		));

		// register the SELF PROMO Block
		acf_register_block_type(array(
			'name'              => 'single-self-promo',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Self Promo', 'FORBES'),
			'description'       => esc_html__('Promotional content with a link'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/single-page-blocks/self-promo/index.php',
			'category'          => 'common',
			'icon'              => 'buddicons-buddypress-logo',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('self', 'FORBES'), esc_html__('promo', 'FORBES')),
		));

		// register the RELATED POST LINKS
		acf_register_block_type(array(
			'name'              => 'single-related-post-links',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Related Posts Links', 'FORBES'),
			'description'       => esc_html__('Block with gray background listing two links either selected or from the same category as the original post'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/single-page-blocks/related-post-links/index.php',
			'category'          => 'common',
			'icon'              => 'admin-links',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('related', 'FORBES'), esc_html__('post', 'FORBES'), esc_html__('link', 'FORBES')),
			'align'				=> 'center',
		));

		// register the HIGHLIGHTED TEXT
		acf_register_block_type(array(
			'name'              => 'single-highlighted-text',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Highlighted Text', 'FORBES'),
			'description'       => esc_html__('Block with gray background and a custom text content'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/single-page-blocks/highlighted-text/index.php',
			'category'          => 'common',
			'icon'              => 'feedback',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('highlight', 'FORBES'), esc_html__('highlighted', 'FORBES'), esc_html__('text', 'FORBES')),
			'align'				=> 'center',
		));

		// register the RELATED POST LINKS
		acf_register_block_type(array(
			'name'              => 'single-breaking-news',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Breaking News Slider', 'FORBES'),
			'description'       => esc_html__('Carousel with the breaking news for a SINGLE ARTICLE', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/single-page-blocks/breaking-news-slider/index.php',
			'category'          => 'common',
			'icon'              => 'slides',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('breaking', 'FORBES'), esc_html__('news', 'FORBES')),
			'align'				=> 'center',
			'enqueue_assets'	=> function () {
				if (!wp_script_is('breaking-news-slider', 'enqueued')) {
					wp_enqueue_script('breaking-news-slider', get_template_directory_uri() . '/minified-js/breaking-news-slider/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/breaking-news-slider/scripts.min.js'), true);
				}
			}
		));

		// register the IFRAME
		acf_register_block_type(array(
			'name'              => 'single-iframe',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Iframe', 'FORBES'),
			'description'       => esc_html__('Block to trender iframes', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/single-page-blocks/iframe/index.php',
			'category'          => 'common',
			'icon'              => 'welcome-view-site',
			'mode'				=> 'preview',
			'keywords'          => array('iframe'),
			'align'				=> 'center',
		));

		// register the RELATED PRODUCT
		acf_register_block_type(array(
			'name'              => 'single-related-product',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Related Product', 'FORBES'),
			'description'       => esc_html__('Inserts a block with a chosen product'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/single-page-blocks/related-product/index.php',
			'category'          => 'common',
			'icon'              => 'text-page',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('related', 'FORBES'), esc_html__('product', 'FORBES')),
			'align'				=> 'center',
		));

		// register the ONEPAGE ARTICLE ANCHOR
		acf_register_block_type(array(
			'name'              => 'single-article-anchor',
			'title'             => esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('OnePage Article Anchor', 'FORBES'),
			'description'       => esc_html__('Back link to the parent OnePager article, if this article has one', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/single-page-blocks/onepage-article-anchor/index.php',
			'category'          => 'common',
			'icon'              => 'admin-links',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('onepage', 'FORBES'), esc_html__('article', 'FORBES'), esc_html__('anchor', 'FORBES')),
			'align'				=> 'center',
			'supports'			=> array('mode' => false)
		));

		if (COUNTRY == 'cz') {
			// register the Flatzone Widget
			acf_register_block_type(array(
				'name'              => 'single-property-widget',
				'title'             => esc_html__('Forbes Articles', 'FORBES') . ': Flatzone Widget',
				'description'       => 'Z excel sheet flatzone vyberte data, která chcete zobrazit',
				'render_template'   => get_template_directory() . '/blocks/acf-blocks/CZ/single-page-blocks/property-widget/index.php',
				'category'          => 'common',
				'icon'              => 'text-page',
				'mode'				=> 'preview',
				'keywords'          => array(esc_html__('flatzone', 'FORBES'), esc_html__('widget', 'FORBES'), esc_html__('property', 'FORBES')),
				'align'				=> 'center',
			));
		}
	}
}

if (COUNTRY == 'cz') {
	add_filter('register_block_type_args', 'forbes_core_block_renames', 10, 2);
	function forbes_core_block_renames($args, $block_type) {
		// Quote - core/quote
		if ($block_type !== 'core/quote') {
			return $args;
		}
		$args['title'] = esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Quote', 'FORBES');

		return $args;
	}
}

if (COUNTRY == 'sk') {
	add_filter('register_block_type_args', 'forbes_core_block_renames', 10, 2);
	function forbes_core_block_renames($args, $block_type) {
		// Gallery - core/gallery
		if ($block_type !== 'core/gallery') {
			return $args;
		}
		$args['title'] = esc_html__('Forbes Articles', 'FORBES') . ': ' . esc_html__('Gallery', 'FORBES');

		return $args;
	}
}
