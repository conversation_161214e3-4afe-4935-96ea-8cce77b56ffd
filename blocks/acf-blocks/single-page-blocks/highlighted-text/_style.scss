.highlighted-text {
  margin: $spacing-09-48 $spacing-00 $spacing-09-48 -8.6rem;
  padding: $spacing-08-40;
  background-color: $color-surface-primary;

  &__title {
    display: block;
    margin-bottom: $spacing-06-24;
    text-transform: uppercase;
  }

  &__content {
    * {
      margin-bottom: $spacing-06-24 !important;
    }

    & > :first-child {
      margin-top: $spacing-00 !important;
    }

    & > :last-child {
      margin-bottom: $spacing-00 !important;
    }

    ul {
      margin: $spacing-08-40 $spacing-00 !important;
      list-style: none !important;
      padding: $spacing-00 !important;
      line-height: 3.2rem !important;

      li {
        position: relative !important;
        padding-left: $spacing-00 !important;
        margin-bottom: $spacing-02 !important;

        &:before {
          content: '';
          position: relative;
          margin-right: $spacing-02;
          width: 0.8rem;
          height: 0.8rem;
          background-color: $color-text-brand;
          display: inline-block;
          top: -0.2rem;
        }
      }
    }

    a {
      margin-bottom: $spacing-00;
    }

    p,
    a,
    li {
      line-height: 3.2rem !important;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin: $spacing-09-48 $spacing-00 $spacing-09-48 $spacing-00;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-top: $spacing-07-32;
    margin-bottom: $spacing-07-32;
    padding: $spacing-06-24;

    &__title {
      margin-bottom: $spacing-04-16;
    }

    &__content {
      & > * {
        margin-bottom: $spacing-06-24 !important;
      }
    }
  }
}

.single {
  .highlighted-text {
    margin: $spacing-09-48 $spacing-00 $spacing-09-48 -8.6rem;

    @media screen and (max-width: map-get($container-max-widths, lg )) {
      margin: $spacing-07-32 $spacing-00;
    }
  }
}
