.related-post-links {
  padding: 4rem;
  background-color: $color-surface-secondary;
  display: flex;
  flex-direction: column;
  margin-top: $spacing-09-48;
  margin-bottom: $spacing-09-48;

  &__title {
    margin-bottom: $spacing-06-24;
    display: flex;
  }

  &__link-wrapper {
    margin-bottom: $spacing-04-16;
    display: flex;

    &:last-child {
      margin-bottom: $spacing-00;
    }
  }

  &__link {
    padding: $spacing-00 $spacing-01;
    font-size: 1.2rem;
    text-decoration: none;

    @media (hover: hover) {
      &:hover {
        text-decoration: underline;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    padding: $spacing-06-24;
    margin-top: $spacing-07-32;
    margin-bottom: $spacing-07-32;

    &__title {
      margin-bottom: $spacing-04-16;
    }
  }
}
