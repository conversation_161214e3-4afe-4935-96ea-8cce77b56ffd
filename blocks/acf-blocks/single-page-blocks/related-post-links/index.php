<?php

/**
 * Whether this block lists the post automatically or by preconfigured IDs
 * @var string
 */
$mode = get_field('mode');

/**
 * The name of the posts' category
 * @var wp_term
 */
$main_term = frontend_get_primary_tag(get_the_ID());

$args = array(
	'post_status'	=> 'publish',
	'post_type'		=> 'post',
	'posts_per_page' => '2',
	'orderby'		=> 'date',
	'order'			=> 'DESC'
);

if ('auto' === $mode) {
	/**
	 * The ID of the current category
	 * @var int
	 */
	$term_id = $main_term ? $main_term->term_id : null;

	$args['tax_query'] = array(
		array(
			'taxonomy'	=> CATEGORY_TYPE,
			'field'		=> 'term_id',
			'terms'		=> $term_id
		)
	);
	$args['post__not_in'] = array(get_the_ID());
} else {
	/**
	 * The IDs of the selected posts
	 * @var array
	 */
	$id_array = get_field('posts');

	if (is_array($id_array) && !empty($id_array)) {
		$ids = array_map(
			fn ($post) =>  $post['post'],
			$id_array
		);

		$args['posts_per_page'] = count($ids);
		$args['orderby'] = 'post__in';
		$args['post__in'] = $ids;
	}
}

$query = new WP_Query($args);

?>

<?php if ($query->have_posts()) : ?>

	<div class="related-post-links">

		<span class="related-post-links__title minititle"><?= esc_html__('More from this', 'FORBES'); ?></span>

		<?php while ($query->have_posts()) : $query->the_post(); ?>

			<div class="related-post-links__link-wrapper">
				<a href="<?= get_the_permalink(get_the_ID()); ?>" class="related-post-links__link"><?= get_the_title(get_the_ID()); ?></a>
			</div>

		<?php endwhile; ?>

		<?php wp_reset_postdata(); ?>

	</div>

<?php endif; ?>
