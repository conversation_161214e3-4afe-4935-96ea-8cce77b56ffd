<?php

/**
 * The block's ACF fields
 * @var array
 */
$iarp = get_fields();
?>

<?php if (!empty($iarp['related_posts'])) : ?>

	<div class="ia-related-posts">

		<div class="ia-related-posts__bg-wrapper">

			<?php foreach ($iarp['related_posts'] as $key => $related_post) : ?>

				<?php
				// The post thumbnail's ID
				$thumbnail = get_the_post_thumbnail_url($related_post['post'], 'large');
				$id = $related_post['post'];

				if ('cz' === COUNTRY && !$thumbnail) {
					$thumbnail = get_field('primaryImage', $id) ? wp_get_attachment_image_url(get_field('primaryImage', $id), 'large', false) : (get_field('thumbnail_placeholder', 'option') ?? '');
				}
				?>
				<img src="<?= $thumbnail ?>" alt="<?php echo strip_tags(get_the_title($related_post['post'])) ?>" class="ia-related-posts__bg h-object-fit--cover<?= 0 === $key ? ' active' : ''; ?>">

			<?php endforeach; ?>

		</div>

		<div class="ia-related-posts__content-wrapper">

			<?php foreach ($iarp['related_posts'] as $key => $related_post) : ?>

				<a href="<?php the_permalink($related_post['post']) ?>" class="ia-related-posts__link">

					<div class="ia-related-posts__post-wrapper<?= 0 === $key ? ' active' : ''; ?>">

						<?php if (count($iarp['related_posts']) > 1) : ?>

							<div class="ia-related-posts__post-progress-wrapper">

								<div class="ia-related-posts__post-progress"></div>

							</div>

						<?php endif; ?>

						<div class="ia-related-posts__post-details">

						<?php
							$contains_remp_lock_block = has_block('remp-paywall/lock', $related_post['post']);

						?>

							<span class="ia-related-posts__post-category cta-link-tag <?php if($contains_remp_lock_block) :?> has-premium-lock<?php endif; ?>">

								<?php if ('cz' === COUNTRY) : ?>

									<?php
									$primary_tag = frontend_get_primary_tag($related_post['post']);
									echo $primary_tag ? $primary_tag->name : '';
									?>
								<?php else : ?>
									<?= get_the_category($related_post['post'])[0]->name ?? ''; ?>

								<?php endif; ?>

							</span>

							<span class="ia-related-posts__post-author">

								<?php
								if ('cz' === COUNTRY) {
									$authors = wp_get_post_terms($related_post['post'], 'coauthor');
									$display_name = !is_wp_error($authors) && isset($authors[0]) ? $authors[0]?->name : '';
								} else {
									// The wp ID of the post author
									$author_id = get_post_field('post_author', $related_post['post']);
									// The display name of the post author
									$display_name = get_the_author_meta('display_name', $author_id);
								}

								$manual_authors = get_field('authors', $related_post['post']);

								if (is_array($manual_authors) && !empty($manual_authors)) {
									$author_id = array_values(array_filter($manual_authors, fn ($author) => $author['main_author']))[0]['author'] ?? $manual_authors[0];
									$display_name = 'cz' === COUNTRY ? get_term($author_id)?->name : get_the_author_meta('display_name', $author_id);
								}

								echo $display_name;
								?>

							</span>

							<span class="ia-related-posts__post-read-time">

								<?= get_post_read_time($related_post['post']); ?>

							</span>

						</div>

						<h5 class="ia-related-posts__post-title"><?php echo strip_tags(get_the_title($related_post['post'])); ?></h5>

					</div>

				</a>

			<?php endforeach; ?>

		</div>

	</div>

<?php endif; ?>
