// Exit if no block is on page
const relatedPostsBlocks = Array.from(document.querySelectorAll('.ia-related-posts'));

relatedPostsBlocks.forEach((block) => {
  // The bg images for the posts
  const images = Array.from(block.querySelectorAll('.ia-related-posts__bg'));

  // The related post elements
  const posts = Array.from(block.querySelectorAll('.ia-related-posts__post-wrapper'));

  if (posts.length < 2) return;

  // Index of the currently selected post element
  let startingPostIndex = 0;

  startProgress();

  /*
        Initialize the slideshow
    */
  function startProgress() {
    // The currently active post
    const currentPost = posts[startingPostIndex];

    // The currently active image
    const currentImage = images[startingPostIndex];

    /*
			Calculate the height of the element after selection
		*/
    function calculateNewHeight() {
      currentPost.classList.add('active');
      const height = currentPost.clientHeight;
      currentPost.classList.remove('active');
      return height;
    }

    // The original height of the element
    const oldHeight = currentPost.clientHeight;

    const newHeight = calculateNewHeight();

    // Animating the height of the post element
    const heightAnimation = currentPost.animate([{ height: oldHeight + 'px' }, { height: newHeight + 'px' }], {
      duration: 300,
      fill: 'forwards',
    });

    // Marking the current post and image as active
    currentImage.classList.add('active');
    currentPost.classList.add('active');

    // The progress bar element of the current post
    const progressBar = currentPost.querySelector('.ia-related-posts__post-progress');

    // Unhide the progress meter
    progressBar.style.display = 'block';

    // Animating the progressbar
    progressBar.animate([{ maxHeight: 0 }, { maxHeight: '100%' }], {
      duration: 3000,
      fill: 'forwards',
    });

    /*
			Upon completion reset the post and start the animation of the new one
		*/
    setTimeout(() => {
      progressBar.style.display = 'none';

      heightAnimation.reverse();

      startingPostIndex = startingPostIndex === posts.length - 1 ? 0 : startingPostIndex + 1;

      posts.forEach((post) => post.classList.remove('active'));
      images.forEach((image) => image.classList.remove('active'));

      startProgress();
    }, 3000);
  }
});
