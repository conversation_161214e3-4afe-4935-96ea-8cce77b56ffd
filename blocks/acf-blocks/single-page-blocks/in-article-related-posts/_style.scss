@import './assets/scss/base/_mixins.scss';

.ia-related-posts {
  width: auto;
  margin: $spacing-09-48 $spacing-00 $spacing-09-48 -8.6rem;
  padding: $spacing-06-24;
  min-height: 43.5rem;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;

  &__bg-wrapper,
  &__bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  &__bg-wrapper {
    z-index: 0;

    &:after {
      content: '';
      position: absolute;
      z-index: 10;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      display: inline-block;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
    }
  }

  &__bg {
    opacity: 0;
    z-index: 0;
    transition: opacity 0.5s ease;
    object-fit: cover;
    width: 100%;
    height: 100%;

    &.active {
      opacity: 1;
      z-index: 9;
    }
  }

  &__content-wrapper {
    position: relative;
    z-index: 1;
    width: 100%;
  }

  &__link {
    text-decoration: none;
    margin-bottom: $spacing-04-16;
    display: block;

    &:last-child {
      margin-bottom: $spacing-00;
    }
  }

  &__post-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: $spacing-03-12;
    position: relative;
    max-width: 50%;
    opacity: 0.7;
    transition: padding 0.5s ease;
    margin-bottom: $spacing-07-32;

    &.active {
      opacity: 1;
      max-width: 100%;
      padding-left: $spacing-06-24;
      margin-bottom: $spacing-04-16;

      .ia-related-posts__post-details {
        margin-bottom: $spacing-02;
      }

      .ia-related-posts__post-category,
      .ia-related-posts__post-author,
      .ia-related-posts__post-read-time {
        font-size: 1.4rem;
        line-height: 1.6rem;
        margin-right: $spacing-04-16;
      }

      .ia-related-posts__post-category {
        padding-left: $spacing-09-48;

        &:after {
          width: 4rem;
        }
      }

      .ia-related-posts__post-author {
        &:after {
          right: -0.8rem;
          top: -0.3rem;
        }
      }

      .ia-related-posts__post-title {
        font-size: 1.8rem;
        line-height: 2.8rem;
      }
    }
  }

  &__post-progress-wrapper {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0.2rem;
    border-radius: 1rem;

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      background-color: $color-white-darkfixed;
      opacity: 0.24;
    }
  }

  &__post-progress {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    max-height: 0;
    background-color: $color-white-darkfixed;
  }

  &__post-details {
    margin-bottom: $spacing-01;
    display: flex;
  }

  &__post-category,
  &__post-author,
  &__post-read-time {
    font-size: 0.9rem;
    line-height: 1.1rem;
    margin-right: $spacing-02;
    color: $color-text-secondary;
    transition:
      font-size 0.5s ease,
      margin 0.5s ease color 0.5s ease;
  }

  &__post-category {
    color: $color-text-brand;
    text-transform: uppercase;
    padding-left: $spacing-06-24;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2.3rem;
      height: 0.1rem;
      border-bottom: 0.1rem solid $color-text-brand;
      transition: width 0.5s ease;
    }

    &.has-premium-lock {
      display: flex;
      align-items: center;
      padding-left: $spacing-00 !important;

      &:after {
        content: none;
      }

      &:before {
        content: '';
        @include mask-properties;
        mask-image: url('assets/icons/ds2024/icon-diamond-empty.svg');
        -webkit-mask-image: url('assets/icons/ds2024/icon-diamond-empty.svg');
        background-color: $color-text-brand;
        width: 1.6rem;
        height: 1.6rem;
        margin-right: $spacing-01;
      }
    }
  }

  &__post-author {
    position: relative;

    &:after {
      content: '.';
      position: absolute;
      top: -0.2rem;
      right: -0.5rem;
    }
  }

  &__post-author,
  &__post-read-time {
    color: $color-white-darkfixed;
    opacity: 0.72;
  }

  &__post-title {
    font-size: 1rem;
    line-height: 1.6rem;
    color: $color-white-darkfixed;
    font-weight: 500;
    margin-bottom: $spacing-00;
    margin-top: $spacing-00;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin: $spacing-10-56 $spacing-00 $spacing-10-56 $spacing-00;
    padding: $spacing-04-16;
    min-height: 40rem;

    &__post-wrapper {
      padding-left: $spacing-02;
      max-width: 33%;

      &.active {
        padding-left: $spacing-04-16;

        .ia-related-posts__post-category {
          padding-left: $spacing-03-12;

          &:after {
            width: 0.8rem;
          }
        }
      }
    }

    &__post-category {
      padding-left: $spacing-02;

      &:after {
        width: 0.5rem;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin: $spacing-07-32 calc((100% - 100vw) / 2) $spacing-07-32;

    &__post-details {
      flex-wrap: wrap;
      row-gap: $spacing-01;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, sm )) {
    &__post-wrapper {
      max-width: 100%;

      &.active {
        .ia-related-posts__post-title {
          max-width: 100%;
        }

        .ia-related-posts__post-details {
          white-space: nowrap;
        }
      }
    }

    &__post-title {
      max-width: 66%;
    }
  }
}
