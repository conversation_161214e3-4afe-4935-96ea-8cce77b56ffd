@import '../../../../assets/scss/base/mixins';

html.dark-mode {
  .single {
    .related-magazine {
      &__background {
        display: none;

        &--dark {
          display: block;
        }
      }
    }
  }
}

.related-magazine {
  min-height: 27rem;
  position: relative;
  @include flexbox-properties;
  margin-top: $spacing-09-48;
  margin-bottom: $spacing-09-48;

  &__background {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    height: calc(100% - (2 * 33.5px));

    &--dark {
      display: none;
    }
  }

  &__content-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
    z-index: 1;
    padding: $spacing-00 $spacing-08-40;
  }

  &__magazine {
    position: relative;
    min-width: 19.3rem;
    width: auto;
    max-width: 33%;
    height: 100%;
    object-fit: contain;
    margin-right: $spacing-07-32;
    box-shadow: $box-shadow-level-4;
  }

  &__text-wrapper {
    height: 100%;
    width: 100%;
    padding: $spacing-09-48 $spacing-00;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
  }

  &__description-wrapper {
    margin-bottom: $spacing-02;
  }

  &__description {
    margin-bottom: $spacing-00 !important;
    color: $color-text-primary;
    display: -webkit-box;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
  }

  &__button {
    color: $color-divider;

    @media (hover: hover) {
      &:hover {
        background-color: $color-surface-invert !important;
      }
    }
  }

  &--light {
    .related-magazine {
      &__description {
        color: $color-divider;
      }

      &__button {
        background-color: $color-divider;
        color: $color-text-primary;

        @media (hover: hover) {
          &:hover {
            background-color: $color-divider !important;
          }
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    min-height: 22.4rem;

    &__background {
      height: calc(100% - (2 * 21.5px));
    }

    &__text-wrapper {
      padding: $spacing-08-40 $spacing-00;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    min-height: 23.9rem;
    width: 100vw;
    margin-left: calc((100% - 100vw) / 2);

    &__background {
      height: 100%;
    }

    &__text-wrapper {
      padding: $spacing-00;
    }

    &__content-wrapper {
      padding: $spacing-07-32 $spacing-04-16;
    }

    &__magazine {
      min-width: 12.7rem;
      max-height: 17.5rem;
      margin-right: $spacing-04-16;
      box-shadow: $box-shadow-level-5;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-top: $spacing-07-32;
    margin-bottom: $spacing-07-32;
  }
}
