<?php

/**
 * The ID of the related magazine
 * @var int
 */
$magazine_id = get_field('magazine');

/**
 * The background color for the block
 * @var string
 */
$background_color = get_field('background_color', $magazine_id);

/**
 * The background color for the block in dark mode
 * @var string
 */
$background_color_dark_mode = get_field('background_color_dark_mode', $magazine_id);

/**
 * The external URL of the magazine (if any)
 * @var string
 */
$external_url = get_field('url_issue', $magazine_id);

/**
 * The cover of the magazine
 * @var string
 */
$image = get_the_post_thumbnail($magazine_id, 'medium', array('class' => 'related-magazine__magazine', 'alt' => get_the_title($magazine_id)));

/**
 * The label on the button
 * @var string
 */
$button_label = get_field('button_label');

/**
 * The expiration time after which the block should dissappear
 * @var string
 */
$expiration_time = strtotime(get_field('expiration_time'));

/**
 * The current time
 * @var string
 */
$current_time = current_time('timestamp');


/**
 * If the post is expired
 * @var boolean
 */
$expired = $expiration_time - $current_time < 0;

/*
	Don't hide the block in the admin editor even it is expired, so it can be deleted manually
*/
if (is_admin() && $expiration_time && $expired) {
	$admin_message_shown = true;
}

?>

<?php if ($magazine_id && !$expired) : ?>

	<div class="related-magazine">

		<div class="related-magazine__background" style="background-color: <?= $background_color; ?>"></div>

		<div class="related-magazine__background related-magazine__background--dark" style="background-color: <?= $background_color_dark_mode; ?>"></div>

		<div class="related-magazine__content-wrapper">

			<?php if ($image) : ?>

				<?= $image; ?>

			<?php endif; ?>

			<div class="related-magazine__text-wrapper">

				<div class="related-magazine__description-wrapper">

					<p class="related-magazine__description callout"><?= strip_tags(get_the_excerpt($magazine_id)); ?></p>

				</div>

				<?php if ($button_label) : ?>

					<a href="<?= $external_url ? $external_url : get_permalink($magazine_id); ?>" <?php if ($external_url) : echo 'target="_blank" rel="noopener"';
																									endif; ?> class="related-magazine__button button button--medium button--primary">
						<?= $button_label; ?>
					</a>

				<?php endif; ?>

			</div>

		</div>

	</div>

<?php endif; ?>

<?php if (isset($admin_message_shown) && $admin_message_shown) : ?>

	<div class="related-magazine">

		<div>
			<h5 class="related-magazine__admin-message"><?= esc_html__('Block expired and hidden on the Frontend, please delete it from here too!', 'FORBES'); ?></h5>
		</div>

	</div>

<?php endif; ?>
