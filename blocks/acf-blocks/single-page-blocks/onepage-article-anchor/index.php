<?php

global $parent_one_page_id;

if (is_admin(  )) {
	$parent_one_page_id = \OnePage\get_onepage_parent_id();
}

if ($parent_one_page_id) {
	/**
	 * Get the OnePager article anchor field group
	 * @var array
	 */
	$article_anchor_field_group = get_field('article_anchor', $parent_one_page_id);

	/**
	 * Get the OnePager article anchor title
	 * @var string
	 * */
	$title = $article_anchor_field_group['title'];

	/**
	 * Get the OnePager article anchor description
	 * @var string
	 * */
	$description = $article_anchor_field_group['description'];

	/**
	 * Get the OnePager article anchor image
	 * @var string
	 * */
	$image_id = $article_anchor_field_group['image'];

	/**
	 * Get the OnePager article anchor category type
	 * 	@var string
	 * */
	$category_type = $article_anchor_field_group['category_type'];

	/**
	 * Get the OnePager article anchor category text
	 * @var string
	 */
	$category_text = $category_type === 'text' ? $article_anchor_field_group['category_text'] : null;

	/**
	 * Get the OnePager article anchor category image
	 * @var string
	 */
	$category_image = $category_type === 'image' ? $article_anchor_field_group['category_image'] : null;
}
?>

<?php if ($parent_one_page_id) : ?>

	<div class="single-onepage-article-anchor<?= is_admin(  ) ? ' admin' : ''; ?>">

		<?php if ($image_id) : ?>

			<a href="<?= get_the_permalink($parent_one_page_id); ?>" class="single-onepage-article-anchor__image-wrapper">
				<?= wp_get_attachment_image($image_id, 'small', false, array('class' => 'single-onepage-article-anchor__image')); ?>
			</a>

		<?php endif; ?>

		<div class="single-onepage-article-anchor__content-wrapper">

			<?php if ($category_text) : ?>

				<span class="single-onepage-article-anchor__category"><?= $category; ?></span>

			<?php elseif ($category_image) : ?>

				<div class="single-onepage-article-anchor__category-image-wrapper">
					<?= wp_get_attachment_image($category_image, 'small', false, array('class' => 'single-onepage-article-anchor__category-image')); ?>
				</div>

			<?php endif; ?>

			<?php if ($title) : ?>

				<a href="<?= get_the_permalink($parent_one_page_id); ?>" class="single-onepage-article-anchor__title-wrapper">
					<h4 class="single-onepage-article-anchor__title"><?= $title; ?></h4>
				</a>

			<?php endif; ?>

			<?php if ($description) : ?>

				<div class="single-onepage-article-anchor__description"><?= $description; ?></div>

			<?php endif; ?>

		</div>

	</div>

<?php elseif (is_admin()) : ?>

	<p class="callout"><?= esc_html__('This post is not related to any OnePager.', 'FORBES') ?></p>

<?php endif; ?>
