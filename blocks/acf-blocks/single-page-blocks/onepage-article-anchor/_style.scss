.single-onepage-article-anchor {
  border-bottom: 0.1rem solid $color-divider;
  border-top: 0.1rem solid $color-divider;
  display: flex;
  gap: $spacing-04-16;
  margin-bottom: $spacing-09-48;
  padding-block: $spacing-06-24;
  margin-inline: 13.014%;
  width: auto;

  &.admin {
    margin-inline: $spacing-00;
    min-height: 22rem;
  }

  &__image-wrapper {
    height: auto;
    max-width: 17.7rem;
    min-width: 17.7rem;
    position: relative;
    overflow: hidden;
  }

  &__image {
    height: 100%;
    left: 0;
    object-fit: cover;
    position: absolute;
    top: 0;
    width: 100%;
    transition: all 0.3s ease-in-out;
  }

  &__category-image-wrapper {
    height: 1.6rem;
    width: auto;
    margin-bottom: $spacing-02;
  }

  &__category-image {
    height: 100%;
    width: auto;
    object-fit: contain;
  }

  &__category {
    color: var(--post-font-color, $color-text-brand);
    font-family: $font-archivo;
    display: block;
    font-size: 1.2rem;
    font-weight: 500;
    line-height: 1.4rem;
    margin-bottom: $spacing-02;
    padding-left: $spacing-09-48;
    position: relative;

    &:before {
      content: '';
      border-bottom: 0.1rem solid $color-text-brand;
      display: inline-block;
      left: 0;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 4rem;
    }
  }

  &__title-wrapper {
    display: block !important;
    text-decoration: none;
  }

  &__title {
    margin-bottom: $spacing-02;
  }

  &__description {
    & > p,
    a {
      font-size: 1.6rem !important;
      line-height: 2.4rem !important;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    margin-inline: $spacing-00;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-07-32;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    flex-direction: column;

    &__image-wrapper {
      aspect-ratio: 16/9;
      width: 100%;
      max-width: none;
    }
  }
}
