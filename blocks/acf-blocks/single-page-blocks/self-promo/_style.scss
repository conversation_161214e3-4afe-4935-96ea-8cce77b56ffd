.self-promo {
  position: relative;
  aspect-ratio: 3/2;
  overflow: hidden;
  padding: $spacing-08-40;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  margin-top: $spacing-09-48;
  margin-bottom: $spacing-09-48;

  &__image-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }

  &__image {
    height: 100%;
    width: 100%;
    object-fit: cover;

    &--mobile {
      display: none;
    }
  }

  &__content-wrapper {
    position: relative;
    z-index: 1;
  }

  &__excerpt {
    color: $color-divider;
    margin-bottom: $spacing-04-16 !important;
    display: -webkit-box;
    -webkit-line-clamp: 11;
    -webkit-box-orient: vertical;
    overflow: hidden;
    visibility: visible;
  }

  &__button {
    display: inline-block !important;
    padding: $spacing-04-16;
    color: $color-text-primary;
    background-color: $color-divider;
  }

  &--light {
    .self-promo__excerpt {
      color: $color-text-primary;
    }

    .self-promo__button {
      color: $color-divider;
      background-color: $color-text-primary;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    aspect-ratio: 4/3;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    aspect-ratio: 3/2;

    &__spacing-correction {
      margin-bottom: -3.2rem;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    aspect-ratio: 16/15;
    width: 100vw;
    margin-left: calc((100% - 100vw) / 2);
    padding: $spacing-06-24 $spacing-04-16;
    margin-top: $spacing-07-32;
    margin-bottom: $spacing-07-32;

    &__image {
      &--desktop {
        display: none;
      }

      &--mobile {
        display: block;
      }
    }

    &__button {
      padding: $spacing-03-12 $spacing-04-16;
    }
  }
}
