<?php

/**
 * The promo item post's ID
 * @var int
 */
$promo_id = get_field('promo_item');

/**
 * The image ID to be used on mobile
 * @var int
 */
$mobile_thumbnail = get_field('mobile_thumbnail', $promo_id);

/**
 * The URL to the promo item
 * @var string
 */
$url = get_field('button_url', $promo_id);

/**
 * The label for the button
 * @var string
 */
$button_label = get_field('button_label', $promo_id);

/**
 * The expiration time after which the block should dissappear
 * @var string
 */
$expiration_time = strtotime(get_field('expiration_time', $promo_id));

/**
 * The current time
 * @var string
 */
$current_time = current_time('timestamp');


/**
 * If the post is expired
 * @var boolean
 */
$expired = $expiration_time ? ($expiration_time - $current_time < 0) : false;

/**
 * Light/dark mode
 * @var string
 */
$mode = get_field('mode');
?>


<?php if ($promo_id && !$expired) : ?>

	<div class="self-promo<?= 'light' === $mode ? ' self-promo--light' : ''; ?>">

		<div class="self-promo__image-wrapper">

			<?= get_the_post_thumbnail($promo_id, 'medium', array('class' => 'self-promo__image self-promo__image--desktop', 'alt' => get_the_title($promo_id))); ?>

			<?php if ($mobile_thumbnail) : ?>
				<?= wp_get_attachment_image($mobile_thumbnail, 'medium', false, array('class' => 'self-promo__image self-promo__image--mobile', 'alt' => get_the_title($promo_id))); ?>
			<?php endif; ?>

		</div>

		<div class="self-promo__content-wrapper">

			<p class="self-promo__excerpt callout"><?= strip_tags(get_the_excerpt($promo_id)); ?></p>

			<?php if ($url && $button_label) : ?>

				<a href="<?= $url; ?>" class="self-promo__button cta-link-tag"><?= $button_label ?></a>

			<?php endif; ?>

		</div>

	</div>

<?php endif; ?>
