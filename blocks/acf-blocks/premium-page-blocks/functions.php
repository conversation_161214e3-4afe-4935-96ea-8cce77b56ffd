<?php
add_action('acf/init', 'forbes_init_premium_page_block_types');
function forbes_init_premium_page_block_types() {

	// Check function exists.
	if (function_exists('acf_register_block_type')) {
		acf_register_block_type(array(
			'name'              => 'premium-header',
			'title'             => esc_html__('Premium: Header', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/premium-page-blocks/header/index.php',
			'category'          => 'common',
			'icon'              => 'status',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('header', 'FORBES'), esc_html__('subscribe', 'FORBES')),
			'align'				=> 'center',
		));

		// register the WHY SUBSCRIBE
		acf_register_block_type(array(
			'name'              => 'premium-why-subscribe-2',
			'title'             => esc_html__('Premium: Why Subscribe? New', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/premium-page-blocks/why-subscribe-2/index.php',
			'category'          => 'common',
			'icon'              => 'welcome-learn-more',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('why', 'FORBES'), esc_html__('subscribe', 'FORBES')),
			'align'				=> 'center',
		));

		// register the HIGHLIGHT CONTENT
		acf_register_block_type(array(
			'name'              => 'highlight-content',
			'title'             => esc_html__('Highlight Content', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/premium-page-blocks/highlight-content/index.php',
			'category'          => 'common',
			'icon'              => 'admin-customizer',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('highlight', 'FORBES'), esc_html__('content', 'FORBES')),
			'align'				=> 'center',
		));
	}
}
