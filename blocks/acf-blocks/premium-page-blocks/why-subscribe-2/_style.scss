.subscription-why-subscribe-2 {
  margin: $spacing-11-64 $spacing-00;
  margin-left: 13.014%;
  margin-right: 13.014%;
  width: auto;

  &__title {
    margin-bottom: $spacing-07-32;
    font-weight: 400;
  }

  &__item-wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: $spacing-07-32;
  }

  &__item {
    &__image {
      height: 12.8rem;
      width: 12.8rem;
      object-fit: contain;
    }

    &__text {
      font-size: 2.4rem;
      line-height: 3.2rem;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, xl)) {
    &__item {
      max-width: 22.5rem;
    }

    &__item-wrapper {
      justify-content: space-around;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin-left: $spacing-00;
    width: 100%;
    margin: $spacing-07-32 $spacing-00;

    &__title {
      font-size: 3rem;
      line-height: 4rem;
    }

    &__item {
      flex: 45%;

      &__text {
        font-size: 2rem;
        line-height: 2.6rem;
      }
    }
  }
}
