.event-program {
  margin-bottom: $spacing-09-48;

  &__title {
    margin-bottom: $spacing-07-32 !important;
  }

  &__grid {
    grid-template-columns: 1fr;
  }

  &__program-point-wrapper {
    display: flex;
    width: 100%;
    align-items: flex-start;
    padding: $spacing-06-24 $spacing-00;
    border-bottom: 0.1rem solid $color-divider;

    &:first-child {
      padding-top: $spacing-00;
    }

    &:last-child {
      border: none;
      padding-bottom: $spacing-00;
    }
  }

  &__program-point-time-wrapper {
    flex: 0 0 30%;
  }

  &__program-point-content-wrapper {
    flex: 0 0 70%;
  }

  &__program-point-time {
    color: $color-link-visited;
    margin-bottom: $spacing-00;
  }

  &__program-point-title {
    margin-bottom: $spacing-00;
  }

  &__program-point-description {
    margin-top: $spacing-02;

    a,
    li,
    p {
      font-size: 1.6rem;
      line-height: 2.4rem;
      margin-bottom: $spacing-02;
    }

    p {
      color: $color-text-primary;
    }

    & > :last-child {
      margin-bottom: $spacing-00;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-07-32;

    &__program-point-time-wrapper {
      flex: 0 0 40%;
    }

    &__program-point-content-wrapper {
      flex: 0 0 60%;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__program-point-wrapper {
      padding: $spacing-06-24 $spacing-00;
      flex-direction: column;
    }

    &__program-point-time-wrapper {
      margin-bottom: $spacing-04-16;
    }

    &__program-point-description {
      margin-top: $spacing-02;

      a,
      li,
      p {
        font-size: 1.4rem;
        line-height: 2rem;
      }
    }
  }
}
