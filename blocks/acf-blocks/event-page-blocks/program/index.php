<?php

/**
 *  The program points of the event
 * @var ąrray
 */
$program_points = get_field('program_points');
?>

<?php if ($program_points && !empty($program_points)) : ?>


	<div class="event-program">

		<h4 class="event-program__title"><?= esc_html__('Program', 'FORBES'); ?></h4>

		<div class="event-program__grid">

			<?php foreach ($program_points as $program_point) : ?>

				<div class="event-program__program-point-wrapper">

					<div class="event-program__program-point-time-wrapper">

						<?php if ($program_point['timing']) : ?>

							<span class="event-program__program-point-time footnote"><?= $program_point['timing']; ?></span>

						<?php endif; ?>

					</div>

					<div class="event-program__program-point-content-wrapper">

						<?php if ($program_point['name']) : ?>

							<h5 class="event-program__program-point-title"><?= $program_point['name']; ?></h5>

						<?php endif; ?>

						<?php if ($program_point['description']) : ?>

							<div class="event-program__program-point-description"><?= $program_point['description']; ?></div>

						<?php endif; ?>

					</div>

				</div>

			<?php endforeach; ?>

		</div>

	</div>

<?php endif; ?>
