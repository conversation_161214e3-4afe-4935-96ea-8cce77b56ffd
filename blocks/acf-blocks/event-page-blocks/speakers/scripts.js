document.addEventListener('DOMContentLoaded', () => {
  const speakers = Array.from(document.querySelectorAll('.event-speakers__speaker-wrapper'));

  speakers.length &&
    speakers.forEach((speaker) => {
      const nameWrapper = speaker.querySelector('.event-speakers__speaker-name-wrapper');

      setTimeout(() => {
        const height = nameWrapper.clientHeight / 2;

        const positionWrapper = speaker.querySelector('.event-speakers__speaker-position');

        positionWrapper.style.marginTop = height * -1 + 'px';
      }, 100);
    });
});
