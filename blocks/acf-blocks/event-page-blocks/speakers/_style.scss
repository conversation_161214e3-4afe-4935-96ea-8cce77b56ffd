.event-speakers {
  margin-bottom: $spacing-09-48;

  &__grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: $spacing-07-32;
  }

  &__speaker-image-wrapper {
    position: relative;
    width: 100%;
    height: auto;
    overflow: hidden;
  }

  &__speaker-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
    transition: all 0.3s ease;

    @media (hover: hover) {
      &:hover {
      }
    }
  }

  &__speaker-name-wrapper {
    position: relative;
    background-color: $color-surface-primary;
    display: inline-flex;
    flex-direction: column;
    padding: $spacing-02;
    z-index: 1;
    transform: translateY(-50%);
  }

  &__speaker-position {
    padding: $spacing-00 $spacing-02;
    color: $color-text-secondary;
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    &__title {
      margin-bottom: $spacing-07-32;
    }

    &__speaker-image-wrapper {
      padding-top: 100%;
      height: 0;

      @supports (aspect-ratio: 1/1) {
        aspect-ratio: 1/1;
        padding-top: $spacing-00;
        height: auto;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-07-32;

    &__speaker-image-wrapper {
      padding-top: $spacing-00;
      height: auto;

      @supports (aspect-ratio: unset) {
        aspect-ratio: unset;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__grid {
      grid-template-columns: repeat(3, 1fr);
      grid-row-gap: $spacing-04-16;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, sm )) {
    &__grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
