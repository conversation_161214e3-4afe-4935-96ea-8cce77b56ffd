<?php

/**
 * The speakers of the event
 * @var array
 */
$speakers = get_field('speakers');
?>

<?php if ($speakers && count($speakers)) : ?>

	<div class="event-speakers">

		<div class="row">

			<div class="col-12 col-xl-3">

				<h4 class="event-speakers__title"><?= esc_html__('Speakers', 'FORBES'); ?></h4>

			</div>

			<div class="col-12 col-xl-9">

				<div class="event-speakers__grid">

					<?php foreach ($speakers as $speaker) : ?>

						<div class="event-speakers__speaker-wrapper">


							<?php $image = wp_get_attachment_image($speaker['portrait'], 'small', false, array('class' => 'event-speakers__speaker-image', 'alt' => $speaker['first_name'] . ' ' . $speaker['last_name'])); ?>

							<?php if ($image) : ?>

								<div class="event-speakers__speaker-image-wrapper">

									<?= $image; ?>

								</div>

							<?php endif; ?>


							<?php if ($speaker['first_name'] && $speaker['last_name']) : ?>

								<div class="event-speakers__speaker-name-wrapper">

									<span class="event-speakers__speaker-first-name callout"><?= $speaker['first_name'] ?></span>
									<span class="event-speakers__speaker-last-name callout"><?= $speaker['last_name'] ?></span>

								</div>

							<?php endif; ?>

							<?php if ($speaker['position']) : ?>

								<div class="event-speakers__speaker-position">

									<span class="caption"><?= $speaker['position'] ?></span>

								</div>

							<?php endif; ?>

						</div>

					<?php endforeach; ?>

				</div>

			</div>

		</div>

	</div>

<?php endif; ?>
