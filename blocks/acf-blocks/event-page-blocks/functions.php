<?php
add_action('acf/init', 'forbes_init_event_page_block_types');
function forbes_init_event_page_block_types() {

    // Check function exists.
    if( function_exists('acf_register_block_type') ) {

		/*
		ACF BLOCKS FOR EVENT PAGES
		*/

        // register the EVENT DETAILS
        acf_register_block_type(array(
            'name'              => 'event-event-details',
            'title'             => esc_html__('Event: Event Details', 'FORBES'),
            'description'       => esc_html__('Block with the date, location, and links of the event', 'FORBES'),
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/event-page-blocks/event-details/index.php',
            'category'          => 'common',
            'icon'              => 'info-outline',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('event', 'FORBES'), esc_html__('details', 'FORBES') ),
			'align'				=> 'center',
        ));

		// register the PROGRAM
        acf_register_block_type(array(
            'name'              => 'event-program',
            'title'             => esc_html__('Event: Program', 'FORBES'),
            'description'       => esc_html__('Block with the agenda of the event', 'FORBES'),
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/event-page-blocks/program/index.php',
            'category'          => 'common',
            'icon'              => 'clock',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('program', 'FORBES') ),
			'align'				=> 'center',
        ));

		// register the SPEAKERS
        acf_register_block_type(array(
            'name'              => 'event-speakers',
            'title'             => esc_html__('Event: Speakers', 'FORBES'),
            'description'       => esc_html__('Block with a grid of the speakers of the event', 'FORBES'),
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/event-page-blocks/speakers/index.php',
            'category'          => 'common',
            'icon'              => 'users',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('speaker', 'FORBES') ),
			'align'				=> 'center',
			'enqueue_assets'	=> function() {
				wp_enqueue_script('event-speakers-script', get_template_directory_uri() . '/minified-js/event-page-blocks/speakers/scripts.min.js', array(), filemtime( get_template_directory(  ) . '/minified-js/event-page-blocks/speakers/scripts.min.js' ), true );
			}
        ));

		// register the SPONSORS
        acf_register_block_type(array(
            'name'              => 'event-sponsors',
            'title'             => esc_html__('Event: Sponsors', 'FORBES'),
            'description'       => esc_html__('Block with a grid of the sponsors of the event', 'FORBES'),
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/event-page-blocks/sponsors/index.php',
            'category'          => 'common',
            'icon'              => 'money',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('sponsor', 'FORBES') ),
			'align'				=> 'center',
        ));

		// register the IFRAME
        acf_register_block_type(array(
            'name'              => 'event-iframe',
            'title'             => esc_html__('Event: Iframe', 'FORBES'),
            'description'       => esc_html__('Block with a custom Iframe content', 'FORBES'),
            'render_template'   => get_template_directory(  ) . '/blocks/acf-blocks/event-page-blocks/iframe/index.php',
            'category'          => 'common',
            'icon'              => 'admin-tools',
			'mode'				=> 'preview',
            'keywords'          => array( esc_html__('iframe', 'FORBES') ),
			'align'				=> 'center',
        ));
    }
}
