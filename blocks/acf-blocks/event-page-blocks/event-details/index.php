<?php
$start_date_field_name = 'cz' === COUNTRY ? 'eventStartDate' : 'starting_date';

/**
 * The starting date of the event
 * @var string
 */
$starting_date = frontend_translate_date(get_field($start_date_field_name, get_the_ID()), null, true);

/**
 * The location of the event
 * @var string
 */
$location = get_field('location')['label'];

/**
 * The GMaps link to the location
 * @var string
 */
$location_link = get_field('location')['google_maps_link'];

/**
 * Facebook URL to the event
 * @var string
 */
$facebook_link = get_field('links')['facebook'];

/**
 * LinkedIn URL to the event
 * @var string
 */
$linkedin_link = get_field('links')['linkedin'];

/**
 * The label for the button
 * @var string
 */
$button_label = get_field('links')['button_label'];

/**
 * The link for the button
 * @var string
 */
$button_link = get_field('links')['button_link'];
?>

<div class="event-details">

	<div class="event-details__wrapper">

		<div class="event-details__content">

			<?php if ($starting_date) : ?>

				<div class="event-details__date-wrapper">

					<em class="event-details__date-icon"></em>

					<span class="event-details__date footnote"><?= $starting_date; ?></span>

				</div>

			<?php endif; ?>

			<?php if ($location && $location_link) : ?>

				<div class="event-details__location-wrapper">

					<em class="event-details__location-icon"></em>

					<a href="<?= $location_link; ?>" target="_blank" rel="noopener nofollow" class="event-details__location footnote"><?= $location; ?></a>

				</div>

			<?php endif; ?>

			<?php if ($facebook_link || $linkedin_link) : ?>

				<div class="event-details__social-media-wrapper">

					<?php if ($facebook_link) : ?>

						<a href="<?= $facebook_link; ?>" target="_blank" rel="noopener nofollow" class="event-details__link event-details__link--facebook cta-link-tag"><?= esc_html__('Facebook Event', 'FORBES'); ?></a>

					<?php endif; ?>

					<?php if ($linkedin_link) : ?>

						<a href="<?= $linkedin_link; ?>" target="_blank" rel="noopener nofollow" class="event-details__link event-details__link--linkedin cta-link-tag"><?= esc_html__('LinkedIn Event', 'FORBES'); ?></a>

					<?php endif; ?>

				</div>

			<?php endif; ?>

		</div>

		<?php if ($button_label && $button_link) : ?>

			<div class="event-details__button-wrapper">

				<a href="<?= $button_link; ?>" target="_blank" rel="noopener nofollow" class="event-details__button cta-link-tag button button--primary"><?= $button_label; ?></a>

			</div>

		<?php endif; ?>

	</div>

</div>
