.event-details {
  padding: $spacing-07-32 $spacing-08-40;
  border: 0.1rem solid $color-divider;
  margin-bottom: $spacing-09-48;

  &__wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: $spacing-04-16;
  }

  &__date-wrapper,
  &__location-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-06-24;
  }

  &__date-icon,
  &__location-icon {
    display: inline-block;
    min-width: 2.4rem;
    min-height: 2.4rem;
    margin-right: $spacing-02;
    @include background-properties;
  }

  &__date-icon {
    background-image: url('assets/icons/icon-calendar.svg');
  }

  &__location-icon {
    background-image: url('assets/icons/icon-location.svg');
  }

  &__date,
  &__location {
    text-decoration: none;
    color: $color-text-secondary !important;
  }

  &__date {
    margin-bottom: $spacing-00;
  }

  &__location {
    margin-bottom: $spacing-00;
  }

  &__social-media-wrapper {
    display: flex;
    gap: $spacing-06-24;
  }

  &__link {
    padding: $spacing-02 $spacing-04-16;
    color: $color-text-primary;
    border: 0.1rem solid $color-text-primary;
    text-align: center;
  }

  &__button {
    padding: $spacing-04-16 $spacing-07-32;
    text-align: center;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-07-32;

    &__wrapper {
      flex-direction: column;
      align-items: flex-start;
    }

    &__button-wrapper {
      width: 100%;
      margin-top: $spacing-07-32;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    padding: $spacing-06-24 $spacing-05-20;

    &__social-media-wrapper {
      display: flex;
    }

    &__location {
      line-height: 1.8rem;
    }

    &__link {
      margin: $spacing-00;
      text-align: center;

      &:first-child {
        margin-right: $spacing-02;
      }
    }

    &__button-wrapper {
      margin-top: $spacing-06-24;
    }
  }
}
