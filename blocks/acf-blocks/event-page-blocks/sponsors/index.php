<?php

/**
 * The title of the block
 * @var string
 */
$title = get_field('title');

/**
 * The array that holds more arrays of the different kind of sponsors
 * @var array
 */
$sponsors = get_field('sponsors');

/**
 * The path to the sponsor card template part
 * @var string
 */
$sponsor_card_part = 'blocks/acf-blocks/event-page-blocks/sponsors/sponsor/index';
?>

<div class="event-sponsors">


	<div class="row">

		<div class="col-12 col-xl-3">

			<?php if ($title) : ?>

				<h4 class="event-sponsors__title"><?= $title; ?></h4>

			<?php endif; ?>

		</div>

		<div class="col-12 col-xl-9">

            <?php if ( ! empty($sponsors)) : ?>

                <?php if (isset($sponsors[0]['logos']) && is_array($sponsors[0]['logos'])) : ?>

                    <div class="event-sponsors__main-sponsors-wrapper">

                        <?php foreach ($sponsors[0]['logos'] as $main_sponsor) : ?>

                            <?php get_template_part($sponsor_card_part, null, array('logo' => $main_sponsor['logo'], 'description' => $main_sponsor['label'])); ?>

                        <?php endforeach; ?>

                    </div>

                <?php endif; ?>

                <?php if (count($sponsors) > 1) : ?>

                    <?php foreach (array_slice($sponsors, 1) as $other_sponsor_group) : ?>

                        <div class="event-sponsors__other-sponsors-wrapper">

                            <?php if ( ! empty($other_sponsor_group['sponsor_type'])) : ?>

                                <span class="event-sponsors__small-title caption"><?= $other_sponsor_group['sponsor_type'] ?></span>

                            <?php endif; ?>

                            <?php if ( ! empty($other_sponsor_group['logos'])) : ?>

                                <div class="event-sponsors__other-sponsors-grid">

                                    <?php foreach ($other_sponsor_group['logos'] as $other_sponsor) : ?>

                                        <?php get_template_part($sponsor_card_part, null, array('logo' => $other_sponsor['logo'], 'description' => null)); ?>

                                    <?php endforeach; ?>

                                </div>

                            <?php endif; ?>

                        </div>

                    <?php endforeach; ?>

                <?php endif; ?>

            <?php endif; ?>

		</div>

	</div>

</div>
