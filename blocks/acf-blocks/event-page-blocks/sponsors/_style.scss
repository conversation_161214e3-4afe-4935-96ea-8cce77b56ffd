.event-sponsors {
  margin-bottom: $spacing-09-48;

  &__main-sponsors-wrapper {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: auto;
    grid-gap: $spacing-07-32;
    margin-top: $spacing-08-40;
    padding-bottom: $spacing-07-32;
    border-bottom: 0.1rem solid $color-divider;
  }

  &__small-title {
    color: $color-text-secondary;
  }

  &__other-sponsors-wrapper {
    padding-top: $spacing-07-32;
    margin-bottom: $spacing-09-48;
  }

  &__other-sponsors-grid,
  &__organizers-grid {
    margin-top: $spacing-06-24;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: $spacing-07-32;
  }

  @media screen and (max-width: map-get($container-max-widths, xl )) {
    &__title {
      margin-bottom: $spacing-02;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-07-32;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__other-sponsors-wrapper {
      margin-bottom: $spacing-07-32;
    }

    &__main-sponsors-wrapper,
    &__other-sponsors-grid,
    &__organizers-grid {
      grid-template-columns: repeat(auto-fit, minmax(40%, 1fr));
      grid-row-gap: $spacing-08-40;
    }
  }
}
