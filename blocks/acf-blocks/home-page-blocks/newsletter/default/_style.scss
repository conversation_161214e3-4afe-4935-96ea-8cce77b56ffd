html.dark-mode {
  .home-newsletter {
    &__image {
      display: none;

      &--dark {
        display: block;
      }
    }
  }
}

.home-newsletter {
  margin-bottom: 5.2rem;
  display: flex;
  justify-content: center;
  padding: 2.4rem;
  background-color: #f3f4f6;

  &__content-wrapper,
  &__image-wrapper {
    flex: 1;
  }

  &__image-wrapper {
    margin-left: 1.2rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__content-wrapper {
    margin-right: 1.2rem;
  }

  &__image {
    height: 100%;
    width: 100%;
    object-fit: cover;
    max-height: 305px;

    &--dark {
      display: none;
    }
  }

  &__title {
    padding-bottom: 1.6rem;
    margin-bottom: 2.4rem !important;
    border-bottom: 0.1rem solid #e5e7eb;
    color: #121212;
  }

  &__description {
    margin-bottom: 2.4rem;
    & > p {
      font-size: 1.6rem;
      line-height: 2.4rem;
      color: #363c4a;
    }
  }

  &__form-wrapper {
    margin-bottom: 0.8rem;

    .mc-field-group {
      .mc-field-wrapper {
        display: flex;

        #mc-embedded-subscribe {
          border-radius: 0;
          -webkit-border-radius: 0;
          -moz-border-radius: 0;
          -webkit-appearance: none;
        }
      }

      div.mce_inline_error {
        background-color: transparent !important;
        color: #121212 !important;
        padding: 0 !important;
        margin: 2.4rem !important;
        margin-left: 0 !important;
        font-size: 1.4rem;
        font-weight: 700;
      }
    }

    #mce-responses {
      padding: 0 !important;
      margin-top: 2.4rem !important;
      font-size: 1.4rem;
      font-weight: 700;
    }
  }

  &__consent-wrapper {
    p,
    a {
      font-size: 1.3rem !important;
      line-height: 2rem !important;
    }

    p {
      color: #363c4a !important;
    }

    a {
      color: #d97706 !important;
      transition: color 0.3s ease;

      @media (hover: hover) {
        &:hover {
          color: #b45309 !important;
        }
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, lg )) {
  .home-newsletter {
    flex-direction: column-reverse;
    width: 100vw;
    margin-left: calc((100% - 100vw) / 2);
    padding: 2.4rem 3.9rem;
    margin-bottom: 3.2rem;

    &__image-wrapper {
      margin-left: 0;
      margin-bottom: 2.4rem;
    }

    &__description {
      & > p {
        font-size: 1.4rem;
        line-height: 2rem;
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .home-newsletter {
    padding: 2.4rem 1.5rem;

    &__image {
      max-height: 16rem;
    }

    &__title {
      margin-bottom: 1.6rem !important;
    }

    &__description {
      & > p {
        font-size: 1.4rem;
        line-height: 2rem;
      }
    }

    &__form-wrapper {
      .mc-field-group {
        #mce-EMAIL {
          font-size: 1.4rem !important;
        }

        #mc-embedded-subscribe {
          padding: 1.2rem 0.9rem;
        }

        div.mce_inline_error {
          margin: 0.8rem !important;
          margin-left: 0 !important;
          font-size: 1.2rem;
        }
      }

      #mce-responses {
        margin: 0 !important;
      }
    }
  }
}
