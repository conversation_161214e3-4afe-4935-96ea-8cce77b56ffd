<?php

/**
 * The term object of the appropriate category
 * @var WP_Term $term
 */
$term = get_field('category');

/**
 * The term ID of the appropriate category
 */
$term_id = $term->term_id ?? null;

/**
 * The ID of the selected left side special post
 * @var array
 */
$special = 'yes' === get_field('has_special_posts') ? get_field('special_article') : null;

/**
 * The ID of the selected right side special post
 * @var array
 */
$right_side_special = 'yes' === get_field('has_special_posts') ? get_field('right_side_special_article') : null;

/**
 * The ID of the featured Article of the block
 * @var int
 */
$featured_article_id = get_field('featured_article');

/**
 * Whether to replace the last article with a magazine promo
 * @var boolean
 */
$replace_last_item = get_field('replace_last_item');

if ($replace_last_item) {
	/**
	 * The URL of the promo
	 * @var string
	 */
	$promo_url = get_field('url');

	/**
	 * The image of the promo
	 * @var string
	 */
	$promo_image = get_field('image');
}

/**
 * Get print category ID
 * @var int
 */
$print_category = get_field('print_category', 'option') ?? null;
?>

<?php if ($term_id) : ?>

	<div class="home-category-type-a">

		<div class="home-category-type-a__header">
			<?php get_template_part('template-parts/home-page/category-header/index', null, array('category' => $term_id)); ?>
		</div>

		<div class="home-category-type-a__featured">

			<?php
			$args = array(
				'page'			  => 'home',
				'posts_per_page'  => 4,
				'post_type'		  => 'post',
				'post_status'	  => 'publish',
				'tax_query'		  => array(
					array(
						'terms'	  => $term_id,
                        'taxonomy' => $term->taxonomy ?? 'category',
					),
				),
				'order_by'		  => 'date',
				'order'			  => 'DESC',
				'post__not_in'	  => isset($GLOBALS["used_ids"]) ? $GLOBALS["used_ids"] : []
			);

			if ($print_category) {
				$args['category__not_in'] = array($print_category);
			}

			$args['tax_query'] = array_filter($args['tax_query']); // Remove null values

			if ($featured_article_id) {
				$args['fields'] = 'ids';
				$args['posts_per_page'] = 3;
				$args['post__not_in'][] = $featured_article_id;

				$featured_args = array(
					'page'   => 'home',
					'p'		 => $featured_article_id,
					'fields' => 'ids'
				);
				$featured_query = new WP_Query($featured_args);
				$regular_query = new WP_Query($args);

				$allTheIDs = array_merge($featured_query->posts, $regular_query->posts);
				$query = new WP_Query(
					array(
						'post__in' => $allTheIDs,
						'orderby'  => 'post__in',
					)
				);
			} else {
				$query = new WP_Query($args);
			}

			get_template_part('template-parts/home-page/two-column-featured-articles/index', null, array('query' => $query, 'category' => $term_id));

			wp_reset_query();
			?>

		</div>

		<div class="home-category-type-a__articles">

			<?php
			$args = array(
				'page'			  => 'home',
				'posts_per_page'  => 3,
				'post_type'		  => 'post',
				'post_status'	  => 'publish',
				'tax_query'		  => array(
					array(
						'terms'	  => $term_id,
						'taxonomy' => CATEGORY_TYPE,
					)
				),
				'order_by'		  => 'date',
				'order'			  => 'DESC',
				'offset'		  => '4',
			);

			if ($print_category) {
				$args['category__not_in'] = array($print_category);
			}

			if ($replace_last_item) {
				$args['posts_per_page'] = 2;
			}

			$query = new WP_Query($args);

			pageHome\Exclusions::getInstance()->setExclusions(wp_list_pluck($query->posts, 'ID'));
			?>

			<?php if ($query) : ?>

				<div class="home-category-type-a__articles-grid">

					<?php get_template_part('template-parts/home-page/articles-grid/index', null, array('query' => $query, 'category' => $term_id, 'replace_last_item' => $replace_last_item, 'magazine' => $replace_last_item ? array('url' => $promo_url, 'image' => $promo_image) : null)); ?>

				</div>

			<?php endif; ?>

		</div>

		<?php if ($special) : ?>

			<div class="home-category-type-a__specials">

				<?php get_template_part('template-parts/home-page/two-column-specials/index', null, array('special' => $special, 'right_side_special_article' => $right_side_special)); ?>

			</div>

		<?php endif; ?>

	</div>

<?php endif; ?>
