.home-category-type-a {
  margin-bottom: $spacing-09-48;

  &__featured {
    margin-bottom: $spacing-09-48;
  }

  &__articles {
    margin-bottom: $spacing-09-48;

    .articles-grid {
      .article-card {
        padding-top: $spacing-00;
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, lg )) {
  .home-category-type-a {
    margin-bottom: $spacing-07-32;

    .articles-grid {
      .article-card {
        &:first-child {
          .article-card__image-wrapper {
            display: block;
          }
          .article-card__title {
            font-size: 1.8rem;
            line-height: 2.8rem;
          }
        }
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .home-category-type-a {
    &__articles {
      margin-bottom: $spacing-07-32;
    }
  }
}
