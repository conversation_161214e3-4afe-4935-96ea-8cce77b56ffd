<?php

/**
 * The term object of the appropriate category
 * @var WP_Term $term
 */
$term = get_field('category');

/**
 * The term ID of the appropriate category
 */
$term_id = $term->term_id ?? null;

/**
 * The ID of the featured Article of the block
 * @var int
 */
$featured_article_id = get_field('featured_article');

/**
 * Whether to replace the last article with a magazine promo
 * @var boolean
 */
$replace_last_item = get_field('replace_last_item');

/**
 * Get print category ID
 * @var int
 */
$print_category = get_field('print_category', 'option') ?? null;

$args = array(
	'page'			  => 'home',
	'posts_per_page'  => 4,
	'post_type'		  => 'post',
	'post_status'	  => 'publish',
	'order_by'		  => 'date',
	'order'			  => 'DESC',
	'tax_query'		  => array(
		array(
            'taxonomy' => $term->taxonomy ?? 'category',
			'terms'	   => $term_id,
			'field'	   => 'term_id'
		),
		array(
			'taxonomy' => CATEGORY_TYPE,
			'terms'    => $print_category,
			'field'    => 'term_id',
			'operator' => 'NOT IN',
		),
	),
	'post__not_in'	 => isset($GLOBALS["used_ids"]) ? $GLOBALS["used_ids"] : []
);

if ($featured_article_id) {
	$args['fields'] = 'ids';
	$args['posts_per_page'] = 3;
	$args['post__not_in'] = array($featured_article_id);

	$featured_args = array(
		'p'		 => $featured_article_id,
		'fields' => 'ids'
	);
	$featured_query = new WP_Query($featured_args);
	$regular_query = new WP_Query($args);

	$allTheIDs = array_merge($featured_query->posts, $regular_query->posts);
	$query = new WP_Query(
		array(
			'post__in' => $allTheIDs,
			'orderby'  => 'post__in',
		)
	);
} else {
	$query = new WP_Query($args);
}
?>

<?php if ($term_id) : ?>

	<div class="home-category-type-d">

		<div class="home-category-type-d__header">
			<?php get_template_part('template-parts/home-page/category-header/index', null, array('category' => $term_id)); ?>
		</div>

		<?php get_template_part('template-parts/home-page/two-column-featured-articles/index', null, array('query' => $query, 'category' => $term_id)); ?>

	</div>

<?php endif; ?>
