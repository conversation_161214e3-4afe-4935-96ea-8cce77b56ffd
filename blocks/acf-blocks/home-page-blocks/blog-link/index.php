<?php
/**
 * The Label of the button
 * @var string
 */
$buttonLabel = get_field('label') ?: null;

/**
 * The ID of the Blog Page
 * @var string
 * */
$blogPage = get_field('blog_page') ?: null;

/**
 * The URL of the Blog Page
 * @var string
 * */
$blogPageLink = $blogPage ? get_permalink($blogPage) : null;
?>

<?php if( $buttonLabel && $blogPageLink ):?>

<?php endif;?>
<div class="home-blog-link">

	<?php
		$button = new Button($buttonLabel, $blogPageLink, 'medium', 'primary', false, false, '', '');
		echo $button->render();
	?>

</div>
