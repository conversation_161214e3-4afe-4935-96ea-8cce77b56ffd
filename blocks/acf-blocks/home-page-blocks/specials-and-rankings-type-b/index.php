<?php

/**
 * Title
 * @var string
 * */
$title = get_field('title');

/**
 * Specials and rankings page link
 * @var string
 */
$specials_link = get_field('specials_page_link');

/**
 * Auto () or Manual
 * @var string
 */
$mode = get_field('mode');

/**
 * The number of posts to show
 * @var int
 */
$number_of_posts = is_admin() ? 3 : get_field('number_of_posts_to_show');

if ('auto' === $mode) {
	$args = array(
		'posts_per_page'  => $number_of_posts ?: 5,
		'post_type'		 => 'special',
		'post_status'	 => 'publish',
		'orderby'		 => 'date',
		'order'			 => 'DESC'
	);

	$query = new WP_Query($args);
	$posts = $query->have_posts() ? $query->posts : [];
} else {
	$posts = get_field('specials') ? array_map(function ($special) {
		return $special['special'];
	}, get_field('specials')) : null;
}
$unique_id = uniqid();

?>

<div class="home-specials-rankings-b">

	<div class="home-specials-rankings-b__header">

		<?php get_template_part('template-parts/home-page/category-header/index', null, ['title' => $title, 'url' => $specials_link]) ?>

	</div>

	<div class="home-specials-rankings-b__grid splide" id="home-specials-rankings-b__grid-<?php echo esc_attr( $unique_id ); ?>">

		<div class="splide__track">

			<div class="splide__list">

				<?php if ('auto' === $mode && $query->have_posts()) : ?>

					<?php while ($query->have_posts()) : $query->the_post(); ?>

						<?php
						$url = get_field('cz' === COUNTRY ? 'redirect' : 'url', get_the_ID()) ?? get_the_permalink(get_the_ID());
						if ('cz' === COUNTRY) {
							$image = get_field( 'bigListingImage',
								get_the_ID() ) ? wp_get_attachment_image( get_field( 'bigListingImage', get_the_ID() ),
								'home_specials_rankings_b', false,
								[
									'class'  => 'home-specials-rankings-b__special-image',
									'srcset' => wp_get_attachment_image_url(
											get_field( 'bigListingImage', get_the_ID() ),
											'home_specials_rankings_b2x' ) . ' 2x'
								] ) :
								get_the_post_thumbnail( get_the_ID(),
									'home_specials_rankings_b', [
										'class'  => 'home-specials-rankings-b__special-image',
										'srcset' => wp_get_attachment_image_url(
												get_field( 'bigListingImage', get_the_ID() ),
												'home_specials_rankings_b2x' ) . ' 2x'
									] ) ?? null;
						} else {
							$image = has_post_thumbnail( get_the_ID() ) ? get_the_post_thumbnail( get_the_ID(),
								'home_specials_rankings_b',
								[
									'class'  => 'home-specials-rankings-b__special-image',
									'srcset' => get_the_post_thumbnail_url(
													get_the_ID(),
													'home_specials_rankings_b2x' ) . ' 2x'
								] ) : null;
						}
						?>

						<a href="<?= $url; ?>" <?= get_field('cz' === COUNTRY ? 'redirect' : 'url', get_the_ID()) ? 'target="_blank" rel="noopener"' : ''; ?> class="home-specials-rankings-b__special splide__slide">

							<div class="home-specials-rankings-b__special-image-wrapper">

								<?= $image; ?>

							</div>

							<h4 class="home-specials-rankings-b__item-title callout"><?= get_the_title(get_the_ID()); ?></h4>

						</a>

					<?php endwhile; ?>

				<?php endif; ?>

				<?php if ('manual' === $mode && $posts) : ?>

					<?php foreach ($posts as $post) : ?>

						<?php
						$url = get_field('cz' === COUNTRY ? 'redirect' : 'url', $post) ?? get_the_permalink($post);
						if ('cz' === COUNTRY) {
							$image = get_field( 'bigListingImage',
								$post ) ? wp_get_attachment_image( get_field( 'bigListingImage', $post ),
								'home_specials_rankings_b', false,
								[
									'class'  => 'home-specials-rankings-b__special-image',
									'srcset' => wp_get_attachment_image_url(
													get_field( 'bigListingImage', $post ),
													'home_specials_rankings_b2x' ) . ' 2x'
								] ) :
								get_the_post_thumbnail(
									$post,
									'home_specials_rankings_b',
									[
										'class'  => 'home-specials-rankings-b__special-image',
										'srcset' => get_the_post_thumbnail_url( $post,
												'home_specials_rankings_b2x' ) . ' 2x'
									] ) ?? null;
						} else {
							$image = has_post_thumbnail( $post ) ? get_the_post_thumbnail( $post,
								'home_specials_rankings_b',
								[
									'class'  => 'home-specials-rankings-b__special-image',
									'srcset' => get_the_post_thumbnail_url( $post,
											'home_specials_rankings_b2x' ) . ' 2x'
								] ) : null;
						}
						?>

						<a href="<?= $url; ?>" <?= get_field('cz' === COUNTRY ? 'redirect' : 'url', $post) ? 'target="_blank" rel="noopener"' : ''; ?> class="home-specials-rankings-b__special splide__slide">

							<div class="home-specials-rankings-b__special-image-wrapper">

								<?= $image; ?>

							</div>

							<h4 class="home-specials-rankings-b__item-title callout"><?= get_the_title($post); ?></h4>

						</a>

					<?php endforeach; ?>

				<?php endif; ?>

			</div>

		</div>

	</div>

</div>
