.home-specials-rankings-b {
  margin-bottom: $spacing-07-32;

  &__grid {
    display: block;
    padding-bottom: $spacing-07-32;
  }

  &__special-image-wrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    height: 0;
    overflow: hidden;
    margin-bottom: $spacing-02;

    @supports (aspect-ratio: 16/9) {
      aspect-ratio: 16/9;
      padding-top: 0;
      height: auto;
    }
  }

  &__special-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  &__special {
    text-decoration: none;
    color: initial;
    display: flex;
    flex-direction: column;

    @media (hover: hover) {
      &:hover {
        .home-specials-rankings-b {
          &__item-title {
            text-decoration: underline;
          }
        }
      }
    }
  }

  &__item-title {
    color: $color-text-primary;
    font-weight: 600 !important;
    font-size: 1.8rem;
    line-height: 2.5rem;
    transition: color 0.3s ease;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    &__grid {
      padding-bottom: $spacing-00;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__grid {
      padding-bottom: $spacing-06-24;
    }
  }
}
