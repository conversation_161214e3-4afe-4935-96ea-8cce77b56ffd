document.addEventListener('DOMContentLoaded', function () {
  jQuery(function ($) {
    if (!$('.home-specials-rankings-b__grid').length) {
      return;
    }

    const initSlider = (selector) => {
      const splide = new Splide(selector, {
        type: 'loop',
        perPage: 3,
        perMove: 1,
        gap: '3rem',
        pagination: true,
        arrows: true,
        focus: 'number',
        omitEnd: true,
        autoplay: 'pause',
        interval: 3500,
        speed: 300,
        intersection: {
          rootMargin: '150px',
          inView: {
            autoplay: true,
          },
          outView: {
            autoplay: false,
          },
        },
        breakpoints: {
          768: {
            perPage: 2.2,
            arrows: false,
            pagination: false,
          },
          576: {
            perPage: 1,
            focus: 'center',
            arrows: false,
            pagination: true,
          },
        },
      }).mount(window.splide.Extensions);

      window.splideSliders = window.splideSliders || [];
      window.splideSliders.push(splide);
    };

    const isAdmin = $('body').hasClass('wp-admin');

    if (isAdmin && wp) {
      const { select, subscribe } = wp.data;
      const closeListener = subscribe(() => {
        const isReady = select('core/editor').__unstableIsEditorReady();
        if (isReady) {
          closeListener();

          $('.home-specials-rankings-b__grid').each(function () {
            const selector = '#' + $(this).attr('id');
            initSlider(selector);
          });
        }
      });
    } else {
      $('.home-specials-rankings-b__grid').each(function () {
        const selector = '#' + $(this).attr('id');
        initSlider(selector);
      });
    }
  });
});
