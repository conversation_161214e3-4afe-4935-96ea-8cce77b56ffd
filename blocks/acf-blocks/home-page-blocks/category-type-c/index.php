<?php

/**
 * The term object of the appropriate category
 * @var WP_Term $term
 */
$term = get_field('category');

/**
 * The term ID of the appropriate category
 */
$term_id = $term->term_id ?? null;

/**
 * The featured color of the section
 * @var string
 */
$color = get_field('featured_color');

/**
 * The featured hover color of the section
 * @var string
 */
$hover_color = get_field('featured_hover_color');

/**
 * The ID of the featured Article of the block
 * @var int
 */
$featured_article_id = get_field('featured_article');

/**
 * Whether to replace the last article with a magazine promo
 * @var boolean
 */
$replace_last_item = get_field('replace_last_item');

if ($replace_last_item) {
	/**
	 * The URL of the promo
	 * @var string
	 */
	$promo_url = get_field('url');

	/**
	 * The image of the promo
	 * @var string
	 */
	$promo_image = get_field('image');
}

/**
 * Get print category ID
 * @var int
 */
$print_category = get_field('print_category', 'option') ?? null;

/**
 * The query for the featured post
 * @var array
 */
$args = array(
	'page'			  => 'home',
	'posts_per_page'  => 1,
	'post_type'		  => 'post',
	'post_status'	  => 'publish',
	'order_by'		  => 'date',
	'order'			  => 'DESC',
	'tax_query'		  => array(
		array(
            'taxonomy' => $term->taxonomy ?? 'category',
			'terms'	   => $term_id,
		),
		array(
			'taxonomy' => CATEGORY_TYPE,
			'terms'    => $print_category,
			'field'    => 'term_id',
			'operator' => 'NOT IN',
		),
	),
	'post__not_in'	 => isset($GLOBALS["used_ids"]) ? $GLOBALS["used_ids"] : []
);

if ($featured_article_id) {
	$args['p'] = $featured_article_id;
}

$query = new WP_Query($args);

$found_featured = $featured_article_id ? $featured_article_id : $query->posts[0]->ID;

pageHome\Exclusions::getInstance()->setExclusions(wp_list_pluck($query->posts, 'ID'));
?>

<div class="home-category-type-c">

	<style>
		.home-category-type-c {
			--section-color: <?= $color ?? '#000'; ?>;
			--section-hover-color: <?= $hover_color ?? '#000'; ?>;
		}
	</style>

	<div class="home-category-type-c__header<?= $color ? ' colored' : ''; ?>">
		<?php get_template_part('template-parts/home-page/category-header/index', null, array('category' => $term_id)); ?>
	</div>

	<?php if ($query->have_posts()) : ?>

		<?php while ($query->have_posts()) : $query->the_post(); ?>

			<div class="home-category-type-c__featured">

				<?php get_template_part('template-parts/article-card/index', null, array('post_id' => $featured_article_id ?: get_the_ID(), 'article_card_type' => 'featured-large')); ?>

			</div>

		<?php endwhile; ?>

	<?php endif; ?>

	<div class="home-category-type-c__regular-articles">

		<?php
			$args['posts_per_page'] = $replace_last_item ? 5 : 6;
			unset($args['p']);
			$args['post__not_in'] = array($found_featured);
			$regular_posts_query = new WP_Query($args);
			pageHome\Exclusions::getInstance()->setExclusions(wp_list_pluck($regular_posts_query->posts, 'ID'));
			get_template_part('template-parts/home-page/articles-grid/index', null, array('query' => $regular_posts_query, 'category' => $term_id, 'replace_last_item' => $replace_last_item, 'magazine' => $replace_last_item ? array('url' => $promo_url, 'image' => $promo_image) : null));
		?>

	</div>

</div>
