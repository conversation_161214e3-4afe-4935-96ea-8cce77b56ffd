.home-category-type-c {
  margin-bottom: $spacing-09-48;

  &__header {
    .category-header {
      border-color: var(--section-color) !important;

      &__title {
        color: $color-text-primary;
        color: var(--section-color) !important;

        @media (hover: hover) {
          &:hover {
            opacity: 1 !important;
            color: var(--section-hover-color) !important;
          }
        }
      }
    }

    &.colored {
      .category-header {
        &__title {
          @media (hover: hover) {
            &:hover {
              opacity: 0.66;
            }
          }
        }
      }
    }
  }

  &__featured {
    margin-bottom: $spacing-08-40;
    position: relative;

    .article-card {
      .article-details {
        padding-left: $spacing-00;
      }

      &__image-wrapper {
        margin-bottom: $spacing-00 !important;
      }

      &__content-wrapper {
        position: absolute;
        left: 0;
        bottom: 0;
        background-color: $color-surface-primary;
        max-width: 73rem;
      }
    }
  }

  &__regular-articles {
    .article-card {
      &__category {
        color: var(--section-color) !important;

        &:before {
          border-color: var(--section-color) !important;
        }
      }
    }
  }

  .article-card {
    @media (hover: hover) {
      &:hover {
        .article-card__title {
          color: var(--section-color, $color-link-hover);
        }
      }

      .article-details {
        &__author {
          @media (hover: hover) {
            &:hover {
              color: var(--section-color, $color-link-hover) !important;
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, lg )) {
  .home-category-type-c {
    margin-bottom: $spacing-07-32;

    &__featured {
      margin-bottom: $spacing-07-32;

      .article-card {
        &__category--cz {
          display: none;
        }

        &__content-wrapper {
          max-width: none;
          width: calc(100% - 3.2rem);
          margin: auto;
          position: relative;
          left: unset;
          padding: $spacing-06-24 $spacing-06-24 $spacing-01;
          margin-top: -15%;
        }
      }
    }

    &__regular-articles {
      .article-card {
        &__image-wrapper {
          display: block !important;
        }

        &__title {
          font-weight: 500;
          font-size: 1.6rem;
          line-height: 2.4rem;
        }
      }
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .home-category-type-c {
    &__featured {
      .article-card {
        &__image-wrapper {
          aspect-ratio: 1/1;
        }

        &__image {
          img {
            height: 100%;
            object-fit: cover;
          }
        }

        &__content-wrapper {
          margin-top: -2.2rem;
        }
      }
    }
  }
}
