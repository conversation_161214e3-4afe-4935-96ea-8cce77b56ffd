<?php
/**
 * The term ID of the print category
 * @var int
 */
$print_category = get_field('print_category', 'option') ?? null;
/**
 * The array of taxonomies to exclude from the query
 * @var array
 */
$tax_exceptions = get_field('exclude_most_recent', 'option');

// Initialize the category and tag exception arrays
$cat_exceptions = [];
$tag_exceptions = [];

if (is_array($tax_exceptions) && !empty($tax_exceptions)) {
	foreach ($tax_exceptions as $term) {
		if ('category' === $term->taxonomy) {
			$cat_exceptions[] = $term->term_id;
		} else if ('post_tag' === $term->taxonomy) {
			$tag_exceptions[] = $term->term_id;
		}
	}
}

// Add print category to the category exceptions if it's not already there
if (!in_array($print_category, $cat_exceptions)) {
    $cat_exceptions[] = $print_category;
}

$args = array(
	'page'			   => 'home',
	'posts_per_page'   => 3,
	'post_type'   	   => 'post',
	'post_status' 	   => 'publish',
	'order_by'	  	   => 'date',
	'order'		  	   => 'DESC',
	'tax_query'		   => array(
		'relation' => 'AND',
		array(
			'taxonomy' => CATEGORY_TYPE,
			'field'	   => 'term_id',
			'terms'	   => $cat_exceptions,
			'operator' => 'NOT IN'
		),
		array(
			'taxonomy' => 'post_tag',
			'field'	   => 'term_id',
			'terms'	   => $tag_exceptions,
			'operator' => 'NOT IN'
		)
	)
);

$query = new WP_Query($args);

pageHome\Exclusions::getInstance()->setExclusions(wp_list_pluck($query->posts, 'ID'));
?>

<?php if ($query->have_posts()) :?>

	<div class="home-most-recent-articles">
		<?php get_template_part('template-parts/home-page/articles-grid/index', null, array('query' => $query)); ?>
	</div>

<?php endif; ?>
