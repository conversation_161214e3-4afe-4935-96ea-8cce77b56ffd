<?php

/**
 * The ids of the selected brandvoice posts
 * @var array
 */
$blocks = get_field('blocks');

/**
 * Placement of the text
 * @var string
 */
$text_placement = get_field('text_placement');

/**
 * Placement of the tag
 * @var string
 */
$badge_position = get_field('badge_position');
?>

<?php if ($blocks && (count($blocks) > 1)) : ?>

	<div class="home-featured-brandvoice-articles">

		<?php foreach ($blocks as $block) : ?>

			<?php
			$articles = $block['articles'];

			if (empty($articles)) continue;

			$post_to_show = count($articles) > 1 ? $articles[rand(0, count($articles) - 1)] : $articles[0];
			?>

			<?php
			/**
			 * Show author on card
			 * @var boolean
			 */
			$show_author = $post_to_show['show_author'];
			?>

			<div class="home-featured-brandvoice-articles__article">

				<?php get_template_part('template-parts/brandvoice-card/index', null, array('post_id' => $post_to_show['article'], 'show_author' => $show_author, 'text_placement' => $text_placement, 'badge_position' => $badge_position, 'three_in_a_row' => 2 < count($blocks))); ?>

			</div>

		<?php endforeach; ?>

	</div>

<?php endif; ?>
