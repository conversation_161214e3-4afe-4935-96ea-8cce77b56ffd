.home-featured-brandvoice-articles {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(30%, 1fr));
  grid-gap: 3rem;
  margin-bottom: $spacing-09-48;

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    display: block;

    .brandvoice-card {
      &:first-child {
        margin-bottom: $spacing-06-24;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    margin-bottom: $spacing-07-32;
  }
}
