.home-category-type-b {
  padding: $spacing-00;

  &.on-card {
    .splide {
      &__track {
        height: 18rem;
      }
    }

    .home-category-type-b {
      &__article-content-wrapper {
        padding: $spacing-01 !important;
      }
      &__article-title {
        font-size: 0.8rem !important;
        line-height: 1.4rem !important;
      }
    }

    .article-details {
      &__author,
      &__publish-time,
      &__read-time {
        font-size: 0.7rem !important;
        line-height: 1.4rem;
      }
    }
  }

  &.below-card {
    .article-details {
      &__author {
        display: none;
      }

      &__publish-time {
        color: $color-text-secondary !important;
        font-size: 1.4rem !important;
        margin-bottom: $spacing-00 !important;
        text-decoration: none !important;
        font-family: $font-archivo !important;
      }
    }

    .article-card {
      &__category {
        color: $color-text-secondary !important;
        font-size: 1.4rem !important;
        margin-bottom: $spacing-00 !important;
        text-decoration: none !important;
        font-family: $font-archivo !important;

        &::before {
          content: none;
        }
      }

      &__title {
        font-family: $font-archivo !important;
        color: $color-text-primary;
      }
    }
  }
}
