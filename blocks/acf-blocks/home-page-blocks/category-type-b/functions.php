<?php

namespace BrandvoiceItems;

use WP_Query;

/**
 * Retrieves a list of posts for a given term ID.
 *
 * @param int $term_id The ID of the term to retrieve posts for.
 * @return array An array of post IDs.
 */
function frontend_get_brandvoice_posts(string $type) {
    if (!in_array($type, ['brand', 'ad'])) return [];

    $brandvoice_term = null;
    $prio_posts = [];
	$posts_per_page = get_field('number_of_posts_to_show') ?? 12;

	if ('brand' === $type) {
        $brandvoice_term = get_field('brandvoice_category', 'option');
        $prio_posts = (array) get_field('prioritized_items') ?? [];
    } else {
        $brandvoice_term = get_field('advoice_category', 'option');
        $prio_posts = (array) get_field('prioritized_items') ?? [];
    }

    $posts = [];

    foreach ($prio_posts as $priority_post) {
        if (count($posts) < 6) {
            if (is_array($priority_post)) {
				$posts[] = $priority_post['post'];
			}
        } else {
            break;
        }
    }

    $remaining_posts_count = $posts_per_page - count($posts);

    if ($remaining_posts_count > 0) {
        $args = [
            'posts_per_page'    => $remaining_posts_count,
            'post_type'         => 'post',
            'post_status'       => 'publish',
            'orderby'           => 'date',
            'order'             => 'DESC',
            'tax_query'         => [
               	array(
					'taxonomy'	=> CATEGORY_TYPE,
					'field'		=> 'term_id',
					'terms'		=> $brandvoice_term
				)
            ],
            'post__not_in'      => $posts,
            'fields'            => 'ids',
        ];

        $query = new WP_Query($args);
        $additional_posts = $query->posts;

        foreach ($additional_posts as $post) {
            if (count($posts) < 12) {
                $posts[] = $post;
            } else {
                break;
            }
        }
    }

    return $posts;
}
