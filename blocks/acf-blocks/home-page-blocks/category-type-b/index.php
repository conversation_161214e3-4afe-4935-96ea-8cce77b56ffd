<?php

/**
 * The term object of the appropriate category
 * @var WP_Term $term
 */
$term = get_field('category');

/**
 * The term ID of the appropriate category
 */
$term_id = $term->term_id ?? null;

/**
 * The term IDs of all the brandvoice categories
 * @var array
 * */
$brandvoice_categories = get_field('brandvoice_categories', 'option') ?: [];

/**
 * The term ID of the brandvoice category
 * @var int
 */
$brandvoice_category = get_field('brandvoice_category', 'option');

/**
 * The term ID of the advoice category
 * @var int
 * */
$advoice_category = get_field('advoice_category', 'option');

/**
 * If the selected term is brandvoice
 * @var boolean
 */
$is_brandvoice_type = $term_id === $brandvoice_category;

/**
 * If the selected term is advoice
 * @var boolean
 * */
$is_advoice_type = $term_id === $advoice_category;

/**
 * The number of posts to show
 * @var int
 */
$number_of_posts = in_array($term_id, $brandvoice_categories) ? 12 : get_field('number_of_posts_to_show');

/**
 * The type of the cards shown in the slider (text-on-card or text-below-card)
 * @var string
 */
$type = get_field('type');

/**
 * Get print category ID
 * @var int
 */
$print_category = get_field('print_category', 'option') ?? null;

if ($is_brandvoice_type || $is_advoice_type) {
	$posts = \BrandvoiceItems\frontend_get_brandvoice_posts($is_brandvoice_type ? 'brand' : 'ad');
} else {
	$args = array(
		'page'			  => 'home',
		'posts_per_page'  => $number_of_posts ?: 6,
		'post_type'		  => 'post',
		'post_status'	  => 'publish',
		'order_by'		  => 'date',
		'order'			  => 'DESC',
		'tax_query'		  => array(
			array(
                'taxonomy' => $term->taxonomy ?? 'category',
				'terms'	   => $term_id,
			),
			array(
				'taxonomy' => CATEGORY_TYPE,
				'terms'    => $print_category,
				'field'    => 'term_id',
				'operator' => 'NOT IN',
			),
		),
		'fields'			=> 'ids',
		'post__not_in'	 => isset($GLOBALS["used_ids"]) ? $GLOBALS["used_ids"] : []
	);

	$query = new WP_Query($args);
	$posts = $query->posts;

	wp_reset_query(  );
}

$posts = array_filter($posts);
pageHome\Exclusions::getInstance()->setExclusions($posts);
?>
<?php if (!empty($posts)) : ?>

	<div class="home-category-type-b <?= $type; ?><?= 'cz' === COUNTRY ? ' with-bg' : ''; ?>">

		<?php if ('below-card' === $type && 'cz' === COUNTRY) : ?>

			<div class="home-category-type-b__bg"></div>

		<?php endif; ?>


		<div class="home-category-type-b__header">
			<?php get_template_part('template-parts/home-page/category-header/index', null, array('category' => $term_id)); ?>
		</div>

		<div class="home-category-type-b__slider splide<?= count($posts) < 3 ? ' no-slider' : ''; ?>" data-type="<?= $type; ?>">

			<div class="splide__track">

				<div class="splide__list">

					<?php foreach ($posts as $post) : ?>

						<?php if ('on-card' === $type) : ?>

							<a href="<?= get_the_permalink($post); ?>" class="home-category-type-b__article home-category-type-b__article--on-card splide__slide">

								<div class="home-category-type-b__article-wrapper">

									<div class="home-category-type-b__article-image-wrapper">

										<?php
										/**
										 * The featured image of the post
										 * @var string
										 */
										$image = get_field( 'primaryImage',
											$post ) ? wp_get_attachment_image( get_field( 'primaryImage', $post ),
											'category_type_b_desktop', false, array(
												'class'  => 'home-category-type-b__article-image',
												'alt'    => get_the_title( $post ),
												'srcset' => wp_get_attachment_image_url( get_field( 'primaryImage',
														$post ), 'category_type_b_desktop2x' ) . ' 2x'
											) ) : get_the_post_thumbnail( $post, 'category_type_b_desktop', array(
											'class'  => 'home-category-type-b__article-image',
											'alt'    => get_the_title(),
											'srcset' => get_the_post_thumbnail_url( $post,
													'category_type_b_desktop2x' ) . ' 2x'
										) );
										?>

										<?= $image; ?>

									</div>

									<div class="home-category-type-b__article-content-wrapper">

										<?php if ('cz' === COUNTRY) : ?>

											<?php get_template_part('template-parts/article-details/index', null, ['id' => $post, 'slider' => true, 'slider_type' => 'on-card']); ?>

										<?php endif; ?>

										<h5 class="home-category-type-b__article-title">
											<?= get_the_title($post); ?>
										</h5>

									</div>

								</div>

							</a>

						<?php elseif ('below-card' === $type) : ?>

							<div class="home-category-type-b__article home-category-type-b__article--below-card splide__slide">

								<?php get_template_part('template-parts/article-card/index', null, array('post_id' => $post, 'category' => $term_id, 'hide_read-time' => true, 'slider' => true)); ?>

							</div>

						<?php endif; ?>

					<?php endforeach; ?>

				</div>

			</div>

		</div>


	</div>

<?php endif; ?>
