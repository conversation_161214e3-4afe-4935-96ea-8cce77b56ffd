.home-category-type-b {
  position: relative;
  margin-bottom: $spacing-08-40;

  &__header {
    margin-bottom: $spacing-06-24;
  }

  &.on-card {
    .splide__list {
      height: 100% !important;
    }

    .home-category-type-b {
      &__slider {
        .article-details {
          justify-content: center;
          row-gap: 0;

          &__read-time,
          &__author,
          &__publish-time {
            color: $color-text-secondary;
            font-family: $font-archivo;
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.6rem;
            margin-bottom: -0.2rem;
          }
        }
      }
    }
  }

  &.below-card {
    padding: $spacing-08-40 $spacing-00;

    .article-meta {
      &__reading-time,
      &__button {
        display: none;
      }
    }

    &.with-bg {
      .tag {
        &--background {
          background-color: $color-surface-secondary;
        }
      }
    }
  }

  &__article-content-wrapper {
    bottom: -0.1rem;
    left: 50%;
    width: 80%;
    transform: translateX(-50%);
  }

  &__article {
    &--on-card {
      position: relative;
      width: 25.5rem;

      .home-category-type-b {
        &__article-wrapper {
          aspect-ratio: 16/9;
          width: 100%;
          height: 100%;
          position: relative;
          transition: all 0.3s ease;
        }

        &__article-image-wrapper {
          width: 100%;
          height: 100%;
          position: absolute;
          inset: 0;
          z-index: 0;
          transition: opacity 0.3s ease-in-out;
          overflow: hidden;
        }

        &__article-image {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          font-size: 1.2rem;
          transition: all 0.3s ease;
        }

        &__article-content-wrapper {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          bottom: -0.1rem;
          max-width: 80%;
          z-index: 1;
          background-color: $color-surface-secondary;
          padding: $spacing-02;
          transition: opacity 0.3s ease-in-out;
        }

        &__article-title {
          font-size: 1.2rem;
          line-height: 1.8rem;
          margin-bottom: $spacing-00;
          color: $color-text-primary;
          font-weight: 600 !important;
          text-align: center;
          transition: color 0.3s ease;
        }
      }

      @media (hover: hover) {
        &:hover {
          .home-category-type-b {
            &__article-title {
              text-decoration: underline;
            }
          }
        }
      }
    }

    &--below-card {
      .article-card {
        &__image-wrapper {
          margin-bottom: $spacing-03-12;
          width: 100%;
        }

        &__title {
          font-size: 1.6rem;
          line-height: 2.4rem;
          font-weight: 500;
          font-family: $font-archivo !important;
          display: -webkit-box;
          -webkit-line-clamp: 4;
          -webkit-box-orient: vertical;
          overflow: hidden;
          visibility: visible;
        }
        &__category {
          display: none;
        }
      }
    }
  }

  &__bg {
    position: absolute;
    top: 0;
    left: calc((100% - 100vw) / 2);
    height: 100%;
    width: 100vw;
    background-color: $color-surface-secondary;
    z-index: -1;
  }
}

@media screen and (max-width: map-get($container-max-widths, lg )) {
  .home-category-type-b {
    margin-bottom: $spacing-07-32;

    &.below-card {
      padding: $spacing-07-32 $spacing-00 $spacing-04-16 $spacing-00;
    }

    &__article {
      &--on-card {
        .home-category-type-b__article-content-wrapper {
          padding: $spacing-01;
        }

        .home-category-type-b__article-title {
          font-size: 1rem;
          line-height: 1.4rem;
        }
      }
    }

    &__slider[data-type='on-card'] {
      padding-top: $spacing-09-48;
    }

    &__slider[data-type='below-card'] {
      padding-bottom: $spacing-00;
    }
  }
}

@media screen and (max-width: map-get($container-max-widths, md )) {
  .home-category-type-b {
    margin-bottom: $spacing-07-32;
    overflow-x: hidden !important;

    &.below-card {
      padding: 0;
      padding-block: $spacing-06-24;
      margin-inline: -1.6rem;
      padding-inline: $spacing-04-16;
    }

    &.on-card {
      margin-inline: -1.6rem;
      padding-inline: $spacing-04-16;
    }

    &__article {
      &--on-card {
        .home-category-type-b__article-content-wrapper {
          padding: $spacing-04-16;
          width: calc(100% - 1.6rem) !important;
          max-width: none;
          left: 50%;
          bottom: unset;
          transform: translateX(-50%);
        }

        .home-category-type-b__article-title {
          font-size: 1.6rem;
          line-height: 2.4rem;
        }
      }
      &--below-card {
        .article-card {
          &__title {
            font-weight: 500;
            font-size: 1.6rem;
            line-height: 2.4rem;
          }

          &__category {
            width: min-content;
          }
        }
      }
    }

    &__slider[data-type='below-card'] {
      padding-bottom: $spacing-00;
      padding-inline: $spacing-04-16;
      width: 100vw;
      margin-left: -1.5rem;
    }

    &.on-card {
      .home-category-type-b {
        &__slider {
          .article-details {
            margin-bottom: $spacing-01;

            &__author,
            &__publish-time,
            &__read-time {
              font-size: 1.2rem;
              line-height: 2.2rem;
            }
          }
        }

        &__article-content-wrapper {
          width: 80% !important;
          padding-bottom: $spacing-00;
          bottom: -1rem;
        }

        &__article-title {
          font-size: 1.4rem;
          line-height: 2.4rem;
          font-weight: 600;
        }
      }
    }

    &.below-card {
      padding-bottom: $spacing-06-24;

      .home-category-type-b {
        &__slider {
          margin-block: $spacing-00;
          margin-inline: -1.6rem;
        }
      }
    }
  }
}
