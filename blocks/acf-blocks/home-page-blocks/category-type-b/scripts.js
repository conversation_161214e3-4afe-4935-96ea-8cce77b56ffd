document.addEventListener('DOMContentLoaded', function () {
  jQuery(function ($) {
    // The slider element
    const sliders = $('.home-category-type-b__slider');

    const locale = $('body').data('locale');

    if (!sliders.length) return;

    // Responsive settings for the Carousel with cards that have the text ON the card
    const onCardResponsiveSettings = {
      992: {
        perPage: 2.2,
        arrows: false,
        dots: true,
        gap: '3rem',
      },
      576: {
        perPage: 1,
        arrows: false,
        dots: true,
        gap: '3rem',
      },
    };

    // Responsive settings for the Carousel with cards that have the text UNDER the card
    const belowCardResponsiveSettings = {
      1279: {
        perPage: 3,
      },
      991: {
        perPage: 2.2,
        pagination: true,
        arrows: false,
      },
      767: {
        perPage: 1.2,
      },
      575: {
        autoplay: false,
        perPage: 1.4,
        arrows: false,
        pagination: true,
        type: 'slide',
      },
    };

    const initSliders = () => {
      sliders.each((index, slider) => {
        const type = $(slider).data('type');

        const splide = new Splide(slider, {
          type: 'loop',
          perPage: type === 'on-card' ? 3 : 4,
          perMove: 1,
          gap: type === 'on-card' ? '8rem' : '3rem',
          pagination: true,
          arrows: true,
          focus: type === 'on-card' ? 'center' : 'number',
          speed: 500,
          rewind: true,
          autoplay: 'pause',
          interval: 3500,
          breakpoints: type === 'on-card' ? onCardResponsiveSettings : belowCardResponsiveSettings,
          classes:
            type !== 'on-card' && locale === 'cz'
              ? {
                  arrows: 'splide__arrows splide__arrows--gray',
                }
              : {},
          intersection: {
            rootMargin: '150px',
            inView: {
              autoplay: true,
            },
            outView: {
              autoplay: false,
            },
          },
        }).mount(window.splide.Extensions);

        if (type === 'on-card') {
          splide.on('move', (newSlide, oldSlide, moveType) => {
            const currentSlide = splide.Components.Slides.getAt(oldSlide);
            const nextSlide = splide.Components.Slides.getAt(newSlide);

            currentSlide.slide.classList.remove('is-active');
            nextSlide.slide.classList.add('is-active');
          });
        }

        if (splide.length < 3) {
          splide.options = {
            ...splide.options,
            type: 'slide',
            focus: 'number',
            autoPlay: false,
            arrows: false,
            pagination: false,
            gap: '3rem',
          };
          splide.refresh();
        }

        window.splideSliders = window.splideSliders || [];
        window.splideSliders.push(splide);
      });
    };

    const isAdmin = $('body').hasClass('wp-admin');

    if (isAdmin && wp) {
      const { select, subscribe } = wp.data;
      const closeListener = subscribe(() => {
        const isReady = select('core/editor').__unstableIsEditorReady();
        if (isReady) {
          closeListener();

          initSliders();
        }
      });
    } else {
      initSliders();
    }
  });
});
