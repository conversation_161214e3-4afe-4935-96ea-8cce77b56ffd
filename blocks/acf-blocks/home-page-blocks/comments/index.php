<?php

/**
 * The ID of the comment taxonomy
 */
$comment_tag = get_field('comment_tag', 'option') ?? null;

if ($comment_tag) {
	$args = array(
		'post_type' 	  => 'post',
		'post_status' 	  => 'publish',
		'posts_per_page'  => 15,
		'orderby'		  => 'date',
		'order'			  => 'DESC',
		'tax_query'		  => array(
			array(
				'taxonomy' => $comment_tag->taxonomy,
				'field'	   => 'term_id',
				'terms'	   => $comment_tag->term_id,
			),
		)
	);

	$query = new WP_Query($args);
}
?>

<?php if (isset($query)) : ?>

	<div class="home-comments">

		<div class="home-comments__bg"></div>

		<div class="home-comments__header">
			<?php get_template_part('template-parts/home-page/category-header/index') ?>
		</div>

		<?php if ($query->have_posts()) : ?>

			<div id="comments-slider" class="home-comments__grid<?= is_admin() ? ' no-slider' : ''; ?> splide">

				<div class="splide__track">

					<div class="splide__list">

						<?php while ($query->have_posts()) : $query->the_post(); ?>

							<?php get_template_part('template-parts/comment-card/index'); ?>

						<?php endwhile; ?>

					</div>

				</div>

			</div>

		<?php endif; ?>

	</div>

<?php endif; ?>
