.home-comments {
  position: relative;
  padding: $spacing-08-40 0;
  margin-bottom: $spacing-08-40;
  background-color: $color-surface-secondary;

  .category-header {
    margin-bottom: $spacing-06-24;
    z-index: 1;
    position: relative;

    &__title {
      text-transform: none;
    }
  }

  &__bg {
    position: absolute;
    top: 0;
    left: calc((100% - 100vw) / 2);
    height: 100%;
    width: 100vw;
    background-color: $color-surface-secondary;
    z-index: -1;
  }

  &__grid {
    z-index: 1;
    overflow: hidden;
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin-bottom: $spacing-07-32;
    padding: $spacing-07-32 $spacing-00;
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    padding: $spacing-06-24 $spacing-00;

    &__grid {
      overflow: visible;
      padding-bottom: $spacing-07-32;
    }
  }
}
