document.addEventListener('DOMContentLoaded', function () {
  jQuery(function ($) {
    if (!$('.home-events-carousel').length) return;

    const slider = $('#events-slider');

    const featuredPresent = $('.single-slider').length || $(slider).data('featured');

    const slidesToShow = featuredPresent ? 3 : 4;

    const initSlider = () => {
      const splide = new Splide('#events-slider', {
        type: 'slide',
        perPage: slidesToShow,
        perMove: 1,
        gap: '3rem',
        pagination: true,
        arrows: true,
        focus: 'number',
        omitEnd: true,
        speed: 300,
        autoWidth: !!featuredPresent,
        breakpoints: {
          992: {
            perPage: 2.5,
            arrows: false,
          },
          768: {
            perPage: 1.3,
            arrows: false,
            pagination: true,
          },
        },
      }).mount();

      window.splideSliders = window.splideSliders || [];
      window.splideSliders.push(splide);
    };

    const isAdmin = $('body').hasClass('wp-admin');

    if (isAdmin && wp) {
      const { select, subscribe } = wp.data;
      const closeListener = subscribe(() => {
        const isReady = select('core/editor').__unstableIsEditorReady();
        if (isReady) {
          closeListener();

          initSlider();
        }
      });
    } else {
      initSlider();
    }
  });
});
