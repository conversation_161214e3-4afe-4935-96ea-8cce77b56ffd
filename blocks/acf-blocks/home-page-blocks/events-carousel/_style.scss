.home-events-carousel {
  margin-bottom: $spacing-09-48;

  &__carousel {
    padding-bottom: $spacing-05-20;
  }

  &__event {
    text-decoration: none;
  }

  &__event-image-wrapper {
    height: 37rem;
    margin-bottom: $spacing-03-12;
    overflow: hidden;
  }

  &__event-image {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
  }

  &__event-title {
    margin-bottom: $spacing-01;
    color: $color-text-primary;
  }

  &__event-date {
    color: $color-text-secondary;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-11-64;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__carousel {
      width: 100vw;
      margin-left: calc((100% - 100vw) / 2);
      padding: $spacing-00 $spacing-04-16;
    }

    &__event-title {
      margin-bottom: $spacing-00;
    }
  }
}
