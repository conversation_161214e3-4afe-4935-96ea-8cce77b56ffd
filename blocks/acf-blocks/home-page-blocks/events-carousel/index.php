<?php

/**
 * The number of posts to be shown
 * @var int
 */
$number_of_posts = is_admin() ? 3 : get_field('number_of_posts_to_show') ?? 5;

/**
 * The ID of the selected featured event only on CZ
 * @var int
 */
$featured_event = get_field('featured_event') ?? null;

if (is_admin() && $featured_event) {
	$number_of_posts--;
}

$starting_date_field_name = 'eventStartDate';

$args = array(
	'posts_per_page'  => $number_of_posts,
	'post_type'		  => 'event',
	'post_status'	  => 'publish',
	'orderby' 		  => 'meta_value',
	'order'			  => 'ASC',
	'post__not_in'	  => $featured_event ? array($featured_event) : [],
	'meta_key'		  => $starting_date_field_name,
	'meta_query'	  => array(
		array(
			'key' => $starting_date_field_name,
			'value' => date('Ymd'),
			'type' => 'DATE',
			'compare' => '>='
		)
	)
);

$query = new WP_Query($args);

?>

<div class="home-events-carousel">

	<div class="home-events-carousel__header">
		<?php get_template_part('template-parts/home-page/category-header/index'); ?>
	</div>

	<div id="events-slider" class="home-events-carousel__carousel splide" data-featured="<?= !!$featured_event;?>">

		<div class="splide__track">

			<div class="splide__list">

				<?php if ($featured_event) : ?>

					<?php get_template_part('template-parts/event-card/index', null, ['featured' => $featured_event]) ?>

				<?php endif; ?>

				<?php if ($query->have_posts()) : ?>
					<?php while ($query->have_posts()) : $query->the_post(); ?>

						<?php get_template_part('template-parts/event-card/index') ?>

					<?php endwhile; ?>

					<?php wp_reset_query(); ?>
				<?php endif; ?>

			</div>

		</div>

	</div>

</div>

