<?php

/**
 * The URL for the specials and rankings page
 * @var string
 */
$specials_url = get_field('specials_page_link');

$args = array(
	'post_type' 	 => 'special',
	'post_status' 	 => 'publish',
	'posts_per_page' => 3,
	'orderby'		 => 'date',
	'order'			 => 'DESC',
);
?>

<div class="home-specials-rankings">

	<div class="home-specials-rankings__header">
		<?php get_template_part('template-parts/home-page/category-header/index', null, array('specials' => $specials_url)); ?>
	</div>

	<div class="row">

		<div class="col-12 col-lg-6">

			<div class="home-specials-rankings__left-wrapper">

				<?php $query = new WP_Query($args); ?>

				<?php if ($query->have_posts()) : ?>

					<?php while ($query->have_posts()) : $query->the_post(); ?>

						<a href="<?= 'cz' === COUNTRY ? get_field('redirect', get_the_ID()) : get_field('url', get_the_ID()); ?>" target="_blank" rel="noopener" class="home-specials-rankings__card home-specials-rankings__card--left">

							<div class="home-specials-rankings__card-wrapper">

								<?php
								/**
								 * The image of the card (vertical orientation)
								 */
								if ('cz' === COUNTRY) {
									$mobile_image = get_field('smallListingImage', get_the_ID()) ? wp_get_attachment_image(get_field('smallListingImage', get_the_ID()), 'medium', false, array('class' => 'home-specials-rankings__image', 'alt' => get_the_title())) : wp_get_attachment_image(get_field('mobile_image', get_the_ID()), 'medium', false, array('class' => 'home-specials-rankings__image', 'alt' => get_the_title())) ?? null;
								} else {
									$mobile_image = get_field('mobile_image', get_the_ID()) ? wp_get_attachment_image(get_field('mobile_image', get_the_ID()), 'medium', false, array('class' => 'home-specials-rankings__image', 'alt' => get_the_title())) : null;
								}
								?>

								<?php if ($mobile_image) : ?>

									<div class="home-specials-rankings__image-wrapper">

										<?= $mobile_image; ?>

									</div>

								<?php endif; ?>

								<div class="home-specials-rankings__content-wrapper">

									<h4 class="home-specials-rankings__card-title"><?php the_title(); ?></h4>

									<p class="home-specials-rankings__card-description callout"><?= strip_tags(get_the_excerpt()); ?></p>

								</div>

							</div>

						</a>

					<?php endwhile; ?>

					<?php wp_reset_query(); ?>

				<?php endif; ?>

			</div>

		</div>

		<div class="col-12 col-lg-6">

			<div class="home-specials-rankings__right-wrapper">

				<?php
				$args['offset'] = 3;
				$query = new WP_Query($args);
				?>

				<?php if ($query->have_posts()) : ?>

					<?php while ($query->have_posts()) : $query->the_post(); ?>

						<?php
						$url = 'cz' === COUNTRY ? get_field('redirect', get_the_ID()) : get_field('url', get_the_ID());
						if ('cz' === COUNTRY) {
							$image = get_field('bigListingImage', get_the_ID()) ? wp_get_attachment_image(get_field('bigListingImage', get_the_ID()), 'medium', false, ['class' => 'home-specials-rankings__image']) : get_the_post_thumbnail($post, 'medium', ['class' => 'home-specials-rankings__image']) ?? null;
						} else {
							$image = has_post_thumbnail(get_the_ID()) ? get_the_post_thumbnail(get_the_ID(), 'medium', ['class' => 'home-specials-rankings__image']) : null;
						}
						?>

						<a href="<?= $url; ?>" target="_blank" rel="noopener" class="home-specials-rankings__card home-specials-rankings__card--right">

							<div class="home-specials-rankings__card-wrapper">

								<div class="home-specials-rankings__image-wrapper">

									<?= get_the_post_thumbnail(get_the_ID(), 'small', array('class' => 'home-specials-rankings__image', 'alt' => get_the_title())) ?>

								</div>

								<div class="home-specials-rankings__content-wrapper">

									<?php if (get_field('show_date')) : ?>

										<span class="home-specials-rankings__card-publish-date cta-link-tag"><?= 'hu' === COUNTRY ? get_the_date('Y. M d.', get_the_ID()) : get_the_date('j. M Y', get_the_ID()); ?></span>

									<?php endif; ?>

									<h4 class="home-specials-rankings__card-title callout"><?php the_title(); ?></h4>

								</div>

							</div>

						</a>

					<?php endwhile; ?>

					<?php wp_reset_query(); ?>

				<?php endif; ?>

			</div>

		</div>

	</div>

</div>
