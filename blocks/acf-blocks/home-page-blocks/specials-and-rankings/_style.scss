.home-specials-rankings {
  margin-bottom: $spacing-09-48;

  &__left-wrapper {
    background-color: $color-surface-secondary;
    padding: $spacing-06-24;
  }

  &__card-wrapper {
    display: flex;
    width: 100%;
  }

  &__card {
    display: flex;
    margin-bottom: $spacing-04-16;
    text-decoration: none;
    color: initial;

    &:last-child {
      margin-bottom: 0;
    }

    &--left {
      .home-specials-rankings {
        &__image-wrapper {
          flex: 0 0 10rem;
          height: 13.8rem;
          margin-right: $spacing-04-16;
          overflow: hidden;
        }

        &__card-title,
        &__card-description {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          visibility: visible;
        }

        &__card-title {
          margin-bottom: $spacing-02 !important;
          -webkit-line-clamp: 2;
        }

        &__card-description {
          color: $color-text-secondary;
          -webkit-line-clamp: 3;
        }
      }
    }

    &--right {
      margin-bottom: $spacing-07-32;

      .home-specials-rankings {
        &__image-wrapper,
        &__content-wrapper {
          width: 25.5rem;
        }

        &__image-wrapper {
          margin-right: $spacing-04-16;
          min-height: 14.4rem;
          max-height: 14.4rem;
          overflow: hidden;
        }

        &__content-wrapper {
          margin-left: $spacing-04-16;
          display: flex;
          flex-direction: column;
        }

        &__card-title {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 5;
          overflow: hidden;
          visibility: visible;
        }

        &__card-publish-date {
          margin-bottom: $spacing-02;
          color: $color-text-secondary;
        }
      }
    }
  }

  &__image {
    height: 100%;
    width: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  &__card-title {
    color: $color-text-primary;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-07-32;

    &__left-wrapper {
      margin-bottom: $spacing-06-24;
    }

    &__right-wrapper {
      .home-specials-rankings {
        &__card-wrapper {
          flex-direction: column;
          width: 100%;
        }

        &__image-wrapper,
        &__content-wrapper {
          margin: $spacing-00;
        }

        &__image-wrapper {
          min-height: 0;
          max-height: 0;
          width: 100%;
          margin-bottom: $spacing-03-12;
          padding-top: 56.25%;
          overflow: hidden;

          @supports (aspect-ratio: 16/9) {
            aspect-ratio: 16/9;
            padding-top: $spacing-00;
            height: auto;
            max-height: none;
          }
        }

        &__content-wrapper {
          flex-direction: column-reverse;
        }

        &__card {
          margin-bottom: $spacing-06-24;
        }

        &__card-title {
          margin-bottom: $spacing-02;
        }

        &__card-publish-date {
          margin-bottom: $spacing-00;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__left-wrapper {
      .home-specials-rankings {
        &__image-wrapper {
          flex: 0 0 7.3rem;
          height: 10rem;
          margin-right: $spacing-06-24;
        }

        &__content-wrapper {
          display: block;
        }

        &__card-description {
          display: none;
        }

        &__card-title {
          -webkit-line-clamp: 3;
        }
      }
    }

    &__card {
      &--right {
        margin-bottom: $spacing-06-24;

        .home-specials-rankings__card-title {
          font-weight: 500;
          font-size: 1.6rem;
          line-height: 2.4rem;
          margin-bottom: $spacing-00;
        }

        .home-specials-rankings__card-publish-date {
          font-size: 1.6rem;
          line-height: 1.8rem;
          margin-bottom: $spacing-00;
          margin-top: $spacing-02;
          text-transform: none;
        }

        &:last-child {
          margin-bottom: $spacing-00;
        }
      }
    }
  }
}
