@import '../../../../assets/scss/base/mixins';

.home-daily-cover {
  &__featured-wrapper {
    @include flexbox-properties;
  }

  margin-bottom: $spacing-08-40;

  a {
    text-decoration: none !important;
  }

  .row {
    flex-direction: row-reverse;

    &:first-child {
      margin-bottom: $spacing-07-32;
    }
  }

  .col-lg-8 {
    @media (hover: hover) {
      &:hover {
        + .col-lg-4 .heading {
          text-decoration: underline !important;
        }
      }
      &:hover {
        + .col-lg-4 .home-daily-cover__excerpt {
          color: $color-text-primary !important;
        }
      }
    }
  }

  &__featured-hover-wrapper {
    @media (hover: hover) {
      &:hover {
        .heading {
          text-decoration: underline !important;
        }

        .home-daily-cover__excerpt {
          color: $color-text-primary !important;
        }
      }
    }
  }

  &__featured {
    width: 100%;
    height: 100%;
    position: relative;

    &--image {
      width: 100%;
    }

    .article-meta {
      margin: $spacing-04-16 $spacing-00 $spacing-00;
      border: none;
      padding: $spacing-02 $spacing-00;

      &__inner {
        max-width: 80%;
      }

      &__button {
        &--share,
        &--bookmark,
        &--premium {
          span {
            display: none !important;
          }
        }
      }
    }
  }

  &__image-wrapper {
    width: 100%;
    overflow: hidden;
    aspect-ratio: 16 / 9;
    position: relative;
    height: auto;

    @media (hover: hover) {
      .heading {
        &:hover {
          text-decoration: underline !important;
        }
      }
    }
  }

  &__image {
    object-fit: cover;
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    transition: all 0.3s ease-in-out;
  }

  &__details {
    margin-bottom: $spacing-01;
  }

  &__content {
    padding: $spacing-05-20 $spacing-06-24 $spacing-05-20 $spacing-00;
    width: 40.8rem;
    background-color: $color-surface-primary;
  }

  &__excerpt {
    margin-top: $spacing-04-16;
  }

  &__items {
    .items {
      &__grid {
        @include grid-columns(3);
        margin-bottom: $spacing-04-16;
        grid-gap: 3.2rem 3rem;

        .items__item {
          &:nth-child(2),
          &:nth-child(5) {
            padding-top: $spacing-06-24;
          }

          &:nth-child(3),
          &:nth-child(6) {
            padding-top: $spacing-09-48;
          }
        }
      }

      &__magazine-wrapper {
        max-height: 32rem;
        height: 100%;
        width: 100%;
        position: relative;
        padding: $spacing-04-16;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: hidden;
      }

      &__magazine-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 0;
        transition: all 0.3s ease;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-07-32;

    &__content {
      padding-left: $spacing-06-24;
      top: 0;
      transform: translateY(-10%);
      width: 100%;
      position: relative;
    }

    &__excerpt {
      margin-top: $spacing-04-16;
      -webkit-line-clamp: 7;
    }

    .row:first-child {
      margin-bottom: $spacing-00;
    }

    &__featured--image {
      margin-left: calc((100% - 100vw) / 2);
      width: 100vw;
      display: block;
    }

    &__category {
      padding-left: $spacing-03-12;

      &:before {
        width: 0.8rem;
      }
    }

    &__items {
      .items {
        &__grid {
          display: block;
        }

        &__item {
          margin-bottom: $spacing-06-24;
        }

        &__magazine-wrapper {
          height: 25rem;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__title {
      margin-bottom: $spacing-00;
    }

    &__image-wrapper {
      aspect-ratio: 16/9;
    }

    &__content {
      padding-bottom: $spacing-01;
    }

    &__items {
      .items {
        &__item {
          padding-top: $spacing-00 !important;

          &:last-child {
            margin-bottom: $spacing-00;
          }
        }
      }

      .article-card {
        &__image-wrapper {
          display: block;
          margin-bottom: $spacing-02;
        }
      }
    }

    .article-meta {
      &__button--share,
      &__button--bookmark span {
        display: none !important;
      }
    }

    &__featured {
      h4.heading {
        font-size: 2.6rem;
        line-height: 3.224rem;
      }
    }
  }
}

@media not all and (min-resolution: 0.001dpcm) {
  @supports (-webkit-appearance: none) {
    .home-daily-cover {
      &__featured--content {
        display: contents;
      }
    }
  }
}
