<?php

namespace DailyCover;

function set_cover_posts($page_id) {
    $featured = get_field('featured', $page_id);

    if (empty($featured)) return;

	/**
	 * The current time
	 * @var string
	 */
    $current_time = current_time('timestamp');

	/**
	 * An array of posts and their expiration times
	 * @var array
	 */
    $posts = [
        [
            'post' => $featured['featured_article'],
            'exp' => strtotime($featured['featured_article_expiration_time']),
            'pub' => strtotime($featured['featured_article_publish_time'])
        ],
        [
            'post' => $featured['next_scheduled_article'],
            'exp' => strtotime($featured['next_scheduled_article_expiration_time']),
            'pub' => strtotime($featured['next_scheduled_article_publish_time'])
        ],
        [
            'post' => $featured['later_scheduled_article'],
            'exp' => strtotime($featured['later_scheduled_article_expiration_time']),
            'pub' => strtotime($featured['later_scheduled_article_publish_time'])
        ],
        [
            'post' => $featured['fourth_article'],
            'exp' => strtotime($featured['fourth_article_expiration_time']),
            'pub' => strtotime($featured['fourth_article_publish_time'])
        ],
        [
            'post' => $featured['fifth_article'],
            'exp' => strtotime($featured['fifth_article_expiration_time']),
            'pub' => strtotime($featured['fifth_article_publish_time'])
        ],
        [
            'post' => $featured['last_article'],
            'exp' => strtotime($featured['last_article_expiration_time']),
            'pub' => strtotime($featured['last_article_publish_time'])
        ],
    ];

	/**
	 * Set the archived featured posts
	 */
    foreach ($posts as $index => $post_data) {
        if ($post_data['post'] && $post_data['exp']) {
			/**
			 * Check if the post has expired
			 */
            if ($post_data['exp'] <= $current_time) {
                setArchivedFeaturedPosts($page_id, $post_data['post']);

				/**
				 * Get the next post
				 */
                $next_post_data = $posts[$index + 1] ?? null;

				/**
				 * Update the fields
				 */
                update_field('featured', [
                    'featured_article' => $next_post_data['post'] ?? '',
                    'featured_article_publish_time' => $next_post_data['pub'] ?? '',
                    'featured_article_expiration_time' => $next_post_data['exp'] ?? '',
                    'next_scheduled_article' => $posts[$index + 2]['post'] ?? '',
                    'next_scheduled_article_publish_time' => $posts[$index + 2]['pub'] ?? '',
                    'next_scheduled_article_expiration_time' => $posts[$index + 2]['exp'] ?? '',
                    'later_scheduled_article' => $posts[$index + 3]['post'] ?? '',
                    'later_scheduled_article_publish_time' => $posts[$index + 3]['pub'] ?? '',
                    'later_scheduled_article_expiration_time' => $posts[$index + 3]['exp'] ?? '',
                    'fourth_article' => $posts[$index + 4]['post'] ?? '',
                    'fourth_article_publish_time' => $posts[$index + 4]['pub'] ?? '',
                    'fourth_article_expiration_time' => $posts[$index + 4]['exp'] ?? '',
                    'fifth_article' => $posts[$index + 5]['post'] ?? '',
                    'fifth_article_publish_time' => $posts[$index + 5]['pub'] ?? '',
                    'fifth_article_expiration_time' => $posts[$index + 5]['exp'] ?? '',
                    'last_article' => '',
                    'last_article_publish_time' => '',
                    'last_article_expiration_time' => '',
                ], $page_id);
            }
        }
    }
}

/**
 * Set the archived featured posts
 */
function setArchivedFeaturedPosts($page_id, $post_id) {
    for ($i = 6; $i > 0; $i--) {
        $id = $i === 1 ? $post_id : get_field('archive_featured', $page_id)[$i - 2]['post_id'];
        update_row('archive_featured', $i, array('post_id' => intval($id)), $page_id);
    }
}
?>
