<?php

/**
 * The ID of the home page
 * @var int
 */
global $post;
$page_id = $post->ID;

if (is_admin()) {
	$page_id = $post_id;
}

DailyCover\set_cover_posts($page_id);

$featured = get_field('featured', $page_id);

/**
 * The post ID of the selected post for the block
 * @var int
 */
$featured_post = !empty($featured) ? $featured['featured_article'] : null;

/**
 * The publish time of the selected post for the block
 * @var int
 */
$featured_post_publish_time = !empty($featured) ? strtotime($featured['featured_article_publish_time']) : null;

/**
 * Whether the post can already be shown
 * @var boolean
 */
$featured_published = $featured_post_publish_time ? $featured_post_publish_time <= current_time('timestamp') : false;

/**
 * Get print category ID
 * @var int
 */
$print_category = get_field('print_category', 'option') ?? null;

/**
 * The array of the 3 items shown in the bottom of the block
 * @var array
 */
$items = get_field('items');
$overwrite_ids = [];
/**
 * Checks if all 3 items have been enabled for overwrite, and all data present for each.
 * This is necessary only for the first period, when there is not enough archived posts saved yet
 * @var array
 */
if (is_array($items) && !empty($items)) {

	$items = array_filter($items, function ($item) {
		if ($item['overwrite_flow'] && (($item['image'] && $item['url']) || $item['article'])) {
			return true;
		}

		return false;
	});

	$overwrite_ids = array_column($items, 'article');
}

$args = array(
	'posts_per_page' => COUNTRY === 'cz' ? 5 : 7,
	'post_type'      => 'post',
	'post_status'    => 'publish',
	'orderby'        => 'publish_date',
	'order'          => 'DESC',
	'post__not_in'   => [$featured_post, ...$overwrite_ids],
	'tax_query'      => array(
		array(
			'taxonomy' 	=> CATEGORY_TYPE,
			'terms'     => defined('CONTENT_TYPE') && CONTENT_TYPE === "life" ?
								array('brand-voice', 'advoice', 'premium', 'brandvoice', 'tamogatoi-tartalom', 'prestige', 'brandlab', 'byznys', 'cesky-byznys') :
								array('brand-voice', 'advoice', 'premium', 'brandvoice', 'tamogatoi-tartalom', 'prestige', 'brandlab', 'forbes-life'), /* TODO: LIFE */
			'field'		=> 'slug',
			'operator' 	=> 'NOT IN',
		),
		array(
            'taxonomy' => CATEGORY_TYPE,
            'terms'    => $print_category,
            'field'    => 'term_id',
            'operator' => 'NOT IN',
        ),
	)
);

$premium_args = array(
	'posts_per_page' => 5,
	'post_type'      => 'post',
	'post_status'    => 'publish',
	'orderby'        => 'publish_date',
	'order'          => 'DESC',
	'post__not_in'   => [$featured_post, ...$overwrite_ids],
	'tax_query'      => array(
		array(
			'taxonomy' 	=> CATEGORY_TYPE,
			'terms' 	=> array('premium'),
			'field' 	=> 'slug',
			'operator' 	=> 'IN',
		),
		array(
            'taxonomy' => CATEGORY_TYPE,
            'terms'    => $print_category,
            'field'    => 'term_id',
            'operator' => 'NOT IN',
        ),
	),
);

$query = new WP_Query($args);
$premium_query = new WP_Query($premium_args);

shuffle($premium_query->posts);


/**
 * Expired former featured articles
 * @var array(int)
 */
$archived_posts = [];
$count = 0;
if (COUNTRY === 'cz') {

	if ($query->have_posts()) :

		while ($query->have_posts()) : $query->the_post();

			if (!$featured_post) {
				$featured_post = get_the_ID();

				$featured_post_publish_time = get_post_datetime();

				$featured_published = true;
				$count--;
			} else {
				if ($count < 2) {
					array_push($archived_posts, ['post_id' => get_the_ID()]);
				} else {
					$archived_posts[$count + 1] = ['post_id' => get_the_ID()];
				}
			}

			$count++;

		endwhile;
	endif;

	wp_reset_query();

	if ($premium_query->have_posts()) :
		while ($premium_query->have_posts()) : $premium_query->the_post();

			if ($premium_query->current_post == 0) {
				$archived_posts[2] = ['post_id' => get_the_ID()];
			} else {
				$archived_posts[5] = ['post_id' => get_the_ID()];
			}

		endwhile;
	endif;

	wp_reset_query();
} else {
	if ($query->have_posts()) :

		while ($query->have_posts()) : $query->the_post();

			if (!$featured_post) {
				$featured_post = get_the_ID();

				$featured_post_publish_time = get_post_datetime();

				$featured_published = true;
			} else {
				array_push($archived_posts, ['post_id' => get_the_ID()]);
			}
		endwhile;
	endif;
}

/**
 * Whether we have 3 posts in total to show in the bottom row
 * @var boolean
 */
$archived_count = $archived_posts ? count($archived_posts) : 0;
$manual_set_post_count = is_array($items) ? count($items) : 0;
$bottom_row_valid = $archived_count + $manual_set_post_count >= 6;

$used_ids = [];

/**
 * The ID of the Daily Cover category
 * @var int
 */
$daily_cover_category_id = get_field('daily_cover_category', 'option');

if (!$daily_cover_category_id && is_plugin_active( 'wodpress-seo/wp-seo.php' )) {
	$daily_cover_category_id = yoast_get_primary_term_id(CATEGORY_TYPE, $featured_post);
}
?>


<div class="home-daily-cover">

	<?php if ($featured_post && $featured_published) :
		$contains_remp_lock_block = has_block('remp-paywall/lock', $featured_post);
		?>

		<?php pageHome\Exclusions::getInstance()->setExclusions(array($featured_post)); ?>

		<div class="row home-daily-cover__featured-wrapper">

			<div class="col-12 col-lg-8">

				<a class="home-daily-cover__featured home-daily-cover__featured--image" href="<?= get_field('redirect_url', $featured_post) ?: get_permalink($featured_post); ?>">

					<div class="home-daily-cover__image-wrapper">

						<?php if ('cz' === COUNTRY && !has_post_thumbnail($featured_post)) : ?>

							<?= get_field('primaryImage', $featured_post) ? wp_get_attachment_image(
								get_field('primaryImage', $featured_post),
								'daily_cover_desktop',
								false,
								array(
									'class'         => 'home-daily-cover__image',
									'alt'           => get_the_title($featured_post),
									'fetchpriority' => 'high',
									'srcset' => wp_get_attachment_image_url(
											get_field( 'primaryImage', $featured_post ),
											'daily_cover_desktop_2x'
												) . ' 2x'
								)
							) : ''; ?>

						<?php else : ?>

							<?= get_the_post_thumbnail(
								$featured_post,
								'daily_cover_desktop',
								array(
									'class'         => 'home-daily-cover__image',
									'alt'           => get_the_title($featured_post),
									'fetchpriority' => 'high',
									'srcset' => get_the_post_thumbnail_url( $featured_post,
											'daily_cover_desktop2x' ) . ' 2x'

								)
							) ?>

						<?php endif; ?>

					</div>

				</a>

			</div>

			<div class="col-12 col-lg-4">

				<div class="home-daily-cover__featured home-daily-cover__featured--content">

					<div class="home-daily-cover__content">

						<div class="home-daily-cover__details">

							<?php
							$featured_main_tag = frontend_get_primary_tag($featured_post);
							$featured_term_link = $featured_main_tag ? get_term_link($featured_main_tag) : '#';

							/**
							 * The author's name and link of the article
							 * @var string
							 */
							$author = get_the_main_author($featured_post) ?? null;
							$author_name = get_the_main_author($featured_post)->name ?? null;
							$author_link = get_the_main_author($featured_post)->archive_link ?? null;

							/**
							 * Get the badge toggle for the specified category term
							 * @var array|false
							 */
							$badge_toggle = get_field('badge', CATEGORY_TYPE . '_' . $featured_main_tag->term_id) ?? false;

							/**
							 * Get the font color from the badge toggle
							 * @var string
							 */
							$font_color = '';
							if (is_array($badge_toggle) && isset($badge_toggle['color'])) {
								$font_color = $badge_toggle['color'];
							}

							/**
							 * Get the URL of the badge icon from the badge toggle, or null
							 * @var string|null
							 */
							$label_icon = null;
							if (is_array($badge_toggle) && isset($badge_toggle['icon'])) {
								$label_icon = wp_get_attachment_url($badge_toggle['icon'] ?? null);
							}
							if ($contains_remp_lock_block) {
								$label_icon = get_template_directory_uri() . '/assets/icons/ds2024/icon-diamond-empty.svg';
							}

							/**
							 * Get the label from the badge toggle, or the post category
							 * @var string
							 */
							$label = $featured_main_tag->name;
							if (is_array($badge_toggle) && isset($badge_toggle['label'])) {
								$label = $badge_toggle['label'];
							}
							?>

							<?php
								$dailyCoverCategoryLink = new Tag(
									text: $label,
									icon: $label_icon,
									url: $featured_term_link,
								);
								echo $dailyCoverCategoryLink->render();
							?>

						</div>

						<div class="home-daily-cover__featured-hover-wrapper">
							<a href="<?= get_field('redirect_url', $featured_post) ?: get_permalink($featured_post); ?>" class="home-daily-cover__featured-title-wrapper">
								<h4 class="h4-noto heading"><?= get_the_title($featured_post); ?></h4>
							</a>
							<a href="<?= get_field('redirect_url', $featured_post) ?: get_permalink($featured_post); ?>" class="home-daily-cover__featured-excerpt-wrapper">

								<p class="home-daily-cover__excerpt article-excerpt article-excerpt--large"><?= strip_tags(get_post_excerpt($featured_post)); ?></p>
							</a>
						</div>

						<div class="article-meta__wrapper">
							<?php
								$authors = get_all_the_authors($featured_post);
								$articleData = get_article_data($featured_post);

							?>

							<div class="reactArticleMeta" data-authors="<?php echo htmlspecialchars(json_encode($authors), ENT_QUOTES, 'UTF-8'); ?>" data-article="<?php echo htmlspecialchars(json_encode($articleData), ENT_QUOTES, 'UTF-8'); ?>"></div>
						</div>

					</div>

				</div>


			</div>

		</div>

	<?php endif; ?>

	<?php if ($bottom_row_valid) : ?>

		<div class="row">

			<div class="col-12">

				<div class="home-daily-cover__items">

					<div class="items__grid">

						<?php for ($i = 0; $i < 6; $i++) : ?>

							<?php if (isset($items[$i]) && $items[$i]['overwrite_flow']) : ?>

								<div class="items__item">

									<?php if ($i == 2 && 'hu' === COUNTRY) { ?>

										<?php get_template_part('template-parts/leaderboard-ad/index', null, array('page' => 'home', 'mobile' => true)) ?>

									<?php } ?>


									<?php if ('article' === $items[$i]['type']) : ?>

										<?php
										array_push($used_ids, $items[$i]['article']);
										pageHome\Exclusions::getInstance()->setExclusions(array($items[$i]['article']));
										get_template_part('template-parts/article-card/index', null, array('post_id' => $items[$i]['article'], 'article_card_type' => 'normal'));
										?>

									<?php else : ?>

										<div class="items__magazine-wrapper">

											<?php if ($items[$i]['image'] && $items[$i]['url']) : ?>

												<a href="<?= $items[$i]['url']; ?>" rel="noopener">
													<?= wp_get_attachment_image($items[$i]['image'], 'medium', false, array('class' => 'items__magazine-image', 'alt' => isset($items[$i]['magazine']) ? get_the_title($items[$i]['magazine']) : esc_html__('Magazine', 'FORBES'))); ?>
												</a>

											<?php endif; ?>

										</div>

									<?php endif; ?>

								</div>

							<?php else : ?>

								<?php if ($archived_posts[$i] && $archived_posts[$i]['post_id']) : ?>

									<div class="items__item">

										<?php if ($i == 2 && 'hu' === COUNTRY) { ?>

											<?php get_template_part('template-parts/leaderboard-ad/index', null, array('page' => 'home', 'mobile' => true)) ?>

										<?php } ?>

										<?php pageHome\Exclusions::getInstance()->setExclusions(array($archived_posts[$i]['post_id'])); ?>

										<?php get_template_part('template-parts/article-card/index', null, array('post_id' => $archived_posts[$i]['post_id'], 'article_card_type' => 'normal')); ?>

									</div>

									<?php array_push($used_ids, $archived_posts[$i]['post_id']); ?>

								<?php endif; ?>

							<?php endif; ?>

						<?php
						endfor;
						$GLOBALS["used_ids"] = $used_ids;

						?>

					</div>

				</div>

			</div>

		</div>

	<?php endif; ?>

</div>
