<?php
add_action('acf/init', 'forbes_init_home_page_block_types');
function forbes_init_home_page_block_types() {

	// Check function exists.
	if (function_exists('acf_register_block_type')) {

		/*
			ACF BLOCKS FOR THE HOME PAGE
		*/

		// register the BREAKING NEWS block
		acf_register_block_type(array(
			'name'              => 'home-breaking-news',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Breaking News Slider', 'FORBES'),
			'description'       => esc_html__('Carousel with the breaking news for the HOME PAGE', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/breaking-news/index.php',
			'enqueue_assets'	=> function () {
				if (!wp_script_is('breaking-news-slider', 'enqueued')) {
					wp_enqueue_script('breaking-news-slider', get_template_directory_uri() . '/minified-js/breaking-news-slider/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/breaking-news-slider/scripts.min.js'), true);
				}
			},
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'supports'			=> array(
				'mode'			=> false
			),
			'keywords'          => array(esc_html__('breaking', 'FORBES'), esc_html__('news', 'FORBES')),
			'align'				=> 'center',
		));

		// register the DAILY COVER block
		acf_register_block_type(array(
			'name'              => 'home-daily-cover',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Daily Cover', 'FORBES'),
			'description'       => esc_html__('One selected article, with large featured image on the left', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/daily-cover/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('daily', 'FORBES'), esc_html__('cover', 'FORBES')),
			'align'				=> 'center',
		));

		// register the FEATURED SPECIALS block
		acf_register_block_type(array(
			'name'              => 'home-featured-specials',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Featured Specials', 'FORBES'),
			'description'       => esc_html__('Two featured special article horizontally placed, the first is twice the size', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/featured-specials/index.php',
			'enqueue_assets'	=> function () {
				if (!wp_script_is('featured-special-script', 'enqueued')) {
					wp_enqueue_script('featured-special-script', get_template_directory_uri() . '/minified-js/home-page/two-column-specials/featured-special-script.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/home-page/two-column-specials/featured-special-script.min.js'), true);
				}
			},
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('featured', 'FORBES'), esc_html__('special', 'FORBES'), esc_html__('specials', 'FORBES')),
			'align'				=> 'center',
		));

		// register the MOST RECENT ARTICLES block
		acf_register_block_type(array(
			'name'              => 'home-most-recent-articles',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Most Recent Articles', 'FORBES'),
			'description'       => esc_html__('the most recent article cards in a row', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/most-recent-articles/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('recent', 'FORBES'), esc_html__('articles', 'FORBES'), esc_html__('article', 'FORBES')),
			'align'				=> 'center',
		));

		// register the FEATURED BRANDVOICE ARTICLES block
		acf_register_block_type(array(
			'name'              => 'home-featured-brandvoice-articles',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Featured Brandvoice Articles', 'FORBES'),
			'description'       => esc_html__('2 or 3 brandvoice articles in a row', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/featured-brandvoice-articles/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('featured', 'FORBES'), esc_html__('brandvoice', 'FORBES'), esc_html__('articles', 'FORBES')),
			'align'				=> 'center',
		));

		// register the EVENTS CAROUSEL block
		acf_register_block_type(array(
			'name'              => 'home-events-carousel',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Events Carousel', 'FORBES'),
			'description'       => esc_html__('A carousel with a selected number of the upcoming events', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/events-carousel/index.php',
			'enqueue_assets'	=> function () {
				if (!wp_script_is('event-carousel-script', 'enqueued')) {
					wp_enqueue_script('event-carousel-script', get_template_directory_uri() . '/minified-js/home-page-blocks/events-carousel/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/home-page-blocks/events-carousel/scripts.min.js'), true);
				}
			},
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'supports'			=> array(
				'mode'			=> false
			),
			'keywords'          => array(esc_html__('carousel', 'FORBES'), esc_html__('events', 'FORBES')),
			'align'				=> 'center',
		));

		// register the PODCASTS block
		acf_register_block_type(array(
			'name'              => 'home-podcasts',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Podcasts', 'FORBES'),
			'description'       => esc_html__('Slider with the most recent podcasts', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/podcasts/index.php',
			'enqueue_assets'	=> function () {
				if (!wp_script_is('home-podcasts-script', 'enqueued')) {
					wp_enqueue_script('home-podcasts-script', get_template_directory_uri() . '/minified-js/home-page-blocks/podcasts/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/home-page-blocks/podcasts/scripts.min.js'), true);
				}
			},
			'category'          => 'common',
			'icon'              => 'media-audio',
			'mode'				=> 'preview',
			'supports'			=> array(
				'mode'			=> false
			),
			'keywords'          => array(esc_html__('podcast', 'FORBES'), esc_html__('podcasts', 'FORBES')),
			'align'				=> 'center',
		));

		// register the SPECIALS & RANKINGS block
		acf_register_block_type(array(
			'name'              => 'home-specials-and-rankings',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Specials and Rankings', 'FORBES'),
			'description'       => esc_html__('Block with the featured specials', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/specials-and-rankings/index.php',
			'category'          => 'common',
			'icon'              => 'text',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('special', 'FORBES'), esc_html__('ranking', 'FORBES')),
			'align'				=> 'center',
		));

		// register the SPECIALS & RANKINGS TYPE B block
		acf_register_block_type(array(
			'name'              => 'home-specials-and-rankings-type-b',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Specials and Rankings Type B', 'FORBES'),
			'description'       => esc_html__('Block with the featured specials, 3 cards in a row', 'FORBES'),
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/home-page-blocks/specials-and-rankings-type-b/index.php',
			'enqueue_assets'	=> function () {
				if (!wp_script_is('home-specials-script', 'enqueued')) {
					wp_enqueue_script('home-specials-script', get_template_directory_uri() . '/minified-js/home-page-blocks/specials-and-rankings-type-b/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/home-page-blocks/specials-and-rankings-type-b/scripts.min.js'), true);
				}
			},
			'category'          => 'common',
			'icon'              => 'text',
			'mode'				=> 'preview',
			'supports'			=> array(
				'mode'			=> false
			),
			'keywords'          => array(esc_html__('special', 'FORBES'), esc_html__('ranking', 'FORBES'), esc_html__('type-b', 'FORBES')),
			'align'				=> 'center',
		));

		// register the COMMENTS block
		acf_register_block_type(array(
			'name'              => 'home-comments',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Comments', 'FORBES'),
			'description'       => esc_html__('Block with a comment post types', 'FORBES'),
			'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/home-page-blocks/comments/index.php',
			'enqueue_assets'	=> function () {
				if (!wp_script_is('home-comments-script', 'enqueued')) {
					wp_enqueue_script('home-comments-script', get_template_directory_uri() . '/minified-js/home-page-blocks/comments/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/home-page-blocks/comments/scripts.min.js'), true);
				}
			},
			'category'          => 'common',
			'icon'              => 'admin-comments',
			'mode'				=> 'preview',
			'supports'			=> array(
				'mode'			=> false
			),
			'keywords'          => array(esc_html__('comments', 'FORBES')),
			'align'				=> 'center',
		));

		if ('cz' === COUNTRY) {

			// register the JOBS block
			acf_register_block_type(array(
				'name'              => 'home-jobs',
				'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Jobs', 'FORBES'),
				'description'       => esc_html__('Block with a job post types', 'FORBES'),
				'render_template'   => get_stylesheet_directory() . '/blocks/acf-blocks/CZ/home-page-blocks/jobs/index.php',
				'category'          => 'common',
				'icon'              => 'hammer',
				'mode'				=> 'preview',
				'keywords'          => array(esc_html__('jobs', 'FORBES')),
				'align'				=> 'center',
			));
		}
	}
}

add_action('acf/init', 'forbes_init_home_page_category_block_types');
function forbes_init_home_page_category_block_types() {

	// Check function exists.
	if (function_exists('acf_register_block_type')) {

		// register the CATEGORY TYPE A block
		acf_register_block_type(array(
			'name'              => 'home-type-a-category',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Category Type A', 'FORBES'),
			'description'       => esc_html__('First section: Featured article on left, 3 rows of articles on right. Second section: 3 further articles. Third section(optional): 2 columns of special articles', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/category-type-a/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('type-a', 'FORBES'), esc_html__('category', 'FORBES')),
			'align'				=> 'center',
		));

		// register the CATEGORY TYPE B block
		acf_register_block_type(array(
			'name'              => 'home-type-b-category',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Category Type B', 'FORBES'),
			'description'       => esc_html__('Carousel of posts (3 shown at the same time), title and details on the image', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/category-type-b/index.php',
			'enqueue_assets'	=> function () {
				if (!wp_script_is('home-type-b-category-script', 'enqueued')) {
					wp_enqueue_script('home-type-b-category-script', get_template_directory_uri() . '/minified-js/home-page-blocks/category-type-b/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/home-page-blocks/category-type-b/scripts.min.js'), true);
				}
			},
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'supports'			=> array(
				'mode'			=> false
			),
			'keywords'          => array(esc_html__('type-b', 'FORBES'), esc_html__('category', 'FORBES')),
			'align'				=> 'center',
		));

		// register the CATEGORY TYPE C block
		acf_register_block_type(array(
			'name'              => 'home-type-c-category',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Category Type C', 'FORBES'),
			'description'       => esc_html__('Section with posts from a selected category with a custom color code. First Row: Large featured post. Second Row and Third Row: 3 posts in 3 columns', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/category-type-c/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('type-c', 'FORBES'), esc_html__('category', 'FORBES')),
			'align'				=> 'center',
		));

		// register the CATEGORY TYPE D block
		acf_register_block_type(array(
			'name'              => 'home-type-d-category',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Category Type D', 'FORBES'),
			'description'       => esc_html__('Featured article on left, 3 rows of articles on right', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/category-type-d/index.php',
			'category'          => 'common',
			'icon'              => 'editor-ul',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('type-d', 'FORBES'), esc_html__('category', 'FORBES')),
			'align'				=> 'center',
		));

		// register the Blog Link block
		acf_register_block_type(array(
			'name'              => 'home-blog-link',
			'title'             => esc_html__('Home Page', 'FORBES') . ': ' . esc_html__('Blog Page Link', 'FORBES'),
			'description'       => esc_html__('A button to lead to the blog page', 'FORBES'),
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/home-page-blocks/blog-link/index.php',
			'category'          => 'common',
			'icon'              => 'admin-post',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('blog', 'FORBES'), esc_html__('link', 'FORBES')),
			'align'				=> 'center',
		));
	}
};
