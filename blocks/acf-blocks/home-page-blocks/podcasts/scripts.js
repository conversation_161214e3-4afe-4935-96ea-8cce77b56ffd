document.addEventListener('DOMContentLoaded', function () {
  jQuery(function ($) {
    if (!$('#podcasts-slider').length) return;

    const initSlider = () => {
      const splide = new Splide('#podcasts-slider', {
        type: 'slide',
        perPage: 3,
        perMove: 1,
        gap: '3rem',
        pagination: false,
        arrows: true,
        focus: 'number',
        omitEnd: true,
        speed: 300,
        breakpoints: {
          768: {
            perPage: 2.2,
          },
          576: {
            perPage: 1.3,
            arrows: false,
            pagination: true,
          },
        },
        classes: {
          arrows: 'splide__arrows splide__arrows--gray',
        },
      }).mount();

      window.splideSliders = window.splideSliders || [];
      window.splideSliders.push(splide);
    };

    const isAdmin = $('body').hasClass('wp-admin');

    if (isAdmin && wp) {
      const { select, subscribe } = wp.data;
      const closeListener = subscribe(() => {
        const isReady = select('core/editor').__unstableIsEditorReady();
        if (isReady) {
          closeListener();

          initSlider();
        }
      });
    } else {
      initSlider();
    }
  });
});
