@import '../../../../assets/scss/base/mixins';

.home-podcasts {
  background-color: $color-surface-secondary;
  padding: $spacing-08-40 $spacing-00;
  position: relative;
  margin-bottom: $spacing-08-40;

  &__bg {
    position: absolute;
    top: 0;
    left: calc((100% - 100vw) / 2);
    height: 100%;
    width: 100vw;
    background-color: $color-surface-secondary;
    z-index: -1;
  }

  &__slider {
    padding-top: $spacing-00;
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    margin-bottom: $spacing-07-32;
    padding: $spacing-07-32 $spacing-00;
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    padding: $spacing-06-24 $spacing-00;

    &__slider {
      margin: -1.6rem;
      padding: $spacing-04-16 $spacing-04-16 0;
    }
  }
}
