<?php

/**
 * The period to show podcasts from
 * @var string
 */
$period = get_field('period') ?? '';

/**
 * The number of posts to show
 * @var int
 */
$number_of_posts = get_field('number_of_posts_to_show') ?? 7;

/**
 * The array of the manually selected items to overwrite the default query results
 * @var array
 */
$items = get_field('items');

$podcast_tags = defined('CONTENT_TYPE') && CONTENT_TYPE === 'life'
    ? (get_field('life_podcast_tags', 'option') ?? array())
    : (get_field('podcast_tags', 'option') ?? array());

$args = array(
    'posts_per_page' => $number_of_posts,
    'post_type'      => 'post',
    'post_status'    => 'publish',
    'date_query'     => array(
        array(
            'after' => $period,
        )
    ),
    'fields'         => 'ids',
    'tax_query'      => array(
        array(
            'taxonomy' => CATEGORY_TYPE,
            'field'    => 'term_id',
            'terms'    => $podcast_tags,
        )
    )
);
unset($args['meta_query']);

$query = new WP_Query($args);
$podcasts = $query->posts;

?>

<?php if (!empty($podcasts)) : ?>

	<div class="home-podcasts">

		<div class="home-podcasts__bg"></div>

		<?php get_template_part('template-parts/home-page/category-header/index'); ?>

		<div id="podcasts-slider" class="home-podcasts__slider splide" <?= 'cz' === COUNTRY ? 'data-cz="true"' : ''; ?>>

			<div class="splide__track">

				<div class="splide__list">

					<?php for ($i = 0; $i < count($podcasts); $i++) : ?>

						<?php
						$id = $podcasts[$i];
						if (isset($items[$i]) && $items[$i]['overwrite_flow']) {
							$id = $items[$i]['podcast'];
						}
						?>

						<?php if ($id) : ?>

							<div class="home-podcasts__podcast splide__slide">
								<?php
								if (gettype($id) == 'object') {
									$id = $id->ID;
								}
								?>

								<?php pageHome\Exclusions::getInstance()->setExclusions(array($id)); ?>

								<?php get_template_part('template-parts/podcast-card/index', null, array('id' => $id)); ?>

							</div>

						<?php endif; ?>

					<?php endfor; ?>

				</div>

			</div>

		</div>

	</div>

<?php endif; ?>
