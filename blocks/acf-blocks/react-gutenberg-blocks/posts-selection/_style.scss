.reactPostsSelection {
  padding-bottom: $spacing-08-40;

  .react-block {
    &__card-container {
      padding: $spacing-00;

      .react-block-article-card,
      .image-card {
        &:nth-child(3n + 2) {
          padding-top: $spacing-06-24;
        }

        &:nth-child(3n + 3) {
          padding-top: $spacing-09-48;
        }
      }
    }
  }

  .react-posts-selection-block {
    &__button-wrapper {
      @include flexbox-properties;
      margin: $spacing-08-40 $spacing-00;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg )) {
    .react-block {
      &__card-container {
        .react-block-article-card,
        .image-card {
          &:nth-child(even) {
            padding-top: $spacing-00;
          }

          &:nth-child(odd) {
            padding-top: $spacing-00;
          }
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    .react-posts-selection-block {
      &__button-wrapper {
        .button {
          width: 100%;
        }
      }
    }
  }
}
