<?php
/**
 * The ID of the print tag
 * @var string
 */
$print_category = get_field('print_category', 'option') ?? null;

/**
 * Get the text of the button
 * @var string
 */
$button_text = get_field('button_text') ?? '';

/**
 * Get the link of the button
 * @var string
 */
$button_link = get_field('button_link') ?? '';

/**
 * Determine if we should show only premium articles
 * @var bool
 */
$show_only_premium_articles = get_field('show_only_premium_articles') ?? true;

/**
 * Get the section data
 */
$section_data = get_header_section_data();

// Initialize an empty array for category block data
$manual_articles = array();
$items = get_field('manual_articles');

// Initialize or retrieve global used_ids
$used_ids = isset($GLOBALS["used_ids"]) ? $GLOBALS["used_ids"] : [];

$meta_query = array();
if ($show_only_premium_articles === true) {
    // Adding meta query to filter only articles with 'separate_for_premium_content' set to true
    $meta_query[] = array(
        'key'     => 'separate_for_premium_content',
        'value'   => '1',
        'compare' => '='
    );
}

$args = array(
    'posts_per_page'  => 6,
    'post_type'       => 'post',
    'post_status'     => 'publish',
    'orderby'         => 'date',
    'order'           => 'DESC',
    'post__not_in'    => $used_ids,
	'meta_query'     => $meta_query,
	'tax_query'       => array(
		array(
			'taxonomy'  => CATEGORY_TYPE,
			'field'     => 'term_id',
			'terms'     => $print_category,
			'operator'  => 'NOT IN',
		),
	),
);

$query = new WP_Query($args);

for ($i = 0; $i < count($query->posts); $i++) {
    $id = $query->posts[$i]->ID;

    // Check if manual override exists for this item
    if (!empty($items[$i])) {
        if (isset($items[$i]['overwrite_flow']) && $items[$i]['overwrite_flow'] === true) {
            $type = $items[$i]['type'];

            // If the type is 'article' and an article ID is provided, set $id to this new article ID
            if ($type === 'article' && isset($items[$i]['article'])) {
                $id = $items[$i]['article']; // Use the manual article ID
            } elseif ($type === 'image') {
                $imageData = array(
                    'type' => 'image',
                    'imageUrl' => $items[$i]['image'],
                    'link' => $items[$i]['url'] ?? '',
                );
                $manual_articles[] = $imageData;
                continue; // Skip the rest of the loop for this iteration
            }
        }
    }

    // Check if the post contains the block 'remp-paywall/lock'
    $contains_remp_lock_block = has_block('remp-paywall/lock', $id);

    // Retrieve article data for the post ID
    $articleData = get_article_data($id, $contains_remp_lock_block);

    if (!empty($articleData)) {
        $article = array(
            'type' => 'article',
            'id' => $id,
            'title' => $articleData['title'],
            'authorName' => get_the_main_author($id)->name,
            'authorLink' => get_the_main_author($id)->archive_link,
            'tagLabel' => isset($articleData['category']['label']) ? $articleData['category']['label'] : $articleData['category']->name,
            'categoryLink' => $articleData['category']['link'],
            'publishDate' => $articleData['publishDate'],
            'readTime' => $articleData['readTime'],
            'imageLink' => get_the_post_thumbnail_url($id, 'large'),
            'link' => $articleData['url'],
            'perex' => html_entity_decode(strip_tags(get_post_excerpt($id))),
            'tagIcon' => isset($articleData['category']['icon']) ? $articleData['category']['icon'] : null,
        );
        $manual_articles[] = $article;
        $used_ids[] = $id; // Add the ID to the list of used IDs to prevent reuse
    }
}

wp_reset_postdata();

// Store the updated list of used IDs in the global variable
$GLOBALS["used_ids"] = $used_ids;

$renderBlock = '<div class="reactPostsSelection" data-category-articles=\'' . htmlspecialchars_decode(json_encode($manual_articles)) . '\' data-section-data=\'' . htmlspecialchars
    (
        json_encode($section_data)
    ) . '\' data-button-text=\'' . $button_text . '\' data-button-link=\'' . $button_link . '\'></div>';

// Output the rendered block
echo $renderBlock;
?>
