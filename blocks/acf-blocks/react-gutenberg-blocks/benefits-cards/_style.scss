.react-benefits-cards-block {
  padding-bottom: $spacing-08-40;

  &__wrapper {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 3rem;
  }

  .benefits-card {
    &__image-wrapper {
      position: relative;
      aspect-ratio: 10/12;
      margin-bottom: $spacing-04-16;
      overflow: hidden;
    }

    &__image {
      position: absolute;
      inset: 0;
      height: 100%;
      width: 100%;
      object-fit: cover;
      transition: all 0.3s ease;
    }

    &__description {
      * {
        color: $color-text-secondary;
        font-weight: 600;
        font-family: $font-archivo;
        line-height: 140%;
      }
    }
  }

  &__button-wrapper {
    @include flexbox-properties;
    margin: $spacing-08-40 $spacing-00;
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    &__wrapper {
      display: flex;
      flex-wrap: nowrap;
      width: 100%;
      overflow-x: scroll;
      width: calc(100% + 3rem);
      margin-left: -1.5rem;
      margin-right: -1.5rem;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    .benefits-card {
      width: 25.5rem;
      flex-shrink: 0;

      &:first-child {
        padding-left: 1.5rem;
      }

      &:last-child {
        padding-right: 1.5rem;
      }

      &__image-wrapper {
        aspect-ratio: none;
        width: 25rem;
        height: 30rem;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md)) {
    &__button-wrapper {
      .button {
        width: 100%;
      }
    }
  }
}
