<?php
/**
 * Get the background color of the header
 * @var string
 */
$background_color = get_field('header_background_color');

/**
 * Get the tag label
 * @var string
 */
$tag_label = get_field('tag_label') ?? '';

/**
 * Get the vector of the tag
 * @var int|null
 */
$tag_vector = get_field('tag_vector') ?? null;

/**
 * Get the title of the header
 * @var string
 */
$header_title = get_field('header_title') ?? '';

/**
 * Get the quote/description of the header
 * @var string
 */
$quote = get_field('quote') ?? '';

/**
 * Get the author of the quote
 * @var string
 */
$quote_author = get_field('quote_author') ?? '';

/**
 * Get the image of the author
 * @var int|null
 */
$author_image = get_field('quote_author_image') ?? null;

/**
 * Get the main image of the header
 * @var int|null
 */
$header_image = get_field('header_main_image') ?? null;

/**
 * Get the Scroll Down Button value
 * @var boolean
 */
$scroll_button = get_field('scroll_button') ?? false;

/**
 * Get the buttons/link texts and urls
 */
$primary_button_text = get_field('primary_button_text') ?? '';
$primary_button_url = get_field('primary_button_link') ?? '';

$secondary_button_text = get_field('secondary_button_text') ?? '';
$secondary_button_url = get_field('secondary_button_link') ?? '';

$simple_link_text = get_field('simple_link_text') ?? '';
$simple_link_url = get_field('simple_link_url') ?? '';

?>

<script>
	const backgroundColor = "<?php echo esc_js($background_color); ?>";
    const tagLabel = "<?php echo esc_js($tag_label); ?>";
    const tagVector = <?php echo json_encode(wp_get_attachment_image_src($tag_vector, 'full')); ?>;
    const headerTitle = "<?php echo esc_js($header_title); ?>";
    const quote = <?php echo json_encode($quote); ?>;
    const quoteAuthor = "<?php echo esc_js($quote_author); ?>";
    const authorImage = <?php echo json_encode(wp_get_attachment_image_src($author_image, 'thumbnail')); ?>;
    const headerImage = <?php echo json_encode(wp_get_attachment_image_src($header_image, 'large')); ?>;
    const scrollButton = <?php echo json_encode($scroll_button); ?>;

    const primaryButtonText = "<?php echo esc_js($primary_button_text); ?>";
    const primaryButtonUrl = "<?php echo esc_js($primary_button_url); ?>";
    const secondaryButtonText = "<?php echo esc_js($secondary_button_text); ?>";
    const secondaryButtonUrl = "<?php echo esc_js($secondary_button_url); ?>";
    const simpleLinkText = "<?php echo esc_js($simple_link_text); ?>";
    const simpleLinkUrl = "<?php echo esc_js($simple_link_url); ?>";

    window.pageHeaderData = {
		backgroundColor: backgroundColor,
        tagLabel: tagLabel,
        tagVector: tagVector ? tagVector[0] : null,
        headerTitle: headerTitle,
        quote: quote,
        quoteAuthor: quoteAuthor,
        authorImage: authorImage ? authorImage[0] : null,
        headerImage: headerImage ? headerImage[0] : null,
        scrollButton: scrollButton,

        primaryButton: {
            text: primaryButtonText,
            url: primaryButtonUrl
        },
        secondaryButton: {
            text: secondaryButtonText,
            url: secondaryButtonUrl
        },
        simpleLink: {
            text: simpleLinkText,
            url: simpleLinkUrl
        }
    };
</script>


<div class="reactPageHeader"></div>
