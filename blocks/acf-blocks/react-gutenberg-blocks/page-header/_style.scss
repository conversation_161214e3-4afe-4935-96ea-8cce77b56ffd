.page-header-block {
  margin-bottom: $spacing-08-40;
  position: relative;

  &__background {
    position: absolute;
    width: 100vw;
    height: 100%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  &__image-wrapper {
    height: 100%;
    width: 44.5rem;
    max-width: 100%;
    position: relative;

    img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .tag {
    &--icon {
      padding-left: $spacing-00;
    }
  }

  h2 {
    margin-bottom: $spacing-06-24;
    margin-top: $spacing-01;
  }

  &__content-wrapper {
    padding: $spacing-08-40 $spacing-00;
  }

  &__content {
    border-top: 0.1rem solid $color-divider;
  }

  &__quote-wrapper {
    display: flex;
    column-gap: $spacing-04-16;
    margin-bottom: $spacing-06-24;

    p {
      margin-bottom: $spacing-04-16;
    }
  }

  &__quote-author-image {
    min-width: 4.8rem;
    min-height: 4.8rem;
    height: 4.8rem;
    width: 4.8rem;
    border-radius: 0.4rem;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 0.4rem;
    }
  }

  &__quote-text-wrapper {
    * {
      color: $color-text-secondary;
      font-weight: 500;
      font-family: $font-archivo;
      font-size: 1.6rem;
      line-height: 2.24rem;
    }
  }

  &__quote-author {
    margin-top: $spacing-04-16;
  }

  &__buttons-wrapper {
    display: flex;
    align-items: center;
    gap: $spacing-02;
    flex-wrap: wrap;
  }

  .scroll-button {
    &:after {
      content: '';
      @include mask-properties;
      mask-image: url('assets/icons/ds2024/icon-arrow-down.svg');
      -webkit-mask-image: url('assets/icons/ds2024/icon-arrow-down.svg');
      width: 1.6rem;
      height: 1.6rem;
      background-color: $color-white;
      margin-left: $spacing-01;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, md )) {
    &__content-wrapper {
      padding: $spacing-08-40 $spacing-00 $spacing-06-24;
    }

    &__quote-wrapper {
      margin-bottom: $spacing-00;
    }

    &__image-wrapper {
      height: 37.5rem;
    }

    &__buttons-wrapper {
      justify-content: center;
    }

    #scroll-button,
    .button {
      width: 100%;
    }
  }
}
