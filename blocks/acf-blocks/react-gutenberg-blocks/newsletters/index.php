<?php

/**
 * Get selected newsletters
 * @var array
 */
$newsletters = get_field('selected_newsletters');

/**
 * Check if the section has a background image
 * @var boolean
 */
$hasBackground = get_field('has_background_image');

/**
 * Get the section data
 * @var array
 */
$section_data = get_header_section_data();

?>

<div id="reactNewsletters" data-section-data="<?php echo htmlspecialchars(json_encode($section_data)); ?>" data-selected-newsletters="<?php echo htmlspecialchars(json_encode($newsletters)); ?>" data-has-background="<?php echo $hasBackground; ?>"></div>
