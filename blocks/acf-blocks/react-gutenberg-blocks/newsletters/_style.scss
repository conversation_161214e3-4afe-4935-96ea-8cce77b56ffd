.react-toggle {
  touch-action: pan-x;
  margin-right: $spacing-02;
  margin-top: $spacing-01;
  display: inline-block;
  position: relative;
  cursor: pointer;
  background-color: transparent;
  border: 0;
  padding: $spacing-00;

  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;

  @media (hover: hover) {
    &:hover {
      &:not(.react-toggle--disabled) {
        .react-toggle-track {
          background-color: $color-text-secondary;
        }

        &.react-toggle--checked {
          .react-toggle-track {
            background-color: $color-link-status-success-hover;
          }
        }
      }
    }
  }
}

.react-toggle-screenreader-only {
  border: 0;
  clip: rect(0 0 0 0);
  height: 0.1rem;
  margin: -0.1rem;
  overflow: hidden;
  padding: $spacing-00;
  position: absolute;
  width: 0.1rem;
}

.react-toggle--disabled {
  cursor: not-allowed;
  opacity: 0.5;
  -webkit-transition: opacity 0.25s;
  transition: opacity 0.25s;
}

.react-toggle-track {
  width: 3.2rem;
  height: 2rem;
  padding: $spacing-00;
  border-radius: 3rem;
  background-color: $color-button-secondary-disabled;
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

.react-toggle--checked .react-toggle-track {
  background-color: $color-text-success;
}

.react-toggle-track-x,
.react-toggle-track-check {
  opacity: 0;
}

.react-toggle-thumb {
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1) 0ms;
  position: absolute;
  top: 0.2rem;
  left: 0.2rem;
  width: 1.6rem;
  height: 1.6rem;
  border-radius: 50%;
  background-color: $color-surface-primary;

  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;

  -webkit-transition: all 0.25s ease;
  -moz-transition: all 0.25s ease;
  transition: all 0.25s ease;
}

.react-toggle--checked .react-toggle-thumb {
  left: 1.4rem;
}

#reactNewsletters {
  margin-bottom: $spacing-12-80;

  .category-header {
    h4,
    a {
      .icon {
        path {
          stroke: $color-text-primary !important;
        }
      }
    }

    .link {
      @media (hover: hover) {
        &:hover {
          color: $color-text-brand !important;

          .icon {
            path {
              stroke: $color-text-brand !important;
            }
          }
        }
      }
    }
  }

  .react-newsletter-block {
    &__consent-text {
      p,
      a {
        font-family: $font-archivo;
        font-size: 1.4rem;
        font-weight: 500;
        line-height: 140%;
        color: $color-text-secondary;
        text-align: center;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin-bottom: $spacing-08-40;

    .react-newsletter-block {
      &__box {
        padding: $spacing-06-24;
      }
    }
  }
}
