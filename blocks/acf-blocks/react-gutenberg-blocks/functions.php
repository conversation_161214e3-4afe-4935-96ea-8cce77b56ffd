<?php
add_action('acf/init', 'forbes_init_react_block_types');
function forbes_init_react_block_types() {

	// Check function exists.
	if (function_exists('acf_register_block_type')) {
		// register the LATEST ARTICLES
		acf_register_block_type(array(
			'name'              => 'latest-articles-block',
			'title'             => esc_html__('React: Latest Articles', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/react-gutenberg-blocks/latest-articles/index.php',
			'category'          => 'common',
			'icon'              => 'admin-post',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('latest', 'FORBES'), esc_html__('article', 'FORBES')),
			'align'				=> 'center',
		));

		// register the CATEGORY TYPES
		acf_register_block_type(array(
			'name'              => 'category-block',
			'title'             => esc_html__('React: Category Types', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/react-gutenberg-blocks/category-types/index.php',
			'category'          => 'common',
			'icon'              => 'layout',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('category', 'FORBES'), esc_html__('article', 'FORBES')),
			'align'				=> 'center',
		));

		// register the EDITORS CHOICE
		acf_register_block_type(array(
			'name'              => 'editors-choice',
			'title'             => esc_html__('React: Editors Choice Articles', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/react-gutenberg-blocks/editors-choice/index.php',
			'category'          => 'common',
			'icon'              => 'admin-post',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('editor', 'FORBES'), esc_html__('choice', 'FORBES'), esc_html__('article', 'FORBES')),
			'align'				=> 'center',
		));

		// register the NEWSLETTERS
		acf_register_block_type(array(
			'name'              => 'react-newsletters',
			'title'             => esc_html__('React: Newsletters', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/react-gutenberg-blocks/newsletters/index.php',
			'category'          => 'common',
			'icon'              => 'email-alt',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('newsletter', 'FORBES'), esc_html__('list', 'FORBES')),
			'align'				=> 'center',
		));

		// register the PAGE HEADER
		acf_register_block_type(array(
			'name'              => 'react-page-header-block',
			'title'             => esc_html__('React: Page Header', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/react-gutenberg-blocks/page-header/index.php',
			'category'          => 'common',
			'icon'              => 'cover-image',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('header', 'FORBES'), esc_html__('page', 'FORBES')),
			'align'				=> 'center',
		));

		// register the POSTS SELECTION
		acf_register_block_type(array(
			'name'              => 'react-posts-selection-block',
			'title'             => esc_html__('React: Posts Selection', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/react-gutenberg-blocks/posts-selection/index.php',
			'category'          => 'common',
			'icon'              => 'admin-post',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('posts', 'FORBES'), esc_html__('select', 'FORBES')),
			'align'				=> 'center',
		));

		// register the BENEFITS CARDS
		acf_register_block_type(array(
			'name'              => 'react-benefits-cards-block',
			'title'             => esc_html__('React: Benefits Cards', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/react-gutenberg-blocks/benefits-cards/index.php',
			'category'          => 'common',
			'icon'              => 'awards',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('benefits', 'FORBES'), esc_html__('cards', 'FORBES')),
			'align'				=> 'center',
		));

		// register the FAQ
		acf_register_block_type(array(
			'name'              => 'react-faq-block',
			'title'             => esc_html__('React: FAQ', 'FORBES'),
			'description'       => '',
			'render_template'   => get_template_directory() . '/blocks/acf-blocks/react-gutenberg-blocks/faq/index.php',
			'category'          => 'common',
			'icon'              => 'editor-help',
			'mode'				=> 'preview',
			'keywords'          => array(esc_html__('faq', 'FORBES'), esc_html__('accordion', 'FORBES'), esc_html__('tabs', 'FORBES')),
			'align'				=> 'center',
		));
	}

}
