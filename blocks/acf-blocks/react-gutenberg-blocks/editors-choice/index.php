<?php

$editors_choice = array();
$section_data = get_header_section_data();
$print_category = get_field('print_category', 'option') ?? null;
$used_ids = isset($GLOBALS["used_ids"]) ? $GLOBALS["used_ids"] : [];
$items = get_field('editors_choice_articles_items');
$filter_taxonomy = get_field('filter_taxonomy');

$args = array(
    'post_type' => 'post',
    'posts_per_page' => 4,
    'post_status' => 'publish',
    'order' => 'DESC',
    'orderby' => 'date',
    'tax_query' => array(
        array(
            'taxonomy'  => CATEGORY_TYPE,
            'field'     => 'term_id',
            'terms'     => $print_category,
            'operator'  => 'NOT IN',
        ),
    ),
    'post__not_in' => $used_ids,
);

if (!empty($filter_taxonomy)) {
	$args['tax_query'] = array(
		array(
			'taxonomy' => $filter_taxonomy,
			'operator' => 'EXISTS',
		),
	);
}

$query = new WP_Query($args);

for ($i = 0; $i < count($query->posts); $i++) {
    $id = $query->posts[$i]->ID;

    if (isset($items[$i]) && $items[$i]['article']) {
        $id = $items[$i]['article']; // Use the specified article ID
    }

	// Check if the post contains the block 'remp-paywall/lock'
	$contains_remp_lock_block = has_block('remp-paywall/lock', $id);

    $articleData = get_article_data($id, $contains_remp_lock_block);

    if (!empty($articleData)) {
        $article = array(
            'id' => $id,
            'title' => $articleData['title'],
            'authorName' => get_the_main_author($id)->name,
            'authorLink' => get_the_main_author($id)->archive_link,
            'tagLabel' => isset($articleData['category']['label']) ? $articleData['category']['label'] : $articleData['category']->name,
			'categoryLink' => $articleData['category']['link'],
            'tagIcon' => isset($articleData['category']['icon']) ? $articleData['category']['icon'] : null,
            'publishDate' => $articleData['publishDate'],
            'readTime' => $articleData['readTime'],
            'imageLink' => get_the_post_thumbnail_url($id, 'large'),
            'link' => $articleData['url'],
            'perex' => html_entity_decode(strip_tags(get_post_excerpt($id))),
        );

        // Add the constructed article array to the list of editors choice
        array_push($editors_choice, $article);

        // Add the ID of the current article to the list of used IDs
        array_push($used_ids, $id);
    }
}

wp_reset_postdata();

// Update the global used IDs array with the updated list of used post IDs
$GLOBALS["used_ids"] = $used_ids;
?>

<script>
	const editorsChoiceArticles = <?php echo htmlspecialchars_decode(wp_json_encode($editors_choice)); ?>;
    const editorsChoiceSectionTitle = "<?php echo esc_js($section_data['title']); ?>";
    const editorsChoiceTitleLink = <?php echo json_encode($section_data['hasTitleLink']); ?>;
    const editorsChoiceSectionLinkType = "<?php echo esc_js($section_data['sectionLinkType']); ?>";
    const editorsChoiceSectionLinkText = "<?php echo esc_js($section_data['sectionLinkText']); ?>";
    const editorsChoiceSectionUrl = "<?php echo esc_js($section_data['sectionUrl']); ?>";
    const editorsChoiceShowIcon = <?php echo json_encode($section_data['showIcon']); ?>;
    const editorsChoiceIconUrl = "<?php echo esc_js($section_data['iconUrl']); ?>";
	const editorsChoiceLinkIcon = "<?php echo esc_js($section_data['linkIcon']); ?>";
	const editorsChoiceSectionHideHeader = <?php echo json_encode($section_data['hideHeader']); ?>;

    window.editorsChoiceData = {
        articles: editorsChoiceArticles,
        sectionTitle: editorsChoiceSectionTitle,
        hasTitleLink: editorsChoiceTitleLink,
        sectionLinkType: editorsChoiceSectionLinkType,
        sectionLinkText: editorsChoiceSectionLinkText,
        sectionUrl: editorsChoiceSectionUrl,
        showIcon: editorsChoiceShowIcon,
        iconUrl: editorsChoiceIconUrl,
		linkIcon: editorsChoiceLinkIcon,
		hideHeader: editorsChoiceSectionHideHeader
    };
</script>

<div id="reactEditorsChoice"></div>
