#reactEditorsChoice {
  margin-bottom: $spacing-12-80;

  .react-block {
    // forced dark mode styles for editor's picks and forbes.cz selection sections ===>
    &__editors-picks {
      background-color: $color-background-darkfixed;
      width: calc(100vw);
      margin-left: calc(50% - 50vw);
      padding-bottom: $spacing-08-40;

      .container {
        display: block;
        padding-bottom: $spacing-00;
      }

      .category-header {
        h4,
        a {
          color: $color-white-darkfixed !important;

          .icon {
            path {
              stroke: $color-white-darkfixed !important;
            }
          }
        }

        .link {
          @media (hover: hover) {
            &:hover {
              color: $color-tag-default-darkfixed !important;

              .icon {
                path {
                  stroke: $color-tag-default-darkfixed !important;
                }
              }
            }
          }
        }

        &__icon-wrapper {
          background-color: $color-white-darkfixed;
        }

        &__icon {
          background-color: $color-background-darkfixed;
        }
      }

      .react-block-article-card {
        .tag {
          background-color: transparent;
          color: $color-tag-life-default-darkfixed;

          &__icon {
            background-color: $color-tag-life-default-darkfixed;
          }

          &:before {
            background-color: $color-tag-life-default-darkfixed;
          }
        }

        &__title-link {
          color: $color-white-darkfixed;
        }

        .desktop-body {
          &.hovered {
            color: $color-white-darkfixed !important;
          }
        }

        &--small {
          .tag {
            background-color: $color-background-darkfixed;
            color: $color-tag-hover-darkfixed;

            &:before {
              background-color: $color-tag-hover-darkfixed;
            }
          }
        }

        &--one-third {
          .tag {
            background-color: $color-background-darkfixed;
          }

          .desktop-body {
            -webkit-line-clamp: 3;
          }
        }

        h4,
        h6 {
          color: $color-white-darkfixed;
        }

        .desktop-body {
          color: $color-bodytext-darkfixed;
        }

        .article-meta {
          * {
            color: $color-bodytext-darkfixed;
          }

          span,
          button {
            &:before {
              background-color: $color-bodytext-darkfixed;
            }
          }
        }
      }

      .react-life-article-meta {
        .icon {
          path {
            stroke: $color-white-darkfixed !important;
          }
        }

        span,
        a {
          color: $color-white-darkfixed;
        }
      }
    }

    &__editors-picks-featured {
      display: flex;
      height: 100%;
      width: 100%;
      aspect-ratio: 16 / 9;
      position: relative;
    }

    &__editors-picks-featured-gradient {
      padding: $spacing-12-80 $spacing-00 $spacing-08-40;
      @include grid-columns(1);
      gap: $spacing-07-32;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .react-block-article-card {
        max-width: 50%;

        &__image-wrapper {
          display: none;
        }
      }
    }

    &__editors-picks-featured-flex {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 100%;

      a.react-block__section-header-title-wrapper {
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    @media screen and (max-width: map-get($container-max-widths, lg)) {
      &__editors-picks-featured-flex {
        padding: $spacing-00 $spacing-03-12;
      }

      &__editors-picks {
        &-featured {
          aspect-ratio: 1 / 1 !important;
          width: 100vw;
          margin-left: calc(50% - 50vw);

          &-gradient {
            display: flex;
          }
        }
      }
    }

    @media screen and (max-width: map-get($container-max-widths, md)) {
      &__editors-picks {
        padding-bottom: $spacing-00;

        &-featured {
          aspect-ratio: 1 / 1.5 !important;
        }
      }

      &__editors-picks-featured-gradient {
        padding-top: $spacing-08-40;

        .react-block-article-card {
          max-width: 100%;
        }
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin-bottom: $spacing-08-40;
  }
}
