<?php
// Initialize an empty array to store latest articles data
$latest_articles = array();

// Check if the global variable "used_ids" is set, otherwise initialize it as an empty array
$used_ids = isset($GLOBALS["used_ids"]) ? $GLOBALS["used_ids"] : [];

/**
 * The ID of the print tag
 * @var string
 */
$print_category = get_field('print_category', 'option') ?? null;

$items = get_field('latest_articles_items');

$args = array(
    'post_type' => 'post',
    'posts_per_page' => 3,
    'post_status' => 'publish',
    'order' => 'DESC',
    'orderby' => 'date',
    'tax_query' => array(
        array(
            'taxonomy' => CATEGORY_TYPE,
            'terms'    => $print_category,
            'field'    => 'term_id',
            'operator' => 'NOT IN',
        ),
    ),
    'post__not_in' => $used_ids, // Exclude posts with IDs already used
);

$query = new WP_Query($args);

for ($i = 0; $i < count($query->posts); $i++) {
    $id = $query->posts[$i]->ID;

    // Check if an article ID is specified in the latest articles items array
    if (isset($items[$i]) && $items[$i]['article']) {
        $id = $items[$i]['article']; // Use the specified article ID
    }

	// Check if the post contains the block 'remp-paywall/lock'
	$contains_remp_lock_block = has_block('remp-paywall/lock', $id);

    $articleData = get_article_data($id, $contains_remp_lock_block);

    if (!empty($articleData)) {
        $article = array(
            'id' => $id,
            'title' => $articleData['title'],
            'authorName' => get_the_main_author($id)->name,
            'authorLink' => get_the_main_author($id)->archive_link,
            'tagLabel' => isset($articleData['category']['label']) ? $articleData['category']['label'] : $articleData['category']->name,
            'categoryLink' => $articleData['category']['link'],
            'tagIcon' => isset($articleData['category']['icon']) ? $articleData['category']['icon'] : null,
            'publishDate' => $articleData['publishDate'],
            'readTime' => $articleData['readTime'],
            'imageLink' => get_the_post_thumbnail_url($id, 'large'),
            'link' => $articleData['url'],
            'perex' => html_entity_decode(strip_tags(get_post_excerpt($id))),
        );

        // Add the constructed article array to the list of latest articles
        array_push($latest_articles, $article);

        // Add the ID of the current article to the list of used IDs
        array_push($used_ids, $id);
    }
}

wp_reset_postdata();

// Store the updated list of used IDs in the global variable
$GLOBALS["used_ids"] = $used_ids;
?>

<div id="reactLatestArticles" data-latest-articles="<?= esc_attr(wp_json_encode($latest_articles)) ?>"></div>
