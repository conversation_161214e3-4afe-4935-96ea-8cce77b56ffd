#reactLatestArticles {
  margin-bottom: $spacing-12-80;

  .react-block-article-card {
    &--4 {
      .article-meta {
        position: relative;
        justify-content: center;

        &__inner {
          max-width: 65%;
        }

        &__buttons {
          position: absolute;
          right: 0;
          gap: $spacing-02;
        }
      }
    }
  }

  .react-block {
    &__page-top-articles {
      @include grid-columns(2);
      grid-gap: $spacing-07-32;
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin-bottom: $spacing-08-40;

    .react-block {
      &__page-top-articles {
        display: flex;
        flex-direction: column;
        gap: $spacing-07-32;
      }
    }
  }
}
