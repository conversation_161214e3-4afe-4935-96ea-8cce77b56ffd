.reactCategoryTypeA,
.reactCategoryTypeB,
.reactCategoryTypeC {
  margin-bottom: $spacing-12-80;

  .react-block {
    &__type-c {
      padding: $spacing-12-80 $spacing-00;
      background-color: $color-surface-primary-invert;
      width: calc(100vw);
      margin-left: calc(50% - 50vw);

      .container {
        display: block;
        padding-bottom: $spacing-00;
      }

      .category-header {
        h4,
        a {
          color: $color-white-darkfixed !important;

          .icon {
            path {
              stroke: $color-white-darkfixed !important;
            }
          }
        }

        .link {
          @media (hover: hover) {
            &:hover {
              color: $color-tag-default-darkfixed !important;

              .icon {
                path {
                  stroke: $color-tag-default-darkfixed !important;
                }
              }
            }
          }
        }

        &__icon-wrapper {
          background-color: $color-white-darkfixed;
        }

        &__icon {
          background-color: $color-background-darkfixed;
        }
      }

      .react-block-article-card {
        .tag {
          background-color: transparent;
          color: $color-tag-life-default;
          &:before {
            background-color: $color-tag-life-default;
          }
        }

        &--small {
          .tag {
            background-color: $color-surface-primary-invert;
            color: $color-tag-default-darkfixed;

            &:before {
              background-color: $color-tag-default-darkfixed;
            }

            &__icon {
              background-color: $color-tag-default-darkfixed;
            }
          }
        }

        &--one-third {
          .tag {
            background-color: $color-surface-primary-invert;
          }
        }

        h4,
        h6 {
          color: $color-white-darkfixed;
        }

        .desktop-body {
          color: $color-bodytext-darkfixed;
        }

        &__title-link {
          text-decoration-color: $color-white-darkfixed;
          color: $color-white-darkfixed;
        }
      }

      .react-life-article-meta {
        .icon {
          path {
            stroke: $color-white-darkfixed !important;
          }
        }

        span,
        a {
          color: $color-white-darkfixed;
        }
      }
    }

    &__two-one-layout {
      &-wrapper {
        @include grid-columns(3);
        gap: $spacing-07-32;

        &-featured {
          grid-column: 3 / -1;
        }
      }

      &-featured {
        grid-column: 1 / 3;
      }

      &-non-featured {
        grid-column: 3 / -1;

        display: flex;
        flex-direction: column;
        gap: $spacing-07-32;
      }
    }

    &__card-container {
      &--type-a {
        padding: $spacing-07-32 $spacing-00 $spacing-00 $spacing-00;
      }

      &--type-c {
        @include grid-columns(2);
        padding: $spacing-00;
      }
    }

    @media screen and (max-width: map-get($container-max-widths, lg)) {
      &__type-c {
        padding: $spacing-08-40 $spacing-00;
      }

      &__two-one-layout-wrapper {
        display: flex;
        flex-direction: column;
      }
    }
  }

  @media screen and (max-width: map-get($container-max-widths, lg)) {
    margin-bottom: $spacing-08-40;
  }
}

.reactCategoryTypeA {
  .react-block-article-card {
    &--5 {
      .article-meta {
        justify-content: center;
        align-items: center;
        gap: 1.4rem;
      }

      @media screen and (max-width: map-get($container-max-widths, md)) {
        .article-meta {
          align-items: flex-start;
        }
      }
    }
  }

  .image-card {
    aspect-ratio: 1 / 1;
    img {
      height: 100%;
      object-fit: cover;
      object-position: top center;
    }
  }
}

.reactCategoryTypeC {
  .react-block-article-card {
    .article-meta {
      * {
        color: $color-bodytext-darkfixed;
      }

      span,
      button {
        &:before {
          background-color: $color-bodytext-darkfixed;
        }
      }
    }
  }

  .react-block {
    &__section-header {
      .link-underline-14 {
        @media (hover: hover) {
          &:hover {
            color: $color-tag-hover-darkfixed !important;

            .icon-wrapper {
              .icon {
                path {
                  stroke: $color-tag-hover-darkfixed !important;
                }
              }
            }
          }
        }
      }
    }
  }
}
