<?php
/**
 * Get the type of category
 * @var string
 */
$category_type = get_field('type');

/**
 * Get the category
 * @var \WP_Term
 */
$category_term = get_field('category');
$category = $category_term->term_id;

/**
 * The ID of the print tag
 * @var string
 */
$print_category = get_field('print_category', 'option') ?? null;

/**
 * Get the section data
 */
$args = array('category' => $category);
$section_data = get_header_section_data($args);

// Initialize an empty array for category block data
$manual_articles = array();
$repeater_field_name = '';

// Determine the repeater field name based on category type
switch ($category_type) {
    case 'a':
        $repeater_field_name = 'manual_type_a_articles';
        break;
    case 'b':
        $repeater_field_name = 'manual_type_b_articles';
        break;
    case 'c':
        $repeater_field_name = 'manual_type_c_articles';
        break;
}

$items = get_field($repeater_field_name);

/**
 * Determine the number of posts to display based on the category type
 * @var int
 */
$postPerPage = ($category_type === 'a') ? 7 : (($category_type === 'b') ? 3 : 6);

// Initialize or retrieve global used_ids
$used_ids = isset($GLOBALS["used_ids"]) ? $GLOBALS["used_ids"] : [];

$args = array(
    'posts_per_page'  => $postPerPage,
    'post_type'       => 'post',
    'post_status'     => 'publish',
    'tax_query'       => array(
        array(
            'terms'     => $category,
            'taxonomy' => $category_term->taxonomy,
		),
		array(
            'taxonomy'  => CATEGORY_TYPE,
            'field'     => 'term_id',
            'terms'     => $print_category,
            'operator'  => 'NOT IN',
        ),
    ),
    'orderby'         => 'date',
    'order'           => 'DESC',
    'post__not_in'    => $used_ids,
);

$query = new WP_Query($args);

foreach ($query->posts as $index => $post) {
	$id = $post->ID;

    // Check if there is an entry in $items array for the current index
    if (!empty($items[$index])) {

        // Check if the 'overwrite_flow' flag is true for the current item
		if($items[$index]['overwrite_flow'] === true) {

			$type = $category_type === 'b' ? 'article' : $items[$index]['type']; // Get the type of the current item

			// If the type is 'article' and an article ID is provided, set $id to this new article ID
			if ($type === 'article' && isset($items[$index]['article'])) {

				$id = $items[$index]['article'];
			} elseif ($type === 'image') {
				$imageData = array(
					'type' => 'image',
					'imageUrl' => $items[$index]['image'],
					'link' => $items[$index]['url'] ?? '',
				);
				$manual_articles[] = $imageData;
				continue; // Skip the rest of the loop for the current iteration
			}

			// Check if the ID is already used, if so, skip to the next iteration
			if (in_array($id, $used_ids)) {
				continue; // Skip this iteration if ID is already used
			}
		}

		// Check if the post contains the block 'remp-paywall/lock'
		$contains_remp_lock_block = has_block('remp-paywall/lock', $id);

        // Retrieve article data for the post ID
        $articleData = get_article_data($id, $contains_remp_lock_block);

        // Check if article data is not empty
        if (!empty($articleData)) {
            $article = array(
                'type' => 'article',
                'id' => $id,
                'title' => $articleData['title'],
                'authorName' => get_the_main_author($id)->name,
                'authorLink' => get_the_main_author($id)->archive_link,
                'tagLabel' => isset($articleData['category']['label']) ? $articleData['category']['label'] : $articleData['category']->name,
                'categoryLink' => $articleData['category']['link'],
                'publishDate' => $articleData['publishDate'],
                'readTime' => $articleData['readTime'],
                'imageLink' => get_the_post_thumbnail_url($id, 'large'),
                'link' => $articleData['url'],
                'perex' => html_entity_decode(strip_tags(get_post_excerpt($id))),
                'tagIcon' => isset($articleData['category']['icon']) ? $articleData['category']['icon'] : null,
            );
            $manual_articles[] = $article;
            $used_ids[] = $id; // Add the ID to the list of used IDs to prevent reuse
        }
    }
}

wp_reset_postdata();

// Store the updated list of used IDs in the global variable
$GLOBALS["used_ids"] = $used_ids;

// Render block based on category type
switch ($category_type) {
    case 'a':
        $renderBlock = '<div class="reactCategoryTypeA" data-category-articles=\'' . htmlspecialchars_decode(json_encode($manual_articles)) . '\' data-section-data=\'' .
                       htmlspecialchars(json_encode($section_data)) . '\'></div>';
        break;
    case 'b':
        $renderBlock = '<div class="reactCategoryTypeB" data-category-articles=\'' . htmlspecialchars_decode(
                json_encode($manual_articles)
            ) . '\' data-section-data=\'' . htmlspecialchars(json_encode($section_data)) . '\'></div>';
        break;
    case 'c':
        $renderBlock = '<div class="reactCategoryTypeC" data-category-articles=\'' . htmlspecialchars_decode(
                json_encode($manual_articles)
            ) . '\' data-section-data=\'' . htmlspecialchars(json_encode($section_data)) . '\'></div>';
        break;
}

// Output the rendered block
echo $renderBlock;
?>
