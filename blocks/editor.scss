@import 'assets/scss/base/_variables.scss';

:root {
  font-size: 10px;
}

.editor-post-title__block .editor-post-title__input {
  font-size: calc(2.25rem * 1);
  line-height: calc(2.25rem * 1.2);
}

.editor-styles-wrapper {
  overflow: hidden;

  .acf-gallery-sort.disabled {
    background-image: none;
  }

  .acf-block-preview {
    @import 'assets/scss/base/_typography.scss';
    @import 'assets/scss/helpers/_grid.scss';
    @import 'assets/scss/overrides/splide.scss';

    @import 'blocks/acf-blocks/single-page-blocks/in-article-related-posts/_editor.scss';
    @import 'blocks/acf-blocks/single-page-blocks/related-magazine/_editor.scss';
    @import 'blocks/acf-blocks/single-page-blocks/related-product/_editor.scss';
    @import 'blocks/acf-blocks/single-page-blocks/related-post-links/_editor.scss';

    @import 'blocks/acf-blocks/home-page-blocks/daily-cover/_editor.scss';
    @import 'blocks/acf-blocks/home-page-blocks/category-type-b/_editor.scss';
    @import 'blocks/acf-blocks/home-page-blocks/podcasts/_editor.scss';
    @import 'blocks/acf-blocks/home-page-blocks/comments/_editor.scss';

    @import 'blocks/acf-blocks/subscription-page-blocks/about/_editor.scss';
    @import 'blocks/acf-blocks/subscription-page-blocks/why-subscribe/_editor.scss';

    @import 'blocks/acf-blocks/premium-page-blocks/header/_editor.scss';

    @import 'blocks/acf-blocks/one-page-blocks/hero/_editor.scss';

    @import 'template-parts/article-card/_editor.scss';
    @import 'template-parts/article-details/_editor.scss';
    @import 'template-parts/brandvoice-card/_editor.scss';
    @import 'template-parts/daily-cover-frame/_editor.scss';
    @import 'template-parts/podcast-card/_editor.scss';
    @import 'template-parts/comment-card/_editor.scss';

    @import 'template-parts/home-page/articles-grid/_editor.scss';
    @import 'template-parts/home-page/two-column-specials/_editor.scss';

    a {
      pointer-events: none;
      text-decoration: none !important;
      color: initial;
    }

    img {
      height: 100%;
      font-size: 10px !important;
      line-height: 12px !important;
    }

    .forbes-gutenberg-block-placeholder {
      display: flex !important;
      justify-content: center;
      align-items: center;
      gap: 12px;
      padding: 20px;
      background-color: #f3f3f3;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
    }
  }
}

@import 'blocks/acf-blocks/CZ/single-page-blocks/property-widget/_editor.scss';

body.locale-cs-cz {
  .editor-styles-wrapper {
    .acf-block-preview {
      .row {
        flex-wrap: nowrap;
      }
      @import 'blocks/acf-blocks/CZ/home-page-blocks/featured-special/_editor.scss';
      @import 'blocks/acf-blocks/CZ/home-page-blocks/special-list/_editor.scss';
      @import 'blocks/acf-blocks/home-page-blocks/specials-and-rankings-type-b/_editor.scss';

      @import 'template-parts/home-page/list-item/_editor.scss';
    }
  }
}

// Character counter for title on post page
.edit-post-visual-editor__post-title-wrapper {
  position: relative;
}

.wp-block-post-title--counter {
  position: absolute;
  right: 0;
  top: -25px;
}
