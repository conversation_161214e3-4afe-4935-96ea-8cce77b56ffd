<?php

/**
 * Template Name: Page subscription
 */

get_header();

/**
 * Funnel label of the header
 * @var string
 */
$funnel_label = get_field('funnel_label') ?? '';

/**
 * Link of the funnel label
 * @var string
 */
$funnel_label_link = get_field('funnel_label_link') ?? '';

/**
 * Funnel title of the header
 * @var string
 */
$funnel_title = get_field('funnel_title') ?? '';

/**
 * Funnel subtitle of the header
 * @var string
 */
$funnel_subtitle = get_field('funnel_subtitle') ?? '';

/**
 * The array of "menu_name" => "menu_id" pairs to determine which menu should we use in according to the $menu_name's value
 * @var int[]
 * */
$locations = get_nav_menu_locations();

/**
 * If the menu is extending, the menu will be displayed on the right side of the page.
 * @var bool $is_extending_menu
 */
$is_extending_menu = get_field('funnel_is_extending') ?? false;

/**
 * The sales funnel menu object. Displayed on the right side of the page.
 * @var object
 */
$sales_funnel_menu = isset($locations['sales-funnel-menu']) ? wp_get_nav_menu_object($locations['sales-funnel-menu']) : null;

/**
 * Tree like structure of the sales funnel menu items.
 * If a menu item has submenu items, these submenu items can be accessed through the "menu_item_children" property.
 * @var array(object)
 */
$sales_funnel_menu_tree = $sales_funnel_menu ? Navigation\frontend_nav_menu_2_tree($sales_funnel_menu->term_id) : null;

/**
 * Get current url of the page
 * @var string
 */
$current_url = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
?>

<!--
    Permanently add scrollbar since the
    page will always be longer due to the iframe
    to avoid flickering on pageload
-->
<style>
    @media (min-width: 992px) {
        html {
            overflow-y: scroll;
        }
    }
</style>

<main class="page-subscription">

    <?php if (COUNTRY !== 'cz'): ?>

        <!-- Reset the navigation background color for SK first step of sales funnel-->
        <style>
            body.page-template-template-subscription .navigation {
                background-color: var(--post-background-color, var(--color-surface-primary));
            }
        </style>

      <div class="page-subscription__header--sk <?php
	  if (COUNTRY === 'hu') : ?> tw:!pb-0 <?php
	  endif; ?>">
            <div class="container">
                <div class="pricing-page__section-header pricing-page__header">
                    <a
                        href="/"
                        class=""
                        rel="noopener noreferrer"
                    >
                        <?php if( $funnel_label && $funnel_label_link ) : ?>
                            <a href="<?php echo $funnel_label_link ?>" class="tag-link"><div class="tag tag__dash tag__link"><div class="tag__text"><?php echo $funnel_label ?></div></div></a>
                        <?php endif; ?>
                    </a>

                    <h2><?php echo $funnel_title ?></h2>
                </div>

                <?php
                    if ($is_extending_menu) {
                        $salesFunnelMenu = new SegmentedControl(
                            name: 'sales-funnel-extending-menu',
                        );
                    } else {
                        $salesFunnelMenu = new SegmentedControl(
                            name: 'sales-funnel-menu',

                        );
                    }
                    echo $salesFunnelMenu->render();
                ?>

            </div>
        </div>

    <?php else: ?>
        <div class="page-subscription__header">
            <div class="container">
                <div class="inner">

                    <?php if( $funnel_label && $funnel_label_link ) : ?>
                        <a href="<?php echo $funnel_label_link ?>" class="tag-link"><div class="tag tag__dash tag__link"><div class="tag__text"><?php echo $funnel_label ?></div></div></a>
                    <?php endif; ?>

                    <h1 class="heading heading--archivo heading--800"><?php echo $funnel_title ?></h1>

                    <div class="page-subscription__filters-wrapper">
                        <div class="page-subscription__filters">
                            <?php foreach ($sales_funnel_menu_tree as $item) : ?>

                                <a href="<?php echo $item->url; ?>" class="tab <?php if($item->url === $current_url) : ?> active <?php endif; ?>"><span><?php echo $item->title; ?></span></a>

                            <?php endforeach; ?>
                        </div>
                    </div>

                </div>
            </div>
            <div class="page-sales-funnel-pricing__bar">
                <div class="input-group button-group">
                    <button id="continue-to-second-step" class="button button--large button--primary bar__button">
                        Pokračovat k objednávce&nbsp
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M12 6L14 8L12 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="square"/>
                            <path d="M3 8H13" stroke="currentColor" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>

            </div>
        </div>
    <?php endif; ?>

	<?php
	if ((COUNTRY === 'hu') && ! empty($funnel_subtitle)):
		?>
		<h3 class="container tw:!mb-[4rem]"><?php
		  echo $funnel_subtitle ?></h3>
	<?php
	endif; ?>

    <div id="reactSalesFunnelEmbed"></div>

</main>

<?php
    $modal = new BootstrapModal(
        id: 'subscriptionInfoModal',
        size: 'large',
        title: 'Modal title',
        componentPath: 'components/sales-funnel/features'
    );

    echo $modal->render();

?>

<script type="text/javascript" src="https://widget.packeta.com/v6/www/js/library.js"></script>

<script>

    document.addEventListener('DOMContentLoaded', function() {

        window.forbes_config = window.forbes_config || {};

        const subscriptionData = localStorage.getItem('subscriptionData');
        if (subscriptionData) {
            const data = JSON.parse(subscriptionData);
            if (data.payload && data.payload.selectedSubscription) {
                window.forbes_config.selectedSubscription = data.payload.selectedSubscription;
                console.log("frontend selected subscription: ", data.payload.selectedSubscription);
            }
        }

        // Note: this is needed to prevent the page from scrolling to the bottom when the iframe is loaded
        jQuery(window).on('load', function() {
            const retryCount = 20;
            const retryDelay = 200;

            for (let i = 0; i < retryCount; i++) {
                setTimeout(function() {
                    iFrameResize({
                        log: false,
                        heightCalculationMethod: 'grow'
                    }, '#outside');
                }, i * retryDelay);
            }
        });

        // Note: get cookie by name
        const getCookie = (name) => {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
        }

        // Note: pass subscription data after modal
        if (localStorage.getItem('subscriptionData')) {
            const data = JSON.parse(localStorage.getItem('subscriptionData'));
            console.log("Full subscription data: ", data);

            if (getCookie('n_token')) {
                localStorage.removeItem('subscriptionData');
            }

            const iframe = document.getElementById('outside');
            const url = "<?php echo DN_REMP_CRM_HOST; ?>sales-funnel/sales-funnel-frontend/show?funnel=<?php echo get_field('funnel_name') ?>";
			console.log("URL: ", url);
            const email = data.payload?.email;
            const selectedSubscription = data.payload?.selectedSubscription;

            console.log("EMAIL: ", email, "selectedSubscription: ", selectedSubscription);

            console.log("payload: ", data.payload);

            if (iframe && data && email && selectedSubscription) {
                const payload = btoa(JSON.stringify(data.payload));
                const finalUrl = `${url}&payload=${payload}`;
                iframe.src = finalUrl;
            }
        }

        // Note: sets the value of a react input
        const setNativeValue = (element, value) => {
            let lastValue = element.value;
            element.value = value;

            let event = new Event("input", { target: element, bubbles: true });
            event.simulated = true;

            let tracker = element._valueTracker;

            if (tracker) {
                tracker.setValue(lastValue);
            }

            element.dispatchEvent(event);
        }

        const button = document.getElementById('continue-to-second-step');

        // Function to handle button click
        function handleButtonClick() {
            const iframe = document.getElementById('outside');
            if (iframe) {
                const message = JSON.stringify({ action: "confirmSelectedSubscriptionType" });
                iframe.contentWindow.postMessage(message, "*");

                setTimeout(function() {
                    window.scrollTo({
                        top: 0,
                        left: 0,
                        behavior: 'auto' // This disables smooth scrolling
                    });
                }, 100);
            } else {
                console.error("Iframe not found");
            }
        }

        // Function to attach event listener to the button
        function attachEventListenerToButton() {
            if (button) {
                button.addEventListener('click', handleButtonClick);
                return true;
            }
            return false;
        }

        // Try attaching event listener immediately
        if (!attachEventListenerToButton()) {
            // If button is not available immediately, check periodically
            const intervalId = setInterval(() => {
                if (attachEventListenerToButton()) {
                    clearInterval(intervalId);
                }
            }, 1000); // Check every second
        }

      const modalElementJQ = jQuery('#subscriptionInfoModal');
      const modalElement = modalElementJQ.get(0);

      // Event listener for when the modal is shown
      modalElementJQ.on('shown.bs.bootstrap-modal', function () {
        document.querySelector('html').style.overflow = 'hidden';
      });

      // Event listener for when the modal is hidden
      modalElementJQ.on('hidden.bs.bootstrap-modal', function () {
        document.querySelector('html').style.overflow = 'auto';
      });

        window.addEventListener('message', (event) => {
            // Check if event.data is a string and could be a JSON
            if (typeof event.data === 'string' && event.data.trim().startsWith('{') && event.data.trim().endsWith('}')) {
                try {
                    const data = JSON.parse(event.data);

	                function handleSubscriptionData() {
		                const token = getCookie('n_token');
		                console.log('Received email: ', data.payload.email);

		                // Note: if n_token exists we need to send to prepladne as well
		                if (token) {
			                data.payload.token = token;
			                localStorage.setItem('subscriptionData', JSON.stringify(data));
			                console.log('subscriptionData after login modal: ', localStorage.getItem('subscriptionData'));

		                } else {
			                localStorage.setItem('subscriptionData', JSON.stringify(data));
		                }
	                }

                    // Check for the specific modal trigger message
                    if (data.type === 'triggerModal' && data.payload?.type === 'subscriptionTypeInfo') {
                        const features = data.payload?.features || [];

                      if (modalElement) {
                            // Initialize the modal
                            const modal = new bootstrap.Modal(modalElement);

                            // Update the modal title if provided in the message
                            const modalTitle = data.payload?.title || 'Default Title';
                            const titleElement = document.getElementById('subscriptionInfoModalTitle');

                            // Hide all sections initially
                            const sections = modalElement.querySelectorAll('.benefit-card');
                            sections.forEach(section => section.style.display = 'none');

                            // Show only the sections that are in the features array
                            features.forEach(feature => {
                                const sectionElement = document.getElementById(feature);
                                if (sectionElement) {
                                    sectionElement.style.display = '';
                                }
                            });

                            if (titleElement) {
                                titleElement.textContent = modalTitle;
                            }

                            // Show the modal
                            modal.show();

                        } else {
                            console.error("Modal element not found");
                        }
                    }

                    // Handle subscriptionChanged event
                    if (data.type === 'subscriptionChanged') {
                        const subscriptionType = data.payload?.subscription;
                        const headerDiv = document.querySelector('.page-subscription__header');

						if (headerDiv) {
							if (subscriptionType) {
								// If subscription is present in payload
								headerDiv.style.display = 'none';
							} else if (Object.keys(data.payload).length === 0) {
								// If payload is empty
								headerDiv.style.display = 'block';
							}
						}

						// Scroll to the top of the page
						window.scrollTo({
							top: 0,
							behavior: 'smooth' // For smooth scrolling
						});
					}

	                function getLastVisitedContentUrl() {
		                try {
			                const lastVisitedContent = JSON.parse(localStorage.getItem('lastVisitedContent') || '{}');
			                return lastVisitedContent.url || '';
		                } catch (error) {
			                console.error('Error parsing last visited content:', error);
			                return '';
		                }
	                }

	                function generateSocialLoginUrl(provider) {
		                const baseUrl = window.crmUrl;
		                if (!baseUrl) return;

						const encodedReturnUrl = encodeURIComponent(`${window.location.href}?socialLoginStatus=success`);
						const encodedFailedReturnUrl = encodeURIComponent(`${window.location.href}?socialLoginStatus=failed`);
						const referer = encodeURIComponent(getLastVisitedContentUrl() || '');

						let url = (window.country === 'sk' || window.country === 'hu')
							? `${baseUrl}/social-login/social-sign/sign?social_provider_key=${provider}&success_login_url=${encodedReturnUrl}&failed_login_url=${encodedFailedReturnUrl}`
							: `${baseUrl}/users/${provider}/sign?url=${encodedReturnUrl}`;

                      url = referer ? `${url}&referer=${referer}` : url;

                      // Check if this is iOS WebView
                      const ua = navigator.userAgent;
                      const isIOS = /iPad|iPhone|iPod/.test(ua) && !window.MSStream;
                      const isWebView = isIOS && !/(Safari)/i.test(ua);

                      // Check if iOS version is 16
                      const iosVersion = ua.match(/OS (\d+)_/);
                      const isIOS16 = iosVersion && parseInt(iosVersion[1], 10) === 16;

                      // If it's iOS 16 WebView, show alert
                      if (isIOS && isWebView && isIOS16) {
                        window.alert('Please open this page in Safari to continue with authentication.');
                        return null;
                      }

                      // If it's iOS WebView (not iOS 16), modify the URL protocol
                      if (isIOS && isWebView) {
                        url = url.replace(/^https:/, 'x-safari-https:');
                      }

                      return url;
	                }

                    // Handle triggerLogin event for Google provider
                    if (data.type === 'triggerLogin' && data.payload?.provider === 'google') {
	                    handleSubscriptionData();

	                    window.location.href = generateSocialLoginUrl(data.payload?.provider);
                    }

                    // Handle triggerLogin event for Facebook provider
                    if (data.type === 'triggerLogin' && data.payload?.provider === 'facebook') {
	                    handleSubscriptionData();

                        window.location.href = generateSocialLoginUrl(data.payload?.provider);
                    }

                    // Handle login modal with normal email
                    if (data.payload?.email && data.payload.email.trim() !== '') {
	                    handleSubscriptionData();

	                    const loginModal = document.getElementById('react-login-modal');

                        if (loginModal) {
                            const prefillEmail = data.payload?.email;

                            if (prefillEmail) {
                                const loginModalEmailInput = document.querySelector('.input-group__input[type="email"]');

                                if (loginModalEmailInput) {
                                    setNativeValue(loginModalEmailInput, prefillEmail);
                                }
                            }

                            window.openReactLoginModal();
                        }
                    }

                    // Handle Packeta widget
                    if (data.type === "selectPickupPoint") {
                        const packetaApiKey = '5d225b9165f0c7c5';
                        const packetaOptions = { country: "cz", defaultCurrency: "CZK", language: "cs", view: "modal" };

                        window.Packeta.Widget.pick(packetaApiKey, (point) => {
							const iframe = document.getElementById('outside');
							if (point) {
								const message = JSON.stringify({ type: "pickupPointSelected", point });
                                iframe.contentWindow.postMessage(message, "*");
                            }
                        }, packetaOptions);
                    }

					// Handle step completed = scroll to top
					if (data.type === "stepCompleted") {
						const iframe = document.getElementById('outside');
						iframe.scrollIntoView({ block: 'start', behavior: 'smooth' });
					}
                } catch (error) {
                    console.error("Error processing message: ", error);
                }
            } else {

            }
        }, '*');

    });
</script>

<?php get_footer(); ?>
