<?php

/**
 * Template Name: Contact Page
 */

/**
 * The Departments with their data (name, responsible person, phone, email) to be shown
 * @var array
 */
$departments = get_field('department');

/**
 * The social media accounts (name, url)
 * @var array
 */
$social_media_platforms = get_field('social_media');

get_header();
?>

<main class="page-contact">

	<div class="container">

		<?php if (!get_field('hide_title')) : ?>

			<?php $pageTitle = new Heading(get_the_title(), 'h1', null, '700', 'noto-serif'); echo $pageTitle->render(); ?>

		<?php endif; ?>

		<?php if ($departments && !empty($departments)) : ?>

			<div class="page-contact__departments">

				<?php foreach ($departments as $department) : ?>

					<div class="page-contact__department-wrapper">

						<div class="page-contact__department-content-wrapper">

							<?php if ($department['department_name']) : ?>
								<span class="page-contact__department-name cta-link-tag"><?= $department['department_name']; ?></span>
							<?php endif; ?>

							<?php if ($department['responsible_person']) : ?>
								<span class="page-contact__department-person callout"><?= $department['responsible_person']; ?></span>
							<?php endif; ?>

							<?php if ($department['address']  && $department['gmaps_link']) : ?>
								<a href="<?= $department['gmaps_link']; ?>" target="_blank" rel="noopener nofollow" class="page-contact__department-address"><span class="callout"><?= $department['address']; ?></span></a>
							<?php endif; ?>

							<?php if ($department['phone_number']) : ?>
								<a href="<?= 'tel:' . $department['phone_number']; ?>" class="page-contact__department-phone"><span class="callout"><?= $department['phone_number']; ?></span></a>
							<?php endif; ?>

							<?php if ($department['email']) : ?>
								<a href="<?= 'mailto:' . $department['email']; ?>" target="_blank" rel="noopener" class="page-contact__department-email "><span class="callout"><?= $department['email']; ?></span></a>
							<?php endif; ?>

						</div>

					</div>

				<?php endforeach; ?>

			</div>

		<?php endif; ?>


		<?php if ($social_media_platforms && !empty($social_media_platforms)) : ?>
			<div class="page-contact__social-media-wrapper">

				<?php foreach ($social_media_platforms as $platform) : ?>

					<?php if ($platform['platform'] && $platform['link']) : ?>

						<a href="<?= $platform['link']; ?>" target="_blank" rel="noopener nofollow" class="page-contact__social-media-platform cta-link-tag">
							<?= $platform['platform']; ?>
						</a>

					<?php endif; ?>

				<?php endforeach; ?>

			</div>

		<?php endif; ?>

	</div>

</main>

<?php get_footer(); ?>
