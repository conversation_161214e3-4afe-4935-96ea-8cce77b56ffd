<?php

/**
 * Template Name: Blog
 */

get_header();

$paged = get_query_var('paged') ?: 1;

$query = new WP_Query([
    'post_type' => 'post',
    'posts_per_page' => get_option('posts_per_page'),
    'orderby' => 'date',
    'order' => 'DESC',
    'paged' => $paged
]); ?>

<main class="page-blog">
	<div class="container">
		<div class="row">
			<div class="col-12">
				<div class="page-blog__articles-wrapper">
					<?php while ($query->have_posts()) : $query->the_post(); ?>
						<?php get_template_part('template-parts/article-card/index', null, array(
							'post_id'           => get_the_ID(),
							'article_card_type' => 'archive'
						)); ?>
					<?php endwhile; ?>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-12">
				<?php get_template_part('template-parts/pagination/index', null, array(
					'posts_found' => $query->found_posts,
					'paged' => $paged,
					'max_num_pages' => $query->max_num_pages
				)); ?>
			</div>
		</div>
	</div>
</main>

<?php get_footer(); ?>
