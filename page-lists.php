<?php

/**
 * Template Name: Lists Page
 */

get_header();

$type = get_field('type');

$specials_tax = get_field('specials_tag', 'option');

$paged1 = isset($_GET['paged1']) ? (int) $_GET['paged1'] : 1;

if ($type == 'lists') {
	$args = array(
		'posts_per_page' => 9,
		'paged'			 => $paged1,
		'post_type' 	 => 'list-group',
		'post_status' 	 => 'publish',
	);
} else {
	$args = array(
		'posts_per_page' => 6,
		'paged'			 => $paged1,
		'post_type' 	 => 'post',
		'post_status' 	 => 'publish',
		'tax_query'			=> array(
			array(
				'taxonomy'	=> CATEGORY_TYPE,
				'field'		=> 'term_id',
				'terms'		=> $specials_tax?->term_id
			),
		),
	);
}

$query = new WP_Query($args);

$featured_posts = get_field($type == 'lists' ? 'featured_lists' : 'featured_specials');

?>

<main class="page-lists">

	<div class="page-lists__featured">

		<div class="container">

			<p class="page-lists__other-title"><?= esc_html__('Other Forbes lists', 'FORBES') ?></p>

			<div class="page-lists__featured-wrapper">
				<?php foreach ($featured_posts as $item) : ?>

					<div class="article-card">
						<div class="article-card__image-wrapper">
							<a href="<?= $item['redirect_url']; ?>">
								<?php if ($item['custom_image']) : ?>
									<img class="article-card__image" src="<?= wp_get_attachment_image_url($item['custom_image'], 'medium', false); ?> " alt="">
								<?php endif; ?>
							</a>
							<div class="article-card__tag-wrapper article-card__tag-wrapper--image">
								<?php
									$tag = new Tag(
										text: $type == 'lists' ? esc_html__('Top Lists', 'FORBES') : esc_html__('Top Specials', 'FORBES'),
										hasBackground: true
									);
									echo $tag->render();
								?>
							</div>

						</div>
						<a href="<?= $item['redirect_url'] ?: get_permalink($item['item']); ?>" class="article-card__title">
							<h3 class="article-card__title">
								<?= $item['display_name'] ?: get_the_title($item['item']); ?>
							</h3>
						</a>
						<?php if ($item['date']) : ?>
							<span class="article-card__date"><?= esc_html__('Updated', 'FORBES') ?> <?= $item['date']; ?></span>
						<?php endif; ?>
					</div>

				<?php endforeach; ?>
			</div>
		</div>
	</div>

	<div class="page-lists__other">

		<div class="container">

			<?php if ($query->have_posts()) : ?>
				<div class="page-lists__articles-wrapper">

					<?php while ($query->have_posts()) : $query->the_post(); ?>

						<?php $years = get_field('years'); ?>

						<?php if (!empty($years)) : ?>

							<?php $first_link = $years[0]['redirect_url'] ?: get_permalink($years[0]['list']);?>

							<div class="article-card">
								<div class="article-card__image-wrapper">

									<a href="<?= $first_link; ?>">
										<img class="article-card__image" src="<?= get_the_post_thumbnail_url(get_the_ID(), 'medium') ?> " alt="">
									</a>
									<?php if (frontend_get_primary_tag(get_the_ID())) : ?>
										<a class="article-card__category cta-link-tag after-icon after-icon--line">

											<?= frontend_get_primary_tag(get_the_ID())->name; ?>

										</a>
									<?php endif; ?>
								</div>

								<a href="<?= $first_link; ?>" class="article-card__title"><?= get_the_title(); ?></a>

								<p class="article-card__description"><?= get_the_excerpt(); ?></p>

								<?php if ($type == 'lists') : ?>

									<?php
										$firstButton = new Button($years[0]['button_text'] ?: get_the_title($years[0]['list']), $first_link, 'medium', 'primary', false, false, '', '');
										echo $firstButton->render();
									?>

									<?php if (count($years) > 1) : ?>

										<select class="button button--secondary button--medium page-lists__other-year" data-key="<?= $query->current_post ?>">
											<option disabled selected hidden value="" class="after-icon after-icon--line"><?= esc_html__('Other years', 'FORBES') ?></option>

											<?php foreach ($years as $key => $year) : ?>

												<?php if( $key !== 0 ):?>

													<option data-link="<?= $year['redirect_url'] ?: get_permalink($year['list']) ?>" class=""><?= $year['button_text'] ?: get_the_title($year['list']); ?></option>

												<?php endif;?>

											<?php endforeach; ?>

										</select>

									<?php endif; ?>

								<?php endif; ?>
							</div>

						<?php endif; ?>

					<?php endwhile; ?>

				</div>

			<?php endif; ?>
		</div>

		<?php if ($query->found_posts > 9) : ?>

			<div class="container">

				<?php get_template_part('template-parts/pagination/index', null, ['posts_found' => $query->found_posts, 'paged' => $paged1, 'max_num_pages' => $query->max_num_pages, 'is_single_magazine' => true, 'pagination_string' => 'paged1']) ?>

			</div>

		<?php endif; ?>
	</div>

</main>

<?php get_footer(); ?>
