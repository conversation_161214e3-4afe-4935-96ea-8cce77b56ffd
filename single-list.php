<?php

// Get the template part data for the current post
$template_part_data = \ListRouting\ListRouter::get_template_part_data(get_the_ID());

// Extract the item, is_sub_list, and chosen_list from the template part data
$item = $template_part_data['args']['item'] ?? false;
$is_sub_list = $template_part_data['args']['is_sub_list'] ?? false;
$chosen_list = $template_part_data['args']['chosen_list'] ?? null;

// Determine the post name to use for SEO title and Open Graph title
$postName = $item['title'] ?? $chosen_list['title'] ?? false;
if ($postName) {
    // Set the SEO title using the post name
    add_filter('wpseo_title', static fn($title) => "{$postName} - {$title}", 15);

    // Determine the meta description to use for SEO and Open Graph
    $metaDescription = $item['meta_description'] ?? $chosen_list['description'] ?? \Lists\ListData::get_legacy_meta_description(trim($_SERVER['REQUEST_URI'], '/'));
    if ($metaDescription) {
        // Set the SEO meta description
        add_filter('wpseo_metadesc', static fn($desc) => $metaDescription, 15);
        // Set the Open Graph description
        add_filter('wpseo_opengraph_desc', static fn($desc) => $metaDescription, 15);
    }

    // Set the Open Graph title using the post name
    add_filter('wpseo_opengraph_title', static fn($title) => "{$postName} - {$title}", 15);
    // Set the Open Graph URL
    add_filter('wpseo_opengraph_url', static fn($url) => sprintf('https://%s%s', DOMAIN, $_SERVER['REQUEST_URI']), 15);

    // Determine the correct image to use for Open Graph
    if ($item && !empty($item['image'])) {
        // Use the image from the item if available
        $imageUrl = $item['image'];
    } elseif ($chosen_list && !empty($chosen_list['list_image'])) {
        // Use the list image from the chosen list if the item does not have an image
        $imageUrl = wp_get_attachment_url($chosen_list['list_image']);
    } else {
        // If no image is available, set imageUrl to an empty string
        $imageUrl = '';
    }

    // If an image URL is available, set the Open Graph image
    if ($imageUrl) {
        add_filter('wpseo_opengraph_image', static fn() => $imageUrl, 15);
    }

    // Modify the Yoast SEO presenters
    add_action(
        'wpseo_frontend_presenters',
        static fn($presenters) => array_filter($presenters, static function ($presenter) use ($imageUrl) {
            // Remove canonical links on lists pages
            if ($presenter instanceof \Yoast\WP\SEO\Presenters\Canonical_Presenter) {
                return false;
            }

            // Hide the og:image meta tag if the image URL is not available
            if (!$imageUrl && $presenter instanceof \Yoast\WP\SEO\Presenters\Open_Graph\Image_Presenter) {
                return false;
            }

            return true;
        })
    );
}

get_header();

get_template_part($template_part_data['path'], null, $template_part_data['args']);

get_footer();
?>
