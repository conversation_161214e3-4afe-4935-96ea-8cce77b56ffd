<?php

get_template_part('template-parts/header/header');
?>


<?php
/*
Setting up the leaderboard/branding ad containers based on locale and admin settings
*/

?>
<?php
if ( ! is_404()) : ?>

	<?php
	$branding = true;
	if ('sk' === COUNTRY && ! get_field('enable_branding')) {
		$branding = false;
	}
	?>

	<?php
	if ($branding) : ?>

		<!-- Google Ad Manager Branding -->
		<div class="gam-branding">

			<div class="gam-branding__placeholder">
				<div class="gam-branding__placeholder-inner"></div>
			</div>

			<div class="gam-branding__ad-container<?= is_admin_bar_showing() ? ' admin-bar' : ''; ?>">

				<a href="#"
				   class="gam-branding__top gam-branding__top--a<?= is_admin_bar_showing() ? ' admin-bar' : ''; ?>"
				   target="_blank"
				   aria-label="Forbes Branding Ad Top A"></a>
				<a href="#"
				   class="gam-branding__top gam-branding__top--b<?= is_admin_bar_showing() ? ' admin-bar' : ''; ?>"
				   target="_blank"
				   aria-label="Forbes Branding Ad Top B"></a>
				<a href="#"
				   class="gam-branding__top gam-branding__top--c<?= is_admin_bar_showing() ? ' admin-bar' : ''; ?>"
				   target="_blank"
				   aria-label="Forbes Branding Ad Top C"></a>
				<a href="#" class="gam-branding__left<?= is_admin_bar_showing() ? ' admin-bar' : ''; ?>" target="_blank"
				   aria-label="Forbes Branding Ad Left"></a>
				<a href="#" class="gam-branding__right<?= is_admin_bar_showing() ? ' admin-bar' : ''; ?>"
				   target="_blank"
				   aria-label="Forbes Branding Ad Right"></a>

			</div>

		</div>

	<?php
	endif; ?>

	<?php
	$hide = new HideAdsLoc\HideAds(get_the_ID());
	$hide = $hide->get_hide();

	if ( ! (is_single() && $hide) && ! is_404()) {
		$isHome = is_page('home') || is_front_page();
		if (COUNTRY === 'hu' && $isHome) {
			get_template_part('template-parts/leaderboard-ad/index', null, array('page' => 'home'));
		} elseif (COUNTRY === 'hu' && ! $isHome) {
			get_template_part('template-parts/leaderboard-ad/index');
		} else {
			get_template_part('template-parts/leaderboard-ad/index');
		}
	}
	?>

<?php
endif; ?>

<?php
/**
 * Get the URL of the print page from the option.
 * @var string|null $print_page URL of the print page or null if not set.
 */

global $print_page;
$print_page = get_field('print_page_link', 'option') ?? null;

/**
 * Get the navigation settings array from the option.
 * @var array $navigation Array containing navigation settings.
 */
$navigation = get_field('navigation', 'option');

/**
 * Extract the URL of the subscription page from the navigation settings array.
 * @var string|null $subscription_link URL of the subscription page or null if not set.
 */
$subscription_link = $navigation['subscription_link'] ?? null;

$renewal_link = $navigation['renewal_link'] ?? null;

/**
 * Extract the URL of the authentication page from the navigation settings array.
 * @var string|null $auth_link URL of the authentication page or null if not set.
 */
$auth_link = $navigation['authentication_page_link'] ?? null;

/**
 * Extract the URL of the my account page from the navigation settings array.
 * @var string|null $my_account_link URL of the my account page or null if not set.
 */
$my_account_link = $navigation['my_account_page_link'] ?? null;

/**
 * Get the URL of the subscription activation page from the option.
 * @var string|null $sub_activation_page_link URL of the subscription activation page or null if not set.
 */
$sub_activation_page_link = get_field('sub_activation_page_link', 'option') ?? null;

/**
 * Extract the slug (last segment of the URL path) of the subscription page.
 * @var string $subscription_page_slug Slug of the subscription page.
 */
global $subscription_page_slug;
$subscription_page_slug = basename(parse_url($subscription_link, PHP_URL_PATH));

global $renewal_page_slug;
$renewal_page_slug = basename(parse_url($renewal_link, PHP_URL_PATH));

/**
 * Extract the slug (last segment of the URL path) of the authentication page.
 * @var string $authentication_page_slug Slug of the authentication page.
 */
global $authentication_page_slug;
$authentication_page_slug = basename(parse_url($auth_link, PHP_URL_PATH));

/**
 * Extract the slug (last segment of the URL path) of the my account page.
 * @var string $my_account_page_slug Slug of the my account page.
 */
global $my_account_page_slug;
$my_account_page_slug = basename(parse_url($my_account_link, PHP_URL_PATH));

/**
 * Extract the slug (last segment of the URL path) of the subscription activation page.
 * @var string $activate_sub_page_slug Slug of the subscription activation page.
 */
global $activate_sub_page_slug;
$activate_sub_page_slug = basename(parse_url($sub_activation_page_link, PHP_URL_PATH));

$printModalData     = get_field('print_page', 'option');
$premiumModalFields = get_field('premium_modal', 'option');

get_template_part('components/search/index');
get_template_part('components/navigation/index');
?>

<script>
	window.printPageLink = <?php echo json_encode($print_page) ?>;
	window.subscriptionPageSlug = <?php echo json_encode($subscription_page_slug) ?>;
	window.renewalPageSlug = <?php echo json_encode($renewal_page_slug) ?>;
	window.renewalPageLink = <?php echo json_encode($renewal_link) ?>;
	window.activateSubPageSlug = <?php echo json_encode($activate_sub_page_slug) ?>;
	window.authenticationPageSlug = <?php echo json_encode($authentication_page_slug) ?>;
	window.myAccountPageSlug = <?php echo json_encode($my_account_page_slug) ?>;
	window.printModalData = <?php echo json_encode($printModalData); ?>;
	window.premiumModalFields = <?php echo json_encode($premiumModalFields); ?>;
</script>

