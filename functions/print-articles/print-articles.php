<?php

 /**
 * Function to log debug information.
 *
 * @param string $info The debug information to log.
 */
function log_debug_info($info) {
    $enable_debug = true; // Set this to false to disable debug logging
    $log_to_file = true;  // Set this to true to enable logging to file
    $debug_file = ABSPATH . 'wp-content/debug.log'; // Define the path to the debug.log file

    if ($enable_debug) {
        echo '<p>' . $info . '</p>';
    }

    if ($log_to_file) {
        error_log($info . PHP_EOL, 3, $debug_file);
    }
}

/**
 * Add noindex, nofollow for posts in the 'print_category'.
 */
function yoast_print_category_no_index( $string = '' ) {
    if ( is_single() ) {
        $category_id = get_field('print_category', 'option');
        // log_debug_info('category_id: ' . $category_id);
        if ( $category_id && has_category( $category_id ) ) {
            // log_debug_info('Post has print_category: ' . $category_id);
            $string = 'noindex, nofollow';
        } else {
            // log_debug_info('Post does not have print_category: ' . $category_id);
        }
    }
    return $string;
}

add_filter( 'wpseo_robots', 'yoast_print_category_no_index', 999 );