<?php

namespace ForbesEcommerceSync;

if ( ! defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Register Taxonomy Category
class EcommerceCategoryTaxonomy extends EcommerceTaxonomy
{

    protected function set_taxonomy(): void
    {
        $this->taxonomy = 'ecommerce_category';
    }

    protected function set_post_type(): void
    {
        $this->postType = ['ecommerce_product'];
    }

    protected function set_labels(): void
    {
        $this->labels = array(
            'name'                       => _x('Categories', 'Taxonomy General Name', 'FORBES'),
            'singular_name'              => _x('Category', 'Taxonomy Singular Name', 'FORBES'),
            'menu_name'                  => _x('Categories', 'Admin Menu text', 'FORBES'),
            'all_items'                  => __('All Categories', 'FORBES'),
            'parent_item'                => __('Parent Category', 'FORBES'),
            'parent_item_colon'          => __('Parent Category:', 'FORBES'),
            'new_item_name'              => __('New Category Name', 'FORBES'),
            'add_new_item'               => __('Add New Category', 'FORBES'),
            'edit_item'                  => __('Edit Category', 'FORBES'),
            'update_item'                => __('Update Category', 'FORBES'),
            'view_item'                  => __('View Category', 'FORBES'),
            'separate_items_with_commas' => __('Separate categories with commas', 'FORBES'),
            'add_or_remove_items'        => __('Add or remove categories', 'FORBES'),
            'choose_from_most_used'      => __('Choose from the most used', 'FORBES'),
            'popular_items'              => __('Popular Categories', 'FORBES'),
            'search_items'               => __('Search Categories', 'FORBES'),
            'not_found'                  => __('Not Found', 'FORBES'),
            'no_terms'                   => __('No categories', 'FORBES'),
            'items_list'                 => __('Categories list', 'FORBES'),
            'items_list_navigation'      => __('Categories list navigation', 'FORBES'),
        );
    }

    protected function set_args(): void
    {
        $this->args = array(
            'labels'            => $this->labels,
            'hierarchical'      => true,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
			'rewrite'           => array( 'slug' => 'market/kategorie' ),
        );
    }

    protected function set_fields(): void
    {
        $this->fields = [
            'key'      => 'group_ecommerce_category',
            'title'    => __('Category Fields', 'FORBES'),
            'fields'   => [
                [
                    'key'   => 'category_remote_id',
                    'label' => __('Category ID', 'FORBES'),
                    'name'  => 'remote_id',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'category_attributes',
                    'label' => __('Category Attributes', 'FORBES'),
                    'name'  => 'category_attributes',
                    'type'  => 'textarea'
                ]
            ],
            'location' => [
                [
                    [
                        'param'    => 'taxonomy',
                        'operator' => '==',
                        'value'    => 'ecommerce_category',
                    ],
                ],
            ],
        ];
    }
}

new EcommerceCategoryTaxonomy();
