<?php

namespace ForbesEcommerceSync;

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly
}

class EcommerceVendorTaxonomy extends EcommerceTaxonomy
{
	protected function set_taxonomy(): void
	{
		$this->taxonomy = 'ecommerce_vendor';
	}

	protected function set_post_type(): void
	{
		$this->postType = ['ecommerce_product'];
	}

	protected function set_labels(): void
	{
		$this->labels = array(
			'name' => _x('Vendors', 'Taxonomy General Name', 'FORBES'),
			'singular_name' => _x('Vendor', 'Taxonomy Singular Name', 'FORBES'),
			'menu_name' => _x('Vendors', 'Admin Menu text', 'FORBES'),
			'all_items' => __('All Vendors', 'FORBES'),
			'parent_item' => __('Parent Vendor', 'FORBES'),
			'parent_item_colon' => __('Parent Vendor:', 'FORBES'),
			'new_item_name' => __('New Vendor Name', 'FORBES'),
			'add_new_item' => __('Add New Vendor', 'FORBES'),
			'edit_item' => __('Edit Vendor', 'FORBES'),
			'update_item' => __('Update Vendor', 'FORBES'),
			'view_item' => __('View Vendor', 'FORBES'),
			'separate_items_with_commas' => __('Separate vendors with commas', 'FORBES'),
			'add_or_remove_items' => __('Add or remove vendors', 'FORBES'),
			'choose_from_most_used' => __('Choose from the most used', 'FORBES'),
			'popular_items' => __('Popular Vendors', 'FORBES'),
			'search_items' => __('Search Vendors', 'FORBES'),
			'not_found' => __('Not Found', 'FORBES'),
			'no_terms' => __('No vendors', 'FORBES'),
			'items_list' => __('Vendors list', 'FORBES'),
			'items_list_navigation' => __('Vendors list navigation', 'FORBES'),
		);
	}

	protected function set_args(): void
	{
		$this->args = array(
			'labels' => $this->labels,
			'hierarchical' => false,
			'public' => true,
			'show_ui' => true,
			'show_admin_column' => true,
			'show_in_nav_menus' => true,
			'show_tagcloud' => true,
			'rewrite' => array('slug' => 'market/vendor'),
		);
	}

	protected function set_fields(): void
	{
		$this->fields = [
			'key' => 'group_ecommerce_vendor',
			'title' => __('Vendor Fields', 'FORBES'),
			'fields' => [
				[
					'key' => 'vendor_remote_id',
					'label' => __('Vendor ID', 'FORBES'),
					'name' => 'remote_id',
					'type' => 'text',
				],
				[
					'key' => 'vendor_logo_url',
					'label' => __('Vendor Logo URL', 'FORBES'),
					'name' => 'logo_url',
					'type' => 'text',
				]
			],
			'location' => [
				[
					[
						'param' => 'taxonomy',
						'operator' => '==',
						'value' => 'ecommerce_vendor',
					],
				],
			],
		];
	}
}

new EcommerceVendorTaxonomy();
