<?php

namespace ForbesEcommerceSync;

if ( ! defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Register Gender Taxonomy
class EcommerceGenderTaxonomy extends EcommerceTaxonomy
{

    protected function set_taxonomy(): void
    {
        $this->taxonomy = 'ecommerce_gender';
    }

    protected function set_post_type(): void
    {
        $this->postType = ['ecommerce_product'];
    }

    protected function set_labels(): void
    {
        $this->labels = array(
            'name'                       => _x('Gender', 'Taxonomy General Name', 'FORBES'),
            'singular_name'              => _x('Gender', 'Taxonomy Singular Name', 'FORBES'),
            'menu_name'                  => _x('Gender', 'Admin Menu text', 'FORBES'),
            'all_items'                  => __('All Gender', 'FORBES'),
            'parent_item'                => __('Parent Gender', 'FORBES'),
            'parent_item_colon'          => __('Parent Gender:', 'FORBES'),
            'new_item_name'              => __('New Gender Name', 'FORBES'),
            'add_new_item'               => __('Add New Gender', 'FORBES'),
            'edit_item'                  => __('Edit Gender', 'FORBES'),
            'update_item'                => __('Update Gender', 'FORBES'),
            'view_item'                  => __('View Gender', 'FORBES'),
            'separate_items_with_commas' => __('Separate categories with commas', 'FORBES'),
            'add_or_remove_items'        => __('Add or remove categories', 'FORBES'),
            'choose_from_most_used'      => __('Choose from the most used', 'FORBES'),
            'popular_items'              => __('Popular Gender', 'FORBES'),
            'search_items'               => __('Search Gender', 'FORBES'),
            'not_found'                  => __('Not Found', 'FORBES'),
            'no_terms'                   => __('No categories', 'FORBES'),
            'items_list'                 => __('Gender list', 'FORBES'),
            'items_list_navigation'      => __('Gender list navigation', 'FORBES'),
        );
    }

    protected function set_args(): void
    {
        $this->args = array(
            'labels'             => $this->labels,
            'hierarchical'       => false,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_admin_column'  => true,
            'show_in_nav_menus'  => true,
            'show_tagcloud'      => true,
			'rewrite'            => array( 'slug' => 'market/pohlavi' ),
        );
    }

    protected function set_fields(): void
    {
        $this->fields = [
            'key'      => 'group_ecommerce_gender',
            'title'    => __('Gender Fields', 'FORBES'),
            'fields'   => [
                [
                    'key'   => 'gender_remote_id',
                    'label' => __('Gender ID', 'FORBES'),
                    'name'  => 'remote_id',
                    'type'  => 'text'
                ],
            ],
            'location' => [
                [
                    [
                        'param'    => 'taxonomy',
                        'operator' => '==',
                        'value'    => 'ecommerce_gender',
                    ],
                ],
            ],
        ];
    }
}

new EcommerceGenderTaxonomy();
