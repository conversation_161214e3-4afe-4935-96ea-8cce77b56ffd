<?php

namespace ForbesEcommerceSync;

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly
}


class EcommerceBrandTaxonomy extends EcommerceTaxonomy
{

	// Register Taxonomy Brand
	protected function set_taxonomy(): void
	{
		$this->taxonomy = 'ecommerce_brand';
	}

	protected function set_post_type(): void
	{
		$this->postType = ['ecommerce_product'];
	}

	protected function set_labels(): void
	{
		$this->labels = array(
			'name' => _x('Brands', 'Taxonomy General Name', 'FORBES'),
			'singular_name' => _x('Brand', 'Taxonomy Singular Name', 'FORBES'),
			'menu_name' => _x('Brands', 'Admin Menu text', 'FORBES'),
			'all_items' => __('All Brands', 'FORBES'),
			'parent_item' => __('Parent Brand', 'FORBES'),
			'parent_item_colon' => __('Parent Brand:', 'FORBES'),
			'new_item_name' => __('New Brand Name', 'FORBES'),
			'add_new_item' => __('Add New Brand', 'FORBES'),
			'edit_item' => __('Edit Brand', 'FORBES'),
			'update_item' => __('Update Brand', 'FORBES'),
			'view_item' => __('View Brand', 'FORBES'),
			'separate_items_with_commas' => __('Separate brands with commas', 'FORBES'),
			'add_or_remove_items' => __('Add or remove brands', 'FORBES'),
			'choose_from_most_used' => __('Choose from the most used', 'FORBES'),
			'popular_items' => __('Popular Brands', 'FORBES'),
			'search_items' => __('Search Brands', 'FORBES'),
			'not_found' => __('Not Found', 'FORBES'),
			'no_terms' => __('No brands', 'FORBES'),
			'items_list' => __('Brands list', 'FORBES'),
			'items_list_navigation' => __('Brands list navigation', 'FORBES'),
		);
	}

	protected function set_args(): void
	{
		$this->args = array(
			'labels' => $this->labels,
			'hierarchical' => false,
			'public' => true,
			'show_ui' => true,
			'show_admin_column' => true,
			'show_in_nav_menus' => true,
			'show_tagcloud' => true,
			'rewrite' => array('slug' => 'market/znacka'),
		);
	}

	protected function set_fields(): void
	{
		$this->fields = [
			'key' => 'group_ecommerce_brand',
			'title' => __('Brand Fields', 'FORBES'),
			'fields' => [
				[
					'key' => 'brand_remote_id',
					'label' => __('Brand ID', 'FORBES'),
					'name' => 'remote_id',
					'type' => 'text',
				],
				[
					'key' => 'brand_logo_url',
					'label' => __('Brand Logo URL', 'FORBES'),
					'name' => 'logo_url',
					'type' => 'text',
				]
			],
			'location' => [
				[
					[
						'param' => 'taxonomy',
						'operator' => '==',
						'value' => 'ecommerce_brand',
					],
				],
			],
		];
	}
}

new EcommerceBrandTaxonomy();
