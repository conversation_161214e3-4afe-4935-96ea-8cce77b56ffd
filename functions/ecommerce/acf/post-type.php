<?php

namespace ForbesEcommerceSync;

abstract class EcommercePostType
{
    protected $postType;
    protected $labels;
    protected $args;
    protected $fields;

    public function __construct()
    {
        add_action('init', [$this, 'register_post_type']);
        add_action('init', [$this, 'add_fields']);
    }

    public function register_post_type(): void
    {
        $this->set_post_type();
        $this->set_labels();
        $this->set_args();

        register_post_type($this->postType, $this->args);
    }

    abstract protected function set_post_type();

    abstract protected function set_labels();

    abstract protected function set_args();

    public function add_fields(): void
    {
        if (function_exists('acf_add_local_field_group')) {
            $this->set_fields();
            acf_add_local_field_group($this->fields);
        }
    }

    abstract protected function set_fields();
}
