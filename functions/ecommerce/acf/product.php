<?php

namespace ForbesEcommerceSync;

if ( ! defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Register Post Type Product
class EcommerceProductPostType extends EcommercePostType
{

    protected function set_post_type()
    {
        $this->postType = 'ecommerce_product';
    }

    protected function set_labels(): void
    {
        $this->labels = array(
            'name'                  => _x('Ecommerce Products', 'Post Type General Name', 'FORBES'),
            'singular_name'         => _x('Ecommerce Product', 'Post Type Singular Name', 'FORBES'),
            'menu_name'             => _x('Ecommerce Products', 'Admin Menu text', 'FORBES'),
            'name_admin_bar'        => _x('Ecommerce Product', 'Add New on Toolbar', 'FORBES'),
            'archives'              => __('Product Archives', 'FORBES'),
            'attributes'            => __('Product Attributes', 'FORBES'),
            'parent_item_colon'     => __('Parent Product:', 'FORBES'),
            'all_items'             => __('All Products', 'FORBES'),
            'add_new_item'          => __('Add New Product', 'FORBES'),
            'add_new'               => __('Add New', 'FORBES'),
            'new_item'              => __('New Product', 'FORBES'),
            'edit_item'             => __('Edit Product', 'FORBES'),
            'update_item'           => __('Update Product', 'FORBES'),
            'view_item'             => __('View Product', 'FORBES'),
            'view_items'            => __('View Products', 'FORBES'),
            'search_items'          => __('Search Product', 'FORBES'),
            'not_found'             => __('Not found', 'FORBES'),
            'not_found_in_trash'    => __('Not found in Trash', 'FORBES'),
            'featured_image'        => __('Featured Image', 'FORBES'),
            'set_featured_image'    => __('Set featured image', 'FORBES'),
            'remove_featured_image' => __('Remove featured image', 'FORBES'),
            'use_featured_image'    => __('Use as featured image', 'FORBES'),
        );
    }

    protected function set_args()
    {
        $this->args = array(
            'label'               => __('Ecommerce Product', 'FORBES'),
            'description'         => __('Ecommerce Products', 'FORBES'),
            'labels'              => $this->labels,
            'menu_icon'           => 'dashicons-cart',
            'supports'            => array('title', 'editor', 'thumbnail', 'revisions', 'custom-fields'),
            'taxonomies'          => array('ecommerce_brand', 'ecommerce_category', 'ecommerce_gender'),
            'hierarchical'        => false,
            'public'              => true,
            'show_ui'             => true,
            'show_in_menu'        => true,
            'menu_position'       => 5,
            'show_in_admin_bar'   => true,
            'show_in_nav_menus'   => true,
            'can_export'          => true,
            'has_archive'         => false,
            'exclude_from_search' => false,
            'publicly_queryable'  => true,
            'capability_type'     => 'post',
			'rewrite'             => array( 'slug' => 'market/produkt' ),
        );
    }

    protected function set_fields()
    {
        $this->fields = [
            'key'      => 'group_ecommerce_product',
            'title'    => __('Product Fields', 'FORBES'),
            'fields'   => [
                [
                    'key'   => 'product_remote_id',
                    'label' => __('Product ID', 'FORBES'),
                    'name'  => 'remote_id',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'product_url',
                    'label' => __('Product URL', 'FORBES'),
                    'name'  => 'product_url',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'product_url_domain',
                    'label' => __('Product URL Domain', 'FORBES'),
                    'name'  => 'product_url_domain',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'product_availability',
                    'label' => __('Product Availability', 'FORBES'),
                    'name'  => 'product_availability',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'product_condition',
                    'label' => __('Product Condition', 'FORBES'),
                    'name'  => 'product_condition',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'product_price_amount',
                    'label' => __('Product Price Amount (Formatted)', 'FORBES'),
                    'name'  => 'product_price_amount',
                    'type'  => 'text'
                ],
				[
                    'key'   => 'product_price_amount_num',
                    'label' => __('Product Price Amount (Number)', 'FORBES'),
                    'name'  => 'product_price_amount_num',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'product_price_currency',
                    'label' => __('Product Price Currency', 'FORBES'),
                    'name'  => 'product_price_currency',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'product_discounted_price_amount',
                    'label' => __('Product Discounted Price Amount', 'FORBES'),
                    'name'  => 'product_discounted_price_amount',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'product_discounted_price_currency',
                    'label' => __('Product Discounted Price Currency', 'FORBES'),
                    'name'  => 'product_discounted_price_currency',
                    'type'  => 'text'
                ],
                [
                    'key'   => 'product_is_link_visible_on_list',
                    'label' => __('Product Is Link Visible On List', 'FORBES'),
                    'name'  => 'product_is_link_visible_on_list',
                    'type'  => 'true_false',
                ],
                [
                    'key'   => 'product_labels',
                    'label' => __('Product Labels', 'FORBES'),
                    'name'  => 'product_labels',
                    'type'  => 'textarea',
                ],
				[
					'key'   => 'forbes_raw_data',
					'label' => __('Raw Data', 'FORBES'),
					'name'  => 'forbes_raw_data',
					'type'  => 'textarea',
				],
                [
                    'key'        => 'product_images',
                    'label'      => __('Product Images', 'FORBES'),
                    'name'       => 'product_images',
                    'type'       => 'repeater',
                    'sub_fields' => [
                        [
                            'key'   => 'product_image_url',
                            'label' => __('Product Image URL', 'FORBES'),
                            'name'  => 'url',
                            'type'  => 'text',
                        ],
                        [
                            'key'   => 'product_image_type',
                            'label' => __('Product Image Type', 'FORBES'),
                            'name'  => 'type',
                            'type'  => 'text',
                        ],
                    ],
                ],
				[
					'key'   => 'product_attributes',
                    'label' => __('Product Attributes', 'FORBES'),
                    'name'  => 'product_attributes',
                    'type'       => 'repeater',
                    'sub_fields' => [
                        [
                            'key'   => 'product_attribute_name',
                            'label' => __('Name', 'FORBES'),
                            'name'  => 'name',
                            'type'  => 'text',
                        ],
                        [
                            'key'   => 'product_attribute_value',
                            'label' => __('Value', 'FORBES'),
                            'name'  => 'value',
                            'type'  => 'text',
                        ],
                    ],
				],
                [
                    'key'   => 'product_ean',
                    'label' => __('Product EAN', 'FORBES'),
                    'name'  => 'product_ean',
                    'type'  => 'text',
                ]
            ],
            'location' => [
                [
                    [
                        'param'    => 'post_type',
                        'operator' => '==',
                        'value'    => 'ecommerce_product',
                    ],
                ],
            ],
        ];
    }
}

new EcommerceProductPostType();
