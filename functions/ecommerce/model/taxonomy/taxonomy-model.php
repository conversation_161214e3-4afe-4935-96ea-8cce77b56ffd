<?php

namespace ForbesEcommerceSync;

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

abstract class TaxonomyModel
{
    public int $remoteId;
    public int $id = 0;
    public string $name;
    public string $description = '';
    public string $url;
    public string $slug;
    public ?int $parent = null;

    public static abstract function getTaxonomy(): string;

	protected function doSave(): void
	{
	}

	protected function doLoad(): void
	{
	}

    private static function fromWPTerm(\WP_Term $term): static
    {
        $model = new static();
        $model->id = $term->term_id;
        $model->remoteId = get_term_meta($term->term_id, 'remote_id', true);
        $model->name = $term->name;
        $model->description = $term->description;
        $model->url = get_term_link($term->term_id);
        $model->slug = $term->slug;
		$model->parent = $term->parent;

		$model->doLoad();

        return $model;
    }

    private static function fromWPTerms(array $terms): array
    {
        return array_map(fn (\WP_Term $term): TaxonomyModel => static::fromWPTerm($term), $terms);
    }

    private static function fetch(array $wpQueryParams = []): array
    {
        $wpQueryParams['hide_empty'] = false;
        $wpQueryParams['taxonomy'] = static::getTaxonomy();

        $query = new \WP_Term_Query($wpQueryParams);
        $terms = $query->get_terms();

        return static::fromWPTerms($terms);
    }

    private static function fetchOne(array $wpQueryParams): ?static
    {
        $models = static::fetch($wpQueryParams);

        if (empty($models)) {
            return null;
        }

        return $models[0];
    }

    public static function getByRemoteId(int $remoteId): ?static
    {
        return static::fetchOne([
            'meta_query' => [
                [
                    'key' => 'remote_id',
                    'value' => $remoteId,
                ],
            ],
        ]);
    }

    public static function getById(int $id): ?static
    {
        return static::fetchOne([
            'include' => [$id],
        ]);
    }

    public static function getBySlug(string $slug): ?static
    {
        return static::fetchOne([
            'slug' => $slug,
        ]);
    }

    /** @return static[] */
    public static function getAll(): array
    {
        return static::fetch();
    }

    /** @return static[] */
    public static function getTopLevel(): array
    {
        return static::fetch([
            'parent' => 0,
        ]);
    }

    /** @return static[] */
    public static function getAllByPostId(int $postId): array
    {
        return static::fetch([
            'object_ids' => [$postId],
        ]);
    }

    public static function getOneByPostId(int $postId): ?static
    {
        $models = static::getAllByPostId($postId);

        if (empty($models)) {
            return null;
        }

        return $models[0];
    }

    public final function save(): void
    {
        $params = [
            'cat_ID' => $this->id,
            'taxonomy' => static::getTaxonomy(),
            'cat_name' => $this->name,
            'category_description' => $this->description,
            'category_parent' => $this->parent ?? '',
        ];

        $idOrWpError = wp_insert_category($params);

        if ($idOrWpError instanceof WP_Error) {
            throw new \Exception($idOrWpError->get_error_message());
        }

        if ($idOrWpError === 0) {
            throw new \Exception('Failed to save the category.');
        }

        $this->id = $idOrWpError;
        $this->url = get_term_link($idOrWpError);
        $this->slug = get_term($idOrWpError)->slug;

        update_term_meta($this->id, 'remote_id', $this->remoteId);

	    $this->doSave();
    }
}
