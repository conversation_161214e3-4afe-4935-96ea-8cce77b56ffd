<?php

namespace ForbesEcommerceSync;

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly
}

class Vendor extends TaxonomyModel
{
	public string $logoUrl = '';

	public static function getTaxonomy(): string
	{
		return 'ecommerce_vendor';
	}

	protected function doSave(): void
	{
		update_term_meta($this->id, 'logo_url', $this->logoUrl);
	}

	protected function doLoad(): void
	{
		$this->logoUrl = get_term_meta($this->id, 'logo_url', true);
	}
}
