<?php

namespace ForbesEcommerceSync;

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly
}

class ProductQuery
{
	private int $perPage = -1;
	private int $page = 1;
	private ?int $id = null;
	private ?int $remoteId = null;
	private ?int $brandId = null;
	private ?int $vendorId = null;
	private ?int $categoryId = null;
	private ?int $genderId = null;
	private ?string $sortBy = null;
	private ?string $sortOrder = null;
	private array $excludeIds = [];
	private array $includeIds = [];
	private bool $includeDrafts = false;

	public function setPerPage(?int $perPage): self
	{
		$this->perPage = $perPage;
		return $this;
	}

	public function setPage(?int $page): self
	{
		$this->page = $page;
		return $this;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function setRemoteId(?int $remoteId): self
	{
		$this->remoteId = $remoteId;
		return $this;
	}

	public function setBrand(?Brand $brand): self
	{
		$this->brandId = $brand->id ?? null;
		return $this;
	}

	public function setVendor(?Vendor $vendor): self
	{
		$this->vendorId = $vendor->id ?? null;
		return $this;
	}

	public function setCategory(?Category $category): self
	{
		$this->categoryId = $category->id ?? null;
		return $this;
	}

	public function setGender(?Gender $gender): self
	{
		$this->genderId = $gender->id ?? null;
		return $this;
	}

	public function setBrandId(?int $brandId): self
	{
		$this->brandId = $brandId;
		return $this;
	}

	public function setVendorId(?int $vendorId): self
	{
		$this->vendorId = $vendorId;
		return $this;
	}

	public function setCategoryId(?int $categoryId): self
	{
		$this->categoryId = $categoryId;
		return $this;
	}

	public function setGenderId(?int $genderId): self
	{
		$this->genderId = $genderId;
		return $this;
	}

	public function setSortBy(?string $sortBy): self
	{
		$this->sortBy = $sortBy;
		return $this;
	}

	public function setSortOrder(?string $sortOrder): self
	{
		$this->sortOrder = $sortOrder;
		return $this;
	}

	public function addExcludeId(int $id): self
	{
		$this->excludeIds[] = $id;
		return $this;
	}

	public function setExcludeIds(array $ids): self
	{
		$this->excludeIds = $ids;
		return $this;
	}

	public function addIncludeId(int $id): self
	{
		$this->includeIds[] = $id;
		return $this;
	}

	public function setIncludeIds(array $ids): self
	{
		$this->includeIds = $ids;
		return $this;
	}

	public function setIncludeDrafts(bool $includeDrafts): self
	{
		$this->includeDrafts = $includeDrafts;
		return $this;
	}

	private function getPostStatus(): array
	{
		$status = ['publish'];

		if ($this->includeDrafts) {
			$status[] = 'draft';
		}

		return $status;
	}

	/** @return Product[] */
	public function fetchAll(): array
	{
		$args = [
			'post_type' => Product::getPostType(),
			'posts_per_page' => $this->perPage,
			'paged' => $this->page,
			'tax_query' => [],
			'meta_query' => [],
			'post__not_in' => $this->excludeIds,
			'post__in' => $this->includeIds,
			'post_status' => $this->getPostStatus(),
		];

		if ($this->id) {
			$args['p'] = $this->id;
		}

		if ($this->remoteId) {
			$args['meta_query'][] = [
				'key' => 'remote_id',
				'value' => $this->remoteId,
			];
		}

		if ($this->brandId) {
			$args['tax_query'][] = [
				'taxonomy' => Brand::getTaxonomy(),
				'field' => 'term_id',
				'terms' => $this->brandId,
			];
		}

		if ($this->vendorId) {
			$args['tax_query'][] = [
				'taxonomy' => Vendor::getTaxonomy(),
				'field' => 'term_id',
				'terms' => $this->vendorId,
			];
		}

		if ($this->categoryId) {
			$args['tax_query'][] = [
				'taxonomy' => Category::getTaxonomy(),
				'field' => 'term_id',
				'terms' => $this->categoryId,
			];
		}

		if ($this->genderId) {
			$args['tax_query'][] = [
				'tax_query' => [
					[
						'taxonomy' => Gender::getTaxonomy(),
						'field' => 'term_id',
						'terms' => $this->genderId,
					]
				]
			];
		}

		if ($this->sortBy) {
			switch ($this->sortBy) {
				case 'price_amount':
					$args['orderby'] = 'meta_value_num';
					$args['meta_key'] = 'product_price_amount_num';
					break;
                case 'rand':
                    $args['orderby'] = 'rand';
                    break;
			}

			if ($this->sortOrder) {
				$args['order'] = $this->sortOrder;
			}
		} else if (!empty($this->includeIds)) {
			$args['orderby'] = 'post__in';
		} else {
			// Default sort order
		}

		$query = new \WP_Query($args);

		return array_map(fn(\WP_Post $post): Product => Product::fromPost($post), $query->posts);
	}

	public function fetchOne(): ?Product
	{
		$this->perPage = 1;

		$products = $this->fetchAll();

		if (empty($products)) {
			return null;
		}

		return $products[0];
	}

	public function exists(): bool
	{
		return !!$this->fetchOne();
	}

	public function hasNextPage(): bool
	{
		$query = clone $this;
		$query->page = $this->page * $this->perPage + 1;
		$query->perPage = 1;

		return $query->exists();
	}

	public static function byId(int $id): ?Product
	{
		return (new self())
			->setIncludeDrafts(true)
			->setId($id)
			->fetchOne();
	}

	public static function byRemoteId(int $remoteId): ?Product
	{
		return (new self())
			->setIncludeDrafts(true)
			->setRemoteId($remoteId)
			->fetchOne();
	}

	/** @return Product[] */
	public static function byMultipleIds(array $ids): array
	{
		return (new self())
			->setIncludeDrafts(true)
			->setIncludeIds($ids)
			->fetchAll();
	}
}
