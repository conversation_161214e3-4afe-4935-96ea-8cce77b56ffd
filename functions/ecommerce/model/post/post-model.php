<?php

namespace ForbesEcommerceSync;

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

abstract class PostModel
{
    public int $id = 0;
    public int $remoteId;
    public string $title = '';
    public string $slug;
    public string $content;
    public string $permalink;
	public string $postStatus = 'publish';

    public abstract static function getPostType(): string;

    protected function doSave(): void
    {
    }

    protected function doLoad(): void
    {
    }

    public final function save(): void
    {
        $args = [
            'ID' => $this->id,
            'post_title' => $this->title,
			'post_status' => $this->postStatus,
            'post_type' => static::getPostType(),
            'post_content' => $this->content,
        ];

        if (isset($this->slug)) {
            $args['post_name'] = $this->slug;
        }

        $idOrWpError = wp_insert_post($args);

        if ($idOrWpError instanceof \WP_Error) {
            throw new \Exception($idOrWpError->get_error_message());
        }

        if ($idOrWpError === 0) {
            throw new \Exception('Failed to save the post.');
        }

        $this->id = $idOrWpError;
        $this->permalink = get_permalink($this->id);

        update_post_meta($this->id, 'remote_id', $this->remoteId);

        $this->doSave();
    }

    public static function fromPost(\WP_Post $post): static
    {
        $model = new static();
        $model->id = $post->ID;
        $model->remoteId = get_post_meta($post->ID, 'remote_id', true);
        $model->title = $post->post_title;
        $model->slug = $post->post_name;
        $model->content = $post->post_content;
        $model->permalink = get_permalink($post->ID);
		$model->postStatus = $post->post_status;

        $model->doLoad();

        return $model;
    }
}
