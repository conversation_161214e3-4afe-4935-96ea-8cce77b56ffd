<?php

namespace ForbesEcommerceSync;

if ( ! defined( 'ABSPATH' ) ) {
  exit; // Exit if accessed directly
}

class Product extends PostModel {
  public string $url = '';
  public string $urlDomain = '';
  public string $availability = '';
  public string $condition = '';
  public string $priceAmount = '';
  public string $priceAmountNum = '';
  public string $priceCurrency = '';
  public string $discountedPriceAmount = '';
  public string $discountedPriceCurrency = '';
  public string $isLinkVisibleOnList = '';
  public array $images = [];
  public array $attributes = [];
  public string $labels = '';

  /** @var Gender[] */
  public array $genders = [];

  /** @var Category[] */
  public array $categories = [];

  public ?Brand $brand = null;
  public ?Vendor $vendor = null;

  public static function getPostType(): string {
    return 'ecommerce_product';
  }

  public function getGenderIds(): array {
    return array_map(fn(Gender $gender): int => $gender->id, $this->genders);
  }

  public function getCategoryIds(): array {
    return array_map(fn(Category $category): int => $category->id, $this->categories);
  }

  public function getBrandIds(): array {
    return $this->brand ? [$this->brand->id] : [];
  }

  public function getVendorIds(): array {
	return $this->vendor ? [$this->vendor->id] : [];
  }

  public function addGender(Gender $gender): void {
    $this->genders[] = $gender;
  }

  public function addCategory(Category $category): void {
    $this->categories[] = $category;
  }

  protected function doSave(): void {
    update_field('product_url', $this->url, $this->id);
    update_field('product_url_domain', $this->urlDomain, $this->id);
    update_field('product_availability', $this->availability, $this->id);
    update_field('product_condition', $this->condition, $this->id);
    update_field('product_price_amount', $this->priceAmount, $this->id);
	update_field('product_price_amount_num', $this->priceAmountNum, $this->id);
    update_field('product_price_currency', $this->priceCurrency, $this->id);
    update_field('product_discounted_price_amount', $this->discountedPriceAmount, $this->id);
    update_field('product_discounted_price_currency', $this->discountedPriceCurrency, $this->id);
    update_field('product_is_link_visible_on_list', $this->isLinkVisibleOnList, $this->id);
    update_field('product_images', $this->images, $this->id);
	update_field('product_attributes', $this->attributes, $this->id);
	update_field('product_labels', $this->labels, $this->id);

    wp_set_object_terms($this->id, $this->getGenderIds(), Gender::getTaxonomy());
    wp_set_object_terms($this->id, $this->getBrandIds(), Brand::getTaxonomy());
    wp_set_object_terms($this->id, $this->getCategoryIds(), Category::getTaxonomy());
	wp_set_object_terms($this->id, $this->getVendorIds(), Vendor::getTaxonomy());
  }

  protected function doLoad(): void {
    $this->url = get_field('product_url', $this->id) ?? '';
    $this->urlDomain = get_field('product_url_domain', $this->id) ?? '';
    $this->availability = get_field('product_availability', $this->id) ?? '';
    $this->condition = get_field('product_condition', $this->id) ?? '';
    $this->priceAmount = get_field('product_price_amount', $this->id) ?? '';
	$this->priceAmountNum = get_field('product_price_amount_num', $this->id) ?? '';
    $this->priceCurrency = get_field('product_price_currency', $this->id) ?? '';
    $this->discountedPriceAmount = get_field('product_discounted_price_amount', $this->id) ?? '';
    $this->discountedPriceCurrency = get_field('product_discounted_price_currency', $this->id) ?? '';
    $this->isLinkVisibleOnList = get_field('product_is_link_visible_on_list', $this->id) ?? '';

	$images = get_field('product_images', $this->id);
	if (is_array($images)) {
		$this->images = $images;
	}

	$attributes = get_field('product_attributes', $this->id);
	if (is_array($attributes)) {
		$this->attributes = $attributes;
	}

    $this->brand = Brand::getOneByPostId($this->id);
	$this->categories = Category::getAllByPostId($this->id);
	$this->genders = Gender::getAllByPostId($this->id);
	$this->vendor = Vendor::getOneByPostId($this->id);
  }
}
