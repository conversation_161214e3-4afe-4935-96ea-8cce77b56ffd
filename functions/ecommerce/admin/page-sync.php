<?php

namespace ForbesEcommerceSync;

if (!defined('ABSPATH')) {
  exit; // Exit if accessed directly
}

if (is_admin()) {
  class PageSync
  {
    public $title;
    private $capability = 'administrator';
    public $slug = 'acc_customer_cards';
    private $position = 999;
    private $page_hook = null;
    private $page_active = false;

    public function menu(): void
    {
      $this->page_hook = add_submenu_page(
        'edit.php?post_type=ecommerce_product',
        $this->title,
        $this->title,
        $this->capability,
        $this->slug,
        [$this, 'page_content'],
        $this->position
      );
    }

    /**
     * The page content
     */
    public function page_content(): void
    { ?>

      <div class="wrap">
        <h1 class="wp-heading-inline"><?php echo $this->title; ?></h1>
        <div>
          <p><?php _e('This will sync the products from the CRM to the Ecommerce.', 'FORBES'); ?></p>
          <button
            id="forbes-start-sync"
            class="button button-primary"
            data-loading-label="<?php _e('Syncing ...', 'FORBES'); ?>"
            data-done-label="<?php _e('Sync done', 'FORBES'); ?>"
            data-intermediate-label="<?php _e('Synced products: ', 'FORBES'); ?>"
          >
            <?php _e('Start sync', 'FORBES'); ?>
          </button>
        </div>
      </div>

    <?php }

    public function admin_head()
    { ?>

      <style>
      </style>

      <script>
        window.addEventListener('DOMContentLoaded', () => {
          const eButton = document.getElementById('forbes-start-sync');

          if (!eButton) {
            return;
          }

          function syncProducts(page = 1) {
            return new Promise((resolve, reject) => {
              fetch('/wp-admin/admin-ajax.php', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                  action: 'forbes_ecommerce_sync_product',
                  page,
                }),
              })
                .then((response) => response.json())
                .then((data) => {
                  if (data.success) {
                    if (data.data.hasNext) {
                      eButton.textContent = eButton.getAttribute('data-intermediate-label') + page * 10;
                      syncProducts(page + 1);
                    } else {
                      resolve();
                    }
                  } else {
                    reject(data.data);
                  }
                })
                .catch(reject);
            });
          }

          function syncTaxonomy() {
            return fetch('/wp-admin/admin-ajax.php', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
              },
              body: new URLSearchParams({
                action: 'forbes_ecommerce_sync_taxonomy',
              }),
            });
          }

          async function syncAll() {
            eButton.disabled = true;
            eButton.textContent = eButton.getAttribute('data-loading-label');

            await syncTaxonomy();
            await syncProducts();

            eButton.textContent = eButton.getAttribute('data-done-label');
          }

          eButton.addEventListener('click', syncAll);
        });
      </script>

    <?php }

    public function __construct()
    {
      $this->title = __('Settings', 'FORBES');

      // Menu item and admin page
      add_action('admin_menu', [$this, 'menu'], 9999);

      $this->page_active = isset($_REQUEST['page']) && $_REQUEST['page'] == $this->slug;

      if ($this->page_active) {
        add_action('admin_head', [$this, 'admin_head']);
      }
    }
  }

  new PageSync();
}
