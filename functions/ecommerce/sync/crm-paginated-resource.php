<?php

namespace ForbesEcommerceSync;

if ( ! defined( 'ABSPATH' ) ) {
  exit; // Exit if accessed directly
}

class CRMPaginatedResource {
  private string $endpoint;
  private int $page = 1;
  private int $perPage = 10;
  private bool $hasNext = true;

  public function hasNext(): bool {
    return $this->hasNext;
  }

  public function getPage(): int {
    return $this->page;
  }

  public function fetch(): array {
    $ch = curl_init();

    try {
      curl_setopt($ch, CURLOPT_URL, $this->getEndpointWithQuery());
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
      curl_setopt($ch, CURLOPT_TIMEOUT, Config::CURL_TIMEOUT);

      curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json'
      ]);

      $result = curl_exec($ch);

      if (curl_errno($ch) || $result === false) {
        throw new \Exception('cURL error: ' . curl_error($ch));
      }

      $result = json_decode($result, true);

      if (json_last_error() !== JSON_ERROR_NONE) {
        throw new \Exception('JSON error: ' . json_last_error_msg());
      }

      if (isset($result['pagination']['next_url'])) {
        $this->page += 1;
      } else {
        $this->hasNext = false;
      }

      return $result;
    } catch (\Throwable $e) {
      // TODO: Consult with BE developers about error logging (stderr, syslog, etc.)
      error_log($e->getMessage());
      throw new CRMFetchException($e->getMessage());
    } finally {
      curl_close($ch);
    }
  }

  private function getEndpointWithQuery(): string {
	$endpoint = DN_REMP_CRM_HOST . $this->endpoint;

	$query = http_build_query([
	  'page' => $this->page,
	  'items_per_page' => $this->perPage
	]);

	return "$endpoint?$query";
  }

  public function __construct(string $endpoint, int $page = 1) {
    $this->endpoint = $endpoint;
    $this->page = $page;
  }
}
