<?php

namespace ForbesEcommerceSync;

if (!defined('ABSPATH')) {
  exit; // Exit if accessed directly
}

class SyncTaxonomy
{
  private function syncChildren(string $TaxonomyModel, array $children, int $parentId): void
  {
    foreach ($children as $child) {
      $taxonomy = $TaxonomyModel::getByRemoteId($child['id']);

      if ($taxonomy === null) {
        $taxonomy = new $TaxonomyModel();
      }

      $taxonomy->remoteId = $child['id'];
      $taxonomy->name = $child['name'];
      $taxonomy->parent = $parentId;
      $taxonomy->save();

      if (isset($child['children']) && is_array($child['children']) && !empty($child['children'])) {
        $this->syncChildren($TaxonomyModel, $child['children'], $taxonomy->id);
      }
    }
  }

  private function syncTaxonomy(CRMPaginatedResource $resource, string $TaxonomyModel, string $dataKey): void
  {
    while ($resource->hasNext()) {
      $results = $resource->fetch();

      foreach ($results[$dataKey] as $result) {
        if (empty(trim($result['name']))) {
            $result['name'] = '(Unnamed)';
        }

        $taxonomy = $TaxonomyModel::getByRemoteId($result['id']);

        if ($taxonomy === null) {
          $taxonomy = new $TaxonomyModel();
        }

        $taxonomy->remoteId = $result['id'];
        $taxonomy->name = $result['name'];
        $taxonomy->description = $result['description'] ?? '';

        if ($taxonomy instanceof Brand) {
          $taxonomy->logoUrl = $result['logo_url'] ?? '';
        }

        $taxonomy->save();

        if (isset($result['children']) && is_array($result['children']) && !empty($result['children'])) {
          $this->syncChildren($TaxonomyModel, $result['children'], $taxonomy->id);
        }
      }
    }
  }

  public function syncBrand(): void
  {
    $this->syncTaxonomy(new CRMPaginatedResource(Config::CRM_BRANDS_ENDPOINT), Brand::class, 'brands');
  }

  public function syncCategory(): void
  {
    $this->syncTaxonomy(new CRMPaginatedResource(Config::CRM_CATEGORIES_ENDPOINT), Category::class, 'categories');
  }

  public function syncGender(): void
  {
    $this->syncTaxonomy(new CRMPaginatedResource(Config::CRM_GENDERS_ENDPOINT), Gender::class, 'genders');
  }

  public function syncVendor(): void
  {
    $this->syncTaxonomy(new CRMPaginatedResource(Config::CRM_VENDORS_ENDPOINT), Vendor::class, 'vendors');
  }
}
