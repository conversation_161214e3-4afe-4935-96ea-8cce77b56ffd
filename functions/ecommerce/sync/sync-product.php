<?php

namespace ForbesEcommerceSync;

if ( ! defined( 'ABSPATH' ) ) {
  exit; // Exit if accessed directly
}

class SyncProduct {
  public function syncProduct(int $firstPage = 1, int $maxPages = 5): bool {
    $resource = new CRMPaginatedResource(Config::CRM_PRODUCTS_ENDPOINT, $firstPage);

    for ($x = 0; $x < $maxPages && $resource->hasNext(); $x++) {
      $results = $resource->fetch();

      foreach ($results['products'] as $result) {
        $product = ProductQuery::byRemoteId($result['id']);

        if (!$product) {
          $product = new Product();
        }

        $product->title = $result['title'];
        $product->remoteId = $result['id'];
        $product->content = $result['description'] ?? '';
        $product->url = $result['url'] ?? '';
        $product->urlDomain = $result['url_domain'] ?? '';
        $product->availability = $result['availability'] ?? '';
        $product->condition = $result['condition'] ?? '';
        $product->priceAmountNum = str_replace(',', '', $result['price_amount'] ?? '');
		$product->priceAmount = $result['price_amount'] ?? '';
        $product->priceCurrency = $result['price_currency'] ?? '';
        $product->discountedPriceAmount = $result['discounted_price_amount'] ?? '';
        $product->discountedPriceCurrency = $result['discounted_price_currency'] ?? '';
        $product->isLinkVisibleOnList = $result['is_link_visible_on_list'] ?? '';
        $product->images = $result['images'] ?? [];
		$product->attributes = $result['attributes'] ?? [];
		$product->labels = json_encode($result['labels'] ?? []);
		$product->postStatus = ($result['is_visible'] ?? true) ? 'publish' : 'draft';

        if (isset($result['genders'])) {
          $product->genders = [];
          foreach ($result['genders'] as $gender) {
            $genderTerm = Gender::getByRemoteId($gender['id']);
            $product->addGender($genderTerm);
          }
        }

        if (isset($result['categories'])) {
          $product->categories = [];
          foreach ($result['categories'] as $category) {
            $categoryTerm = Category::getByRemoteId($category['id']);
            $product->addCategory($categoryTerm);
          }
        }

        if (isset($result['brand'])) {
          $product->brand = Brand::getByRemoteId($result['brand']['id']);
        }

		if (isset($result['vendor'])) {
		  $product->vendor = Vendor::getByRemoteId($result['vendor']['id']);
		}

        $product->save();

		update_post_meta($product->id, 'forbes_raw_data', json_encode($result, JSON_PRETTY_PRINT));
      }
    }

    return $resource->hasNext();
  }
}
