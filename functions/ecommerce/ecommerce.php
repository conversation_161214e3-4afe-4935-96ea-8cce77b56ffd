<?php

// TODO: Use autoload

require_once __DIR__ . '/config.php';

require_once __DIR__ . '/acf/post-type.php';
require_once __DIR__ . '/acf/taxonomy.php';
require_once __DIR__ . '/acf/product.php';
require_once __DIR__ . '/acf/brand.php';
require_once __DIR__ . '/acf/category.php';
require_once __DIR__ . '/acf/gender.php';
require_once __DIR__ . '/acf/vendor.php';

require_once __DIR__ . '/model/post/post-model.php';
require_once __DIR__ . '/model/post/product.php';
require_once __DIR__ . '/model/post/product-query.php';

require_once __DIR__ . '/model/taxonomy/taxonomy-model.php';
require_once __DIR__ . '/model/taxonomy/brand.php';
require_once __DIR__ . '/model/taxonomy/category.php';
require_once __DIR__ . '/model/taxonomy/gender.php';
require_once __DIR__ . '/model/taxonomy/vendor.php';

require_once __DIR__ . '/sync/crm-fetch-exception.php';
require_once __DIR__ . '/sync/crm-paginated-resource.php';
require_once __DIR__ . '/sync/sync-product.php';
require_once __DIR__ . '/sync/sync-taxonomy.php';

require_once __DIR__ . '/ajax/ajax-sync-product.php';
require_once __DIR__ . '/ajax/ajax-sync-taxonomy.php';

require_once __DIR__ . '/admin/page-sync.php';

require_once __DIR__ . '/endpoint/endpoint.php';
require_once __DIR__ . '/endpoint/get-products.php';
