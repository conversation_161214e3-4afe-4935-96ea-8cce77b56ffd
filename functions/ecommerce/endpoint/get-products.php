<?php

namespace ForbesEcommerceSync;

use WP_REST_Response;

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly
}

class GetProducts extends Endpoint
{

	protected function doHandle(): WP_REST_Response
	{
		$query = new ProductQuery();

		if (isset($_GET['category_id'])) {
			$query->setCategoryId((int) $_GET['category_id']);
		}

		if (isset($_GET['category_slug'])) {
			$category = Category::getBySlug($_GET['category_slug']);

			if ($category) {
				$query->setCategory($category);
			}
		}

		if (isset($_GET['page'])) {
			$query->setPage((int) $_GET['page']);
		}

		if (isset($_GET['items_per_page'])) {
			$query->setPerPage((int) $_GET['items_per_page']);
		}

		if (isset($_GET['brand_id'])) {
			$query->setBrandId((int) $_GET['brand_id']);
		}

		if (isset($_GET['vendor_id'])) {
			$query->setVendorId((int) $_GET['vendor_id']);
		}

		if (isset($_GET['sort_by'])) {
			$query->setSortBy($_GET['sort_by']);
		}

		if (isset($_GET['sort_order'])) {
			$query->setSortOrder($_GET['sort_order']);
		}

		if (isset($_GET['gender_id'])) {
			$query->setGenderId((int) $_GET['gender_id']);
		}

		$products = $query->fetchAll();
		$hasNext = $query->hasNextPage();

		return new WP_REST_Response(compact('products', 'hasNext'));
	}

	protected function getMethod(): string
	{
		return 'GET';
	}
}

GetProducts::register();
