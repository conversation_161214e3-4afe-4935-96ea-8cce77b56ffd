<?php

namespace ForbesEcommerceSync;

use WP_REST_Response;

if ( ! defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * The base class for all endpoints. Uses admin/wp-ajax.php to handle requests.
 */
abstract class Endpoint
{
    protected array $request;

    public function __construct()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST' || $_SERVER['REQUEST_METHOD'] === 'PUT') {
            $this->request = json_decode(file_get_contents('php://input'), true, 512) ?? [];
        } else {
            $this->request = $_REQUEST;
        }
    }

    /**
     * Handles the request.
     */
    final public static function handleImpl(): WP_REST_Response
    {
        return (new static())->handle();
    }

    /**
     * The generic request handler (factory method).
     */
    private function handle(): WP_REST_Response
    {
        // todo: error handling

        return $this->doHandle();
    }

    /**
     * The endpoint-specific implementation of the request handler.
     */
    abstract protected function doHandle(): WP_REST_Response;

    /**
     * Registers the endpoint.
     */
    final public static function register(): void
    {
        add_action('rest_api_init', [static::class, 'registerWpEndpoint']);
    }

    /**
     * Uses WP's REST API to register the endpoint.
     */
    public static function registerWpEndpoint(): void
    {
        $parts = explode('\\', static::class);

        if (is_array($parts) && count($parts) >= 1) {
            $action = end($parts);
            $handler = [static::class, 'handleImpl'];

            self::registerEndpoint($action, $handler);
        }

        static::doRegister();
    }

    private static function registerEndpoint(false|string $action, array $handler): void
    {
        $method = (new static())->getMethod();

        register_rest_route('wp/v2', "/{$action}", [
            'methods'             => $method,
            'callback'            => $handler,
            'permission_callback' => '__return_true',
        ]);
    }

    abstract protected function getMethod(): string;

    /**
     * The endpoint-specific registration method.
     */
    protected static function doRegister(): void
    {
    }

    protected function validateRequest(array $array): void
    {
        foreach ($array as $key => $value) {
            if (!isset($this->request[$key])) {
                throw new RuntimeException("Missing required parameter: {$key}");
            }
        }
    }
}
