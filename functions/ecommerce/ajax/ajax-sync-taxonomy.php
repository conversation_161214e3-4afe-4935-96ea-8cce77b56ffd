<?php

namespace ForbesEcommerceSync;

if ( ! defined( 'ABSPATH' ) ) {
  exit; // Exit if accessed directly
}

add_action('wp_ajax_forbes_ecommerce_sync_taxonomy', function(): void {
  if (!current_user_can('administrator')) {
    wp_send_json_error('Unauthorized', 403);
  }

  try {
    $syncTaxonomy = new SyncTaxonomy();

    $syncTaxonomy->syncBrand();
    $syncTaxonomy->syncCategory();
    $syncTaxonomy->syncGender();
	$syncTaxonomy->syncVendor();
  } catch (\Throwable $e) {
	\Sentry\captureException($e);
    wp_send_json_error($e->getMessage(), 500);
  }
});
