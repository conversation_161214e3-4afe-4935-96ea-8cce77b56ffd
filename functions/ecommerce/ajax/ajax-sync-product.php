<?php

namespace ForbesEcommerceSync;

if ( ! defined( 'ABSPATH' ) ) {
  exit; // Exit if accessed directly
}

add_action('wp_ajax_forbes_ecommerce_sync_product', function(): void {
  if (!current_user_can('administrator')) {
    wp_send_json_error('Unauthorized', 403);
  }

  if (!isset($_POST['page'])) {
    wp_send_json_error('Page is required', 400);
  }

  try {
    $syncProduct = new SyncProduct();
    $hasNext = $syncProduct->syncProduct($_POST['page'], 1);

    wp_send_json_success(['hasNext' => $hasNext]);
  } catch (\Throwable $e) {
	\Sentry\captureException($e);
    wp_send_json_error($e->getMessage(), 500);
  }
});
