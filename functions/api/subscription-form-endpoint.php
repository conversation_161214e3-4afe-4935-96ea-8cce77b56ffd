<?php

/**
 * Register REST API endpoint for subscription form submissions
 */
function register_subscription_form_endpoint()
{
	register_rest_route('forbes/v1', '/subscription-form', array(
		'methods'             => 'POST',
		'callback'            => 'handle_subscription_form_submission',
		'permission_callback' => '__return_true', // Public endpoint
	));
}

add_action('rest_api_init', 'register_subscription_form_endpoint');

/**
 * Handle subscription form submissions
 *
 * @param  WP_REST_Request  $request  Full data about the request.
 *
 * @return WP_REST_Response Response object.
 */
function handle_subscription_form_submission($request)
{
	// Get form data from request
	$form_data = $request->get_params();

	// Validate required fields
	$required_fields = array('name', 'surname', 'email', 'phone');
	foreach ($required_fields as $field) {
		if (empty($form_data[$field])) {
			return new WP_REST_Response(array(
				'success' => false,
				'message' => sprintf(__('Field %s is required', 'FORBES'), $field),
			), 400);
		}
	}

	// Create post title from name and surname
	$post_title = sanitize_text_field($form_data['name'] . ' ' . $form_data['surname']);

	// Create post
	$post_id = wp_insert_post(array(
		'post_title'  => $post_title,
		'post_type'   => 'subs-form-data',
		'post_status' => 'publish',
	));

	if (is_wp_error($post_id)) {
		return new WP_REST_Response(array(
			'success' => false,
			'message' => $post_id->get_error_message(),
		), 500);
	}

	// Save form data as ACF fields
	update_field('form_id', sanitize_text_field($form_data['form_id']), $post_id);
	update_field('name', sanitize_text_field($form_data['name']), $post_id);
	update_field('surname', sanitize_text_field($form_data['surname']), $post_id);
	update_field('email', sanitize_email($form_data['email']), $post_id);
	update_field('phone', sanitize_text_field($form_data['phone']), $post_id);

	// Save number_of_subscribers if provided (only for inline form)
	if ( ! empty($form_data['numberOfSubscribers'])) {
		update_field('number_of_subscribers', sanitize_text_field($form_data['numberOfSubscribers']), $post_id);
	}

	// Save submission date
	update_field('submission_date', current_time('mysql'), $post_id);

	// Send email notification if form_emails is provided
	if ( ! empty($form_data['form_emails'])) {
		$to      = $form_data['form_emails'];
		$subject = __('New Subscription Form Submission', 'FORBES');
		$message = sprintf(
			__('New subscription form submission from %s %s (%s, %s)', 'FORBES'),
			$form_data['name'],
			$form_data['surname'],
			$form_data['email'],
			$form_data['phone']
		);

		if ( ! empty($form_data['numberOfSubscribers'])) {
			$message .= "\n" . sprintf(
					__('Number of subscribers: %s', 'FORBES'),
					$form_data['numberOfSubscribers']
				);
		}

		wp_mail($to, $subject, $message);
	}

	return new WP_REST_Response(array(
		'success' => true,
		'message' => __('Form submitted successfully', 'FORBES'),
		'post_id' => $post_id,
	), 200);
}