<?php
add_action('init', 'unregister_tags');
function unregister_tags() {
	unregister_taxonomy_for_object_type('post_tag', 'post');
}

add_action('init', 'frontend_create_stitek_taxonomy', 3);
//create a custom taxonomy name it stitek for the posts
function frontend_create_stitek_taxonomy() {
	$labels = array(
		'name' => _x('Štítky', 'taxonomy general name'),
		'singular_name' => _x('Štítek', 'taxonomy singular name'),
		'search_items' =>  esc_html__('Vyhledávání štítků'),
		'all_items' => esc_html__('Všechny štítky'),
		'parent_item' => esc_html__('Rodičovský štítek'),
		'parent_item_colon' => esc_html__('Nadřazený štítek:'),
		'edit_item' => esc_html__('Upravit štítek'),
		'update_item' => esc_html__('Aktualizace štítku'),
		'add_new_item' => esc_html__('Přidat nový štítek'),
		'new_item_name' => esc_html__('Nový štítek'),
		'menu_name' => esc_html__('Štítky'),
	);

	register_taxonomy('stitek', array('post'), array(
		'hierarchical' => false,
		'publicly_queryable' => true,
		'labels' => $labels,
		'show_ui' => true,
		'show_in_rest' => true,
		'show_admin_column' => true,
		'query_var' => true,
		'meta_box_cb' => false,
		'rewrite' => array('slug' => 'tag'),
		'capabilities' => [
			'manage_terms' => 'administrator',
			'edit_terms' => 'administrator',
			'delete_terms' => 'administrator',
			'assign_terms' => 'assign_categories',
		],
	));
}


/**
 * Add custom category to Special post type
 */
function frontend_create_special_stitek_tax() {

	register_taxonomy(
		'special_tag',
		'special',
		array(
			'label' => esc_html__('Štítky speciálů'),
			'rewrite' => array('slug' => 'stitek'),
			'hierarchical' => true,
			'show_admin_column' => true,
			'show_ui'           => true,
			'show_in_rest' => true
		)
	);
}
add_action('init', 'frontend_create_special_stitek_tax');