<?php

if (is_plugin_active('advanced-custom-fields-pro/acf.php')) {
	add_action('init', 'frontend_create_category_globals');
	function frontend_create_category_globals()
	{
		global $print_category;
		$print_category = get_field('print_category', 'option');

		global $breaking_news_tag_id;
		$breaking_news_tag_id = get_field('breaking_news_tag', 'option');
	}
}

/**
 * Add custom categories to post types
 */
function frontend_create_categories()
{

	register_taxonomy(
		'special-category',
		'special',
		array(
			'label' => esc_html__('Categories'),
			'rewrite' => array('slug' => 'special-category'),
			'hierarchical' => true,
			'show_admin_column' => true,
			'show_ui'           => true,
			'show_in_rest' => true
		)
	);

	register_taxonomy(
		'event-category',
		'event',
		array(
			'label' => esc_html__('Categories'),
			'rewrite' => array('slug' => 'event-category'),
			'hierarchical' => true,
			'show_admin_column' => true,
			'show_ui'           => true,
			'show_in_rest' => true
		)
	);

	register_taxonomy(
		'list-item-category',
		'list-item',
		array(
			'label' => esc_html__('Categories'),
			'rewrite' => array('slug' => 'list-item-category'),
			'public' => true,
			'hierarchical' => true,
			'show_admin_column' => true,
			'show_ui'           => true,
			'show_in_rest' => true
		)
	);

	$labels_coauthor = array(
		'name' => _x('Co-authors', 'taxonomy general name', 'FORBES'),
		'singular_name' => _x('Co-author', 'taxonomy singular name', 'FORBES'),
		'menu_name' => _x('Co-authors', 'Admin Menu text', 'FORBES'),
		'all_items' => __('All Co-authors', 'FORBES'),
		'parent_item' => __('Parent Co-author', 'FORBES'),
		'parent_item_colon' => __('Parent Co-author:', 'FORBES'),
		'new_item_name' => __('New Co-author Name', 'FORBES'),
		'add_new_item' => __('Add New Co-author', 'FORBES'),
		'edit_item' => __('Edit Co-author', 'FORBES'),
		'update_item' => __('Update Co-author', 'FORBES'),
		'view_item' => __('View Co-author', 'FORBES'),
		'search_items' => __('Search Co-authors', 'FORBES'),
		'not_found' => __('No co-authors found', 'FORBES'),
	);

	register_taxonomy(
		'coauthor',
		array('post', 'author', 'event'),
		array(
			'labels'			=> $labels_coauthor,
			'rewrite' 			=> array('slug' => 'autor'),
			'hierarchical' 		=> false,
			'show_admin_column' => true,
			'show_ui'           => true,
			'show_in_rest' 		=> true,
			'show_in_quick_edit' => false,
		    'meta_box_cb' => false,
		)
	);

	if (post_type_exists('list-item') && taxonomy_exists('list-item-category')) {
		if (! is_object_in_taxonomy('list-item', 'list-item-category')) {
			register_taxonomy_for_object_type('list-item-category', 'list-item');
		}
	}
}
add_action('init', 'frontend_create_categories', 1);
