<?php

function editorial_enqueue_scripts() {
    if (is_admin()) {
        wp_enqueue_script('frontend_enqueue_editorial_button_script', get_template_directory_uri() . '/assets/js/editorial-ai-tool.js', array('jquery'), null, true);
    }
}

/**
 * Adds a hidden React div to the admin post screen footer.
 *
 * @return void
 */
function editorial_add_hidden_react_div_to_admin() {
    $screen = get_current_screen();

    if ($screen->base == 'post') {
        echo '<div id="react-editorial-ai-tool" style="display:none;"></div>';
    }
}

add_action('admin_enqueue_scripts', 'editorial_enqueue_scripts');
add_action('admin_enqueue_scripts', 'load_react_app');
add_action('admin_footer', 'editorial_add_hidden_react_div_to_admin');
