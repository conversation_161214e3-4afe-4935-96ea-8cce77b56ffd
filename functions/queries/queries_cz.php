<?php

/**
 * Remove print posts from main query
 */
add_action('pre_get_posts', 'frontend_remove_print_posts');
function frontend_remove_print_posts($query) {
	global $print_category;

	if (!is_admin() && $query->is_main_query()) {
		$tax_query = $query->get('tax_query');
		$new_tax_query = array(
			array(
				'taxonomy' => CATEGORY_TYPE,
				'field'    => 'term_id',
				'terms'    => $print_category,
				'operator' => 'NOT IN'
			)
		);
		if ($tax_query) {
			$new_tax_query = array(
				'relation' => 'AND',
				$tax_query,
				$new_tax_query
			);
		}
		$query->set('tax_query', $new_tax_query);
	}
}

/*
	Modify Magazine Archive page query
	*/
add_action('pre_get_posts', 'forbes_cz_modify_magazine_query', 999);
function forbes_cz_modify_magazine_query($query) {
	if (
		!is_admin() &&
		(is_page_template('page-premium.php') || is_post_type_archive('magazine')) &&
		'latest_magazine' !== $query->get('id') &&
		(isset($query->query_vars['post_type']) &&
			'magazine' === $query->query_vars['post_type']
		)
	) {
		$query->set('posts_per_page', -1);
		$query->set('meta_query',  array(
			'relation'		=> 'AND',
			array(
				'key'		=> 'issueType',
				'value'		=> 'none',
				'compare'	=> 'NOT IN'
			),
			array(
				'key'		=> 'show_in_premium',
				'value'		=> '1',
				'operator'	=> '=='
			)
		));
	}
}

/*
	Modify Events Archive page query
	*/
add_action('pre_get_posts', 'forbes_modify_special_archive_query', 9999);
function forbes_modify_special_archive_query($query) {
	if (
		!is_admin() &&
		is_archive() &&
		$query->is_main_query() &&
		(isset($query->query_vars['post_type']) &&
			'special' === $query->query_vars['post_type']
		)
	) {
		$query->set('posts_per_page', 9);
	}

	return $query;
}
