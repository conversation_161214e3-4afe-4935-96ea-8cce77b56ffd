<?php

/*
	Prevent duplicating posts in the front page
*/
add_action('pre_get_posts', 'frontend_prevent_duplication_on_home_page');
function frontend_prevent_duplication_on_home_page($query) {
	if ($query->get('page') === 'home') {

		$home_exclude = \pageHome\Exclusions::getInstance()->getExclusions();

		$originally_excluded = $query->get('post__not_in');

		if ($originally_excluded) {
			$home_exclude = array_unique(array_merge($originally_excluded, $home_exclude));
		}

		$query->set('post__not_in', $home_exclude);
	}

	return $query;
}

/*
	Modify Events Archive page query
*/
add_action('pre_get_posts', 'forbes_modify_events_archive_query', 9999);
function forbes_modify_events_archive_query($query) {
	$field_name = 'cz' === COUNTRY ? 'eventStartDate' : 'starting_date';

	if (
		!is_admin() &&
		is_archive() &&
		$query->is_main_query() &&
		(isset($query->query_vars['post_type']) &&
			'event' === $query->query_vars['post_type']
		)
	) {
		$query->set('order', 'ASC');
		$query->set('orderby', 'meta_value');
		$query->set('meta_key', $field_name);
		$query->set('posts_per_page', 'cz' === COUNTRY ? 9 : 8);
		$query->set('paged', get_query_var('paged') ? get_query_var('paged') : 1);
		$query->set('meta_query', array(
			array(
				'key' => $field_name,
				'value' => date('Ymd'),
				'type' => 'DATE',
				'compare' => '>='
			)
		));
	}

	return $query;
}

/*
	Modify Magazine Archive page query
*/
add_action('pre_get_posts', 'forbes_modify_magazine_archive_query', 0);
function forbes_modify_magazine_archive_query($query) {
	if (
		!is_admin() &&
		(isset($query->query_vars['post_type']) &&
			'magazine' === $query->query_vars['post_type']
		)
	) {
		$query->set('meta_key', 'issue_date');
		$query->set('orderby', array(
			'meta_value_num' => 'DESC',
			'date' => 'DESC',
		));
	}
}

/*
	Exclude the breaking news tag from the home page (except the breaking news section)
*/
add_action('pre_get_posts', 'frontend_exclude_breaking_news_tag');
function frontend_exclude_breaking_news_tag($query) {
    global $pagenow;

    if ($query->get('post_type') === 'post' && ! is_admin() && (is_page_template('page-home.php') || 'post.php' === $pagenow)) {
        $tax_query             = $query->get('tax_query');
        $breaking_news_tag_id = get_field('breaking_news_tag', 'option');
        $breaking_news_tag = get_term($breaking_news_tag_id);

        if ( ! ($breaking_news_tag instanceof \WP_Term)) {
            return;
        }

        $breaking_news_taxonomy = $breaking_news_tag->taxonomy;
        $exclude_breaking_news = true;

        $exclude_breaking_news_query = array(
            'taxonomy' => $breaking_news_taxonomy,
            'field'    => 'term_id',
            'terms'    => $breaking_news_tag_id,
            'operator' => 'NOT IN',
        );

        if (is_array($tax_query)) {
            foreach ($tax_query as $item) {
                if (isset($item['taxonomy'], $item['terms']) && is_array($item)) {
                    $taxonomy = $item['taxonomy'];
                    $operator = strtoupper($item['operator'] ?? 'IN');
                    $terms    = is_array($item['terms']) ? $item['terms'] : [$item['terms']];

                    $is_explicit_breaking_news_query =
                        $taxonomy === $breaking_news_taxonomy &&
                        $operator === 'IN' &&
                        in_array($breaking_news_tag_id, $terms);

                    if ($is_explicit_breaking_news_query) {
                        $exclude_breaking_news = false;
                        break;
                    }
                }
            }

            if ($exclude_breaking_news) {
                $tax_query[] = $exclude_breaking_news_query;
                $query->set('tax_query', $tax_query);
            }
        } else {
            $query->set('tax_query', [$exclude_breaking_news_query]);
        }
    }
}

/*
	Modify Lists Archive page query
	*/
add_action('pre_get_posts', 'forbes_modify_list_archive_query', 9999);
function forbes_modify_list_archive_query($query) {
	if (
		!is_admin() &&
		is_archive() &&
		$query->is_main_query() &&
		(isset($query->query_vars['post_type']) &&
			'list' === $query->query_vars['post_type']
		)
	) {
		$query->set('posts_per_page', 8);
	}

	return $query;
}

// Add random order query to WP API
function add_rand_orderby_rest_post_collection_params($query_params) {
	$query_params['orderby']['enum'][] = 'rand';
	return $query_params;
}
add_filter('rest_post_collection_params', 'add_rand_orderby_rest_post_collection_params');


function frontend_posts_where($where) {
	$where = str_replace("meta_key = 'related_articles_$", "meta_key LIKE 'related_articles_%", $where);
	return $where;
}

add_filter('posts_where', 'frontend_posts_where');
