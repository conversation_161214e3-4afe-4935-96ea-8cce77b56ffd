<?php

/**
 * Access the query before execution and set an attribute each time to exclude the PRINT category
 */
function frontend_exclude_print_category_from_loops($query) {
	$tmpToken = $_SERVER['HTTP_X_AUTH_TOKEN'] ?? '';

	$printCategory = $GLOBALS['print_category'] ?? null;
	$pagenow = $GLOBALS['pagenow'] ?? '';

	if (
		$tmpToken !== '&TH&`J<eXPCp+}n]-%2&+4Rz3B<39*m:' &&
		(!is_admin() || in_array($pagenow, ['post.php', 'admin-ajax.php'])) &&
		$printCategory &&
		(
			(isset($query->query_vars['post_type']) &&
				'post' === $query->query_vars['post_type']
			) ||
			(is_array($query->query_vars['post_type'] ?? []) &&
				in_array('post', $query->query_vars['post_type'] ?? [])
			)
		)
	) {
		$original_exclusions = $query->get('category__not_in');
		$original_exclusions = is_array($original_exclusions) ? $original_exclusions : [];
		$original_exclusions[] = $printCategory;
		$query->set('category__not_in', $original_exclusions);
	}

	return $query;
}
add_action('pre_get_posts', 'frontend_exclude_print_category_from_loops');
