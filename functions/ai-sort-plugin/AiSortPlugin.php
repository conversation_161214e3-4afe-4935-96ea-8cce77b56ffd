<?php


namespace AiSortPlugin;

require_once 'PluginHelpers/AdminPagesHelper.php';
require_once 'PluginHelpers/SettingsHelper.php';
require_once 'PluginHelpers/APIHelper.php';

use AiSortPlugin\PluginHelpers\AdminPagesHelper;
use AiSortPlugin\PluginHelpers\SettingsHelper;
use AiSortPlugin\PluginHelpers\APIHelper;

//pages
require_once 'Pages/CategoriesPage.php';
require_once 'Pages/RulesPage.php';
require_once 'Pages/ArticlesPage.php';
require_once 'Pages/TasksPage.php';
require_once 'Pages/ArticleDetailPage.php';

use AiSortPlugin\Pages\CategoriesPage;
use AiSortPlugin\Pages\RulesPage;
use AiSortPlugin\Pages\ArticlesPage;
use AiSortPlugin\Pages\TasksPage;
use AiSortPlugin\Pages\ArticleDetailPage;

//tasks
require_once 'Tasks/PrepareArticlesTask.php';
require_once 'Tasks/RuleArticlesTask.php';
require_once 'Tasks/SortArticlesTask.php';
require_once 'Tasks/ApplyCategoriesTask.php';
require_once 'Tasks/DeleteArticlesTask.php';
require_once 'Tasks/CreateCategoryTreeTask.php';
require_once 'Tasks/DropTablesTask.php';
require_once 'Tasks/CreateTablesTask.php';

use AiSortPlugin\Tasks\PrepareArticlesTask;
use AiSortPlugin\Tasks\RuleArticlesTask;
use AiSortPlugin\Tasks\SortArticlesTask;
use AiSortPlugin\Tasks\ApplyCategoriesTask;
use AiSortPlugin\Tasks\DeleteArticlesTask;
use AiSortPlugin\Tasks\CreateCategoryTreeTask;
use AiSortPlugin\Tasks\DropTablesTask;
use AiSortPlugin\Tasks\CreateTablesTask;


class AiSortPlugin{
    protected $adminPagesHelper;
    protected $settingsHelper;

    protected $apiHelper;

    public function __construct(){
        $this->adminPagesHelper = new AdminPagesHelper();
        $this->settingsHelper = new SettingsHelper();
        $this->apiHelper = new APIHelper("ai-sort");

        register_taxonomy('life_category', 'post', [
            'labels' => [
				// This taxonomy is only for the CZ version of the site so we use the CZ translation.
                'name'                       => _x('Rubriky Life', 'Taxonomy General Name', 'FORBES'),
                'singular_name'              => _x('Rubrika Life', 'Taxonomy Singular Name', 'FORBES'),
                'menu_name'                  => _x('Rubriky Life', 'Admin Menu text', 'FORBES'),
                'all_items'                  => __('Všechny rubriky Life', 'FORBES'),
                'parent_item'                => __('Rodičovská rubrika Life', 'FORBES'),
                'parent_item_colon'          => __('Rodičovská rubrika Life:', 'FORBES'),
                'new_item_name'              => __('Nová rubrika Life', 'FORBES'),
                'add_new_item'               => __('Vytvořit novou rubriku Life', 'FORBES'),
                'edit_item'                  => __('Upravit rubriku Life', 'FORBES'),
                'update_item'                => __('Upravit rubriku Life', 'FORBES'),
                'view_item'                  => __('Zobrazit rubriku Life', 'FORBES'),
            ],
            'hierarchical' => true,
            'show_ui' => true,
            'show_in_rest' => true,
            'show_admin_column' => false,
			'show_in_quick_edit' => false,
		    'meta_box_cb' => false,
            'query_var' => true,
            'rewrite' => ['slug' => 'life-category'],
			'capabilities' => [
				'manage_terms' => 'administrator',
				'edit_terms' => 'administrator',
				'delete_terms' => 'administrator',
				'assign_terms' => 'assign_categories',
			],
        ]);

        /**
         * create pages
         */
        $this->adminPagesHelper->addMenuPage(
            'AI Sort Plugin',
            'AI Sort',
            'manage_options',
            'ai-sort',
            [TasksPage::class, 'render']
        );

        $this->adminPagesHelper->addSubMenuPage(
            'ai-sort',
            'AI Sort Articles',
            'Articles',
            'manage_options',
            'ai-sort-articles',
            [ArticlesPage::class, 'render']
        );

        $this->adminPagesHelper->addHiddenPage(
            'AI Sort Article Detail',
            'Article Detail',
            'manage_options',
            'ai-sort-article-detail',
            [ArticleDetailPage::class, 'render']
        );

        $this->adminPagesHelper->addSubMenuPage(
            'ai-sort',
            'AI Sort Categories',
            'Categories',
            'manage_options',
            'ai-sort-categories',
            [CategoriesPage::class, 'render']
        );

        $this->adminPagesHelper->addSubMenuPage(
            'ai-sort',
            'AI Sort Rules',
            'Rules',
            'manage_options',
            'ai-sort-rules',
            [RulesPage::class, 'render']
        );

        $this->adminPagesHelper->addSubMenuPage(
            'ai-sort',
            'AI Sort Settings',
            'Settings',
            'manage_options',
            'ai-sort-settings',
            [$this, 'pageSettings']
        );

        add_action('admin_menu', [$this->adminPagesHelper, 'createPages']);

        /**
         * create settings
         */

        $this->settingsHelper->addSetting(
            'ai_sort_api',
            'ai_sort_api_key',
            'API Settings',
            "Api key",
            [$this, 'settingInputApiKeyCB'],
            ''
        );

        $this->settingsHelper->addSetting(
            'ai_sort_api',
            'ai_sort_api_url',
            'API Settings',
            "Api url",
            [$this, 'settingInputApiUrlCB'],
            'https://api.anthropic.com/v1/messages'
        );

        $this->settingsHelper->addSetting(
            'ai_sort_api',
            'ai_sort_api_version',
            'API Settings',
            "Api version",
            [$this, 'settingInputApiVersionCB'],
            '2023-06-01'
        );

        $this->settingsHelper->addSetting(
            'ai_sort_sort',
            'ai_sort_post_tag_taxonomy',
            'Sort Settings',
            "Post tag taxonomy",
            [$this, 'settingInputSortPostTagTaxonomyCB'],
            'stitok'
        );

        $this->settingsHelper->addSetting(
            'ai_sort_sort',
            'ai_sort_post_type',
            'Sort Settings',
            "Post type",
            [$this, 'settingInputSortPostTypeCB'],
            'post'
        );

        $this->settingsHelper->addSetting(
            'ai_sort_sort',
            'ai_sort_default_category_id',
            'Sort Settings',
            "Category 'Base' ID",
            [$this, 'settingInputSortDefaultCategoryCB'],
            '100'
        );

        $this->settingsHelper->addSetting(
            'ai_sort',
            'ai_sort_safe_key',
            'Settings',
            "Safe key",
            [$this, 'settingInputSafeKeyCB'],
            'sef9852'
        );

        add_action('admin_init', [$this->settingsHelper, 'registerSettings']);

        /**
         * create api routes
         */
        $this->apiHelper->addRoute(
            '/prepare-articles',
            'GET',
            [PrepareArticlesTask::class, 'prepareArticles']
        );

        $this->apiHelper->addRoute(
            '/delete-articles',
            'POST',
            [DeleteArticlesTask::class, 'deleteArticles']
        );

        $this->apiHelper->addRoute(
            '/drop-tables',
            'POST',
            [DropTablesTask::class, 'dropTables']
        );

        $this->apiHelper->addRoute(
            '/rule-articles',
            'GET',
            [RuleArticlesTask::class, 'ruleArticles']
        );

        $this->apiHelper->addRoute(
            '/rule-article',
            'GET',
            [RuleArticlesTask::class, 'ruleOneArticle']
        );

        $this->apiHelper->addRoute(
            '/sort-articles',
            'GET',
            [SortArticlesTask::class, 'sortArticles']
        );

        $this->apiHelper->addRoute(
            '/sort-article',
            'GET',
            [SortArticlesTask::class, 'sortOneArticle']
        );

        $this->apiHelper->addRoute(
            '/create-categories',
            'GET',
            [CreateCategoryTreeTask::class, 'createCategories']
        );

        $this->apiHelper->addRoute(
            '/apply-categories',
            'GET',
            [ApplyCategoriesTask::class, 'applyCategories']
        );

        if($this->isAnyTableMissing()){
            $this->actionCreateTables();
        }
    }

    public function settingInputApiKeyCB(){
        $value = $this->settingsHelper->getValue('ai_sort_api', 'ai_sort_api_key');
        ?>
        <input type="text" name="ai_sort_api_key" value="<?php echo $value; ?>">
        <?php
    }

    public function settingInputApiUrlCB(){
        $value = $this->settingsHelper->getValue('ai_sort_api', 'ai_sort_api_url');
        ?>
        <input type="text" name="ai_sort_api_url" value="<?php echo $value; ?>">
        <?php
    }

    public function settingInputApiVersionCB(){
        $value = $this->settingsHelper->getValue('ai_sort_api', 'ai_sort_api_version');
        ?>
        <input type="text" name="ai_sort_api_version" value="<?php echo $value; ?>">
        <?php
    }

    public function settingInputSortPostTagTaxonomyCB(){
        $value = $this->settingsHelper->getValue('ai_sort_sort', 'ai_sort_post_tag_taxonomy');
        ?>
        <input type="text" name="ai_sort_post_tag_taxonomy" value="<?php echo $value; ?>">
        <?php
    }

    public function settingInputSortPostTypeCB(){
        $value = $this->settingsHelper->getValue('ai_sort_sort', 'ai_sort_post_type');
        ?>
        <input type="text" name="ai_sort_post_type" value="<?php echo $value; ?>">
        <?php
    }

    public function settingInputSortDefaultCategoryCB(){
        $value = $this->settingsHelper->getValue('ai_sort_sort', 'ai_sort_default_category_id');

        ?>
        <input type="number" name="ai_sort_default_category_id" value="<?php echo $value; ?>">
        <?php
    }

    public function settingInputSafeKeyCB(){
        $value = $this->settingsHelper->getValue('ai_sort', 'ai_sort_safe_key');

        ?>
        <input type="text" name="ai_sort_safe_key" value="<?php echo $value; ?>">
        <?php
    }


    public function pageSettings(){

        ?>
        <div class="wrap">
            <h1>AI Sort Settings</h1>
            <form method="post" action="options.php">
                <?php
                // Output security fields for the registered setting group
                settings_fields('ai_sort');

                // Output setting sections and their fields
                // (sections are registered for "ai_sort", each field is registered to a specific section)
                do_settings_sections('ai_sort');

                // Output save settings button
                submit_button('Save Settings');
                ?>
            </form>
        </div>
        <?php
    }

    public function actionCreateTables(){
        CreateTablesTask::createTables();
    }

    public function isAnyTableMissing(){
        global $wpdb;

        $tables = [
            'ai_sort_articles',
            'ai_sort_categories',
            'ai_sort_rules',
            'ai_sort_logs'
        ];

        foreach($tables as $table){
            $sql = "SHOW TABLES LIKE '{$table}'";
            $result = $wpdb->get_results($sql);

            if(count($result) == 0){
                return true;
            }
        }

        return false;
    }
}