<?php

namespace AiSortPlugin\Pages;

require_once aiSortPath('Models/RulesModel.php');
require_once aiSortPath('Models/CategoryModel.php');

use AiSortPlugin\Models\RulesModel;
use AiSortPlugin\Models\CategoryModel;

class RulesPage{

    public static function render(){
        if(isset($_POST['add_form'])){
            RulesModel::addRule([
                'weight' => $_POST['weight'],
                'ord' => $_POST['ord'],
                'tag_slug' => $_POST['tag_slug'],
                'with_tag_slug' => $_POST['with_tag_slug'],
                'action' => $_POST['action'],
                'action_category_id' => $_POST['action_category_id']
            ]);
            //redirect to avoid form resubmission
            header("Location: ".$_SERVER['REQUEST_URI']);
        }
        //if delete form is submitted
        if(isset($_POST['delete'])){
            RulesModel::deleteRule($_POST['id']);

            //redirect to avoid form resubmission
            header("Location: ".$_SERVER['REQUEST_URI']);
        }

        //if import form is submitted
        if(isset($_FILES['rules_json'])){
            //make sure file is json and assume utf-8 encoding
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mime = finfo_file($finfo, $_FILES['rules_json']['tmp_name']);

            if($mime != 'application/json'){
                echo "Invalid file type";
                return;
            }

            $json = file_get_contents($_FILES['rules_json']['tmp_name']);

            $rules = json_decode($json, true);

            RulesModel::importRules($rules, $_POST['weight']??0);

            //redirect to avoid form resubmission
            header("Location: ".$_SERVER['REQUEST_URI']);
        }

        ?>
        <div class="wrap">
            <h1>AI Sort Rules</h1>
        </div>
        <?php
            $rules = RulesModel::getRules();
            $categories = CategoryModel::getCategories();

            function get_category_title($categories, $id){
                foreach($categories as $category){
                    if($category->id == $id){
                        return $category->full_title;
                    }
                }
                return "";
            }

            function get_action_title($action){
                if($action == 'move'){
                    return 'Move';
                }elseif($action == 'sort'){
                    return 'Sort';
                } else {
                    return 'Unknown';
                }
            }
        ?>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Weight</th>
                    <th>Order</th>
                    <th>Tag</th>
                    <th>With tag</th>
                    <th>Action</th>
                    <th>Move To / Sort To</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach($rules as $rule): ?>
                    <tr>
                        <td><?php echo $rule->weight; ?></td>
                        <td><?php echo $rule->ord; ?></td>
                        <td><?php echo $rule->tag_slug; ?></td>
                        <td><?php echo $rule->with_tag_slug; ?></td>
                        <td><?php echo get_action_title($rule->action); ?></td>
                        <td><?php echo get_category_title($categories, $rule->action_category_id); ?></td>
                        <td>
                            <form method="post" onsubmit="return confirm('Are you sure?');">
                                <input type="hidden" name="id" value="<?php echo $rule->id; ?>">
                                <input type="submit" name="delete" value="Delete">
                            </form>
                        </td>
                    </tr>

                <?php endforeach; ?>

                <tr>
                    <form method="post">
                        <td><input type="number" name="weight" placeholder="Weight"></td>
                        <td><input type="number" name="ord" placeholder="Order"></td>
                        <td><input type="text" name="tag_slug" placeholder="Tag"></td>
                        <td><input type="text" name="with_tag_slug" placeholder="With tag"></td>
                        <td>
                            <select name="action">
                                <option value="move">Move</option>
                                <option value="sort">Sort</option>
                            </select>
                        </td>
                        <td>
                            <select name="action_category_id">
                                <option value="">Select Category</option>
                                <?php foreach($categories as $category): ?>
                                    <option value="<?php echo $category->id; ?>"><?php echo $category->full_title; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </td>
                        <td><input type="submit" name="add_form" value="Add"></td>
                    </form>
            </tbody>
        </table>

        <!-- import sort categories as json form -->
        <h2>Import Rules</h2>
        <form method="post" enctype="multipart/form-data">
            <input type="file" name="rules_json">
            <input type="number" name="weight" placeholder="Weight">
            <input type="submit" value="Import">
        </form>

        <?php
    }
}