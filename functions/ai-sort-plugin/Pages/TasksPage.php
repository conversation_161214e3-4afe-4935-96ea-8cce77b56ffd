<?php

namespace AiSortPlugin\Pages;

require_once aiSortPath('Models/ArticleModel.php');
require_once aiSortPath('Models/LogsModel.php');

use AiSortPlugin\Models\ArticleModel;
use AiSortPlugin\Models\LogsModel;


class TasksPage{

    public static function render(){
        //get post type from settings
        $postType = get_option('ai_sort_post_type');

        //get lastId from $_GET params
        $lastId = ArticleModel::getMaxWpId();

        //get total articles
        $artTotal = ArticleModel::countTotalPosts($postType);
        //get done articles
        $artDone = ArticleModel::countDonePosts($postType, $lastId);
        $artPerc = 0;

        if($artTotal > 0){
            $artPerc = ($artDone/$artTotal) * 100;
        }

        //rule articles
        $articlesTotal = ArticleModel::countTotalArticles();
        $articlesRuled = ArticleModel::countRuledArticles();
        $articlesSorted = ArticleModel::countSortedArticles();
        $articlesToSort = ArticleModel::countTotalToSort();

        $articlesToApply = ArticleModel::countTotalToApply();
        $articlesApplied = ArticleModel::countAppliedArticles();

        $rulePerc = 0;
        $donePerc = 0;
        $applyPerc = 0;

        if($articlesTotal > 0){
            $rulePerc = ($articlesRuled/$articlesTotal) * 100;
            $donePerc = ($articlesSorted/$articlesTotal) * 100;
        }

        if(($articlesApplied+$articlesToApply) > 0){
            $applyPerc = ($articlesApplied/($articlesApplied+$articlesToApply)) * 100;
        }

        if(isset($_GET['logs'])){
            //select last logs
            $logs = LogsModel::getlastLogs();

            if($logs):?>
            <pre>
                <?php print_r($logs);?>
            </pre>
            <?php endif;
            die();
        }

        ?>
        <style>
            .task{
                margin-bottom: 20px;
                margin-right: 20px;
            }

            .progress{
                background: #f1f1f1;
                margin-bottom: 10px;
                box-sizing: border-box;
            }

            .progress-bar {
                background: #fff;
                height: 8px;
                position: relative;
                box-sizing: border-box;
                border-radius: 5px;
                overflow: hidden;
            }

            .progress-bar-inner {
                height: 100%;
                background: #00a0d2;
                box-sizing: border-box;
            }

            .progress-label {
                text-align: center;
                font-size: 14px;
                margin-bottom: 10px;
                box-sizing: border-box;
                padding: 5px;
            }

            .button {
                display: inline-block;
                padding: 10px 20px;
                background: #0073aa;
                color: #fff;
                text-decoration: none;
                border-radius: 5px;
            }

            .button-primary{
                background: #00a0d2;
            }
        </style>


        <h1>AI Sort</h1>


        <div class="task">
            <h2>Prepare Articles</h2>
            <div class="progress">
                <div class="progress-bar">
                    <div class="progress-bar-inner" id="taskPrepareProgress" style="width: <?php echo $artPerc;?>%"></div>
                </div>
                <div class="progress-label" id="taskPrepareLabel"><?php echo $artDone;?> / <?php echo $artTotal;?></div>
                <div id="taskPrepareActions" style="<?php echo ($artDone == $artTotal) ? 'display:none;' : '';?>">
                    <a href="javascript:tasks.prepareArticles();" class="button button-primary">Run</a>
                </div>
            </div>
        </div>

        <div class="task">
            <h2>Rule / Move Articles</h2>
            <div class="progress">
                <div class="progress-bar">
                    <div class="progress-bar-inner" id="taskRuleProgress" style="width: <?php echo $rulePerc;?>%"></div>
                </div>
                <div class="progress-label" id="taskRuleLabel"><?php echo $articlesRuled;?> / <?php echo $articlesTotal;?></div>
                <div id="taskRuleActions" style="<?php echo ($articlesRuled == $articlesTotal) ? 'display:none;' : '';?>">
                    <a href="javascript:tasks.ruleArticles();" class="button button-primary">Run</a>
                </div>
            </div>
        </div>

        <div class="task">
            <h2>Sort Articles</h2>
            <div class="progress">
                <div class="progress-bar">
                    <div class="progress-bar-inner" id="taskSortProgress" style="width: <?php echo $donePerc;?>%"></div>
                </div>
                <div class="progress-label" id="taskSortLabel"><?php echo $articlesSorted;?> / <?php echo $articlesToSort;?></div>
                <div id="taskSortActions" style="<?php echo ($articlesSorted == $articlesTotal) ? 'display:none;' : '';?>">
                    <a href="javascript:tasks.sortArticles();" class="button button-primary">Run</a>
                </div>
            </div>
        </div>

        <div class="task">
            <h2>Apply Categories</h2>
            <div class="progress">
                <div class="progress-bar">
                    <div class="progress-bar-inner" id="taskApplyProgress" style="width: <?php echo $applyPerc;?>%"></div>
                </div>
                <div class="progress-label" id="taskApplyLabel"><?php echo $articlesApplied;?> / <?php echo ($articlesApplied);?></div>
                <div id="taskApplyActions" style="<?php echo ($articlesToApply==0) ? 'display:none;' : '';?>">
                    <a href="javascript:tasks.applyCategories();" class="button button-primary">Run</a>
                </div>
            </div>
        </div>

        <script>
            function HTTPClient(){


            }

            HTTPClient.prototype.post = function(url, data){
                return new Promise(function(resolve, reject){
                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', url, true);
                    xhr.setRequestHeader('Content-Type', 'application/json');
                    xhr.onload = function(){
                        if(xhr.status >= 200 && xhr.status < 400){
                            try{
                                resolve(JSON.parse(xhr.responseText));
                            }catch(e){
                                resolve(xhr.responseText);
                            }
                        } else {
                            reject(xhr.responseText);
                        }
                    }

                    xhr.send(data);
                });
            }

            HTTPClient.prototype.get = function(url){
                return new Promise(function(resolve, reject){
                    var xhr = new XMLHttpRequest();
                    xhr.open('GET', url, true);
                    xhr.onload = function(){
                        if(xhr.status >= 200 && xhr.status < 400){
                            try{
                                resolve(JSON.parse(xhr.responseText));
                            }catch(e){
                                resolve(xhr.responseText);
                            }
                        } else {
                            reject(xhr.responseText);
                        }
                    }

                    xhr.send();
                });
            }

            var tasks = {
                httpClient : new HTTPClient(),
                prepareLastId: <?php echo $lastId;?>,
                prepareArticlesStep: async function(){
                    var self = this;
                    let url = "/wp-json/ai-sort/prepare-articles";

                    document.getElementById('taskPrepareActions').style.display = 'none';

                    try {
                        let result = await self.httpClient.get(url);

                        self.prepareLastId = result.lastId;

                        //set progress
                        var perc = Math.ceil((result.done/result.total) * 100);

                        document.getElementById('taskPrepareProgress').style.width = perc + "%";

                        document.getElementById('taskPrepareLabel').innerHTML = result.done + " / " + result.total;

                        if(result.done == result.total){
                            return false;
                        }else{
                            return true;
                        }
                    } catch(e) {
                        console.error(e);
                        return false;
                    }
                },
                prepareArticles: async function(){
                    var self = this;

                    var continueTask = true;

                    while(continueTask){
                        continueTask = await self.prepareArticlesStep();
                        await self.wait(1000);
                    }
                },
                ruleArticlesStep: async function(){
                    var self = this;
                    let url = "/wp-json/ai-sort/rule-articles";

                    document.getElementById('taskRuleActions').style.display = 'none';

                    try {
                        let result = await self.httpClient.get(url);

                        //set progress
                        var perc = Math.ceil((result.done/result.total) * 100);

                        document.getElementById('taskRuleProgress').style.width = perc + "%";

                        document.getElementById('taskRuleLabel').innerHTML = result.done + " / " + result.total;

                        if(result.done == result.total){
                            return false;
                        }else{
                            return true;
                        }
                    } catch(e) {
                        console.error(e);
                        return false;
                    }
                },
                ruleArticles: async function(){
                    var self = this;

                    var continueTask = true;

                    while(continueTask){
                        continueTask = await self.ruleArticlesStep();
                        await self.wait(1000);
                    }
                },
                sortArticlesStep: async function(){
                    var self = this;
                    let url = "/wp-json/ai-sort/sort-articles";

                    document.getElementById('taskSortActions').style.display = 'none';

                    try {
                        let result = await self.httpClient.get(url);

                        //set progress
                        var perc = Math.ceil((result.done/result.total) * 100);

                        document.getElementById('taskSortProgress').style.width = perc + "%";

                        document.getElementById('taskSortLabel').innerHTML = result.done + " / " + result.total;

                        if(result.done == result.total){
                            return false;
                        }else{
                            return true;
                        }
                    } catch(e) {
                        console.error(e);
                        return false;
                    }
                },
                sortArticles: async function(){
                    var self = this;

                    var continueTask = true;

                    while(continueTask){
                        continueTask = await self.sortArticlesStep();
                        await self.wait(1000);
                    }
                },
                applyCategoriesStep: async function(){
                    var self = this;
                    let url = "/wp-json/ai-sort/apply-categories";

                    document.getElementById('taskApplyActions').style.display = 'none';

                    try {
                        let result = await self.httpClient.get(url);

                        //set progress
                        var perc = Math.ceil((result.done/result.total) * 100);

                        document.getElementById('taskApplyProgress').style.width = perc + "%";

                        document.getElementById('taskApplyLabel').innerHTML = result.done + " / " + result.total;

                        if(result.done == result.total){
                            return false;
                        }else{
                            return true;
                        }
                    } catch(e) {
                        console.error(e);
                        return false;
                    }
                },
                createCategories: async function(){
                    var self = this;
                    let url = "/wp-json/ai-sort/create-categories";

                    try {
                        let result = await self.httpClient.get(url);

                        return true;
                    } catch(e) {
                        console.error(e);
                        return false;
                    }
                },
                applyCategories: async function(){
                    var self = this;

                    //call create category tree first
                    await self.createCategories();


                    var continueTask = true;

                    while(continueTask){
                        continueTask = await self.applyCategoriesStep();
                        await self.wait(1000);
                    }
                },
                wait: function(ms){
                    return new Promise(resolve => setTimeout(resolve, ms));
                }
            }



        </script>

        <?php
    }
}