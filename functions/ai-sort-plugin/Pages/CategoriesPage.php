<?php

namespace AiSortPlugin\Pages;

require_once aiSortPath('Models/CategoryModel.php');

use AiSortPlugin\Models\CategoryModel;

class CategoriesPage{

    public static function render(){

        function copyGet($toAdd = []){
            $query = $_GET;

            foreach($toAdd as $key => $value){
                $query[$key] = $value;
            }

            return "?".http_build_query($query);
        }

        function removeGet($toRemove = []){
            $query = $_GET;

            foreach($toRemove as $key){
                unset($query[$key]);
            }

            return "?".http_build_query($query);
        }


        //if add form is submitted
        if(isset($_POST['add_form'])){

            $sort_parent_id = $_POST['sort_parent_id'];
            if($_POST['sort_disconect'] == 1){
                $sort_parent_id = null;
            }

            CategoryModel::addCategory([
                'title' => $_POST['title'],
                'parent_id' => $_POST['parent_id'],
                'sort_parent_id' => $sort_parent_id,
                'keywords' => $_POST['keywords'],
                'slug' => $_POST['slug'],
                'wp_id' => $_POST['wp_id'],
                'wp_taxonomy' => $_POST['wp_taxonomy']
            ]);
            //redirect to avoid form resubmission
            $url = $_SERVER['REQUEST_URI'];

            echo "<script>window.location = '$url';</script>";
            die();
        }

        if(isset($_POST['edit_form'])){

            CategoryModel::editCategory($_POST['category_id'], [
                'title' => $_POST['title'],
                'parent_id' => $_POST['parent_id'],
                'sort_parent_id' => $_POST['sort_parent_id'],
                'keywords' => $_POST['keywords'],
                'slug' => $_POST['slug'],
                'wp_id' => $_POST['wp_id'],
                'wp_taxonomy' => $_POST['wp_taxonomy']
            ]);
            //redirect to avoid form resubmission
            $url = substr($_SERVER['REQUEST_URI'], 0, strpos($_SERVER['REQUEST_URI'], "?")).removeGet(["category_id"]);
            echo "<script>window.location = '$url';</script>";
            die();
        }

        //if delete form is submitted
        if(isset($_POST['delete'])){
            CategoryModel::deleteCategory($_POST['id']);

            //redirect to avoid form resubmission
            header("Location: ".$_SERVER['REQUEST_URI']);
        }

        //if import form is submitted
        if(isset($_FILES['categories_json'])){
            //make sure file is json and assume utf-8 encoding
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            $mime = finfo_file($finfo, $_FILES['categories_json']['tmp_name']);

            if($mime != 'application/json'){
                echo "Invalid file type";
                return;
            }

            $json = file_get_contents($_FILES['categories_json']['tmp_name']);

            $categories = json_decode($json, true);

            $importTo = $_POST['parent_id'] ?? null;

            if($importTo == ""){
                $importTo = null;
            }

            CategoryModel::importCategories($categories, $importTo);

            //redirect to avoid form resubmission
            header("Location: ".$_SERVER['REQUEST_URI']);
        }

        if(isset($_GET["category_id"])):

            $category = CategoryModel::getCategory($_GET["category_id"]);

            if($category):
            ?>

            <h2>Edit Category</h2>
            <form method="post">
                <input type="hidden" name="category_id" value="<?php echo $category->id; ?>">
                <table>
                <tr>
                    <th style="text-align: right;">Title: </th>
                    <td><input type="text" name="title" value="<?php echo $category->title; ?>" placeholder="Title"></td>
                </tr>
                <tr>
                    <th style="text-align: right;">Parent: </th>
                    <td><select name="parent_id">
                        <option value="">Select Parent</option>
                        <?php foreach(CategoryModel::getCategories() as $cat): ?>
                            <option value="<?php echo $cat->id; ?>" <?php echo $cat->id == $category->parent_id ? 'selected' : ''; ?>><?php echo $cat->full_title; ?></option>
                        <?php endforeach; ?>
                    </select></td>
                </tr>

                <tr>
                    <th style="text-align: right;">Sort Parent: </th>

                    <td><select name="sort_parent_id">
                        <option value="">Disconnected</option>
                        <?php foreach(CategoryModel::getCategories() as $cat): ?>
                            <option value="<?php echo $cat->id; ?>" <?php echo $cat->id == $category->sort_parent_id ? 'selected' : ''; ?>><?php echo $cat->full_title; ?></option>
                        <?php endforeach; ?>
                    </select>
                    <p>Use "Disconnected" to leave out of global sort OR same as Parent</p></td>
                </tr>
                <tr>
                    <th style="text-align: right;">Slug: </th>
                    <td><input type="text" name="slug" value="<?php echo $category->slug; ?>" placeholder="Slug"></td>
                </tr>
                <tr>
                    <th style="text-align: right;">Keywords: </th>
                    <td><textarea name="keywords" placeholder="Keywords" style="height: 300px; min-width:500px;"><?php echo $category->keywords; ?></textarea></td>
                </tr>
                <tr>
                    <th style="text-align: right;">Wordpress Id: </th>
                    <td><input type="text" name="wp_id" value="<?php echo $category->wp_id; ?>" placeholder="Wordpress Id"></td>
                </tr>
                <tr>
                    <th style="text-align: right;">Wordpress Taxonomy: </th>
                    <td><input type="text" name="wp_taxonomy" value="<?php echo $category->wp_taxonomy; ?>" placeholder="Wordpress Taxonomy"></td>
                </tr>
                </table>
                <input type="submit" name="edit_form" value="Edit">
            </form>
        <?php
            endif;
        endif;

        ?>
        <div class="wrap">
            <h1>AI Sort Categories</h1>
        </div>
        <?php
            
            $categories = CategoryModel::getCategories();
        ?>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Full Title</th>
                    <th>Slug</th>
                    <th>Keywords</th>
                    <th>Wordpress Id</th>
                    <th>Taxonomy</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach($categories as $category): ?>
                    <tr>
                        <td><?php echo $category->title; ?></td>
                        <td><?php echo $category->full_title; ?></td>
                        <td><?php echo $category->slug; ?></td>
                        <td style="font-size: 10px;"><?php echo $category->keywords; ?></td>
                        <td><?php echo $category->wp_id; ?></td>
                        <td><?php echo $category->wp_taxonomy; ?></td>
                        <td>
                            <a href="<?php echo copyGet(["category_id"=>$category->id]);?>">Edit</a>
                            <form method="post" onsubmit="return confirm('Are you sure?');">
                                <input type="hidden" name="id" value="<?php echo $category->id; ?>">
                                <input type="submit" name="delete" value="Delete">
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>

                <tr>
                    <form method="post">
                        <td><input type="text" name="title" placeholder="Title"></td>
                        <td>
                            <select name="parent_id">
                                <option value="">Select Parent</option>
                                <?php foreach($categories as $category): ?>
                                    <option value="<?php echo $category->id; ?>"><?php echo $category->full_title; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <input type="hidden" name="sort_disconect" value="0">
                            <label><input type="checkbox" name="sort_disconect" value="1"> Disconnect parent on sort </label>
                        </td>
                        <td><input type="text" name="slug" placeholder="Slug"></td>
                        <td><textarea name="keywords" placeholder="Keywords"></textarea></td>
                        <td><input type="text" name="wp_id" placeholder="Wordpress Id"></td>
                        <td><input type="text" name="wp_taxonomy" placeholder="Wordpress Taxonomy"></td>
                        <td><input type="submit" name="add_form" value="Add"></td>
                    </form>
                </tr>
            </tbody>
        </table>

        <!-- import sort categories as json form -->
        <h2>Import Categories</h2>
        <form method="post" enctype="multipart/form-data">
            <input type="file" name="categories_json">
            <select name="parent_id">
                <option value="">Root</option>
                <?php foreach($categories as $category): ?>
                    <option value="<?php echo $category->id; ?>"><?php echo $category->full_title; ?></option>
                <?php endforeach; ?>
            </select>
            <input type="submit" value="Import">
        </form>

        <?php
    }
}