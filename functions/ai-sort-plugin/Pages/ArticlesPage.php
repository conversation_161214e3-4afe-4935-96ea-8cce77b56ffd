<?php

namespace AiSortPlugin\Pages;

require_once aiSortPath('Models/ArticleModel.php');
require_once aiSortPath('Models/CategoryModel.php');

use AiSortPlugin\Models\ArticleModel;
use AiSortPlugin\Models\CategoryModel;

class ArticlesPage{

    public static function render(){
        ?>
        <div class="wrap">
            <h1>AI Sort Articles</h1>
        </div>
        <?php
            $query = "";
            $page = 1;

            if(isset($_GET['q'])){
                $query = $_GET['q'];
            }

            if(isset($_GET['pg'])){
                $page = intval($_GET['pg']);
            }

            $articles = ArticleModel::getArticles($page, $query);
            $categories = CategoryModel::getCategories();
            $articlePages = ArticleModel::getTotalPages($query);

            function get_category_title($categories, $id){
                foreach($categories as $category){
                    if($category->id == $id){
                        return $category->full_title;
                    }
                }
                return "--none--";
            }

            function get_action_title($action){
                if($action == 'move'){
                    return 'Move';
                }elseif($action == 'sort'){
                    return 'Sort';
                } else {
                    return 'Unknown';
                }
            }

            function get_bool($value){
                if($value == 1){
                    return "Yes";
                }else{
                    return "No";
                }
            }
        ?>

        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Title</th>
                    <th>Action</th>
                    <th>Move To / Sort To</th>
                    <th>Category</th>
                    <th>Ruled / Moved</th>
                    <th>Sorted</th>
                    <th>Applied</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach($articles as $article): ?>
                    <tr>
                        <td><?php echo $article->title; ?></td>
                        <td><?php echo get_action_title($article->action); ?></td>
                        <td><?php echo get_category_title($categories, $article->action_category_id); ?></td>
                        <td><?php echo get_category_title($categories, $article->category_id); ?></td>
                        <td><?php echo get_bool($article->ruled); ?></td>
                        <td><?php echo get_bool($article->sorted); ?></td>
                        <td><?php echo get_bool($article->applied); ?></td>
                        <td>
                            <a href="<?php echo admin_url('admin.php?page=ai-sort-article-detail&article_id='.$article->id); ?>">Detail</a>
                        </td>
                    </tr>

                <?php endforeach; ?>
            </tbody>
        </table>

        <!-- pagination -->
        <div class="pagination" style="padding: 10px; text-align:center;">
            <?php
                $currentPage = $page;

                $min = 1;
                $max = $articlePages;

                if($currentPage > 5){
                    $min = $currentPage - 5;
                }

                if($currentPage + 5 < $articlePages){
                    $max = $currentPage + 5;
                }

                function copyGet($toAdd = []){
                    $query = $_GET;

                    foreach($toAdd as $key => $value){
                        $query[$key] = $value;
                    }

                    return "?".http_build_query($query);
                }

            ?>

            <?php if($currentPage > 1): ?>
                <a href="<?php echo copyGet(["pg"=>1]);?>"> << </a>
                <?php if($currentPage > 2): ?>
                    <span>...</span>
                <?php endif; ?>
                <a href="<?php echo copyGet(["pg"=>$currentPage - 1]);?>"> < </a>
            <?php endif; ?>

            <?php for($i = $min; $i <= $max; $i++): ?>
                <a href="<?php echo copyGet(["pg"=>$i]);?>" <?php if($i == $currentPage) echo 'class="active"'; ?>><?php echo $i; ?></a>
            <?php endfor; ?>

            <?php if($currentPage < $articlePages): ?>
                <a href="<?php echo copyGet(["pg"=>$currentPage + 1]);?>"> > </a>
                <?php if($currentPage < $articlePages - 1): ?>
                    <span>...</span>
                <?php endif; ?>
                <a href="<?php echo copyGet(["pg"=>$articlePages]);?>"> >> </a>
            <?php endif; ?>

        </div>
        <?php
    }
}