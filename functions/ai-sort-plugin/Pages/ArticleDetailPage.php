<?php

namespace AiSortPlugin\Pages;

require_once aiSortPath('Models/ArticleModel.php');
require_once aiSortPath('Models/CategoryModel.php');

use AiSortPlugin\Models\ArticleModel;
use AiSortPlugin\Models\CategoryModel;

class ArticleDetailPage{

    public static function render(){
        //get article by $_GET['article_id']
        $article_id = $_GET['article_id'];

        $article = ArticleModel::getById($article_id);

        function get_category_title($categories, $id){
            foreach($categories as $category){
                if($category->id == $id){
                    return $category->full_title;
                }
            }
            return "--none--";
        }

        function get_action_title($action){
            if($action == 'move'){
                return 'Move';
            }elseif($action == 'sort'){
                return 'Sort';
            } else {
                return 'Unknown';
            }
        }

        function get_bool($value){
            if($value == 1){
                return "Yes";
            }else{
                return "No";
            }
        }

        ?>
        <script>
            function HTTPClient(){


            }

            HTTPClient.prototype.post = function(url, data){
                return new Promise(function(resolve, reject){
                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', url, true);
                    xhr.setRequestHeader('Content-Type', 'application/json');
                    xhr.onload = function(){
                        if(xhr.status >= 200 && xhr.status < 400){
                            try{
                                resolve(JSON.parse(xhr.responseText));
                            }catch(e){
                                resolve(xhr.responseText);
                            }
                        } else {
                            reject(xhr.responseText);
                        }
                    }

                    xhr.send(data);
                });
            }

            HTTPClient.prototype.get = function(url){
                return new Promise(function(resolve, reject){
                    var xhr = new XMLHttpRequest();
                    xhr.open('GET', url, true);
                    xhr.onload = function(){
                        if(xhr.status >= 200 && xhr.status < 400){
                            try{
                                resolve(JSON.parse(xhr.responseText));
                            }catch(e){
                                resolve(xhr.responseText);
                            }
                        } else {
                            reject(xhr.responseText);
                        }
                    }

                    xhr.send();
                });
            }


            function ruleArticle(articleId){
                var client = new HTTPClient();

                client.get('/wp-json/ai-sort/rule-article?article_id='+articleId)
                    .then(function(response){
                        console.log(response);
                        //reload page
                        location.reload();
                    })
                    .catch(function(error){
                        console.log(error);
                    });
            }

            function sortArticle(articleId){
                var client = new HTTPClient();

                client.get('/wp-json/ai-sort/sort-article?article_id='+articleId)
                    .then(function(response){
                        console.log(response);
                        //reload page
                        location.reload();
                    })
                    .catch(function(error){
                        console.log(error);
                    });
            }
        </script>
        <div class="wrap">
            <h1><?php echo $article->title;?></h1>

            <table class="wp-list-table fixed striped">
                <tr>
                    <td>Title</td>
                    <td><?php echo $article->title;?></td>
                </tr>
                <tr>
                    <td>Action</td>
                    <td><?php echo get_action_title($article->action);?></td>
                </tr>
                <tr>
                    <td>Action Category</td>
                    <td><?php echo get_category_title(CategoryModel::getCategories(), $article->action_category_id); ?></td>
                </tr>
                <tr>
                    <td>Category</td>
                    <td><?php echo get_category_title(CategoryModel::getCategories(), $article->category_id); ?></td>
                </tr>
                <tr>
                    <td>Ruled</td>
                    <td><?php echo get_bool($article->ruled); ?></td>
                </tr>
                <tr>
                    <td>Sorted</td>
                    <td><?php echo get_bool($article->sorted); ?></td>
                </tr>
                <tr>
                    <td>Applied</td>
                    <td><?php echo get_bool($article->applied); ?></td>
                </tr>
                <tr>
                    <td>Sort Error</td>
                    <td><?php echo get_bool($article->sort_error); ?></td>
                </tr>
                <tr>
                    <td>Apply Error</td>
                    <td><?php echo get_bool($article->apply_error); ?></td>
                </tr>
            </table>

            <a href="<?php echo admin_url('admin.php?page=ai-sort-articles');?>">Back</a>
            <a href="javascript:ruleArticle(<?php echo $article->id;?>)">Rule</a>
            <a href="javascript:sortArticle(<?php echo $article->id;?>)">Sort</a>

        </div>
        <?php
    }
}