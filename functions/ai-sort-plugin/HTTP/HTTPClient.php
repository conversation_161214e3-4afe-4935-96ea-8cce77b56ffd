<?php

namespace AiSortPlugin\HTTP;

require_once aiSortPath('HTTP/HTTPRequest.php');
require_once aiSortPath('HTTP/HTTPClient.php');

use AiSortPlugin\HTTP\HTTPResponse;
use AiSortPlugin\HTTP\HTTPRequest;


class HTTPClient{
    public static function doRequest(HTTPRequest $request): HTTPResponse {
        $ch = curl_init($request->getUrl());
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $request->getMethod());
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $headers = [];
        foreach($request->getHeaders() as $header => $value){
            $headers[] = $header . ': ' . $value;
        }
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        if($request->getBody() && in_array($request->getMethod(), ['POST', 'PUT', 'PATCH'])){
            //set raw post data
            curl_setopt($ch, CURLOPT_POSTFIELDS, $request->getBody());
        }

        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headers = curl_getinfo($ch);
        curl_close($ch);
        return new HTTPResponse($statusCode, $headers, $response);
    }

    public static function createRequest($url, $method = 'GET'){
        return new HTTPRequest($url, $method);
    }
}
