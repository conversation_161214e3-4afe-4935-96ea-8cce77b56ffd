<?php

namespace AiSortPlugin\HTTP;

require_once aiSortPath('HTTP/HTTPRequest.php');
require_once aiSortPath('HTTP/HTTPClient.php');

use AiSortPlugin\HTTP\HTTPClient;
use AiSortPlugin\HTTP\HTTPResponse;


class HTTPRequest{
    protected $url;
    protected $method;
    protected $headers = [];
    protected $body;

    public function __construct($url, $method = 'GET')
    {
        $this->url = $url;
        $this->method = $method;
    }

    public function addHeader($header, $value){
        $this->headers[$header] = $value;
        return $this;
    }

    public function addHeaders($headers){
        $this->headers = array_merge($this->headers, $headers);
        return $this;
    }

    public function setBody($body){
        $this->body = $body;
    }

    public function setUrl($url){
        $this->url = $url;
        return $this;
    }

    public function setMethod($method){
        $this->method = $method;
        return $this;
    }

    public function getUrl(){
        return $this->url;
    }

    public function getMethod(){
        return $this->method;
    }

    public function getHeaders(){
        return $this->headers;
    }

    public function getBody(){
        return $this->body;
    }

    public function send(): HTTPResponse{
        return HTTPClient::doRequest($this);
    }

    public function asJson(){
        return [
            'url' => $this->url,
            'method' => $this->method,
            'headers' => $this->headers,
            'body' => $this->body
        ];
    }

}