<?php

namespace AiSortPlugin\Models;

class RulesModel{
    public static function getRules(){
        global $wpdb;

        $qry = "SELECT * FROM ai_sort_rules order by weight, ord";

        return $wpdb->get_results($qry);
    }

    public static function getRule($id){
        global $wpdb;

        $qry = "SELECT * FROM ai_sort_rules where id = $id";

        return $wpdb->get_row($qry);
    }

    public static function deleteRule($id){
        global $wpdb;

        $wpdb->delete('ai_sort_rules', ['id' => $id]);
    }

    public static function addRule($rule){
        global $wpdb;

        $data = [
            'weight' => $rule['weight']??0,
            'ord' => $rule['ord']??0,
            'tag_slug' => $rule['tag_slug'],
            'with_tag_slug' => $rule['with_tag_slug']??null,
            'action' => $rule['action'],
            'action_category_id' => $rule['action_category_id']??null
        ];

        if($data['with_tag_slug'] == ""){
            $data['with_tag_slug'] = null;
        }

        if($data['action_category_id'] == ""){
            $data['action_category_id'] = null;
        }


        $wpdb->insert('ai_sort_rules', $data);
    }

    public static function importRules($rules, $weight = 0){
        global $wpdb;

        foreach($rules as $i=>$rule){
            $rule['weight'] = $weight;
            $rule['ord'] = $i;

            //get category id by slug
            if($rule['action_category'] && !$rule['action_category_id']){
                $sql = "SELECT id FROM ai_sort_categories where slug = '{$rule['action_category']}'";

                $category = $wpdb->get_row($sql);

                if($category){
                    $rule['action_category_id'] = $category->id;
                }
            }

            self::addRule($rule);
        }
    }
}