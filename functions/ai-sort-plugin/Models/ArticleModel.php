<?php

namespace AiSortPlugin\Models;

class ArticleModel{
    public static $articesPerPage = 50;

    public static function getArticles($page = 1, $query = ""){
        global $wpdb;

        $offset = ($page - 1) * self::$articesPerPage;

        if($query == ""){
            $qry = "SELECT * FROM ai_sort_articles order by wp_id desc limit $offset, ".self::$articesPerPage;
        }else{
            $qry = "SELECT * FROM ai_sort_articles where title like '%$query%' order by wp_id desc limit $offset, ".self::$articesPerPage;
        }

        return $wpdb->get_results($qry);
    }

    public static function getByWpId($wp_id){
        global $wpdb;

        $qry = "SELECT * FROM ai_sort_articles where wp_id = $wp_id";

        return $wpdb->get_row($qry);
    }

    public static function getById($id){
        global $wpdb;

        $qry = "SELECT * FROM ai_sort_articles where id = $id";

        return $wpdb->get_row($qry);
    }

    public static function getMaxWpId(){
        global $wpdb;

        $qry = "SELECT max(wp_id) as max_wp_id FROM ai_sort_articles";

        $result = $wpdb->get_row($qry);

        if(!$result->max_wp_id){
            return 0;
        }

        return $result->max_wp_id;
    }

    public static function getTotalPages($query = ""){
        global $wpdb;

        if($query == ""){
            $qry = "SELECT count(*) as total FROM ai_sort_articles";
        }else{
            $qry = "SELECT count(*) as total FROM ai_sort_articles where title like '%$query%'";
        }

        $result = $wpdb->get_row($qry);

        return ceil($result->total / self::$articesPerPage);
    }

    public static function insertArticle($article){
        global $wpdb;

        $art = [
            'wp_id' => $article['wp_id'],
            'title' => $article['title']
        ];

        $wpdb->insert('ai_sort_articles', $art);
    }

    public static function countTotalArticles(){
        global $wpdb;

        $qry = "SELECT count(*) as total FROM ai_sort_articles";

        $result = $wpdb->get_row($qry);

        if(!$result){
            return 0;
        }

        return intval($result->total);
    }

    public static function countRuledArticles(){
        global $wpdb;

        $qry = "SELECT count(*) as ruled FROM ai_sort_articles where ruled = 1";

        $result = $wpdb->get_row($qry);

        if(!$result->ruled){
            return 0;
        }

        return intval($result->ruled);
    }

    public static function countAppliedArticles(){
        global $wpdb;

        $qry = "SELECT count(*) as applied FROM ai_sort_articles where applied = 1";

        $result = $wpdb->get_row($qry);

        if(!$result->applied){
            return 0;
        }

        return intval($result->applied);
    }

    public static function countTotalToApply(){
        global $wpdb;

        $qry = "SELECT count(*) as total FROM ai_sort_articles where sorted = 1 AND sort_error = 0 AND applied = 0";

        $result = $wpdb->get_row($qry);

        if(!$result->total){
            return 0;
        }

        return intval($result->total);
    }

    public static function countTotalToSort(){
        global $wpdb;

        $qry = "SELECT count(*) as total FROM ai_sort_articles where ruled=1";

        $result = $wpdb->get_row($qry);

        if(!$result->total){
            return 0;
        }

        return intval($result->total);
    }

    public static function countDoneArticles(){
        return self::countSortedArticles();
    }

    public static function countSortedArticles(){
        global $wpdb;

        $qry = "SELECT count(*) as done FROM ai_sort_articles where sorted = 1";

        $result = $wpdb->get_row($qry);

        if(!$result->done){
            return 0;
        }

        return intval($result->done);
    }

    public static function countTotalPosts($postType){
        global $wpdb;

        $qry = "SELECT count(*) as total FROM wp_posts where post_type = '$postType' and post_status = 'publish'";

        $result = $wpdb->get_row($qry);

        if(!$result->total){
            return 0;
        }

        return $result->total;
    }

    public static function countDonePosts($postType, $lastId){
        global $wpdb;

        $qry = "SELECT count(*) as done FROM wp_posts where post_type = '$postType' and post_status = 'publish' and ID <= $lastId";

        $result = $wpdb->get_row($qry);

        if(!$result->done){
            return 0;
        }

        return $result->done;
    }


}