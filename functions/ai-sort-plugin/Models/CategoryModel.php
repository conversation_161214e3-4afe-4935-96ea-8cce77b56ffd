<?php

namespace AiSortPlugin\Models;

class CategoryModel{
    public static function getCategories(){
        global $wpdb;

        $qry = "SELECT * FROM ai_sort_categories order by full_title";

        return $wpdb->get_results($qry);
    }

    public static function getCategory($id){
        global $wpdb;

        $qry = "SELECT * FROM ai_sort_categories where id = $id";

        return $wpdb->get_row($qry);
    }

    public static function deleteCategory($id){
        global $wpdb;

        $wpdb->delete('ai_sort_categories', ['id' => $id]);
    }

    public static function slugify($text)
    {
        // replace all special czech characters wirth their english counterparts e.g. č -> c
        $translateTable = array(
            'á' => 'a',
            'ä' => 'a',
            'č' => 'c',
            'ď' => 'd',
            'é' => 'e',
            'ě' => 'e',
            'í' => 'i',
            'ľ' => 'l',
            'ĺ' => 'l',
            'ň' => 'n',
            'ó' => 'o',
            'ô' => 'o',
            'ř' => 'r',
            'ŕ' => 'r',
            'š' => 's',
            'ť' => 't',
            'ú' => 'u',
            'ů' => 'u',
            'ý' => 'y',
            'ž' => 'z',
            '&' => ' and '
        );

        $slug = mb_strtolower($text, 'UTF-8');

        //replace special characters
        $slug = strtr($slug, $translateTable);

        //replace all non-alphanumeric characters with a dash
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);

        //replace spaces with a dash
        $slug = str_replace(' ', '-', $slug);

        //remove multiple dashes
        $slug = preg_replace('/-+/', '-', $slug);

        //remove dashes from the beginning and the end
        $slug = trim($slug, '-');

        return $slug;
    }

    public static function getCategoryBySlug($slug){
        global $wpdb;

        $qry = "SELECT * FROM ai_sort_categories where slug = '$slug'";

        return $wpdb->get_row($qry);
    }

    public static function editCategory($categoryId, $category){
        global $wpdb;

        $data = [];

        if(isset($category['title'])){
            $data['title'] = $category['title'];
        }

        if(isset($category['parent_id'])){
            $data['parent_id'] = $category['parent_id'];
        }

        if(isset($category['sort_parent_id'])){
            $data['sort_parent_id'] = $category['sort_parent_id'];
        }

        if(isset($category['keywords'])){
            $data['keywords'] = $category['keywords'];
        }

        if(isset($category['slug'])){
            $data['slug'] = $category['slug'];
        }

        if(isset($category['wp_id'])){
            $data['wp_id'] = $category['wp_id'];

            if($data['wp_id'] == 0 || $data['wp_id'] == ""){
                $data['wp_id'] = null;
            }
        }

        if(isset($category['wp_taxonomy'])){
            $data['wp_taxonomy'] = $category['wp_taxonomy'];
        }

        if($data['title']){
            $data['full_title'] = $data['title'];

            //find parent ither by parent_id in $category (if provided) or in db (if not)
            $parent_id = $category['parent_id'] ?? $wpdb->get_var("SELECT parent_id FROM ai_sort_categories where id = $categoryId");

            if($parent_id){
                $parent = CategoryModel::getCategory($parent_id);
                $data['full_title'] = $parent->full_title." :: ".$data['title'];
            }
        }


        if(empty($data)){
            return;
        }

        $wpdb->update('ai_sort_categories', $data, ['id' => $categoryId]);
    }

    public static function addCategory($category){
        global $wpdb;

        $cat = [
            'title' => $category['title'],
            'parent_id' => $category['parent_id'] ?? null,
            'sort_parent_id' => $category['sort_parent_id'] ?? null,
            'keywords' => $category['keywords'] ?? '',
            'slug' => $category['slug'] ?? null,
            'wp_id' => $category['wp_id'] ?? null,
            'wp_taxonomy' => $category['wp_taxonomy'] ?? 'category',
            'full_title' => $category['full_title'] ?? null
        ];

        if($cat['parent_id'] == 0 || $cat['parent_id'] == ""){
            $cat['parent_id'] = null;
        }

        if($cat['sort_parent_id'] == 0 || $cat['sort_parent_id'] == ""){
            $cat['sort_parent_id'] = null;
        }

        if($cat['wp_id'] == 0 || $cat['wp_id'] == ""){
            $cat['wp_id'] = null;
        }

        if($cat['slug'] == null || $cat['slug'] == ""){
            $cat['slug'] = self::slugify($cat['title']);

            if($cat['parent_id']){
                $parent = CategoryModel::getCategory($cat['parent_id']);
                $cat['slug'] = $parent->slug."_".$cat['slug'];
            }
        }

        //if slug starts with "base_" remove it
        if(strpos($cat['slug'], "base_") === 0){
            $cat['slug'] = substr($cat['slug'], 5);
        }

        if($cat['full_title'] == null || $cat['full_title'] == ""){
            $cat['full_title'] = $cat['title'];

            if($cat['parent_id']){
                $parent = CategoryModel::getCategory($cat['parent_id']);
                $cat['full_title'] = $parent->full_title." :: ".$cat['title'];
            }
        }

        $wpdb->insert('ai_sort_categories', $cat);

        return $wpdb->insert_id;
    }

    public static function importCategories($categories, $parent_id = null, $taxonomy = 'category'){
        global $wpdb;

        foreach($categories as $category){
            $category['parent_id'] = $parent_id;
            $category['sort_parent_id'] = $parent_id;

            if(isset($category['disconnect']) && ($category['disconnect']==true)){
                $category['sort_parent_id'] = null;
            }

            if(!isset($category['wp_taxonomy'])){
                $category['wp_taxonomy'] = $taxonomy; //set taxonomy for this category and all children (if they don't have their own)
            }

            $catId = CategoryModel::addCategory($category);

            if(isset($category['children'])){
                CategoryModel::importCategories($category['children'], $catId, $category['wp_taxonomy']);
            }
        }
    }

    public static function getLeafCategoriesRecursive($parent_id = null){
        $result = [];

        global $wpdb;

        $qry = "SELECT * FROM ai_sort_categories where sort_parent_id = $parent_id";

        if($parent_id == null){
            $qry = "SELECT * FROM ai_sort_categories where sort_parent_id is null)";
        }

        //get all categories in current parent
        $categories = $wpdb->get_results($qry);

        foreach($categories as $category){
            //check if category has children
            $children = CategoryModel::getLeafCategoriesRecursive($category->id);

            if(count($children) == 0){
                $result[] = $category;
            } else {
                $result = array_merge($result, $children);
            }
        }

        return $result;
    }

    public static function getLeafCategories($parent_id = null){
        if(!$parent_id){
            $parent_id = get_option('ai_sort_default_category_id');
        }

        $categories = self::getLeafCategoriesRecursive($parent_id);


        $result = [];

        foreach($categories as $category){
            //split full_title by " :: " 
            //ignore first part (Life/Base)
            //set second part as level1_title
            //set third part as level2_title (if exists)

            $parts = explode(" :: ", $category->full_title);

            $level1_title = $parts[1];
            $level2_title = null;

            if(count($parts) > 2){
                $level2_title = $parts[2];
            }


            $leaves[] = [
                'category_id' => $category->id,
                'level1_title' => $level1_title,
                'level2_title' => $level2_title,
                'keywords' => $category->keywords
            ];
        }

        return $leaves;
    }
}