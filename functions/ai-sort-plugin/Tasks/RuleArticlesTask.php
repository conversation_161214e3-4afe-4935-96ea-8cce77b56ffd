<?php

namespace AiSortPlugin\Tasks;

require_once aiSortPath('Models/ArticleModel.php');
require_once aiSortPath('Models/RulesModel.php');

use AiSortPlugin\Models\ArticleModel;
use AiSortPlugin\Models\RulesModel;

class RuleArticlesTask
{
    public static $batchSize = 100;

    public static $rules = [];
    public static $tagTaxonomy = 'post_tag';

    public static $categoryDefaultId = 1;

    public static function ruleOneArticle(){
        $id = $_GET['article_id'];

        if(!$id){
            return;
        }

        //set rules for this task
        self::$rules = RulesModel::getRules();
        self::$tagTaxonomy = get_option('ai_sort_post_tag_taxonomy');
        self::$categoryDefaultId = get_option('ai_sort_default_category_id');

        $article = ArticleModel::getById($id);

        if(!$article){
            return;
        }

        self::ruleArticle($article);
    }

    public static function ruleArticle($article){

        //get article tags
        $tags = wp_get_post_terms($article->wp_id, self::$tagTaxonomy);

        //if tags is wp_error return
        if(is_wp_error($tags)){
            return;
        }

        //create array of tag slugs
        $tagSlugs = array_map(function($tag){
            return $tag->slug;
        }, $tags);

        //rules should be set before calling this function and in correct order
        $ruledData = null;

        foreach(self::$rules as $rule){
            //if $rule->tag_slug is in $tags continue
            if(!in_array($rule->tag_slug, $tagSlugs)){
                continue;
            }

            //if $rule->with_tag_slug is set and not in $tags continue
            if($rule->with_tag_slug && !in_array($rule->with_tag_slug, $tagSlugs)){
                continue;
            }

            $ruledData = $rule;
            break;
        }

        global $wpdb;

        if(!$ruledData){
            //no rule found sort to default category
            $updateData = [
                'ruled' => 1,
                'sorted' => 0,
                'action' => "sort",
                'action_category_id' => self::$categoryDefaultId, //base category id,
                'category_id' => null
            ];
        }else{

            $updateData = [
                'ruled' => 1,
                'sorted' => 0,
                'action' => $ruledData->action,
                'action_category_id' => $ruledData->action_category_id,
                'category_id' => null
            ];

            if($ruledData->action == 'move'){
                //if action is move, we can set final category and mark as sorted
                $updateData['category_id'] = $ruledData->action_category_id;
                $updateData['sorted'] = 1;
            }
        }

        $wpdb->update('ai_sort_articles', $updateData, ['id' => $article->id]);
    }

    public static function ruleArticles(){
        //get batch number of articles from  ai_sort_articles table
        //that have not been ruled yet

        global $wpdb;

        $qry = "SELECT * FROM ai_sort_articles where ruled = 0 order by id limit ".self::$batchSize;
        $articles = $wpdb->get_results($qry);

        //set rules for this task
        self::$rules = RulesModel::getRules();
        self::$tagTaxonomy = get_option('ai_sort_post_tag_taxonomy');
        self::$categoryDefaultId = get_option('ai_sort_default_category_id');


        //get total articles
        $total = ArticleModel::countTotalArticles();

        //get done articles
        $done = ArticleModel::countRuledArticles();

        foreach($articles as $article){
            self::ruleArticle($article);
        }

        return [
            'total' => $total,
            'done' => $done,
            'batchSize' => self::$batchSize
        ];
    }
}