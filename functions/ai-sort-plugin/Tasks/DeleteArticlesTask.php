<?php

namespace AiSortPlugin\Tasks;


class DeleteArticlesTask
{

    public static $batchSize = 1000;

    public static function checkAuth(){
        if($_GET['safe_key'] != get_option('ai_sort_safe_key')){
            die('Auth error');
        }
    }

    public static function deleteArticles(){

        //delete batch of articles
        $query = "DELETE FROM ai_sort_articles ORDER BY id ASC LIMIT ".self::$batchSize;

        global $wpdb;

        $res = $wpdb->query($query);

        return [
            'deleted' => $res,
            'batchSize' => self::$batchSize
        ];
    }
}