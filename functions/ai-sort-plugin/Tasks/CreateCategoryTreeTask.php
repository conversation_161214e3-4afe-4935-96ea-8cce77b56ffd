<?php

namespace AiSortPlugin\Tasks;

require_once aiSortPath('Models/CategoryModel.php');

use AiSortPlugin\Models\CategoryModel;

class CreateCategoryTreeTask
{
    public static function createWPCategoryTree($parent_id, $taxonomy = 'category', $parent_wp_id = null){
        global $wpdb;

        $qry = "SELECT * FROM ai_sort_categories where parent_id = $parent_id";

        $children = $wpdb->get_results($qry);

        //for each child check if it exists as term in wordpress
        foreach($children as $child){
            $term = null;
            if($parent_wp_id == null){
                $term = term_exists($child->title, $taxonomy);
            } else {
                $term = term_exists($child->title, $taxonomy, $parent_wp_id);
            }

            if(!$term){
                $term = wp_insert_term($child->title, $taxonomy, ['parent' => $parent_wp_id]);
            }

            // if term is WP_Error
            if(is_wp_error($term)){
                echo "Error inserting term: ".$term->get_error_message();
                die();
            }


            //save wp_id in ai_sort_categories
            $qry = "UPDATE ai_sort_categories set wp_id = $term[term_id] where id = $child->id";
            $wpdb->query($qry);

            //recursively call this function for each child
            self::createWPCategoryTree($child->id, $taxonomy, $term['term_id']);
        }
    }

    public static function createCategories(){

        global $wpdb;

        $categories = CategoryModel::getCategories();

        //foreach category with parent_id = null, call createWPCategoryTree
        foreach($categories as $category){
            if($category->parent_id == null){
                self::createWPCategoryTree($category->id, $category->wp_taxonomy);
            }
        }

        return [
            'done' => true
        ];
    }
}