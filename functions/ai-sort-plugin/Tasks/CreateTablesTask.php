<?php

namespace AiSortPlugin\Tasks;


class CreateTablesTask
{

    public static function createTables(){

        global $wpdb;

        //create ai_sort_categories table
        $sql = "CREATE TABLE IF NOT EXISTS ai_sort_categories (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            wp_id INT NULL,
            wp_taxonomy VARCHAR(255) NULL,
            parent_id INT UNSIGNED NULL,
            sort_parent_id INT UNSIGNED NULL,
            title VARCHAR(255) NOT NULL,
            full_title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL,
            keywords TEXT NULL
        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;";

        $wpdb->query($sql);

        //create indexes for ai_sort_categories table if not exists
        $wpdb->query("CREATE INDEX IF NOT EXISTS wp_id_index ON ai_sort_categories (wp_id);");

        //create foreign key
        $wpdb->query("ALTER TABLE ai_sort_categories ADD CONSTRAINT fk_parent_id FOREIGN KEY (parent_id) REFERENCES ai_sort_categories(id);");
        $wpdb->query("ALTER TABLE ai_sort_categories ADD CONSTRAINT fk_sort_parent_id FOREIGN KEY (sort_parent_id) REFERENCES ai_sort_categories(id);");


        //create ai_sort_articles table
        $sql = "CREATE TABLE IF NOT EXISTS ai_sort_articles (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            wp_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            action varchar(255) NOT NULL,
            action_category_id int UNSIGNED NULL,
            category_id int UNSIGNED NULL,
            ruled BOOLEAN DEFAULT false,
            sorted BOOLEAN DEFAULT false,
            sort_error BOOLEAN DEFAULT false,
            applied BOOLEAN DEFAULT false,
            apply_error BOOLEAN DEFAULT false
        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;";

        $wpdb->query($sql);

        //create indexes for ai_sort_articles table if not exists
        $wpdb->query("CREATE INDEX IF NOT EXISTS action_index ON ai_sort_articles (action);");
        //create unique index for wp_id
        $wpdb->query("CREATE UNIQUE INDEX IF NOT EXISTS wp_id_index ON ai_sort_articles (wp_id);");

        //we use this in where clause
        $wpdb->query("CREATE INDEX IF NOT EXISTS ruled_index ON ai_sort_articles (ruled);");
        $wpdb->query("CREATE INDEX IF NOT EXISTS sorted_index ON ai_sort_articles (sorted);");
        $wpdb->query("CREATE INDEX IF NOT EXISTS sort_error_index ON ai_sort_articles (sort_error);");
        $wpdb->query("CREATE INDEX IF NOT EXISTS applied_index ON ai_sort_articles (applied);");
        $wpdb->query("CREATE INDEX IF NOT EXISTS apply_error_index ON ai_sort_articles (apply_error);");

        //foreign key
        $wpdb->query("ALTER TABLE ai_sort_articles ADD CONSTRAINT fk_action_category_id FOREIGN KEY (action_category_id) REFERENCES ai_sort_categories(id);");
        $wpdb->query("ALTER TABLE ai_sort_articles ADD CONSTRAINT fk_category_id FOREIGN KEY (category_id) REFERENCES ai_sort_categories(id);");


        //create ai_sort_rules table
        $sql = "CREATE TABLE IF NOT EXISTS ai_sort_rules (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            weight INT NOT NULL,
            ord INT NOT NULL,
            tag_slug VARCHAR(255) NOT NULL,
            with_tag_slug VARCHAR(255) NULL,
            action VARCHAR(255) NOT NULL,
            action_category_id INT UNSIGNED NULL
        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;";
        $wpdb->query($sql);

        //create indexes for ai_sort_rules table if not exists (weight, ord - we use this to sort rules)
        $wpdb->query("CREATE INDEX IF NOT EXISTS weight_index ON ai_sort_rules (weight);");
        $wpdb->query("CREATE INDEX IF NOT EXISTS ord_index ON ai_sort_rules (ord);");

        //foreign key
        $wpdb->query("ALTER TABLE ai_sort_rules ADD CONSTRAINT fk_action_category_id FOREIGN KEY (action_category_id) REFERENCES ai_sort_categories(id);");

        //create ai_sort_logs table
        $sql = "CREATE TABLE IF NOT EXISTS ai_sort_logs (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            title varchar(255) NOT NULL,
            prompt TEXT NOT NULL,
            response TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;";
        $wpdb->query($sql);

        //create indexes for ai_sort_logs table if not exists
        $wpdb->query("CREATE INDEX IF NOT EXISTS created_at_index ON ai_sort_logs (created_at);");
    }
}