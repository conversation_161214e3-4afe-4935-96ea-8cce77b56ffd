<?php

namespace AiSortPlugin\Tasks;

require_once aiSortPath('Models/ArticleModel.php');
require_once aiSortPath('HTTP/HTTPClient.php');
require_once aiSortPath('Models/CategoryModel.php');
require_once aiSortPath('AICategorization/AICategorizationProvider.php');

use AiSortPlugin\Models\ArticleModel;
use AiSortPlugin\Models\CategoryModel;
use AiSortPlugin\AICategorization\AICategorizationProvider;

class SortArticlesTask
{
    public static $batchSize = 1;

    public static $tagTaxonomy = 'post_tag';
    public static $postType = 'post';

    public static $aiProvider = null;


    public static function sortOneArticle(){
        $id = $_GET['article_id'];

        if(!$id){
            return [];
        }

        self::$postType = get_option('ai_sort_post_type');
        self::$tagTaxonomy = get_option('ai_sort_post_tag_taxonomy');

        self::$aiProvider = new AICategorizationProvider();

        $article = ArticleModel::getById($id);

        if(!$article){
            return;
        }

        return self::sortArticle($article);
    }


    public static function sortArticle($article){

        global $wpdb;

        try {
            //get article tags
            $tags = wp_get_post_terms($article->wp_id, self::$tagTaxonomy);

            //create array of tag slugs
            $tagNames = array_map(function($tag){
                return $tag->name;
            }, $tags);

            //get article content (without html tags)
            $wpPost = get_post($article->wp_id);

            //get article content
            $content = strip_tags($wpPost->post_content);

            //get article title
            $title = $article->title;

            //get article author
            $author = get_the_author_meta('display_name', $wpPost->post_author);

            //set delay
            sleep(2);

            //get leaf categories for article
            $categories = CategoryModel::getLeafCategories($article->action_category_id);

            $categoryId = self::$aiProvider->categorize($categories, $title, $content, $tagNames, $author);

            //save category id to articles table
            $wpdb->update('ai_sort_articles', ['category_id' => $categoryId, "sorted"=>1], ['id' => $article->id]);
        } catch (\Exception $e) {
            //save category id to articles table
            $wpdb->update('ai_sort_articles', ['category_id' => 0, "sorted"=>1, "sort_error"=>1], ['id' => $article->id]);
        }

        return [
            'category_id' => $categoryId
        ];
    }

    public static function sortArticles(){
        //get batch number of articles from  ai_sort_articles table
        //that have not been ruled yet
        self::$postType = get_option('ai_sort_post_type');
        self::$tagTaxonomy = get_option('ai_sort_post_tag_taxonomy');

        self::$aiProvider = new AICategorizationProvider();

        global $wpdb;

        $qry = "SELECT * FROM ai_sort_articles where sorted = 0 AND ruled = 1 order by id DESC limit ".self::$batchSize;
        $articles = $wpdb->get_results($qry);

        //get total articles
        $total = ArticleModel::countTotalArticles();

        $results = [];

        foreach($articles as $article){
            $results[] = self::sortArticle($article);
        }

        //get done articles
        $done = ArticleModel::countDoneArticles();

        return [
            'total' => $total,
            'done' => $done, // we are not saving, so we want to exit loop - $done,
            'batchSize' => self::$batchSize,
            'results' => $results
        ];
    }
}