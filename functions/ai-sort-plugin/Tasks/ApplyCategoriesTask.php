<?php

namespace AiSortPlugin\Tasks;

require_once aiSortPath('Models/ArticleModel.php');
require_once aiSortPath('Models/CategoryModel.php');

use AiSortPlugin\Models\ArticleModel;

class ApplyCategoriesTask
{
    public static $batchSize = 1;

    public static function applyCategory($article){
        //find category by term_id
        //get category from ai_sort_categories
        global $wpdb;
        $qry = "SELECT * FROM ai_sort_categories where id = $article->category_id";
        $category = $wpdb->get_row($qry);

        if(!$category){
            $wpdb->update('ai_sort_articles', ['applied' => 1, "apply_error"], ['id' => $article->id]);
            return [
                'article' => $article,
                'error' => 'Category not found'
            ];
        }

        if($category->wp_id == null){
            $wpdb->update('ai_sort_articles', ['applied' => 1, "apply_error"], ['id' => $article->id]);
            return [
                'article' => $article,
                'error' => 'Category not found in WP'
            ];
        }

        $term = null;
        if($category->wp_id){
            $term = get_term($category->wp_id);
        }

        if(!$term){
            //update article
            $wpdb->update('ai_sort_articles', ['applied' => 1, "apply_error"], ['id' => $article->id]);
            return [
                'article' => $article,
                'error' => 'Category not found'
            ];
        }

        //get term taxonomy
        $taxonomy = get_taxonomy($term->taxonomy);

        //assign category to article
        $result = wp_set_post_terms($article->wp_id, $term->term_id, $taxonomy->name);

        //update article
        $wpdb->update('ai_sort_articles', ['applied' => 1], ['id' => $article->id]);

        return [
            'article' => $article,
            'result' => $result
        ];

    }


    public static function applyCategories(){
        //get batch number of articles from  ai_sort_articles table
        //that have not been ruled yet
        global $wpdb;


        $qry = "SELECT * FROM ai_sort_articles where sorted = 1 AND applied=0 order by id limit ".self::$batchSize;
        $articles = $wpdb->get_results($qry);

        $totalToApply = ArticleModel::countTotalToApply();

        $results = [];

        foreach($articles as $article){
            $results[] = self::applyCategory($article);
        }

        //get done articles
        $done = ArticleModel::countAppliedArticles();

        return [
            'total' => $totalToApply + $done,
            'done' => $done, // we are not saving, so we want to exit loop - $done,
            'batchSize' => self::$batchSize,
            'results' => $results
        ];
    }
}