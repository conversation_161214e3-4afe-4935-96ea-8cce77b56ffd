<?php

namespace AiSortPlugin\Tasks;


class DropTablesTask
{
    public static function checkAuth(){
        if($_GET['safe_key'] != get_option('ai_sort_safe_key')){
            die('Auth error');
        }
    }

    public static function dropTables(){

        self::checkAuth();

        //delete all articles
        global $wpdb;

        $resArt = $wpdb->query("DROP TABLE IF EXISTS ai_sort_articles");
        $resRules = $wpdb->query("DROP TABLE IF EXISTS ai_sort_rules");
        $resCats = $wpdb->query("DROP TABLE IF EXISTS ai_sort_categories");
        $resLogs = $wpdb->query("DROP TABLE IF EXISTS ai_sort_logs");

        return [
            'articles' => $resArt,
            'rules' => $resRules,
            'categories' => $resCats,
            'logs' => $resLogs
        ];
    }
}