<?php

namespace AiSortPlugin\Tasks;

require_once aiSortPath('Models/ArticleModel.php');

use AiSortPlugin\Models\ArticleModel;

class PrepareArticlesTask
{
    public static $batchSize = 100;

    public static function prepareArticles(){
        //get post type from settings
        $postType = get_option('ai_sort_post_type');

        //get lastId from $_GET params
        $lastId = ArticleModel::getMaxWpId();

        //get "self::$batchSize" published posts of post type Where ID > $lastId
        global $wpdb;
        $qry = "SELECT * FROM wp_posts where post_type = '$postType' and post_status = 'publish' and ID > $lastId order by ID limit ".self::$batchSize;
        $posts = $wpdb->get_results($qry);

        //qry total posts and numeber of post with id <= $lastId "as done"
        $total = ArticleModel::countTotalPosts($postType);
        $done = ArticleModel::countDonePosts($postType, $lastId);

        $newLastId = $lastId;

        //prepare articles
        foreach($posts as $post){
            $article = [
                'wp_id' => $post->ID,
                'title' => $post->post_title
            ];

            ArticleModel::getByWpId($post->ID);

            //insert article
            ArticleModel::insertArticle($article);

            $newLastId = $post->ID;
        }

        return [
            'total' => $total,
            'done' => $done,
            'lastId' => $newLastId,
            'batchSize' => self::$batchSize
        ];
    }
}