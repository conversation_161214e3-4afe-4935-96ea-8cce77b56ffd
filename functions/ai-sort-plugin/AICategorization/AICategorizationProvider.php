<?php

namespace AiSortPlugin\AICategorization;

require_once aiSortPath('HTTP/HTTPRequest.php');
require_once aiSortPath('HTTP/HTTPClient.php');
require_once aiSortPath('HTTP/HTTPResponse.php');

use AiSortPlugin\HTTP\HTTPRequest;
use AiSortPlugin\HTTP\HTTPClient;
use AiSortPlugin\HTTP\HTTPResponse;

class AICategorizationProvider{
    protected $apiUrl;
    protected $apiKey;

    protected $version;

    protected $extraHeaders = [
        'Content-Type' => 'application/json'
    ];

    public function __construct(){
        $this->apiUrl = get_option('ai_sort_api_url');
        $this->apiKey = get_option('ai_sort_api_key');
        $this->version = get_option('ai_sort_api_version');

        $this->extraHeaders['Anthropic-Version'] = $this->version;
    }

    protected function generatePrompt($categories, $title, $content, $tags, $author){
        $promptText = "Tu je zoznam hlavných kategórie Level 1 a podradených kategórií Level 2, a k nim priradené kľúčové slová.\n\n";
        $promptText .= "1st level Category\t2nd Level Category\tKeywords\n";

        foreach($categories as $category){
            $promptText .= $category['level1_title'] . "\t" . ($category['level2_title']??'') . "\t" . ($category['keywords']??'') . "\n";
        }

        $promptText .= "\n";

        $promptText .= "Navrhni mi kategórie pre tento článok nižšie len zo zoznamu kategórií podľa jeho názvu klucovych slov a obsahu:\n\n";

        $promptText .= "Nadpis: " . $title . "\n\n";
        $promptText .= "Autor: " . $author . "\n\n";
        $promptText .= "Kľúčové slová: " . implode(", ", $tags) . "\n\n";

        $promptText .= $content . "\n\n";

        return $promptText;
    }

    public function categorize($categories, $title, $content, $tags, $author){
        $prompt = $this->generatePrompt($categories, $title, $content, $tags, $author);

        $request = HTTPClient::createRequest($this->apiUrl, 'POST');
        $request->addHeaders($this->extraHeaders);
        $request->addHeader('X-Api-Key', $this->apiKey);

        $data = [
            "model" => "claude-3-5-sonnet-20241022",
            "max_tokens" => 100,
            "system" => [
                [
                    "type" => "text",
                    "text" => "Skráť odpoveď na maximálne 2 návrhy. Nehalucinuj."
                ],
                [
                    "type" => "text",
                    "text" => "present your output as json format"
                ],
                [
                    "type" => "text",
                    "text" => "only output a valid json and nothing else"
                ],
                [
                    "type" => "text",
                    "text" => "json format should be like this: {\"categories\": [{\"level1\": \"category level 1\", \"level2\": \"Category level 2\"}, ...]} where level2 is optional"
                ],
                [
                    "type" => "text",
                    "text" => "do not makeup factual information"
                ]
            ],
            "messages"=> [
                [
                    "role" => "user",
                    "content" => $prompt
                ]
            ]
        ];

        $request->setBody(json_encode($data));

        $response = $request->send();

        $responseData = json_decode($response->getBody(), true);

        /*file_put_contents(aiSortPath('Logs/AICategorization/'.date('Y-m-d_H-i-s').'-'.$title.'.json'), json_encode([
            'request' => $data,
            'response' => $responseData
        ], JSON_PRETTY_PRINT));*/


        global $wpdb;
        //insert log
        $wpdb->insert('ai_sort_logs', [
            'title' => $title,
            'prompt' => $prompt,
            'response' => $response->getBody(),
            'created_at' => date('Y-m-d H:i:s')
        ]);


        $categoryId = null;

        //chech it there is content[0] and it has 'text' key
        if(isset($responseData['content']) && isset($responseData['content'][0]) && isset($responseData['content'][0]['text'])){
            try{
                $categoriesData = json_decode($responseData['content'][0]['text'], true);
            }catch(\Exception $e){
                throw new \Exception("Non JSON response from AI categorization service");
            }

            if(isset($categoriesData['categories'])){
                //find returned categories titles in $categories and return their ids

                foreach($categoriesData['categories'] as $category){
                    if(!isset($category['level2'])){
                        $category['level2'] = null;
                    }

                    foreach($categories as $cat){
                        if($cat['level2_title'] == $category['level2'] && $cat['level1_title'] == $category['level1']){
                            $categoryId = $cat['category_id'];
                            break;
                        }

                    }

                    if($categoryId){
                        break;
                    }
                }

                if(!$categoryId){
                    foreach($categoriesData['categories'] as $category){
                        foreach($categories as $cat){
                            if($cat['level1_title'] == $category['level1']){
                                $categoryId = $cat['category_id'];
                                break;
                            }
                        }
                        if($categoryId){
                            break;
                        }
                    }
                }
            }
        }else{
            throw new \Exception("Invalid response from AI categorization service");
        }

        return intval($categoryId);
    }
}