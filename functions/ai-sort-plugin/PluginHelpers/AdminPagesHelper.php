<?php

namespace AiSortPlugin\PluginHelpers;

class AdminPagesHelper{
    public function __construct(){
        $this->menuPages = [];

        //create hidden page for hidden subpages
        $this->menuPages['__hidden__'] = [
            'pageTitle' => '__hidden__',
            'subMenu' => []
        ];
    }

    protected $menuPages = [];

    public function addMenuPage($pageTitle, $menuTitle, $capability, $menuSlug, $callback){
        $this->menuPages[$menuSlug] = [
            'pageTitle' => $pageTitle,
            'menuTitle' => $menuTitle,
            'capability' => $capability,
            'menuSlug' => $menuSlug,
            'callback' => $callback,
            'subMenu' => []
        ];
    }

    public function addSubMenuPage($parentSlug, $pageTitle, $menuTitle, $capability, $menuSlug, $callback){
        if(!isset($this->menuPages[$parentSlug])){
            return;
        }

        $this->menuPages[$parentSlug]['subMenu'][$menuSlug] = [
            'pageTitle' => $pageTitle,
            'menuTitle' => $menuTitle,
            'capability' => $capability,
            'menuSlug' => $menuSlug,
            'callback' => $callback
        ];
    }

    public function addHiddenPage($pageTitle, $menuTitle, $capability, $menuSlug, $callback){
        $this->menuPages['__hidden__']['subMenu'][$menuSlug] = [
            'pageTitle' => $pageTitle,
            'menuTitle' => $menuTitle,
            'capability' => $capability,
            'menuSlug' => $menuSlug,
            'callback' => $callback
        ];
    }

    public function createPages(){
        //create menu pages / sub menu pages based on the menuPages array
        foreach($this->menuPages as $menuPage){
            if($menuPage['pageTitle'] == '__hidden__'){
                continue;
            }

            add_menu_page(
                $menuPage['pageTitle'],
                $menuPage['menuTitle'],
                $menuPage['capability'],
                $menuPage['menuSlug'],
                $menuPage['callback']
            );

            foreach($menuPage['subMenu'] as $subMenu){
                add_submenu_page(
                    $menuPage['menuSlug'],
                    $subMenu['pageTitle'],
                    $subMenu['menuTitle'],
                    $subMenu['capability'],
                    $subMenu['menuSlug'],
                    $subMenu['callback']
                );
            }
        }

        //create hidden subpages
        foreach($this->menuPages['__hidden__']['subMenu'] as $subMenu){
            add_submenu_page(
                null,
                $subMenu['pageTitle'],
                $subMenu['menuTitle'],
                $subMenu['capability'],
                $subMenu['menuSlug'],
                $subMenu['callback']
            );
        }
    }
}
