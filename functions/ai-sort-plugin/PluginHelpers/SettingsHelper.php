<?php

namespace AiSortPlugin\PluginHelpers;


class SettingsHelper{
    public function __construct(){
        $this->settings = [];
    }

    protected $settings = [];

    protected $group = "ai_sort";

    public function addSetting($section, $setting, $sectionName, $settingName, $renderCallback, $defaultValue){
        $this->settings[$section."__".$setting] = [
            'setting' => $setting,
            'section' => $section,
            'settingName' => $settingName,
            'sectionName' => $sectionName,
            'renderCallback' => $renderCallback,
            'defaultValue' => $defaultValue
        ];
    }

    public function registerSettings(){
        foreach($this->settings as $setting){
            register_setting($this->group, $setting['setting']);
        }


        $groups = [];
        //create sections based on groups
        foreach($this->settings as $setting){
            if(!isset($groups[$setting['section']])){
                add_settings_section(
                    $setting['section'],
                    $setting['sectionName'],
                    null,
                    $this->group
                );
            }
        }

        foreach($this->settings as $setting){
            add_settings_field(
                $setting['setting'],
                $setting['settingName'],
                $setting['renderCallback'],
                $this->group,
                $setting['section']
            );
        }
    }

    public function getValue($group, $setting){
        $value = get_option($setting);

        if($value == "" || $value == null){

            if(!isset($this->settings[$group."__".$setting])){
                return null;
            }

            $value = $this->settings[$group."__".$setting]['defaultValue'];
        }

        return $value;
    }
}