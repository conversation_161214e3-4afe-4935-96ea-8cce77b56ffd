<?php

namespace AiSortPlugin\PluginHelpers;

/**
 * APIHelperRoute class, enables API endpoints to be created
 * before `rest_api_init` action is fired
 */

class APIHelperRoute
{
    /**
     * @var string $route
     */
    protected $route;

    /**
     * @var array $methods
     */
    protected $methods;

    /**
     * @var callable $callback
     */
    protected $callback;

    /**
     * @var callable $permissionCallback
     */
    protected $permissionCallback;

    /**
     * Constructor
     * @param string $route
     * @param array $methods
     * @param callable $callback
     * @param callable $permissionCallback
     */
    public function __construct($route, $methods, $callback, $permissionCallback = null)
    {
        $this->route = $route;
        $this->methods = $methods;
        $this->callback = $callback;
        $this->permissionCallback = $permissionCallback;
    }

    /**
     * Get route
     * @return string
     */
    public function getRoute()
    {
        return $this->route;
    }

    /**
     * Get methods
     * @return array
     */
    public function getMethods()
    {
        return $this->methods;
    }

    /**
     * Get callback
     * @return callable
     */
    public function getCallback()
    {
        return $this->callback;
    }

    /**
     * Get permission callback
     * @return callable
     */
    public function getPermissionCallback()
    {
        return $this->permissionCallback;
    }
}

class APIHelper
{
    /**
     * api namespace
     */
    protected $apiNamespace;

    /**
     * @var bool $apiInitialized
     */
    protected $apiInitialized = false;

    /**
     * @var APIHelperRoute[] $routes
     */
    protected $routes = array();

    /**
     * Constructor
     * @param string $apiNamespace
     */
    public function __construct($apiNamespace)
    {
        //set namespace
        $this->apiNamespace = $apiNamespace;

        //empty routes array
        $this->routes = array();

        //add rest_api_init action hook
        add_action('rest_api_init', array($this, 'initialize'));
    }

    /**
     * Initialize API endpoints
     */
    public function initialize()
    {
        //iterate over routes and register them
        foreach ($this->routes as $route) {
            register_rest_route($this->apiNamespace, $route->getRoute(), array(
                'methods' => $route->getMethods(),
                'callback' => $route->getCallback(),
                'permission_callback' => $route->getPermissionCallback()
            ));
        }

        //set apiInitialized flag to true
        $this->apiInitialized = true;
    }

    /**
     * Add route
     * @param string $route
     * @param array $methods
     * @param callable $callback
     * @param callable $permissionCallback
     */
    public function addRoute($route, $methods, $callback, $permissionCallback = null)
    {
        //create route object
        $route = new APIHelperRoute($route, $methods, $callback, $permissionCallback);

        //if api is initialized, register route
        if ($this->apiInitialized) {
            register_rest_route($this->apiNamespace, $route->getRoute(), array(
                'methods' => $route->getMethods(),
                'callback' => $route->getCallback(),
                'permission_callback' => $route->getPermissionCallback()
            ));
        }

        //add route to routes array
        $this->routes[] = $route;
    }
}

