<?php
/*
	Allow only podcast type posts for the featured podcast selector on the home page (CZ)
	*/
add_filter('acf/fields/post_object/query/name=featured_podcast', 'my_acf_fields_post_object_query', 10, 3);
function my_acf_fields_post_object_query($args, $field, $post_id) {
	$args['meta_query'] = array(
		array(
			'key'	  => 'type',
			'compare' => '==',
			'value'	  => 'podcast',
		),
	);

	return $args;
}