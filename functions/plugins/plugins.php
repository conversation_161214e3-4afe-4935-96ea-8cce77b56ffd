<?php
/*
Extended the nonce time for public previews
*/
if (is_plugin_active('public-post-preview/public-post-preview.php')) {
	add_filter('ppp_nonce_life', 'my_nonce_life');
	function my_nonce_life() {
		return 60 * 60 * 24 * 4;
	}
}

add_filter('acf/fields/post_object/query/name=article', function ($args, $field, $post_id) {
	$args['post_status'] = 'publish';
	return $args;
}, 10, 3);

add_filter('remp_content_locked', function ($content, $types, $type) {
	$types = json_encode(array_filter($types));
	ob_start();
	get_template_part('template-parts/premium/blocker/index', null, ['types' => $types, 'type' => $type]);
	$blocker = ob_get_contents();
	ob_end_clean();
	$content .= $blocker;
	return "
			{$content}
		";
}, 10, 3);