<?php

// 1. Add admin page
add_action('admin_menu', function() {
    add_submenu_page(
        'tools.php',
        'Coauthor Migration',
        'Coauthor Migration',
        'manage_options',
        'coauthor-migration',
        function() {
            ?>
            <div class="wrap">
                <h1>Coauthor Migration</h1>
				<p style="max-width: 600px">This tool will migrate authors to coauthors. It will process posts in batches to avoid timeouts. All posts without an assigned coauthor will be checked. Coauthors will be assigned based on the author's last name and first name. <b>No new coauthors will be created, only existing ones will be used.</b></p>
                <button id="start-migration" class="button button-primary">Start Migration</button>
                <div id="migration-progress" style="margin-top: 20px;"></div>
            </div>
            <script type="text/javascript">
            jQuery(document).ready(function($) {
                $('#start-migration').click(function(e) {
                    e.preventDefault();
                    let $button = $(this);
                    $button.prop('disabled', true);

                    const processBatch = (offset = 0) => {
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'coauthor_migration',
                                security: '<?php echo wp_create_nonce('coauthor_migration_nonce'); ?>',
                                offset: offset
                            },
                            success: function(response) {
                                if (response.data && response.data.complete) {
                                    $('#migration-progress').append('<p>Migration complete! Total processed: ' + response.data.total_processed + '</p>');
                                    $button.prop('disabled', false);
                                    return;
                                }

                                $('#migration-progress').prepend(
                                    `<p>Batch: ${response.data.batch_count} posts checked | Assigned: ${response.data.processed} | Total: ${response.data.total_processed}</p>`
                                );

                                processBatch(response.data.new_offset);
                            },
                            error: function() {
                                $('#migration-progress').append('<p>Error occurred!</p>');
                                $button.prop('disabled', false);
                            }
                        });
                    };

                    processBatch();
                });
            });
            </script>
            <?php
        }
    );
});

// 2. AJAX handler for migration
add_action('wp_ajax_coauthor_migration', function() {
    check_ajax_referer('coauthor_migration_nonce', 'security');

    $batch_size = 100;
    $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;

    // Get posts in current batch
    $posts = get_posts([
        'post_type' => 'post',
        'posts_per_page' => $batch_size,
        'offset' => $offset,
        'fields' => 'ids',
    ]);

    if (empty($posts)) {
        wp_send_json_success(['complete' => true]);
    }

    $processed = 0;
    $current_batch_count = count($posts);

    foreach ($posts as $post_id) {
		// Skip if coauthor already exists
		if (has_term('', 'coauthor', $post_id)) continue;

        $author = get_userdata(get_post_field('post_author', $post_id));
        if (!$author) continue;

        $term_name = trim($author->last_name . ' ' . $author->first_name);
        if (empty($term_name)) continue;

        $term = term_exists($term_name, 'coauthor');
        if ($term && !is_wp_error($term)) {
            wp_set_post_terms($post_id, [(int)$term['term_id']], 'coauthor', false);
            $processed++;
        }
    }

    $new_offset = $offset + $current_batch_count;

    wp_send_json_success([
        'processed' => $processed,
        'batch_count' => $current_batch_count,
        'total_processed' => $offset + $current_batch_count,
        'new_offset' => $new_offset,
        'complete' => false
    ]);
});
