<?php

/**
 * Helper function to check if the content type is life.
 *
 * @return bool True if content type is life, otherwise false.
 */
function is_life_content() {
    return defined('CONTENT_TYPE') && CONTENT_TYPE === "life";
}

/**
 * Get the base URL for life content.
 *
 * @return string The base URL for life content.
 */
function get_life_base_url() {
    if(APP_ENV === 'production'){
        return 'https://life.forbes.cz';
    } else {
        return 'https://stage-life.forbes.cz';
    }
}

/**
 * Get the default base URL.
 *
 * @return string The default base URL.
 */
function get_default_base_url() {
    if(APP_ENV === 'production'){
        return 'https://forbes.cz';
    } else {
        return 'https://stage.forbes.cz';
    }
}

/**
 * Get the allowed terms for life content.
 *
 * @return array The array of allowed terms IDs.
 */
function get_allowed_life_terms() {
    return get_field('allowed_terms', 'option'); // Fetch allowed terms IDs
}

/**
 * Check if a post should have separate life content.
 *
 * @param int $post_id The ID of the post.
 * @return bool True if the post has separate life content, otherwise false.
 */
function post_belongs_to_forbes_life($post_id) {
	$post = get_post($post_id);

	if ($post->post_type === 'post') {
		return taxonomy_exists('life_category') && has_term('', 'life_category', $post_id);
	}

	if ($post->post_type === 'ecommerce_product') {
		return true; // E-commerce products always belong to life content.
	}

	return false; // Default placement is Forbes, not Life
}

/**
 * Add a custom body class based on content type.
 *
 * @param array $classes The array of body classes.
 * @return array The modified array of body classes.
 */
function add_custom_body_class_based_on_life_content($classes) {
    if (is_life_content()) {
        $classes[] = 'content-type--life';
    } else {
        $classes[] = 'content-type--default';
    }
    return $classes;
}
add_filter('body_class', 'add_custom_body_class_based_on_life_content');

/**
 * Check if the URL is a preview URL.
 *
 * @return bool True if the URL is a preview URL, otherwise false.
 */
function is_preview_url() {
    return isset($_GET['preview']) && ($_GET['preview'] === 'true' || $_GET['preview'] === '1');
}

/** Redirect single posts based on content type and allowed/disallowed terms. */
function redirect_posts_based_on_content_type() {
	global $wp;

	$is_market = str_starts_with($_SERVER['REQUEST_URI'], '/market/');

	if ($is_market && !is_life_content()) {
		$redirect_url = get_life_base_url() . $_SERVER['REQUEST_URI'];
		wp_redirect($redirect_url, 301);
		exit;
	}

    if (is_single() && !is_preview_url()) {
		$post_belongs_to_life = post_belongs_to_forbes_life(get_the_ID());
		$has_content_mismatch = is_life_content() !== $post_belongs_to_life;

		if ($has_content_mismatch) {
			$post_slug = get_post_field('post_name', get_post());
			$base_url = $post_belongs_to_life ? get_life_base_url() : get_default_base_url();
			$redirect_url = trailingslashit($base_url . '/' . $post_slug);
			$current_url = home_url(add_query_arg([], $wp->request));

			if ($current_url !== $redirect_url && !headers_sent()) {
				wp_redirect($redirect_url, 301);
				exit;
			}
		}
    }
}

add_action('template_redirect', 'redirect_posts_based_on_content_type');

/**
 * Redirect coauthors based on their roles.
 */
function redirect_coauthors_based_on_roles() {
    if (is_tax('coauthor')) {
        global $wp;
        $term = get_queried_object();
        $author_roles = get_field('authorRole', 'coauthor_' . $term->term_id);
        $is_preview = is_preview_url();


        if ($is_preview) {
            return;
        }

        if (is_life_content()) {
            if (!is_array($author_roles) || !in_array('life', $author_roles)) {
                $redirect_url = get_default_base_url() . '/autor/' . $term->slug . '/';

                // Ensure the URL ends with a trailing slash
                if (substr($redirect_url, -1) !== '/') {
                    $redirect_url .= '/';
                }

                if (home_url(add_query_arg(array(), $wp->request)) !== $redirect_url) {
                    if (!headers_sent()) {
                        wp_redirect($redirect_url, 301); // Use 301 for permanent redirect if applicable
                       	exit; // Ensure no further code is executed after redirection
                    }
                }
            }
        } else {
            if (is_array($author_roles) && in_array('life', $author_roles)) {
                $redirect_url = get_life_base_url() . '/autor/' . $term->slug . '/';

                // Ensure the URL ends with a trailing slash
                if (substr($redirect_url, -1) !== '/') {
                    $redirect_url .= '/';
                }

                if (home_url(add_query_arg(array(), $wp->request)) !== $redirect_url) {
                    if (!headers_sent()) {
                        wp_redirect($redirect_url, 301); // Use 301 for permanent redirect if applicable
                        exit; // Ensure no further code is executed after redirection
                    }
                }
            }
        }
    }
}
add_action('template_redirect', 'redirect_coauthors_based_on_roles');

function redirect_stitek_terms() {

	if (is_tax('stitek')) {
		global $wp;
        $term = get_queried_object();
        $allowed_terms = get_field('allowed_terms', 'option'); // Fetch allowed terms IDs

        if (is_life_content()) {

			// Check if the current term ID is not in the allowed list
			if (!in_array($term->term_id, $allowed_terms)) {
				$redirect_url = get_default_base_url() . '/tag/' . $term->slug . '/';
				if (home_url(add_query_arg(array(), $wp->request)) !== $redirect_url) {
                    if (!headers_sent()) {
                        wp_redirect($redirect_url, 301); // Use 301 for permanent redirect if applicable
                       	exit; // Ensure no further code is executed after redirection
                    }
                }
			}
		}else{

			// Check if the current term ID is in the allowed list
			if (in_array($term->term_id, $allowed_terms)) {
				$redirect_url = get_life_base_url() . '/tag/' . $term->slug . '/';
				if (home_url(add_query_arg(array(), $wp->request)) !== $redirect_url) {
					if (!headers_sent()) {
						wp_redirect($redirect_url, 301); // Use 301 for permanent redirect if applicable
						exit; // Ensure no further code is executed after redirection
					}
			}}
		}

    }
}
add_action('template_redirect', 'redirect_stitek_terms');

/**
 * Filter Yoast SEO sitemap for coauthor taxonomy entries.
 *
 * @param string $url The URL of the sitemap entry.
 * @param string $type The type of the sitemap entry.
 * @param object $object The object of the sitemap entry.
 * @return string|false The URL of the sitemap entry or false to exclude it.
 */
function filter_coauthor_entries_from_sitemap($url, $type, $object) {
    if ($type === 'term' && $object->taxonomy === 'coauthor') {
        $term_id = $object->term_id;
        $author_roles = get_field('authorRole', 'coauthor_' . $term_id);

        if (is_life_content()) {
            // If content type is life, include only authors with 'life' role

            if (!is_array($author_roles) || !in_array('life', $author_roles)) {
                return false;
            }
        } else {
            // If default content type, exclude authors with 'life' role
            if (is_array($author_roles) && in_array('life', $author_roles)) {
                return false;
            }
        }
    }
    return $url;
}
add_filter('wpseo_sitemap_entry', 'filter_coauthor_entries_from_sitemap', 10, 3);

function exclude_taxonomies_from_sitemap($should_exclude, $taxonomy) {
	if ($should_exclude) {
		return true;
	}

	$shared_taxonomies = ['stitek', 'coauthor'];
	$is_shared_taxonomy = in_array($taxonomy, $shared_taxonomies);

	if ($is_shared_taxonomy) {
		return false;
	}

	$life_taxonomies = ['life_category', 'ecommerce_category', 'ecommerce_brand', 'ecommerce_vendor', 'ecommerce_gender'];
	$is_life_taxonomy = in_array($taxonomy, $life_taxonomies);
	$has_content_mismatch = is_life_content() !== $is_life_taxonomy;

	return $has_content_mismatch;
}
add_filter('wpseo_sitemap_exclude_taxonomy', 'exclude_taxonomies_from_sitemap', 10, 2);

function exclude_custom_post_types_from_sitemap($should_exclude, $post_type) {
	if ($should_exclude) {
		return true;
	}

	$shared_post_types = ['post', 'page'];
	$is_shared_post_type = in_array($post_type, $shared_post_types);

	if ($is_shared_post_type) {
		return false;
	}

	$life_post_types = ['ecommerce_product'];
	$is_life_post_type = in_array($post_type, $life_post_types);
	$has_content_mismatch = is_life_content() !== $is_life_post_type;

	return $has_content_mismatch;
}
add_filter('wpseo_sitemap_exclude_post_type', 'exclude_custom_post_types_from_sitemap', 10, 2);

/**
 * Filter Yoast SEO sitemap entries based on post terms and separate content setting.
 *
 * @param string $url The URL of the sitemap entry.
 * @param string $type The type of the sitemap entry.
 * @param object $object The object of the sitemap entry.
 * @return string|false The URL of the sitemap entry or false to exclude it.
 */
function filter_sitemap_entries_based_on_content($url, $type, $object) {
    if ($type === 'post') {
        $post_date_str = get_the_date('Y-m-d', $object->ID);
        $post_date = new DateTime($post_date_str);
        $cutoff_date = new DateTime('2021-02-26');
        $post_terms = wp_get_post_terms($object->ID, CATEGORY_TYPE, array('fields' => 'ids')); // Get post terms for the specified taxonomy
        $allowed_terms = get_allowed_life_terms(); // Fetch allowed terms
        $separate_for_life_content = post_belongs_to_forbes_life($object->ID); // Check the separate_for_life_content field

        if (is_life_content()) {
            if ((($post_date > $cutoff_date) && !$separate_for_life_content) || (($post_date <= $cutoff_date) && empty(array_intersect($post_terms, $allowed_terms)))) {
                // If the post should be redirected to the default site, exclude from sitemap
                return false;
            }
        } else {
            if ((($post_date > $cutoff_date) && $separate_for_life_content) || (($post_date <= $cutoff_date) && !empty(array_intersect($post_terms, $allowed_terms)))) {
                // If the post should be redirected to the life site, exclude from sitemap
                return false;
            }
        }
    }
    return $url;
}
add_filter('wpseo_sitemap_entry', 'filter_sitemap_entries_based_on_content', 10, 3);


/**
 * Filter the Yoast SEO canonical URL.
 *
 * This function checks if the current page is a life content page. If it is, it constructs the
 * canonical URL using the base URL for life content and the current request path.
 * If it's not a life content page, it uses Yoast's default canonical URL.
 *
 * @param string $canonical The default canonical URL generated by Yoast.
 * @return string The modified canonical URL.
 */
function custom_canonical_url($canonical) {
    global $wp;

    if (is_life_content()) {
        $base_url = get_life_base_url();
        $canonical = untrailingslashit($base_url . '/' . $wp->request) . '/';
    }

    return $canonical;
}

// Modify the Yoast SEO canonical URL with a higher priority
add_filter('wpseo_canonical', 'custom_canonical_url', 20);

// For every post query, filter out posts that should not be displayed on the current site
add_action('pre_get_posts', function (\WP_Query $query) {
	if ($query->get('post_type') === 'post' && !is_admin()) {
		$tax_query = $query->get('tax_query');
		$is_author_query = $query->is_author() || $query->is_tax('coauthor');

		$content_split_query = [
			'taxonomy' => is_life_content() ? 'life_category' : 'category',
			'operator' => 'EXISTS',
		];

		// Exceptions where the content filter should not be applied:
		//   1. Author / coauthor queries
		//   2. Explicit content filter for life_category or category
		$apply_content_filter = !$is_author_query;

		if (is_array($tax_query)) {
			foreach ($tax_query as $item) {
				$taxonomy = $item['taxonomy'] ?? null;
				$operator = $item['operator'] ?? 'IN';

				$has_explicit_content_filter =
					in_array($taxonomy, ['life_category', 'category']) &&
					in_array($operator, ['IN', 'EXISTS']);

				if ($has_explicit_content_filter) {
					$apply_content_filter = false;
				}
			}

			if ($apply_content_filter) {
				$tax_query = [
					'relation' => 'AND',
					$tax_query,
					$content_split_query,
				];

				$query->set('tax_query', $tax_query);
			}
		} else if ($apply_content_filter) {
			$query->set('tax_query', [$content_split_query]);
		} else {
			// Do not apply the content filter.
		}
	}
});
