<?php

/**
 * Adds a custom RSS feed 'seznam' with callback function 'generate_seznam_rss_feed' to WordPress.
 * Hooked to 'init' action.
 */
function add_custom_seznam_rss_feed() {
    add_feed('seznam', 'generate_seznam_rss_feed');
}
add_action('init', 'add_custom_seznam_rss_feed');

/**
 * Retrieves posts based on specific criteria including allowed terms and premium tag.
 *
 * @param int $limit The maximum number of posts to retrieve. Default is 20.
 * @return WP_Query WP_Query object containing the retrieved posts.
 */
function get_all_posts($limit = 20): WP_Query {
    $premium_term = get_field('premium_tag', 'option');

    return new WP_Query([
        'post_type' => 'post',
        'posts_per_page' => $limit,
        'orderby' => 'date',
        'order' => 'DESC',
		'tax_query' => [
            [
                'taxonomy' => 'stitek',
                'field' => 'term_id',
                'terms' => $premium_term,
                'operator' => 'NOT IN'
			],
		],
        'meta_query' => [
            [
                'key' => 'publish_to_seznam_feed',
                'value' => '1',
                'compare' => '='
			]
		],
    ]);
}

/**
 * Retrieves the post excerpt without the ellipsis ([...]) at the end.
 *
 * @return string The modified post excerpt.
 */
function custom_get_the_excerpt_rss() {
    global $post;
    $excerpt = get_the_excerpt($post); // Get the post excerpt
    return preg_replace('/\s*\[&hellip;\]$/', '', $excerpt); // Remove ellipsis from the excerpt
}

/**
 * Generates the 'seznam' RSS feed with specific formatting for Seznam.cz integration.
 * Outputs XML content directly.
 */
function generate_seznam_rss_feed() {
    $query = get_all_posts(); // Get posts based on criteria defined in get_all_posts()

    // Set RSS header with charset
    header('Content-Type: application/rss+xml; charset=' . get_option('blog_charset'), true);

    // Start generating the RSS feed
    echo '<?xml version="1.0" encoding="' . get_option('blog_charset') . '"?>';
    ?>
<rss xmlns:szn="https://www.seznam.cz" xmlns:atom="http://www.w3.org/2005/Atom" xmlns:content="http://purl.org/rss/1.0/modules/content/" version="2.0">
    <channel>
        <title><?php bloginfo_rss('name'); ?></title>
        <link><?php bloginfo_rss('url'); ?></link>
        <description><?php bloginfo_rss('description'); ?></description>
        <docs>http://www.rssboard.org/rss-specification</docs>
        <generator>wordpress</generator>
        <lastBuildDate><?php echo date('r'); ?></lastBuildDate>
        <?php while ($query->have_posts()) : $query->the_post(); ?>
        <item>
            <title><?php echo html_entity_decode(get_the_title(), ENT_QUOTES, 'UTF-8'); ?></title>
            <link><?php the_permalink_rss(); ?></link>
            <description><?php echo esc_html(html_entity_decode(wp_trim_words(custom_get_the_excerpt_rss(), 30, '...'), ENT_QUOTES, 'UTF-8')); ?></description>
            <guid isPermaLink="false"><?php the_guid(); ?></guid>
            <pubDate><?php echo get_post_time('r', true); ?></pubDate>
            <?php
            $terms = get_the_terms(get_the_ID(), 'stitek');
            if ($terms && !is_wp_error($terms)) {
                foreach ($terms as $term) {
                    echo '<category>' . esc_html($term->name) . '</category>';
                }
            }
            ?>
            <?php if (has_post_thumbnail()) : ?>
            <?php $thumbnail_url = get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>
            <szn:image>
                <szn:url><?php echo esc_url($thumbnail_url); ?></szn:url>
                <szn:title><?php echo html_entity_decode(get_the_title(), ENT_QUOTES, 'UTF-8'); ?></szn:title>
                <szn:height>40</szn:height>
                <szn:previewtype>common</szn:previewtype>
            </szn:image>
            <?php endif; ?>
            <?php
            // Add custom fields for Seznam.cz if available
            $exp_date = get_post_meta(get_the_ID(), 'szn_expDate', true);
            if ($exp_date) {
                echo '<szn:expDate>' . date(DATE_RFC822, strtotime($exp_date)) . '</szn:expDate>';
            }
            if (get_post_meta(get_the_ID(), 'szn_expired', true)) {
                echo '<szn:expired />';
            }
            ?>
        </item>
        <?php endwhile; wp_reset_postdata(); ?>
    </channel>
</rss>
<?php
}
