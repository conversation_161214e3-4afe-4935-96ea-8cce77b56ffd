<?php

/**
 * Enqueue scripts.
 */
function frontend_scripts_sk() {
    $printCategory = get_field('print_category', 'option');
	$show_ads = get_field('show_ads', get_the_ID()) ?? true;
	$hide_ads = get_field('hide_ads', get_the_ID()) ?? false;

    if (defined('WP_DEBUG') && ! WP_DEBUG &&
        $show_ads &&
		! $hide_ads &&
        ! empty($printCategory) &&
        ! is_page_template([
			'page-my-account.php',
			'page-authentication.php',
			'template-subscription.php',
			'page-sales-funnel.php',
			'page-sales-funnel-pricing.php',
			'template-thank-you.php',
	        'template-print.php',
	        'template-pricing.php'
		]) &&
        ! has_term($printCategory, CATEGORY_TYPE, get_the_ID())
	) {
		wp_enqueue_script('google-ads-script-sk', get_template_directory_uri() . '/minified-js/google/googleads_SK.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/google/googleads_SK.min.js'), true);
	}
}
add_action('wp_enqueue_scripts', 'frontend_scripts_sk');

/**
 * Enqueue admin scripts for Slovakia.
 */
function admin_scripts_sk(): void
{
	// Enqueue the media library customization script
	wp_enqueue_script(
		'media-library-sk',
		get_template_directory_uri() . '/minified-js/media-library-sk.min.js',
		array('jquery', 'media-editor'),
		filemtime(get_template_directory() . '/minified-js/media-library-sk.min.js'),
		true
	);
}

add_action('admin_enqueue_scripts', 'admin_scripts_sk');
