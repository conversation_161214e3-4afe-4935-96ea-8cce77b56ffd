<?php

/**
 * Add async scripts
 */
function add_async_to_scripts($url)
{
	$async_tag = '#asyncload';
	if (strpos($url, $async_tag) === false) {
		return $url;
	} else if (is_admin()) {
		return str_replace($async_tag, '', $url);
	} else {
		return str_replace($async_tag, '', $url) . "' async='async";
	}
}
add_filter('clean_url', 'add_async_to_scripts', 11, 1);

/**
 * Enqueue scripts.
 */
function frontend_scripts()
{
	wp_enqueue_script('common-script', get_template_directory_uri() . '/minified-js/common.min.js', array(), filemtime(get_template_directory() . '/minified-js/common.min.js'), true);
	wp_localize_script('common-script', 'ajaxUrlObject', array(
		'url' => admin_url('admin-ajax.php'),
	));

	wp_enqueue_script('remp-premium-lock', get_template_directory_uri() . '/minified-js/premium-lock/premium-lock.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/premium-lock/premium-lock.min.js'), true);

	wp_enqueue_script('remp-api-client-script', get_template_directory_uri() . '/minified-js/remp-api-client.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/remp-api-client.min.js'), true);
	wp_enqueue_script('googleads', 'https://securepubads.g.doubleclick.net/tag/js/gpt.js#asyncload');
	wp_enqueue_script('splide-script', get_template_directory_uri() . '/inc/libraries/splide/splide.min.js', array('jquery'), get_template_directory() . '/inc/libraries/splide/splide.min.js', true);
	wp_enqueue_script('splide-extension-script', get_template_directory_uri() . '/inc/libraries/splide/splide-extension-intersection.min.js', array('jquery'), get_template_directory() . '/inc/libraries/splide/splide-extension-intersection.min.js', true);

	wp_enqueue_script('thank-you-script', get_template_directory_uri() . '/minified-js/thank-you.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/thank-you.min.js'), true);
	wp_localize_script('thank-you-script', 'configHelpers', array(
		'DN_REMP_CRM_HOST' => defined('DN_REMP_CRM_HOST') ? DN_REMP_CRM_HOST : '',
	));
	wp_enqueue_script('remp-banner-script', get_template_directory_uri() . '/minified-js/remp-banner.min.js', array('jquery'), get_template_directory() . '/minified-js/remp-banner.min.js', true);

	// Notification snackbars script
	wp_enqueue_script('notifications-script', get_template_directory_uri() . '/minified-js/notifications/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/notifications/scripts.min.js'), true);
	wp_enqueue_script('auth-script', get_template_directory_uri() . '/minified-js/authentication/scripts.min.js', array('jquery', 'notifications-script'), filemtime(get_template_directory() . '/minified-js/authentication/scripts.min.js'), true);

	$myAccountPageId = get_field('my_account_page_id', 'option');

	wp_localize_script('auth-script', 'vars', array(
		'translations' => array(
			'emailError' => esc_html__('Please enter a valid email address.', 'FORBES'),
			'passwordError' => esc_html__('Please enter a valid password.', 'FORBES'),
		),
		'myAccountPageLink' => get_the_permalink($myAccountPageId) ?? '',
	));

	if (is_archive()) {
		wp_enqueue_script('category-page-script', get_template_directory_uri() . '/minified-js/pages/category-page.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/pages/category-page.min.js'), true);
	}

	if (is_single()) {
		wp_enqueue_script('lity-script', get_template_directory_uri() . '/inc/libraries/lity/lity.min.js', array('jquery'), filemtime(get_template_directory() . '/inc/libraries/lity/lity.min.js'), true);
		wp_enqueue_script('gallery-script', get_template_directory_uri() . '/minified-js/gutenberg/gallery-script.min.js', array('jquery', 'macy-script'), filemtime(get_template_directory() . '/minified-js/gutenberg/gallery-script.min.js'), true);
		wp_enqueue_script('single-page-script', get_template_directory_uri() . '/minified-js/pages/single-page.min.js', array('jquery', 'lity-script'), filemtime(get_template_directory() . '/minified-js/pages/single-page.min.js'), true);
		wp_enqueue_script('iframe-resize', get_template_directory_uri() . '/minified-js/iframe-resize.min.js', array(), filemtime(get_template_directory() . '/minified-js/iframe-resize.min.js'), true);
	}

	if (is_single() ) {
		wp_enqueue_script('macy-script', get_template_directory_uri() . '/inc/libraries/macy/macy.min.js', array('jquery'), filemtime(get_template_directory() . '/inc/libraries/macy/macy.min.js'), true);
		wp_enqueue_script('swipebox-script', get_template_directory_uri() . '/inc/libraries/swipebox/js/jquery.swipebox.js', array('jquery'), filemtime(get_template_directory() . '/inc/libraries/swipebox/js/jquery.swipebox.js'), true);
	}

	if (is_page_template('page-onepage.php')) {
		wp_enqueue_script('macy-script', get_template_directory_uri() . '/inc/libraries/macy/macy.min.js', array('jquery'), filemtime(get_template_directory() . '/inc/libraries/macy/macy.min.js'), true);
		wp_enqueue_script('gallery-script', get_template_directory_uri() . '/minified-js/gutenberg/gallery-script.min.js', array('jquery', 'macy-script'), filemtime(get_template_directory() . '/minified-js/gutenberg/gallery-script.min.js'), true);
		wp_enqueue_script('lity-script', get_template_directory_uri() . '/inc/libraries/lity/lity.min.js', array('jquery'), filemtime(get_template_directory() . '/inc/libraries/lity/lity.min.js'), true);
		wp_enqueue_script('swipebox-script', get_template_directory_uri() . '/inc/libraries/swipebox/js/jquery.swipebox.js', array('jquery'), filemtime(get_template_directory() . '/inc/libraries/swipebox/js/jquery.swipebox.js'), true);
	}
	if (is_page_template('page-sales-funnel.php') || is_page_template('template-thank-you.php') || is_page_template('page-sales-funnel-pricing.php') || is_page_template('template-subscription.php')) {
		wp_enqueue_script('iframe-resizer', get_template_directory_uri() . '/assets/js/iframeResizer/iframeResizer.min.js', array('jquery'), filemtime(get_template_directory() . '/assets/js/iframeResizer/iframeResizer.min.js'), true);
		wp_enqueue_script('page-sales-funnel-script', get_template_directory_uri() . '/minified-js/pages/page-sales-funnel.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/pages/page-sales-funnel.min.js'), true);
	}
	if (is_page_template('page-lists.php')) {
		wp_enqueue_script('page-lists-script', get_template_directory_uri() . '/minified-js/pages/page-lists.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/pages/page-lists.min.js'), true);
	}
	if (is_page_template('page-premium-new.php')) {
		wp_enqueue_script('page-premium-script', get_template_directory_uri() . '/minified-js/premium-page/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/premium-page/scripts.min.js'), true);
	}

	if (is_singular('list')) {
		wp_enqueue_script('list-methodology-script', get_template_directory_uri() . '/minified-js/lists/list-methodology/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/lists/list-methodology/scripts.min.js'), true);
		wp_enqueue_script('list-page-script', get_template_directory_uri() . '/minified-js/lists/list-page/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/lists/list-page/scripts.min.js'), true);
	}

	if (is_singular('event')) {
		wp_enqueue_script('event-carousel-script', get_template_directory_uri() . '/minified-js/home-page-blocks/events-carousel/scripts.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/home-page-blocks/events-carousel/scripts.min.js'), true);
	}

	if (is_page_template('page-auth.php')) {
		wp_enqueue_script('page-auth-script', get_template_directory_uri() . '/minified-js/pages/page-auth.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/pages/page-auth.min.js'), true);
	}

	if (is_page_template('page-authors-forbes.php') || is_page_template('page-authors-all.php') || is_page_template('page-authors-contributors.php') || is_page_template('page-brandvoice-authors.php') || is_singular('post')) {

		wp_localize_script('my-authors-and-topics-script', 'translations', array(
			'follow' => esc_html__('Follow', 'FORBES'),
			'unfollow' => esc_html__('Unfollow', 'FORBES'),
			'loading' => esc_html__('Loading...', 'FORBES'),
			'editorLabel' => esc_html__('editor Forbes', 'FORBES'),
			'latestPostsTitle' => esc_html__('Latest Articles', 'FORBES'),
			'twitterLabel' => esc_html__('X (Twitter)', 'FORBES'),
			'instagramLabel' => esc_html__('Instagram', 'FORBES'),
			'linkedinLabel' => esc_html__('LinkedIn', 'FORBES'),
			'authorButtonText' => esc_html__('Go to author detail', 'FORBES'),
		));
	}

	wp_enqueue_script('account-dropwdown-script', get_template_directory_uri() . '/minified-js/my-account/account-dropdown/script.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/my-account/account-dropdown/script.min.js'), true);

	wp_localize_script('jquery', 'rempBaseUrl', defined('DN_REMP_CRM_HOST') ? rtrim(DN_REMP_CRM_HOST, '/') : '' );

	wp_localize_script('subscriptions-script', 'subscriptionsTranslations', array(
        'unsubscribed' => __('You have successfully unsubscribed', 'FORBES'),
        'subscribed'   => __('You have successfully subscribed', 'FORBES'),
	));

	wp_enqueue_script('newsletter-script', get_template_directory_uri() . '/minified-js/newsletter.min.js', array('jquery'), filemtime(get_stylesheet_directory() . '/minified-js/newsletter.min.js'));
}
add_action('wp_enqueue_scripts', 'frontend_scripts');

/**
 * Script for core image block additional style
 */

function block_modification()
{
	wp_enqueue_script(
		'image-style-addition',
		get_template_directory_uri() . '/minified-js/gutenberg/image-style.min.js',
		array('wp-blocks')
	);
}
add_action('enqueue_block_editor_assets', 'block_modification');


add_action('enqueue_block_editor_assets', function () {
	if (!wp_script_is('common-script', 'enqueued')) {
		wp_enqueue_script('common-script', get_template_directory_uri() . '/minified-js/common.min.js', array(), filemtime(get_template_directory() . '/minified-js/common.min.js'), true);
		wp_localize_script('common-script', 'ajaxUrlObject', array(
			'url' => admin_url('admin-ajax.php'),
		));
	}
	wp_enqueue_script('admin-post-script', get_template_directory_uri() . '/minified-js/admin-post-script.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/admin-post-script.min.js'), true);
	wp_enqueue_script('splide-script', get_template_directory_uri() . '/inc/libraries/splide/splide.min.js', array('jquery'), get_template_directory() . '/inc/libraries/splide/splide.min.js', true);
	wp_enqueue_script('splide-extension-script', get_template_directory_uri() . '/inc/libraries/splide/splide-extension-intersection.min.js', array('jquery'), get_template_directory() . '/inc/libraries/splide/splide-extension-intersection.min.js', true);

	if ('cz' === COUNTRY) {
		global $post;
		wp_enqueue_script('list-item-sync-script', get_template_directory_uri() . '/minified-js/list-item-creator.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/list-item-creator.min.js'), true);
		wp_localize_script(
			'list-item-sync-script',
			'scriptData',
			array('id' => $post?->ID)
		);
	}
});

add_action('admin_enqueue_scripts', function () {
	if ('cz' === COUNTRY) {
		if (!wp_script_is('common-script', 'enqueued')) {
			wp_enqueue_script('common-script', get_template_directory_uri() . '/minified-js/common.min.js', array(), filemtime(get_template_directory() . '/minified-js/common.min.js'), true);
			wp_localize_script('common-script', 'ajaxUrlObject', array(
				'url' => admin_url('admin-ajax.php'),
			));
		}

		global $post;
		wp_enqueue_script('list-item-sync-script', get_template_directory_uri() . '/minified-js/list-item-creator.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/list-item-creator.min.js'), true);
		wp_localize_script(
			'list-item-sync-script',
			'scriptData',
			array('id' => $post?->ID)
		);
	}
});
