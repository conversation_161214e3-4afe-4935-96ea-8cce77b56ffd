<?php

/**
 * Enqueue scripts.
 */
function frontend_scripts_hu() {
	wp_enqueue_script('ga-custom-tracking-script', get_template_directory_uri() . '/minified-js/google/HUN-ga-custom-tracking.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/google/HUN-ga-custom-tracking.min.js'));

    $printCategory = get_field('print_category', 'option');
	$show_ads = get_field('show_ads', get_the_ID()) ?? true;
	$hide_ads = get_field('hide_ads', get_the_ID()) ?? false;

    if (defined('WP_DEBUG') && ! WP_DEBUG &&
        $show_ads &&
		! $hide_ads &&
        ! empty($printCategory) &&
        ! is_page_template([
			'page-my-account.php',
			'page-authentication.php',
			'template-subscription.php',
			'page-sales-funnel.php',
			'page-sales-funnel-pricing.php',
			'template-thank-you.php',
	        'template-print.php',
	        'template-pricing.php'
		]) &&
        ! has_term($printCategory, CATEGORY_TYPE, get_the_ID())
	)
	{
		wp_enqueue_script('google-ads-script-hu', get_template_directory_uri() . '/minified-js/google/googleads_HU.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/google/googleads_HU.min.js'));
	}

	if (is_single() && !is_singular('magazine')) {
		wp_enqueue_script('HUN-googleanalytics-script', get_template_directory_uri() . '/minified-js/google/HUN-googleanalytics-script.min.js', array('jquery'), filemtime(get_template_directory() . '/minified-js/google/HUN-googleanalytics-script.min.js'), true);
		if (isset(get_the_category()[0])) {
			wp_localize_script('HUN-googleanalytics-script', 'pageParams', array(
				'category' => get_the_category()[0]->cat_name,
			));
		}
	}
}
add_action('wp_enqueue_scripts', 'frontend_scripts_hu');
