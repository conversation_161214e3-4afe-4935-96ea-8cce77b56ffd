<?php

/**
 * Enqueue scripts.
 */
function frontend_scripts_cz() {

	if (is_single()) {
		wp_enqueue_script('spotify-embed', 'https://open.spotify.com/embed-podcast/iframe-api/v1', null, null, true);
		wp_enqueue_script('spotify-embed-script', get_template_directory_uri() . '/minified-js/podcast-block/scripts.min.js', array('spotify-embed'), filemtime(get_template_directory() . '/minified-js/podcast-block/scripts.min.js'), true);
	}

	include_once get_template_directory(  ) . '/inc/cz-ads.php';
}
add_action('wp_enqueue_scripts', 'frontend_scripts_cz');
