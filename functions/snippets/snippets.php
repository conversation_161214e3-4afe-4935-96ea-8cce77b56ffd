<?php

/**
 * Include php snippets
 */
add_action('acf/init', function () {
	include get_template_directory() . '/inc/most_read_posts.php';
});

include get_template_directory() . '/inc/list-item-generator.php';

include get_template_directory() . '/inc/my-account/my-subscriptions-client.php';
include get_template_directory() . '/inc/my-account/my-settings-client.php';
include get_template_directory() . '/inc/my-account/my-account-client.php';

include get_template_directory() . '/inc/authentication.php';

include get_template_directory() . '/inc/snippets/cache.php';
include get_template_directory() . '/inc/home_page_exclusions.php';
include get_template_directory() . '/inc/check_to_hide_ads.php';
include get_template_directory() . '/inc/list-data.php';
include get_template_directory() . '/inc/property-excel.php';
include get_template_directory() . '/inc/list-routing.php';
include get_template_directory() . '/inc/list-sitemap.php';
include get_template_directory() . '/inc/custom-data-extractor.php';
include get_template_directory() . '/inc/remp-client.php';
include get_template_directory() . '/inc/one_page.php';

include get_template_directory() . '/inc/snippets/add-svg-support.php';
include get_template_directory() . '/inc/snippets/appearance-access.php';
include get_template_directory() . '/inc/snippets/sanitize-content-on-paste.php';

include get_template_directory() . '/inc/snippets/head/head.php';

include get_template_directory() . '/components/navigation/functions.php';
include get_template_directory() . '/components/footer/functions.php';
include get_template_directory() . '/components/search/functions.php';
include get_template_directory() . '/components/search/ajax_search.php';
include get_template_directory() . '/components/premium-filter/premium-filter.php';

include get_template_directory() . '/template-parts/social-icons/functions.php';
include get_template_directory() . '/template-parts/onepage-theme-switcher/functions.php';
