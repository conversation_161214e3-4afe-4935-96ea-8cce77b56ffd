<?php
if (is_plugin_active('wordpress-seo/wp-seo.php') || is_plugin_active('wordpress-seo-premium/wp-seo-premium.php')) {
	// set a default og image value so wpseo_opengraph_image is triggered
	function default_og_image($image) {
		if (!$image->has_images()) {
			$image->add_image('default');
		}
	}
	add_action('wpseo_add_opengraph_additional_images', 'default_og_image');

	/*
	If a post has no thumbnail, set a placeholer as the OG:image for SEO
	*/
	add_filter('wpseo_opengraph_image', 'frontend_change_yoast_meta_for_image', 10, 1);
	/**
	 * Generates a modified image URL for social media sharing.
	 *
	 * @param mixed $img The image parameter.
	 * @return string The modified image URL.
	 */
	function frontend_change_yoast_meta_for_image($img) {
		if (!is_single()) {
			if (!$img || $img === 'default') {
				// Get the ID of the placeholder image for non-post pages
				$nonPostPagesPlaceholderId = get_field('non_post_pages', 'option');
				// Return the URL of the placeholder image if it exists, otherwise return the original image
				return $nonPostPagesPlaceholderId ? wp_get_attachment_image_url($nonPostPagesPlaceholderId, 'share', false) : $img;
			}

			return $img;
		} else {
			global $breaking_news_tag_id;
			// Check if image is empty or set to 'default',
			// the breaking news tag exists, and the post has the breaking news tag
			if ((!$img || $img === 'default') &&
                ! empty($breaking_news_tag_id) &&
				has_term($breaking_news_tag_id, CATEGORY_TYPE, get_the_ID())
			) {
				// Get the ID of the placeholder image for breaking news posts
				$breakingNewsPlaceholderId = get_field('breaking_news_no_thumbnail', 'option');
				// Return the URL of the placeholder image if it exists, otherwise return the original image
				return $breakingNewsPlaceholderId ? wp_get_attachment_image_url($breakingNewsPlaceholderId, 'share', false) : $img;
			}

			// Get the ID of the placeholder image for posts without thumbnails
			$placeholderImage = get_field('no_thumbnail', 'option') ? wp_get_attachment_image_url(get_field('no_thumbnail', 'option'), 'share', false) : $img;

			if (COUNTRY === 'cz') {
				$primaryImageId = get_field('primaryImage', get_the_ID());

				// Check if the primary image exists
				if ($primaryImageId) {
					$primaryImage = wp_get_attachment_image_url($primaryImageId, 'share', false);

					return $primaryImage;
				}
			}

			// Get the URL of the post's featured image
			$primaryImage = has_post_thumbnail(get_the_ID()) ? get_the_post_thumbnail_url(get_the_ID(), 'share') : null;

			if ($primaryImage) return $primaryImage;

			// Return the URL of the placeholder image
			return $placeholderImage;
		}
	}

	/**
	 * Exclude certain posts from sitemap
	 */

	add_filter('wpseo_exclude_from_sitemap_by_post_ids', function () {
		$exluded_post_ids = get_field('exlude_posts_from_sitemap', 'option');
		return $exluded_post_ids;
	});

	/**
	 * Change the author meta for brandvoice articles if cooauthor is set
	 */
	add_filter('wpseo_meta_author', function ($author_name, $presentation) {
		if (!taxonomy_exists('coauthor')) {
			return $author_name;
		}

		$coauthor = get_all_the_authors(get_the_ID());
		if (is_single() && $coauthor && !is_wp_error($coauthor)) {
			$author_name = $coauthor[0]->name;
		}
		return $author_name;
	}, 10, 2);
}
