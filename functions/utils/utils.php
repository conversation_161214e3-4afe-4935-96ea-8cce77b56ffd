<?php

/*
	Get the post read time from Yoast
*/
function get_post_read_time($post_id)
{
    if (!is_plugin_active('wordpress-seo/wp-seo.php') && !is_plugin_active('wordpress-seo-premium/wp-seo-premium.php')) {
        return '';
    }

    $manual_read_time = get_field('manual_read_time', $post_id);

    if ($manual_read_time) {
        $read_time = $manual_read_time;
    } else {
        $post_meta = YoastSEO()?->meta?->for_post($post_id) ?? null;
        $read_time = $post_meta ? (string) $post_meta?->estimated_reading_time_minutes : '';
    }

    return $read_time . ' ' . __('min', 'FORBES');
}

/**
 * Translates the months in the raw date string from ACF
 * @param string $raw_date: the returned value form the ACF datetime field
 * @return string
 */
function frontend_translate_date($raw_date, $custom_format = null, $with_time = false)
{
    $date_format = 'hu' === COUNTRY ? 'Y. M d.' : 'j. M Y';

    if ($with_time) {
        $date_format .= ' H:i';
    }

    if ($custom_format) $date_format = $custom_format;

    $date_string = date($date_format, strtotime($raw_date));
    if ('hu' === COUNTRY) {
        $date_string = strtr($date_string, array('Jan' => 'január', 'Feb' => 'február', 'March' => 'március', 'Apr' => 'április', 'May' => 'május', 'Jun' => 'június', 'Jul' => 'július', 'Aug' => 'augusztus', 'Sep' => 'szeptember', 'Oct' => 'október', 'Nov' => 'november', 'Dec' => 'december'));
    } elseif ('sk' === COUNTRY) {
        $date_string = strtr($date_string, array('Jan' => 'január', 'Feb' => 'február', 'March' => 'marec', 'Apr' => 'apríl', 'May' => 'máj', 'Jun' => 'jún', 'Jul' => 'júl', 'Aug' => 'august', 'Sep' => 'september', 'Oct' => 'október', 'Nov' => 'november', 'Dec' => 'december'));
    } elseif ('cz' === COUNTRY) {
        $date_string = strtr($date_string, array('Jan' => 'leden', 'Feb' => 'únor', 'March' => 'březen', 'Apr' => 'duben', 'May' => 'květen', 'Jun' => 'červen', 'Jul' => 'červenec', 'Aug' => 'srpen', 'Sep' => 'září', 'Oct' => 'říjen', 'Nov' => 'listopad', 'Dec' => 'prosinec'));
    }
    return $date_string;
}

/* Returns the appropriate string to render based on the difference between now and the publish time
 * @param int $id The post id
 * @return string
 */
function frontend_get_publish_time($id, $regular_card = false, $is_podcast = false)
{
    $publish_time = new DateTime(get_post_time('Y-M-d H:i', true, $id, false));
    $now          = new DateTime();
    $diff = date_diff($publish_time, $now);
    $diff_days    = $diff->format('%a');

    $date_string = '';

    if ($is_podcast) {
        if ($diff_days >= 2) {
            $date_string = get_post_time('d. F', false, $id, true);
        } else if ($diff_days = 1) {
            $date_string = esc_html__('Včera', 'FORBES');
        } else if ($diff_days < 1) {
            $date_string = esc_html__('Dnes', 'FORBES');
        }
    } else {
        if ($regular_card || $diff_days > 2) {
            $date_string = 'hu' === COUNTRY ? get_post_time('Y.n.j.', false, $id, false) : get_post_time('j. n. Y', false, $id, false);
        } else if ($diff_days >= 1) {
            $date_string = esc_html__('yesterday', 'FORBES');
        } else {
            $date_string = get_post_time('H:i', false, $id, false);
        }
    }

    return $date_string;
}

/**
 * Converts the ACF datepicker field value into a date string to be compared to todays date
 * @param string $date_string: the raw output from the ACF datepicker
 * @return int
 */
function frontend_my_str_to_time($date_string)
{
    $date_lowercase = strtolower($date_string);
    $date_cz_replaced = strtr($date_lowercase, array('led' => 'jan', 'úno' => 'feb', 'bře' => 'march', 'dub' => 'apr', 'kvě' => 'may', 'Čvn' => 'jun', 'Čvc' => 'jul', 'srp' => 'aug', 'zář' => 'sep', 'Říj' => 'oct', 'lis' => 'nov', 'pro' => 'dec'));
    $date_time_removed = substr($date_cz_replaced, 0, 11);
    $formatted_date = strtotime($date_time_removed);
    return $formatted_date;
}

/**
 * Retrieves the primary tag associated with a given ID.
 *
 * This function checks for the primary tag set using the legacy 'primaryTag' field first. If a legacy primary tag is found and is not an error, it is returned.
 * Otherwise, the function checks for the primary tag set using the Yoast SEO plugin's '_yoast_wpseo_primary_stitek' meta field.
 * If a Yoast primary tag is found and is not an error, it is returned. If neither a legacy nor a Yoast primary tag is found, the function returns the first regular tag associated with the given ID.
 *
 * @param int $id The ID of the post to retrieve the primary tag for.
 * @return WP_Term|null The primary tag associated with the given ID, or null if there is no primary tag.
 */
function forbes_legacy_frontend_get_primary_tag($id)
{
    $legacy_primary_tag_id = get_field('primaryTag', $id) ?? null;
    $legacy_primary_tag = $legacy_primary_tag_id ? get_term(intval($legacy_primary_tag_id), CATEGORY_TYPE) : null;

    if ($legacy_primary_tag && !is_wp_error($legacy_primary_tag)) return $legacy_primary_tag;

    $yoast_primary_tag_id = get_post_meta($id, '_yoast_wpseo_primary_stitek', true);
    $yoast_primary_tag = null;

    if (!empty($yoast_primary_tag_id)) {
        $term = get_term(intval($yoast_primary_tag_id), CATEGORY_TYPE);

        if (!is_wp_error($term) && 'attachment' !== get_post_type($term)) {
            $yoast_primary_tag = $term;
        }
    }

    if ($yoast_primary_tag) return $yoast_primary_tag;

    return !is_wp_error(wp_get_post_terms($id, CATEGORY_TYPE)) && is_array(wp_get_post_terms($id, CATEGORY_TYPE)) && !empty(wp_get_post_terms($id, CATEGORY_TYPE)) ? wp_get_post_terms($id, CATEGORY_TYPE)[0] : null;
}

function frontend_get_primary_category($id): WP_Term|null
{
	$forbesCategories = get_the_terms($id, 'category');
	$lifeCategories = get_the_terms($id, 'life_category');

	if (is_array($forbesCategories) && !empty($forbesCategories)) {
		return $forbesCategories[0];
	}

	if (is_array($lifeCategories) && !empty($lifeCategories)) {
		return $lifeCategories[0];
	}

	return null;
}

/**
 * Retrieves the primary tag associated with a given ID.
 *
 * This function checks for the primary tag set using the legacy 'primaryTag' field first. If a legacy primary tag is found and is not an error, it is returned.
 * Otherwise, the function checks for the primary tag set using the Yoast SEO plugin's '_yoast_wpseo_primary_stitek' meta field.
 * If a Yoast primary tag is found and is not an error, it is returned. If neither a legacy nor a Yoast primary tag is found, the function returns the first regular tag associated with the given ID.
 *
 * @param int $id The ID of the post to retrieve the primary tag for.
 * @return WP_Term|null The primary tag associated with the given ID, or null if there is no primary tag.
 */
function frontend_get_primary_tag($id): WP_Term|null
{
	$primaryTag = get_field('post_primary_tag', $id);
	$tags = wp_get_post_terms($id, COUNTRY === 'cz' ? 'stitek' : 'post_tag');

	if ($primaryTag instanceof \WP_Term) {
		return $primaryTag;
	}

	if (is_array($tags)) {
		// Sort tags by slug in reverse lexicographical order
		usort($tags, fn ($a, $b) => strcmp($b->slug, $a->slug));

		foreach ($tags as $tag) {
			$is_primary = get_field('default_primary_tag', $tag);
			if ($is_primary) {
				return $tag;
			}
		}
	}

	return frontend_get_primary_category($id);
}


function frontend_no_accent($str)
{
    $accents = array('À' => 'A', 'Á' => 'A', 'Â' => 'A', 'Ã' => 'A', 'Ä' => 'A', 'Å' => 'A', 'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a', 'å' => 'a', 'Ā' => 'A', 'ā' => 'a', 'Ă' => 'A', 'ă' => 'a', 'Ą' => 'A', 'ą' => 'a', 'Ç' => 'C', 'ç' => 'c', 'Ć' => 'C', 'ć' => 'c', 'Ĉ' => 'C', 'ĉ' => 'c', 'Ċ' => 'C', 'ċ' => 'c', 'Č' => 'C', 'č' => 'c', 'Ð' => 'D', 'ð' => 'd', 'Ď' => 'D', 'ď' => 'd', 'Đ' => 'D', 'đ' => 'd', 'È' => 'E', 'É' => 'E', 'Ê' => 'E', 'Ë' => 'E', 'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e', 'Ē' => 'E', 'ē' => 'e', 'Ĕ' => 'E', 'ĕ' => 'e', 'Ė' => 'E', 'ė' => 'e', 'Ę' => 'E', 'ę' => 'e', 'Ě' => 'E', 'ě' => 'e', 'Ĝ' => 'G', 'ĝ' => 'g', 'Ğ' => 'G', 'ğ' => 'g', 'Ġ' => 'G', 'ġ' => 'g', 'Ģ' => 'G', 'ģ' => 'g', 'Ĥ' => 'H', 'ĥ' => 'h', 'Ħ' => 'H', 'ħ' => 'h', 'Ì' => 'I', 'Í' => 'I', 'Î' => 'I', 'Ï' => 'I', 'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i', 'Ĩ' => 'I', 'ĩ' => 'i', 'Ī' => 'I', 'ī' => 'i', 'Ĭ' => 'I', 'ĭ' => 'i', 'Į' => 'I', 'į' => 'i', 'İ' => 'I', 'ı' => 'i', 'Ĵ' => 'J', 'ĵ' => 'j', 'Ķ' => 'K', 'ķ' => 'k', 'ĸ' => 'k', 'Ĺ' => 'L', 'ĺ' => 'l', 'Ļ' => 'L', 'ļ' => 'l', 'Ľ' => 'L', 'ľ' => 'l', 'Ŀ' => 'L', 'ŀ' => 'l', 'Ł' => 'L', 'ł' => 'l', 'Ñ' => 'N', 'ñ' => 'n', 'Ń' => 'N', 'ń' => 'n', 'Ņ' => 'N', 'ņ' => 'n', 'Ň' => 'N', 'ň' => 'n', 'ŉ' => 'n', 'Ŋ' => 'N', 'ŋ' => 'n', 'Ò' => 'O', 'Ó' => 'O', 'Ô' => 'O', 'Õ' => 'O', 'Ö' => 'O', 'Ø' => 'O', 'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o', 'ø' => 'o', 'Ō' => 'O', 'ō' => 'o', 'Ŏ' => 'O', 'ŏ' => 'o', 'Ő' => 'O', 'ő' => 'o', 'Ŕ' => 'R', 'ŕ' => 'r', 'Ŗ' => 'R', 'ŗ' => 'r', 'Ř' => 'R', 'ř' => 'r', 'Ś' => 'S', 'ś' => 's', 'Ŝ' => 'S', 'ŝ' => 's', 'Ş' => 'S', 'ş' => 's', 'Š' => 'S', 'š' => 's', 'ſ' => 's', 'Ţ' => 'T', 'ţ' => 't', 'Ť' => 'T', 'ť' => 't', 'Ŧ' => 'T', 'ŧ' => 't', 'Ù' => 'U', 'Ú' => 'U', 'Û' => 'U', 'Ü' => 'U', 'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u', 'Ũ' => 'U', 'ũ' => 'u', 'Ū' => 'U', 'ū' => 'u', 'Ŭ' => 'U', 'ŭ' => 'u', 'Ů' => 'U', 'ů' => 'u', 'Ű' => 'U', 'ű' => 'u', 'Ų' => 'U', 'ų' => 'u', 'Ŵ' => 'W', 'ŵ' => 'w', 'Ý' => 'Y', 'ý' => 'y', 'ÿ' => 'y', 'Ŷ' => 'Y', 'ŷ' => 'y', 'Ÿ' => 'Y', 'Ź' => 'Z', 'ź' => 'z', 'Ż' => 'Z', 'ż' => 'z', 'Ž' => 'Z', 'ž' => 'z', '„' => '', '“' => '');
    return strtr($str, $accents);
}

function frontend_get_user_profile_picture($id, $is_coauthor = false)
{
    $user_email = get_the_author_meta('user_email', $id);

    $valid_avatar = frontend_validate_gravatar($user_email);

    /**
     * The Gravatar of the user (if exists)
     * @var string|boolean
     */
    $user_image = $valid_avatar ? get_avatar_url($id) : false;

    if (!$user_image) {
        /**
         * ACF profile picture
         * @var string
         */
        $acf_profile_pic_id = $is_coauthor ? (get_field('profileImage', 'coauthor_' . $id) ?? null) : get_field('profile_picture', 'user_' . $id);

        /**
         * The url for the profile image if one has been uploaded to the ACF field
         * @var string
         */
        $user_image = $acf_profile_pic_id ? wp_get_attachment_image_url($acf_profile_pic_id, 'thumbnail', false) : null;

        /*
		If the empty white image is set as profile picture, change it to default placeholder
		*/
        if (!$user_image || str_contains($user_image, 'blank_raiffeisen-150x150')) {
            /**
             * URL to the placeholder image
             * @var string
             * */
            $placeholder_url = get_template_directory_uri() . '/assets/images/author_placeholder.svg';
            $user_image = $placeholder_url ?? '';
        }
    }

    return $user_image;
}

/**
 * Checks whether the user has a set gravatar
 */
function frontend_validate_gravatar($email)
{
    $hash = md5(strtolower(trim($email)));
    $uri = 'http://www.gravatar.com/avatar/' . $hash . '?d=404';
    $headers = get_headers($uri);

    if (!preg_match("|200|", $headers[0])) {
        return false;
    }

    return true;
}


/**
 * Register post type slugs regardless of user language settings
 */
function translateRoute($slug)
{
    switch (COUNTRY) {
        case 'cz':
            $translations = [
                'specials-and-rankings' => 'specialni-nabidky-a-zebricky',
                'guide' => 'guide',
                'products' => 'products',
                'magazine' => 'magazin',
                'self-promo' => 'self-promo',
                'events' => 'eventy',
                'jobs' => 'jobs',
            ];
            break;
        case 'hu':
            $translations = [
                'specials-and-rankings' => 'listak-es-extrak',
                'guide' => 'guide',
                'products' => 'products',
                'magazine' => 'magazin',
                'self-promo' => 'promo',
                'events' => 'esemenyek',
                'jobs' => 'jobs',
            ];
            break;
        case 'sk':
            $translations = [
                'specials-and-rankings' => 'rebricky-a-specialy',
                'guide' => 'guide',
                'products' => 'products',
                'magazine' => 'vydanie',
                'self-promo' => 'self-promo',
                'events' => 'podujatia',
                'jobs' => 'jobs',
            ];
            break;
    }

    return $translations[$slug] ?? esc_html__($slug, 'FORBES');
}

/**
 * Retrieves a list of coauthors based on specified criteria.
 *
 * This function retrieves a list of coauthors, filtering them by optional parameters.
 *
 * @param array|null $authorRoles  An array of roles of the coauthors to filter by (optional).
 *
 * @return array An array of associative arrays containing coauthor data matching the specified criteria.
 */
function frontend_get_coauthors($authorRoles = array())
{
    // Base query arguments for coauthor taxonomy.
    $args = array(
        'taxonomy'   => 'coauthor',
        'orderby'    => 'name',
        'order'      => 'ASC',
        'hide_empty' => true,
        'count'      => true,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key'     => 'authorHiddenOnListing',
                'value'   => '0',
                'compare' => '==',
            ),
        ),
    );

    // Add filters if specific parameters are provided.
    if (!empty($authorRoles)) {
        $args['meta_query'][] = array(
            'key'     => 'authorRole',
            'value'   => $authorRoles,
            'compare' => 'IN',
        );
    }

    // Retrieve coauthor terms based on the criteria.
    $terms = get_terms($args);
    $coauthors = [];

    // Collect metadata and compile it with each term.
    foreach ($terms as $term) {
        $term_id = $term->term_id;

        // Retrieve the author's roles.
        $roles = get_field('authorRole', 'coauthor_' . $term_id);

        // Check if the role matches the filter criteria.
        if (empty($authorRoles) || array_intersect($authorRoles, $roles)) {
            // Build an associative array similar to `get_author_data`.
            $coauthor_data = [
                'term_id'    => $term_id,
                'name'       => $term->name,
                'description' => $term->description,
                'url'        => get_term_link($term_id),
                'role'       => $roles,
                'image'      => get_the_author_image_url($term_id),
                'position'   => get_field('shortDescription', 'coauthor_' . $term_id),
                'email'      => get_field('author_email', 'coauthor_' . $term_id),
                'twitter'    => get_field('twitter_url', 'coauthor_' . $term_id),
                'instagram'  => get_field('instagram_url', 'coauthor_' . $term_id),
                'linkedin'   => get_field('linkedin_url', 'coauthor_' . $term_id),
            ];

            // Add each coauthor's full data to the results array.
            $coauthors[] = $coauthor_data;
        }
    }

    return $coauthors;
}


/**
 * Retrieves a list of coauthors with the 'life' role.
 *
 * This function retrieves a list of coauthors who have the 'life' role,
 * including those who have other roles as well.
 *
 * @return array An array of associative arrays containing coauthor data matching the specified criteria.
 */
function frontend_get_life_coauthors()
{
    // Base query arguments for coauthor taxonomy.
    $args = array(
        'taxonomy'   => 'coauthor',
        'orderby'    => 'name',
        'order'      => 'ASC',
        'hide_empty' => true,
        'count'      => true,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key'     => 'authorHiddenOnListing',
                'value'   => '0',
                'compare' => '==',
            ),
            array(
                'key'     => 'authorRole',
                'value'   => 'life',
                'compare' => 'LIKE',
            ),
        ),
    );

    // Retrieve coauthor terms based on the criteria.
    $terms = get_terms($args);
    $coauthors = [];

    // Collect metadata and compile it with each term.
    foreach ($terms as $term) {
        $term_id = $term->term_id;

        // Retrieve the author's roles.
        $roles = get_field('authorRole', 'coauthor_' . $term_id);

        // Include coauthors who have the 'life' role.
        if (is_array($roles) && in_array('life', $roles)) {
            // Build an associative array similar to `get_author_data`.
            $coauthor_data = [
                'term_id'    => $term_id,
                'name'       => $term->name,
                'description' => $term->description,
                'url'        => get_term_link($term_id),
                'role'       => $roles,
                'image'      => get_the_author_image_url($term_id),
                'position'   => get_field('shortDescription', 'coauthor_' . $term_id),
                'email'      => get_field('author_email', 'coauthor_' . $term_id),
                'twitter'    => get_field('twitter_url', 'coauthor_' . $term_id),
                'instagram'  => get_field('instagram_url', 'coauthor_' . $term_id),
                'linkedin'   => get_field('linkedin_url', 'coauthor_' . $term_id),
            ];

            // Add each coauthor's full data to the results array.
            $coauthors[] = $coauthor_data;
        }
    }

    return $coauthors;
}

function is_brandvoice($post_id): bool
{
    $brandvoice_categories = get_field('brandvoice_categories', 'option') ??  null;
	$terms = get_the_terms($post_id, CATEGORY_TYPE);

	if (is_array($brandvoice_categories) && is_array($terms)) {
		foreach ($terms as $term) {
			if (in_array($term->term_id, $brandvoice_categories)) {
				return true;
			}
		}
	}

	return false;
}

function is_advoice($post_id): bool
{
    $advoice_category = get_field('advoice_category', 'option') ?? null;
	$terms = get_the_terms($post_id, CATEGORY_TYPE);

	if (empty($advoice_category) || !is_array($terms)) {
		return false;
	}

	foreach ($terms as $term) {
		if ($term->term_id === (int) $advoice_category) {
			return true;
		}
	}

	return false;
}
/**
 * Retrieves the list of newsletters, using cached data if available,
 * or fetching from an API and caching the response.
 *
 * @return object|null The list of newsletters or null if an error occurs.
 * @throws Exception If an error occurs during the process.
 */
function get_newsletters()
{
    try {
		return (new RempClient())
			->useCrm()
			->withCache(3000)
			->setPayload([
				'action' => 'remp_get',
				'only_public' => 'true'
			])
			->get('newsletters/lists');
    } catch (Exception $e) {
		return (object)[
			'status' => 'error',
			'lists' => [],
		];
    }
}

/**
 * Retrieves user subscriptions using the RempClient API.
 *
 * This function fetches the user's authentication token from a cookie, then uses it to
 * authenticate and retrieve subscription information via the RempClient API.
 *
 * @return object|null Returns the response object containing user subscriptions,
 *                    or null if the authentication token is missing.
 */
function get_subscriptions()
{
    /**
     * Get the user's authentication token from a cookie
     */
    $token = $_COOKIE['n_token'] ?? null;

    if (!$token) {
        return null;
    }

    $subscriptionsResponse = (new RempClient())
        ->setPayload([
            'show_finished' => 'true'
        ])
        ->asUser($token)
        ->get('users/subscriptions');

    return $subscriptionsResponse;
}

/**
 * Translates a string in the frontend using a JSON file.
 *
 * This function retrieves translations from a JSON file based on the provided string and context.
 *
 * @param string      $string   The string for translation.
 * @param string      $context  The context for the translation.
 *
 * @return string The translated string. If the translation is not found or empty, returns the original string.
 */
function frontend_translate($string, $context)
{
	static $translations = null;

	if ($translations === null) {
		switch (COUNTRY) {
			case 'cz':
				$translations = json_decode(file_get_contents(get_template_directory() . '/languages/cz.json'), true);
				break;
			case 'hu':
				$translations = json_decode(file_get_contents(get_template_directory() . '/languages/hu.json'), true);
				break;
			case 'sk':
				$translations = json_decode(file_get_contents(get_template_directory() . '/languages/sk.json'), true);
				break;
		}
	}

    // Check if the language key and string key exist in the JSON
    if (isset($translations[$context]) && isset($translations[$context][$string])) {
        // Get the translated string
        $translatedString = $translations[$context][$string];
        // Return the translated string if it's not empty, otherwise return the original string
        return !empty($translatedString) ? $translatedString : '';
    }

    // Return the original string if not found in the JSON
    return $string;
}

if (WP_DEBUG) {
    /**
     * Redirects the request to the production server version of a file if it does not exist locally.
     *
     * This function checks if the URI contains "/wp-content/uploads/" and constructs the file path based on the matches.
     * If the file does not exist locally, it redirects the request to the production server version of the file.
     *
     * @return void
     */
    function custom_uploads_redirect()
    {
        // Check if the URI contains /wp-content/uploads/
        if (preg_match('/\/wp-content\/uploads\/(.+)/', $_SERVER['REQUEST_URI'], $matches)) {
            // Construct the file path
            $file_path = WP_CONTENT_DIR . '/uploads/' . $matches[1];
            $country = COUNTRY;

            // Check if the file does not exist
            if (!file_exists($file_path)) {
                // Redirect to the production server version of the file
                wp_redirect('https://' . $country . '.forbes.cz/uploads/' . $matches[1], 302);
                exit;
            }
        }
    }
    add_action('template_redirect', 'custom_uploads_redirect');
}

/**
 * Get coauthor for a post.
 *
 * @param int $post_id The ID of the post.
 */
function get_the_main_author($post_id)
{
    $coauthors = get_the_terms($post_id, 'coauthor') && !is_wp_error(get_the_terms($post_id, 'coauthor')) ? get_the_terms($post_id, 'coauthor') : [];

    if (!empty($coauthors)) {
        // Return the first coauthor as the main author if coauthors exist
        $coauthor = $coauthors[0];
        $archive_link = get_term_link($coauthor->term_id, 'coauthor');

        // Add the archive link to the coauthor object
        $coauthor->archive_link = $archive_link ? $archive_link : '';

        return $coauthor;
    } else {
        // If no coauthors are found, get the main author
        $main_author_id = get_post_field('post_author', $post_id);
        $main_author = get_user_by('id', $main_author_id);

        // Create an object similar to what get_the_terms would return
        $author = (object)[
            'term_id' => $main_author_id,
            'name' => $main_author->display_name,
            'slug' => $main_author->user_nicename,
            'description' => $main_author->description,
            'archive_link' => get_author_posts_url($main_author_id),
            // Add other fields as needed
        ];

        return $author;
    }
}

/**
 * Retrieves all the authors associated with a post.
 *
 * @param int $post_id The ID of the post.
 * @return array An array of author objects.
 */
function get_all_the_authors($post_id)
{
    // Get all the authors (coauthors) from the custom taxonomy
    $authors = get_the_terms($post_id, 'coauthor') && !is_wp_error(get_the_terms($post_id, 'coauthor')) ? get_the_terms($post_id, 'coauthor') : [];

    // Get the main author from the ACF field
    $main_author_id = get_field('main_author', $post_id);

    if ($main_author_id) {
        // Find the main author in the coauthor taxonomy terms
        $main_author_term = get_term($main_author_id, 'coauthor');

        if ($main_author_term && !is_wp_error($main_author_term)) {
            // Check if the main author is already in the authors array and remove it if necessary
            foreach ($authors as $key => $author) {
                if ($author->term_id == $main_author_term->term_id) {
                    unset($authors[$key]);
                }
            }

            // Prepare main author object with additional fields
            $main_author_term->url = home_url('/autor/' . $main_author_term->slug) ?? '';
            $main_author_term->role = get_field('authorRole', 'coauthor_' . $main_author_term->term_id) ?? '';
            $main_author_term->image = wp_get_attachment_url(get_field('profileImage', 'coauthor_' . $main_author_term->term_id)) ?? '';
            $main_author_term->position = get_field('shortDescription', 'coauthor_' . $main_author_term->term_id) ?? '';
            $main_author_term->email = get_field('author_email', 'coauthor_' . $main_author_term->term_id) ?? '';
            $main_author_term->twitter = get_field('twitter_url', 'coauthor_' . $main_author_term->term_id) ?? '';
            $main_author_term->instagram = get_field('instagram_url', 'coauthor_' . $main_author_term->term_id) ?? '';
            $main_author_term->linkedin = get_field('linkedin_url', 'coauthor_' . $main_author_term->term_id) ?? '';

            // Add main author to the beginning of the authors array
            array_unshift($authors, $main_author_term);
        }
    }

    // Add additional fields for all authors
    if (!empty($authors)) {
        foreach ($authors as &$author) {
            $author->url = home_url('/autor/' . $author->slug) ?? '';
            $author->role = get_field('authorRole', 'coauthor_' . $author->term_id) ?? '';
            $author->image = wp_get_attachment_url(get_field('profileImage', 'coauthor_' . $author->term_id)) ?? '';
            $author->position = get_field('shortDescription', 'coauthor_' . $author->term_id) ?? '';
            $author->email = get_field('author_email', 'coauthor_' . $author->term_id) ?? '';
            $author->twitter = get_field('twitter_url', 'coauthor_' . $author->term_id) ?? '';
            $author->instagram = get_field('instagram_url', 'coauthor_' . $author->term_id) ?? '';
            $author->linkedin = get_field('linkedin_url', 'coauthor_' . $author->term_id) ?? '';
        }
    } else {
        // Fallback to the post's main author if no coauthors are found
        $main_author_id = get_post_field('post_author', $post_id);
        $main_author = get_user_by('id', $main_author_id);

        // Assuming you want to mimic the structure of get_the_terms, which is an array of objects
        $authors = [(object)[
            'term_id' => $main_author_id,
            'name' => $main_author->display_name,
            'slug' => $main_author->user_nicename,
            'description' => $main_author->description,
            'archive_link' => get_author_posts_url($main_author_id),
        ]];
    }

    return array_values($authors);
}


/**
 * Retrieves author datas based on author ID.
 *
 * @param int $author_id The ID of the author.
 * @return array Author data.
 */
function get_author_datas($author_id)
{
    // Determine if we're dealing with a WordPress user or a custom coauthor taxonomy term.
    $is_wp_user = ($author = get_user_by('id', $author_id)) !== false;
    $author_meta = $is_wp_user ? get_user_meta($author_id) : null;
    $term = !$is_wp_user ? get_term($author_id, 'coauthor') : null;

    // Prepare author data array with common fields.
    $author_data = [
        'term_id' => $author_id,
        'name' => $is_wp_user ? $author->display_name : $term->name,
        'description' => $is_wp_user ? get_the_author_meta('description', $author_id) : $term->description,
        'url' => $is_wp_user ? get_author_posts_url($author_id) : get_term_link($term),
        'role' => $is_wp_user ? $author->roles[0] : get_field('authorRole', 'coauthor_' . $author_id),
        'image' => get_the_author_image_url($author_id),
        'position' => '',
        'email' => get_field( 'author_email', 'coauthor_' . $author_id ) ?? '',
        'twitter' => $is_wp_user ? ($author_meta['twitter'][0] ?? '') : get_field('twitter_url', 'coauthor_' . $author_id),
        'instagram' => $is_wp_user ? ($author_meta['instagram'][0] ?? '') : get_field('instagram_url', 'coauthor_' . $author_id),
        'linkedin' => $is_wp_user ? ($author_meta['linkedin'][0] ?? '') : get_field('linkedin_url', 'coauthor_' . $author_id),
    ];

    // If not a WordPress user, update the 'position' field from the custom taxonomy.
    if (!$is_wp_user) {
        $author_data['position'] = get_field('shortDescription', 'coauthor_' . $author_id);
    }

    return $author_data;
}


/**
 * Retrieves the URL of the author's image.
 *
 * This function attempts to get the custom coauthor image ID for the given author ID.
 * If no custom coauthor image ID is found, it tries to get the ACF 'profile_picture' field for the user.
 * If an image ID is found (either custom coauthor image or user profile picture), it returns the URL of the image.
 * If no image ID is found, it falls back to the default image URL.
 *
 * @param int $author_id The ID of the author.
 * @param  string  $size  The size of the image to retrieve (default: 'comment_card_desktop').
 *
 * @return string The URL of the author's image.
 */
function get_the_author_image_url($author_id, $size = 'comment_card_desktop')
{
    // Attempt to get the custom coauthor image ID
    $image_id = get_field('profileImage', 'coauthor_' . $author_id);

    // If no custom coauthor image ID is found, try getting the ACF 'profile_picture' field for the user
    if (!$image_id) {
        $image_id = get_field('profile_picture', 'user_' . $author_id);
    }

    // If an image ID is found (either custom coauthor image or user profile picture), get its URL
    if ($image_id) {
        $author_image_url = wp_get_attachment_image_src($image_id, $size)[0];
    } else {
        // Fallback to the default image URL
        $author_image_url = get_template_directory_uri() . '/assets/icons/icon-user-placeholder.svg';
    }

    return $author_image_url;
}

/**
 * Retrieves the URL of an image associated with a term.
 *
 * @param int $term_id The ID of the term.
 * @return string The URL of the image.
 */
function get_term_image_url($term_id)
{
    // Get the image ID using an ACF custom field
    $image_id = get_field('newsletter_image', CATEGORY_TYPE . '_' . $term_id);

    if ($image_id) {
        // Get the URL of the image
        $image_url = wp_get_attachment_image_url($image_id, 'full');

        // Check if the image URL is valid
        if ($image_url) {
            return $image_url;
        } else {
            // Use default image if URL is not valid
            return get_template_directory_uri() . '/assets/images/forbes-topic-default.svg';
        }
    } else {
        // Use default image if ID is not valid or field is empty
        return get_template_directory_uri() . '/assets/images/forbes-topic-default.svg';
    }
}

/**
 * Retrieves all the terms associated with a post ID for a specified category type.
 *
 * @param int $post_id The ID of the post.
 * @return array The array of terms associated with the post ID.
 */
function get_all_the_terms($post_id)
{
    // Define the category type as constant or replace 'CATEGORY_TYPE' with your actual taxonomy
    $category_type = CATEGORY_TYPE;  // Adjust this to your specific taxonomy

    // Retrieve the terms associated with the post ID for the specified category type
    $terms = get_the_terms($post_id, $category_type);

    if ($terms && !is_wp_error($terms)) {
        // Loop through each term to fetch and assign the image URL
        foreach ($terms as $term) {
            $term->image = get_term_image_url($term->term_id);
        }
        return $terms;
    } else {
        // Return an empty array or handle the error as needed
        return [];
    }
}

/**
 * Retrieves data for a specific article that we use in react article meta.
 *
 * This function retrieves various data related to the specified article ID.
 * It includes the estimated reading time, publish date, and the article's ID.
 *
 * @param int $article_id The ID of the article.
 * @param bool $isPremium Flag indicating if the article is premium or not.
 * @return array An array containing data for the article, including 'readTime', 'publishDate', 'articleID', and 'isPremium'.
 */
function get_article_data($article_id, $isPremium = false)
{
    if ( ! $isPremium) {
        // Checking if the article is premium based on the categories
        $premiumTag = get_field('premium_tag', 'option');
        $isPremium = ! empty($premiumTag) && has_term($premiumTag, CATEGORY_TYPE, $article_id);
    }

    // Getting the primary tag (category)
    $primary_tag = frontend_get_primary_tag($article_id);

    // Retrieving custom badge information for the category, if exists
    $badge_data = get_field('badge', CATEGORY_TYPE . '_' . ($primary_tag->term_id ?? -1)) ?? null;
    $badge_icon = null;
    $badge_label = null;

    if ($badge_data && is_array($badge_data)) {
        if (isset($badge_data['icon'])) {
            $badge_icon = wp_get_attachment_url($badge_data['icon']);
        }
        if (isset($badge_data['label'])) {
            $badge_label = $badge_data['label'];
        }
    }

    // If the article is premium, set the diamond icon
    if ($isPremium) {
        $badge_icon = get_template_directory_uri() . '/assets/icons/ds2024/icon-diamond-empty.svg';
    }

	$term_link = get_term_link($primary_tag);

    // Assembling the article data
    $article_data = array(
        'title' => html_entity_decode(get_the_title($article_id)),
        'url' => get_the_permalink($article_id),
        'description' => '', // get_the_excerpt($article_id),
        'image' => get_the_post_thumbnail_url($article_id, 'medium'),
		'thumbnail' => get_the_post_thumbnail_url($article_id, 'thumbnail'),
		'thumbnailSquare' => get_the_post_thumbnail_url($article_id, 'thumbnail-square'),
        'readTime' => get_post_read_time($article_id),
        'publishDate' => COUNTRY === 'hu' ? get_the_date('Y. n. j.', $article_id) : get_the_date('j. n. Y', $article_id),
        'id' => $article_id,
        'category' => array(
            'label' => $badge_label ? $badge_label : ($primary_tag->name ?? ''),
            'icon' => $badge_icon,
            'link' => $term_link instanceof WP_Error ? '' : $term_link,
        ),
        'isPremium' => $isPremium,
    );

    return $article_data;
}


/**
 * Renders logos based on CONTENT_TYPE using static paths in the theme directory.
 *
 * @param string|null $content_type The current content type context ('life' or other).
 */
function render_site_logo($content_type = null)
{
    // Determine the directory based on content type
    $logo_directory = $content_type === 'life' ? 'life' : 'default';

    // Define the static paths for the logos using get_stylesheet_directory_uri()
    $logo_path = get_stylesheet_directory_uri() . "/assets/logos/{$logo_directory}/main.svg";
    $logo_dark_path = get_stylesheet_directory_uri() . "/assets/logos/{$logo_directory}/main-white.svg";

    // Begin logo rendering
    echo '<a class="main-logo" href="/">';
    echo '<img class="main-logo__image h-object-fit--contain" src="' . $logo_path . '" alt="Forbes">';
    echo '<img class="main-logo__image main-logo__image--dark h-object-fit--contain" src="' . $logo_dark_path . '" alt="Forbes">';
    echo '</a>';
}

/**
 * Retrieves selected data for all posts of the 'magazine' custom post type.
 *
 * @return array An array of associative arrays containing the 'featured image', 'title', 'ID', 'label', and 'issue_date'.
 */
function get_magazine_post_data()
{
    // Define the query arguments for the 'magazine' post type
    $args = array(
        'post_type' => 'magazine',
        'posts_per_page' => -1,
        'post_status' => 'publish'
    );

    // Fetch the posts using get_posts and store them in an array
    $posts = get_posts($args);

    // Initialize an empty array to hold the post data
    $post_data = array();

    // Loop through each post to retrieve the desired fields
    foreach ($posts as $post) {
        // Retrieve the featured image URL
        $featured_image = get_the_post_thumbnail_url($post->ID, 'medium');

        // Retrieve the custom fields using Advanced Custom Fields (ACF) functions
        $label = get_field('label', $post->ID);
        $issue_date = get_field('issue_date', $post->ID);
		$issue_year = (int)(explode('-', $issue_date)[0] ?? null);
		$issue_month = (int)(explode('-', $issue_date)[1] ?? null);
		$shop_url = get_field('url_issue', $post->ID);
		$pdf_id = get_field('magazine_pdf', $post->ID);
		$price = get_field('magazine_price', $post->ID);

        // Add the selected data to the post_data array
        $post_data[] = array(
            'id' => $post->ID,
            'title' => get_the_title($post->ID),
			'description' => strip_tags(get_the_content(null, false, $post->ID), '<a><strong><em><i><u>'),
			'content' => strip_tags(str_replace('</p>', '<br><br>', get_the_content(null, false, $post->ID)), '<a><strong><em><br><i><u><br>'),
            'featured_image' => $featured_image,
            'label' => $label,
            'issue_date' => $issue_date,
			'issue_year' => $issue_year,
			'issue_month' => $issue_month,
			'shop_url' => $shop_url,
			'detail_url' => get_permalink($post->ID),
			'pdf_id' => $pdf_id,
			'price' => $price,
        );
    }

    // Return the array of post data
    return $post_data;
}

/**
 * Uses the following algorithm to determine post excerpt:
 *   1. If the post has an excerpt, return it.
 *   2. If the post content has a paragraph, return it.
 *   3. If no paragraph is found, return the default excerpt. (this should not happen = log error)
 */
function get_post_excerpt($post_id)
{
	if (has_excerpt($post_id)) {
		return get_the_excerpt($post_id);
	}

    $full_content = get_post_field('post_content', $post_id);
    preg_match('/<p>(.*?)<\/p>/', $full_content, $matches);

	if (empty($matches[0])) {
		return get_the_excerpt($post_id);
	}

	return $matches[0];
}

/**
 * Retrieves the public newsletter lists using an AJAX request.
 *
 * @return void
 */
function getNewsletterLists()
{
    try {
        $newsletterLists = get_newsletters();

        // Output the response as JavaScript
        echo '<script type="text/javascript">';
        echo 'window.NewsletterLists = ' . json_encode($newsletterLists) . ';';
        echo '</script>';
    } catch (Exception $e) {
		echo '<script type="text/javascript">';
        echo 'window.NewsletterLists = {status: "error", lists: []};';
        echo '</script>';
    }
}

/**
 * Retrieves the section data from Advanced Custom Fields (ACF).
 *
 * This function fetches the necessary section data including the title,
 * link information, icon settings, and URL based on the ACF fields.
 * It consolidates these data points into a single associative array
 * for easier use in templates and components.
 *
 * @param array $args Optional. Additional arguments that might be needed, such as 'category'.
 * @return array Associative array containing the section data:
 *               - 'title' (string): The title of the section.
 *               - 'hasTitleLink' (bool): Whether the title should be a link.
 *               - 'sectionLinkType' (string): The type of the link (page_link, url, category_archive).
 *               - 'sectionLinkText' (string): The text for the section link.
 *               - 'sectionUrl' (string): The URL of the section link.
 *               - 'showIcon' (bool): Whether to show an icon.
 *               - 'iconUrl' (string): The URL of the icon.
 */
function get_header_section_data($args = array())
{
    $section_data = array();

    $section_data['title'] = get_field('title');
    $section_data['hasTitleLink'] = get_field('title_link') ?: false;
    $section_data['sectionLinkType'] = get_field('section_link_type') ?: 'page_link';
    $section_data['sectionLinkText'] = get_field('section_link_text') ?: __('Go to category', 'FORBES');

    $section_data['sectionUrl'] = '';
    if ($section_data['sectionLinkType'] == 'page_link') {
        $section_data['sectionUrl'] = get_field('section_page_link');
    } elseif ($section_data['sectionLinkType'] == 'url') {
        $section_data['sectionUrl'] = get_field('section_url');
    } elseif ($section_data['sectionLinkType'] == 'category_archive' && isset($args['category'])) {
        $id = $args['category'];
        $section_data['title'] = get_field('title') ?: ($id ? get_term($id)->name : '');
        $section_data['sectionUrl'] = $id ? get_term_link(get_term($id), get_term($id)->taxonomy) : '';
    }

    $section_data['linkIcon'] = get_field('section_link_icon') ?? 'internal';
    $section_data['hideHeader'] = get_field('hide_header') ?: false;

    $section_data['showIcon'] = get_field('show_icon') ?: false;
    $icon = get_field('icon') ?: null;
    $section_data['iconUrl'] = $icon ? wp_get_attachment_url($icon) : get_template_directory_uri() . '/assets/icons/ds2024/icon-f.svg';

    return $section_data;
}

// Function to print empty states data to the window object
function print_empty_states_data_to_window()
{
    // Get field values using ACF's get_field() function
    $empty_states_data = array(
        'bookmarks' => array(
            'headline' => get_field('empty_bookmarks_headline', 'option'),
            'body' => get_field('empty_bookmarks_body', 'option'),
            'primary_cta' => array(
                'label' => get_field('empty_bookmarks_primary_cta', 'option')['empty_bookmarks_primary_cta_label'] ?? '',
                'url' => get_field('empty_bookmarks_primary_cta', 'option')['empty_bookmarks_primary_cta_url'] ?? '',
            ),
            'secondary_cta' => array(
                'label' => get_field('empty_bookmarks_secondary_cta', 'option')['empty_bookmarks_secondary_cta_label'] ?? '',
                'url' => get_field('empty_bookmarks_secondary_cta', 'option')['empty_bookmarks_secondary_cta_url'] ?? '',
            ),
            'background_pattern' => get_field('empty_bookmarks_background_pattern', 'option'),
        ),
        'subscriptions' => array(
            'headline' => get_field('empty_subscriptions_headline', 'option'),
            'body' => get_field('empty_subscriptions_body', 'option'),
            'primary_cta' => array(
                'label' => get_field('empty_subscriptions_primary_cta', 'option')['empty_subscriptions_primary_cta_label'] ?? '',
                'url' => get_field('empty_subscriptions_primary_cta', 'option')['empty_subscriptions_primary_cta_url'] ?? '',
            ),
            'secondary_cta' => array(
                'label' => get_field('empty_subscriptions_secondary_cta', 'option')['empty_subscriptions_secondary_cta_label'] ?? '',
                'url' => get_field('empty_subscriptions_secondary_cta', 'option')['empty_subscriptions_secondary_cta_url'] ?? '',
            ),
            'background_pattern' => get_field('empty_subscriptions_background_pattern', 'option'),
        ),
        'past_subscriptions' => array(
            'headline' => get_field('empty_past_subscriptions_headline', 'option'),
            'body' => get_field('empty_past_subscriptions_body', 'option'),
            'primary_cta' => array(
                'label' => get_field('empty_past_subscriptions_primary_cta', 'option')['empty_past_subscriptions_primary_cta_label'] ?? '',
                'url' => get_field('empty_past_subscriptions_primary_cta', 'option')['empty_past_subscriptions_primary_cta_url'] ?? '',
            ),
            'secondary_cta' => array(
                'label' => get_field('empty_past_subscriptions_secondary_cta', 'option')['empty_past_subscriptions_secondary_cta_label'] ?? '',
                'url' => get_field('empty_past_subscriptions_secondary_cta', 'option')['empty_past_subscriptions_secondary_cta_url'] ?? '',
            ),
            'background_pattern' => get_field('empty_past_subscriptions_background_pattern', 'option'),
        ),
        'activation' => array(
            'headline' => get_field('empty_activation_headline', 'option'),
            'body' => get_field('empty_activation_body', 'option'),
            'primary_cta' => array(
                'label' => get_field('empty_activation_primary_cta', 'option')['empty_activation_primary_cta_label'] ?? '',
                'url' => get_field('empty_activation_primary_cta', 'option')['empty_activation_primary_cta_url'] ?? '',
            ),
            'secondary_cta' => array(
                'label' => get_field('empty_activation_secondary_cta', 'option')['empty_activation_secondary_cta_label'] ?? '',
                'url' => get_field('empty_activation_secondary_cta', 'option')['empty_activation_secondary_cta_url'] ?? '',
            ),
            'background_pattern' => get_field('empty_activation_background_pattern', 'option'),
        ),
    );

    // Output the JavaScript
    echo '<script type="text/javascript">';
    echo 'window.emptyStatesData = ' . json_encode($empty_states_data) . ';';
    echo '</script>';
}

function print_default_user_avatar_to_window()
{
    echo '<script type="text/javascript">';
    echo 'window.defaultUserAvatar = "' . get_template_directory_uri() . '/assets/images/forbes-default-user-avatar.svg";';
    echo '</script>';
}

/**
 * Prints onboarding fields to window.onboardingFields.
 */
function print_onboarding_fields_to_window()
{
    $onboardingFields = get_field('onboarding', 'option') ?? [];

    echo '<script type="text/javascript">';
    echo 'window.onboardingFields = ' . json_encode($onboardingFields) . ';';
    echo '</script>';
}
/**
 * Retrieves the list of products, using cached data if available,
 * or fetching from an API and caching the response.
 *
 * @return object|null The list of products or null if an error occurs.
 * @throws Exception If an error occurs during the process.
 */
function get_products(array $options = [])
{
    try {
        $rempClient = new RempClient();
        $rempClient->useCrm()->withCache(3000);

        // default values
        $defaults = [
            'categoryId' => null,
            'itemsPerPage' => null,
            'page' => null,
            'brandId' => null,
            'sortBy' => null,
            'sortOrder' => null,
            'filters' => null,
            'attributes' => null,
            'genderId' => null,
        ];

        // merge the defaults with the options
        $options = array_merge($defaults, $options);

        // build the cache key based on the options
        $cacheKey = 'list_products';
        foreach (['categoryId', 'itemsPerPage', 'page', 'brandId', 'sortBy', 'sortOrder', 'filters', 'attributes', 'genderId'] as $key) {
            if ($options[$key] !== null) {
                $cacheKey .= "_{$options[$key]}";
            }
        }

        // try to get the cached response
        $cachedResponse = Cache::get($cacheKey);

        if ($cachedResponse) {
            // if cache, return the response
            $response = json_decode($cachedResponse, false);
        } else {
            // if no cache, make the API call
            $payload = ['action' => 'remp_get'];

            foreach (['category_id' => 'categoryId', 'items_per_page' => 'itemsPerPage', 'page' => 'page', 'brand_id' => 'brandId', 'sort_by' => 'sortBy', 'sort_order' => 'sortOrder', 'filters' => 'filters', 'gender_id' => 'genderId'] as $payloadKey => $optionKey) {
                if ($options[$optionKey] !== null) {
                    $payload[$payloadKey] = $options[$optionKey];
                }
            }

            if (!empty($options['attributes'])) {
                foreach ($options['attributes'] as $attrKey => $attrValue) {
                    $payload["attributes[$attrKey]"] = $attrValue;
                }
            }

            // make the API call
            $response = $rempClient->setPayload($payload)->get('ecommerce/products');

            // cache the response
            Cache::set($cacheKey, json_encode($response), 3000);
        }

        return $response;
    } catch (Exception $e) {
        throw new Exception('Error: ' . $e->getMessage());
    }
}


/**
 * Retrieves the product detail, using cached data if available,
 * or fetching from an API and caching the response.
 *
 * @return object|null The product detail or null if an error occurs.
 * @throws Exception If an error occurs during the process.
 */
function get_product_detail($product_id)
{
    try {
        $rempClient = new RempClient();
        $rempClient->useCrm()->withCache(3000);

        // Define cache key based on the URL and payload
        $cacheKey = 'product_detail_' . $product_id;

        // Try to get the cached response
        $cachedResponse = Cache::get($cacheKey);

        if ($cachedResponse) {
            // If there is a cached response, decode it as an object (not an associative array)
            $response = json_decode($cachedResponse, false);
        } else {
            // If there is no cached response, make the API call
            $response = $rempClient->setPayload(['action' => 'remp_get', 'id' => $product_id])->get('ecommerce/product');

            // Cache the response (already an object, no need to json_decode here)
            Cache::set($cacheKey, json_encode($response), 3000);
        }

        return $response;
    } catch (Exception $e) {
        throw new Exception('Error: ' . $e->getMessage());
    }
}


/**
 * Retrieves the category detail, using cached data if available,
 * or fetching from an API and caching the response.
 */
function get_ecommerce_category($category_id)
{
    try {
        $rempClient = new RempClient();
        $rempClient->useCrm()->withCache(3000);

        $cacheKey = 'category_detail_' . $category_id;
        $cachedResponse = Cache::get($cacheKey);

        if ($cachedResponse) {
            $response = json_decode($cachedResponse, false);
        } else {
            $response = $rempClient->setPayload(['action' => 'remp_get', 'id' => $category_id])->get('ecommerce/category');
            Cache::set($cacheKey, json_encode($response), 3000);
        }

        return $response;
    } catch (Exception $e) {
        throw new Exception('Error: ' . $e->getMessage());
    }
}


/**
 * Retrieves the genders, using cached data if available,
 * or fetching from an API and caching the response.
 */
function get_genders()
{
    try {
        $rempClient = new RempClient();
        $rempClient->useCrm()->withCache(3000);

        $cacheKey = 'ecommerce_genders';
        $cachedResponse = Cache::get($cacheKey);

        if ($cachedResponse) {
            $response = json_decode($cachedResponse, false);
        } else {
            $response = $rempClient->setPayload(['action' => 'remp_get'])->get('ecommerce/genders');
            Cache::set($cacheKey, json_encode($response), 3000);
        }

        return $response;
    } catch (Exception $e) {
        throw new Exception('Error: ' . $e->getMessage());
    }
}

/**
 * Determines if the user has access to a specific type of content.
 */
function forbes_user_has_access(string $accessType)
{
	$types = explode(',', (string) ($_SERVER['HTTP_X_ACCESS_TYPE'] ?? ''));
	return in_array($accessType, $types);
}

/**
 * Global storage for block timing data
 */
$gutenberg_block_timing_data = [];

/**
 * Checks if the current user is authorized to see block timing debug information
 *
 * @return bool True if user is authorized, false otherwise
 */
function is_block_timing_authorized()
{
    // Safety check: Only run on frontend, not in admin areas
    if (is_admin() || wp_doing_ajax() || wp_doing_cron()) {
        return false;
    }

    // Check if user is logged in and has the specific email
    if (!is_user_logged_in()) {
        return false;
    }

    $current_user = wp_get_current_user();
	$authorized_emails = [
		'<EMAIL>',
		'<EMAIL>',
	];

	return in_array($current_user->user_email, $authorized_emails);
}

/**
 * Captures the start time before block rendering begins
 *
 * @param string|null $pre_render Pre-rendered content (null to continue with normal rendering)
 * @param array $parsed_block The parsed block data
 * @param WP_Block|null $parent_block The parent block instance
 * @return string|null Returns null to continue with normal rendering
 */
function capture_block_start_time($pre_render, $parsed_block, $parent_block)
{
    global $gutenberg_block_timing_data;

    if (!is_block_timing_authorized()) {
        return $pre_render;
    }

    // Skip if block name is not set
    if (empty($parsed_block['blockName'])) {
        return $pre_render;
    }

    // Generate a unique key for this block instance
    $block_key = $parsed_block['blockName'] . '_' . uniqid('', true);

    // Store the start time and block data
    $gutenberg_block_timing_data[$block_key] = [
        'start_time' => microtime(true),
        'block_name' => $parsed_block['blockName'],
        'block_attrs' => isset($parsed_block['attrs']) ? $parsed_block['attrs'] : [],
        'block_key' => $block_key
    ];

    // Store the block key in a way we can retrieve it in render_block
    // We'll use a static counter to match pre_render with render_block calls
    static $block_counter = 0;
    $block_counter++;

    // Store mapping for retrieval in render_block
    $gutenberg_block_timing_data['_mapping'][$block_counter] = $block_key;
    $gutenberg_block_timing_data['_counter'] = $block_counter;

    return $pre_render; // Return null to continue with normal rendering
}

/**
 * Measures and displays the rendering time of each Gutenberg block after rendering is complete
 *
 * @param string $block_content The rendered block content
 * @param array $parsed_block The parsed block data
 * @param WP_Block|null $parent_block The parent block instance
 * @return string Modified block content with timing information
 */
function measure_gutenberg_block_rendering_time($block_content, $parsed_block, $parent_block)
{
    global $gutenberg_block_timing_data;

    if (!is_block_timing_authorized()) {
        return $block_content;
    }

    // Skip if block name is not set
    if (empty($parsed_block['blockName'])) {
        return $block_content;
    }

    // Get the current counter and find the corresponding block key
    static $render_counter = 0;
    $render_counter++;

    $block_key = null;
    if (isset($gutenberg_block_timing_data['_mapping'][$render_counter])) {
        $block_key = $gutenberg_block_timing_data['_mapping'][$render_counter];
    }

    // If we can't find the matching start time, create a fallback measurement
    if (!$block_key || !isset($gutenberg_block_timing_data[$block_key])) {
        // Fallback: measure a minimal time (this will be very small but not 0)
        $start_time = microtime(true) - 0.001; // Simulate 1ms ago
        $block_name = $parsed_block['blockName'];
        $block_attrs = isset($parsed_block['attrs']) ? $parsed_block['attrs'] : [];
    } else {
        // Use the stored start time
        $timing_data = $gutenberg_block_timing_data[$block_key];
        $start_time = $timing_data['start_time'];
        $block_name = $timing_data['block_name'];
        $block_attrs = $timing_data['block_attrs'];

        // Clean up the stored data
        unset($gutenberg_block_timing_data[$block_key]);
    }

    // Calculate the execution time
    $end_time = microtime(true);
    $execution_time = ($end_time - $start_time) * 1000; // Convert to milliseconds

    // Ensure we have a minimum measurable time for display purposes
    if ($execution_time < 0.0001) {
        $execution_time = 0.0001;
    }

    // Create debug output
    $debug_info = [
        'Block Type' => $block_name,
        'Execution Time' => number_format($execution_time, 4) . ' ms',
        'Block Attributes' => !empty($block_attrs) ? $block_attrs : 'None',
        'Content Length' => strlen($block_content) . ' characters',
        'Timestamp' => date('Y-m-d H:i:s'),
        'Measurement Method' => $block_key ? 'Pre-render to Render' : 'Fallback timing'
    ];

    // Format the debug output in a readable way
    $debug_output = "\n<!-- GUTENBERG BLOCK TIMING DEBUG -->\n";
    $debug_output .= "<div style='background: #f0f0f0; border: 1px solid #ccc; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; color: #333;'>";
    $debug_output .= "<strong>🔍 Block Performance Debug</strong><br>";

    foreach ($debug_info as $key => $value) {
        if ($key === 'Block Attributes' && is_array($value)) {
            $debug_output .= "<strong>{$key}:</strong> " . htmlspecialchars(json_encode($value, JSON_PRETTY_PRINT)) . "<br>";
        } else {
            $debug_output .= "<strong>{$key}:</strong> " . htmlspecialchars($value) . "<br>";
        }
    }

    $debug_output .= "</div>\n";
    $debug_output .= "<!-- END GUTENBERG BLOCK TIMING DEBUG -->\n";

    // Return the original content with debug information appended
	return $block_content . $debug_output;
}

// Hook into both pre_render_block and render_block to measure actual rendering time
add_filter('pre_render_block', 'capture_block_start_time', 10, 3);
add_filter('render_block', 'measure_gutenberg_block_rendering_time', 10, 3);
