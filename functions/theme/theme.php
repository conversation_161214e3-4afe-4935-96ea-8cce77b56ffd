<?php

/**
 * Load the theme textdomain
 */
add_action('after_setup_theme', function () {
	load_theme_textdomain('FORBES', get_template_directory() . '/languages');
}, 0, 0);

/*
* Init support for wide and full-width images.
*/
function frontend_theme_setup() {
	add_theme_support('align-wide');
}
add_action('after_setup_theme', 'frontend_theme_setup');

/**
 * Sets up theme defaults and registers support for various WordPress features.
 */
function frontend_setup() {
	/*
		* Let WordPress manage the document title.
		*/
	add_theme_support('title-tag');

	/*
		* Enable support for Post Thumbnails on posts and pages.
		*/
	add_theme_support('post-thumbnails');

	add_image_size('share', 1200, 630, ['center', 'center']);

}
add_action('after_setup_theme', 'frontend_setup');

// Helper to determine if a featured image should use 16:9 ratio for list pages
if ( ! function_exists('is_new_featured_image')) {
	function is_new_featured_image($attachment_id): bool
	{
		// Normalize to attachment ID
		if (is_array($attachment_id)) {
			$attachment_id = $attachment_id['ID'] ?? $attachment_id['id'] ?? 0;
		}
		$attachment_id = intval($attachment_id);
		if ( ! $attachment_id) {
			return false;
		}
		$upload_date = get_post_field('post_date', $attachment_id);
		if ( ! $upload_date) {
			return false;
		}
		// Allow overriding the cutoff date via filter if needed
    $cutoff_date = apply_filters('forbes_list_featured_ratio_cutoff', '2025-08-07');
		$upload_ts   = strtotime($upload_date);
		$cutoff_ts   = strtotime($cutoff_date . ' 00:00:00');
		if ( ! $upload_ts || ! $cutoff_ts) {
			return false;
		}

		return $upload_ts >= $cutoff_ts;
	}
}

/*
* Init support order posts page attributes
*/
function frontend_posts_order_attributes() {
	add_post_type_support('post', 'page-attributes');
}
add_action('admin_init', 'frontend_posts_order_attributes');

/**
 * Delete comments from admin
 */
function frontend_remove_admin_menu_comment() {
	remove_menu_page('edit-comments.php');
}
add_action('admin_menu', 'frontend_remove_admin_menu_comment');

function frontend_remove_comment_support() {
	remove_post_type_support('post', 'comments');
	remove_post_type_support('page', 'comments');
}
add_action('init', 'frontend_remove_comment_support', 100);

function frontend_admin_bar_render() {
	global $wp_admin_bar;
	$wp_admin_bar->remove_menu('comments');
}
add_action('wp_before_admin_bar_render', 'frontend_admin_bar_render');

/**
 * Add the locale as a body attribute
 */
add_action('wp_footer', 'frontend_add_locale_to_body');
function frontend_add_locale_to_body() {
?>
	<script>
		let body = document.querySelector('body');
		body.setAttribute("data-locale", "<?= COUNTRY; ?>");
	</script>
<?php
}

add_action('generate_rewrite_rules', function ($wp_rewrite) {
	$rules = [
		'lists/([^/]+)/([^/]+)/([^/]+)/?$' => 'index.php?post_type=list&name=' . $wp_rewrite->preg_index(1) . '&sub=' . $wp_rewrite->preg_index(2) . '&item=' . $wp_rewrite->preg_index(3),
		'lists/([^/]+)/([^/]+)/?$' => 'index.php?post_type=list&name=' . $wp_rewrite->preg_index(1) . '&sub=' . $wp_rewrite->preg_index(2),
	];
	$wp_rewrite->rules = $rules + $wp_rewrite->rules;
	return $wp_rewrite->rules;
});

/*
	Remove Wp logo and title from oembed iframes
*/
add_filter('embed_site_title_html', '__return_false');

add_action('init', function () {
	/**
	 * Custom Post Types
	 */
	require get_template_directory() . '/inc/post-types.php';
}, 2);

/**
 * Manually change all image request's to .webp
 */
function frontend_change_image_request($downsize, $id, $size) {
	$url = wp_get_attachment_url($id);
	$supported_formats = array('jpg', 'jpeg', 'png', 'gif', 'webp');
	$path_parts = pathinfo($url);

	if (isset($path_parts['extension']) && in_array(strtolower($path_parts['extension']), $supported_formats)) {
		$size_map = array(
			'daily_cover_desktop'                   => [ 'w' => 730, 'h' => 411 ],
			'daily_cover_desktop2x'                 => [ 'w' => 1460, 'h' => 822 ],
			'article_card_desktop_default'          => [ 'w' => 350, 'h' => 197 ],
			'article_card_desktop_default2x'        => [ 'w' => 700, 'h' => 394 ],
			'article_card_desktop_featured'         => [ 'w' => 540, 'h' => 304 ],
			'article_card_desktop_featured2x'       => [ 'w' => 1080, 'h' => 608 ],
			'article_card_desktop_featured_large'   => [ 'w' => 1110, 'h' => 624 ],
			'article_card_desktop_featured_large2x' => [ 'w' => 2220, 'h' => 1248 ],
			'article_card_desktop_small'            => [ 'w' => 210, 'h' => 121 ],
			'article_card_desktop_small2x'          => [ 'w' => 420, 'h' => 242 ],
			'home_featured_special'                 => [ 'w' => 1100, 'h' => 324 ],
			'home_featured_special2x'               => [ 'w' => 2200, 'h' => 648 ],
			'home_featured_special_tablet'          => [ 'w' => 690, 'h' => 201 ],
			'home_featured_special_tablet2x'        => [ 'w' => 1380, 'h' => 402 ],
			'comment_card_desktop'                  => [ 'w' => 64, 'h' => 64 ],
			'comment_card_desktop2x'                => [ 'w' => 128, 'h' => 128 ],
			'thumbnail-square'                      => [ 'w' => 128, 'h' => 128 ],
			'category_type_b_desktop'               => [ 'w' => 317, 'h' => 178 ],
			'category_type_b_desktop2x'             => [ 'w' => 634, 'h' => 356 ],
			'home_specials_rankings_b'              => [ 'w' => 350, 'h' => 197 ],
			'home_specials_rankings_b2x'            => [ 'w' => 700, 'h' => 394 ],
			'home_special_list'                     => [ 'w' => 80, 'h' => 80 ],
			'home_special_list2x'                   => [ 'w' => 160, 'h' => 160 ],
			'home_events_carousel'                  => [ 'w' => 255, 'h' => 255 ],
			'home_events_carousel2x'                => [ 'w' => 510, 'h' => 510 ],
			'events_carousel'   => ['w' => 255],
			'events_carousel2x' => ['w' => 510],
            'home_podcasts'   => ['w' => 100, 'h' => 100],
            'home_podcasts2x' => ['w' => 200, 'h' => 200],
			'single_featured'       => ['w' => 1110, 'h' => 624],
			'single_featured2x'     => ['w' => 2220, 'h' => 1248],
			'list_featured_16_9'    => ['w' => 1110, 'h' => 624],
			'list_featured_16_9_2x' => ['w' => 2220, 'h' => 1248],
            'drawer_magazine'                       => [ 'w' => 420, 'h' => 577 ],
            'drawer_magazine2x'                     => [ 'w' => 840, 'h' => 1154 ],
			'2048x2048'                             => [ 'w' => 2048, 'h' => 2048 ],
			'full'                                  => [ 'w' => 2048 ],
			'1536x1536'                             => [ 'w' => 2048 ],
			'large'                                 => [ 'w' => 1500 ],
			'share'                                 => [ 'w' => 1200 ],
			'medium_large'                          => [ 'w' => 1024 ],
			'medium'                                => [ 'w' => 750 ],
			'small'                                 => [ 'w' => 500 ],
			'post-thumbnail'                        => [ 'w' => 500 ],
			'thumbnail'                             => [ 'w' => 150 ],
		);

		$new_url = $path_parts['dirname'] . '/' . $path_parts['filename'] . '.webp';

		$width = 1024;
		$height   = null;

		if (is_array($size)) {
			$size_key = $size[0];
		} else {
			$size_key = $size;
		}

		if (array_key_exists($size_key, $size_map)) {
			$width  = $size_map[ $size_key ]['w'];
			$height = $size_map[ $size_key ]['h'] ?? null;
		} else if (filter_var($size_key, FILTER_VALIDATE_INT)) {
			$width = $size_key;
		}

		$payload = [
			'w' => $width,
			'q' => 90,
			's' => $path_parts['extension']
		];

		if ( $height !== null ) {
			$payload['h'] = $height;
		}

		if ($size_key === 'share') {
			$payload['h'] = 630;
		}

		$new_url .= '?r=' . urlencode( base64_encode( json_encode( $payload ) ) );

		return array($new_url, $size, null);
	}
	return $downsize;
}

if (isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] !== 'local') {
	add_filter('image_downsize', 'frontend_change_image_request', 1, 3);
}

/**
 * Ensure download option is hidden on all core video blocks
 */
add_filter('render_block', 'frontend_video_block_render', 10, 2);
function frontend_video_block_render($output, $block) {
	if ('core/video' !== $block['blockName']) {
		return $output;
	}

	$output = str_replace(
		'controls',
		'controls controlslist="nodownload"',
		$output
	);

	return $output;
}

remove_filter('the_title', 'capital_P_dangit', 11);
remove_filter('the_content', 'capital_P_dangit', 11);
remove_filter('comment_text', 'capital_P_dangit', 31);

function add_custom_query_var($vars) {
	$vars[] = "stage";
	$vars[] = "token";

	return $vars;
}
add_filter('query_vars', 'add_custom_query_var');
