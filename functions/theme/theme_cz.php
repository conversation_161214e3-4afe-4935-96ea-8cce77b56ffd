<?php
// Disable default SearchWP REST API Integration.
add_filter('searchwp\rest', '__return_false');

/**
 * Remove pagenumbers and not wanted separators from title tag
 */
function frontend_title_text_filters($text) {
	if (is_tax()) {
		if (get_query_var('paged') == 0) {
			$text = preg_replace('/[0-9]+/', '', $text);
			$text = str_replace('stránka', '', $text);
			$text = str_replace('/', '', $text);
			$text = preg_replace('/\s+/', ' ', $text);
			$text = str_replace('- -', '-', $text);
		}
	}

	return $text;
}
add_filter('wpseo_title', 'frontend_title_text_filters');

function additional_query_vars($vars) {
	$vars[] .= 'slide';
	$vars[] .= 'ordering';
	return $vars;
}
add_filter('query_vars', 'additional_query_vars');

add_action('init', function () {
	require get_template_directory() . '/inc/post-types_CZ.php';
	require get_template_directory() . '/inc/magazine_taxonomy_linking.php';
}, 2);
