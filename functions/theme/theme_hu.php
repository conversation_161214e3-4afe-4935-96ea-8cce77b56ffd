<?php
global $prev_featured;
$prev_featured = null;

/**
 * HOME PAGE
 * Before saving the post check if the featured daily cover article has been changed,
 * and save the old one to a global variable
 */
add_action('acf/save_post', 'frontend_acf_before_save', 1);
function frontend_acf_before_save($post_id) {
	$home_page_ID = get_option('page_on_front');
	if (intval($home_page_ID) !== intval($post_id)) {
		return;
	}

	global $prev_featured;
	$previous_scheduled_post = get_field('featured', $post_id)['featured_article'] ?? null;
	$prev_featured = $previous_scheduled_post ?? null;
}

/**
 * HOME PAGE
 * After saving the post, if there was a change, initiate the post rotation function
 * in the daily cover block
 */
add_action('acf/save_post', 'frontend_acf_after_save', 15);
function frontend_acf_after_save($post_id) {
	global $prev_featured;

	if ($prev_featured) {
		$current_featured = get_field('featured', $post_id)['featured_article'] ?? null;

		if ($current_featured && $current_featured !== $prev_featured) {
			DailyCover\setArchivedFeaturedPosts($post_id, $prev_featured);
			$prev_featured = null;
		}
	}
}

/*
	Change the labels for HU page in the media upload meta box
	*/
add_filter('gettext', 'frontend_change_media_upload_caption_label');
function frontend_change_media_upload_caption_label($translated) {
	if (is_admin() && str_contains($_SERVER['REQUEST_URI'], 'upload.php')) {
		$translated = str_replace('Felirat', 'Képalá', $translated);
	}
	return $translated;
}

/*
	Change the labels for HU page in the media upload meta box
	*/
add_filter('gettext', 'frontend_change_media_upload_description_label');
function frontend_change_media_upload_description_label($translated) {
	if (is_admin() && str_contains($_SERVER['REQUEST_URI'], 'upload.php')) {
		$translated = str_replace('Leírás', 'Fotó kredit', $translated);
	}
	return $translated;
}

/*
		Translate pagination slug
	*/
function frontend_translate_pagination_slug() {
	global $wp_rewrite;

	$wp_rewrite->pagination_base = esc_html__('page', 'FORBES');
	$wp_rewrite->author_base = esc_html__('author', 'FORBES');
	$wp_rewrite->flush_rules();
}
add_action('init', 'frontend_translate_pagination_slug', 1);