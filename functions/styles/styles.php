<?php

/**
 * Enqueue styles.
 */
function frontend_styles() {
	wp_enqueue_style('frontend-style', get_template_directory_uri() . '/style.css#asyncload', array(), filemtime(get_template_directory() . '/style.css'));
	wp_enqueue_style('tailwind', get_template_directory_uri() . '/tailwind.css#asyncload', array(), filemtime(get_template_directory() . '/tailwind.css'));
	wp_enqueue_style('react-style', get_template_directory_uri() . '/react.css#asyncload', array(), filemtime(get_template_directory() . '/react.css'));
	wp_enqueue_style('frontend-style-non-critical', get_template_directory_uri() . '/style-non-critical.css#asyncload', array(), filemtime(get_template_directory() . '/style-non-critical.css'));
	wp_enqueue_style('custom-blocks-styles', get_template_directory_uri() . '/blocks.css#asyncload', array(), filemtime(get_template_directory() . '/blocks.css'));
	wp_enqueue_style('splide-styles', get_template_directory_uri() . '/inc/libraries/splide/splide.css#asyncload', array(), filemtime(get_template_directory() . '/inc/libraries/splide/splide.css'));

	if (is_singular('list') || is_page_template('page-lists.php')) {
		wp_enqueue_style('lists-styles', get_template_directory_uri() . '/lists.css#asyncload', array(), filemtime(get_template_directory() . '/lists.css'));
	}

	wp_enqueue_style('my-account-styles', get_template_directory_uri() . '/my-account.css#asyncload', array(), filemtime(get_template_directory() . '/my-account.css'));

	if (is_single()) {
		wp_enqueue_style('single-styles', get_template_directory_uri() . '/single.css#asyncload', array(), filemtime(get_template_directory() . '/single.css'));
	}

	wp_enqueue_style('custom-blocks-non-critical-styles', get_template_directory_uri() . '/blocks-non-critical.css#asyncload', array(), filemtime(get_template_directory() . '/blocks-non-critical.css'));

	if (is_single() || is_page_template('page-onepage.php')) {
		wp_enqueue_style('swipebox-style', 'https://forbes.cz/wp-content/themes/frontend/inc/libraries/swipebox/css/swipebox.min.css');
		wp_enqueue_style('lity-styles', 'https://forbes.cz/wp-content/themes/frontend/inc/libraries/lity/lity.min.css');
	}

	wp_dequeue_style('dashicons');
	wp_deregister_style('dashicons');
	wp_enqueue_style('dashicons', 'https://forbes.cz/wp-includes/css/dashicons.min.css#asyncload', null, null, false);

}
add_action('wp_enqueue_scripts', 'frontend_styles');

/**
 * Enqueue Admin styles
 */
function frontend_admin_styles() {
	wp_enqueue_style('admin-style', get_template_directory_uri() . '/editor.css', array('wp-edit-blocks'), filemtime(get_template_directory() . '/editor.css'));
	wp_enqueue_style('blocks-style', get_template_directory_uri() . '/blocks.css', array('wp-edit-blocks'), filemtime(get_template_directory() . '/blocks.css'));
	wp_enqueue_style('editor-blocks-style', get_template_directory_uri() . '/editor-blocks.css', array('wp-edit-blocks'), filemtime(get_template_directory() . '/editor-blocks.css'));
	wp_enqueue_style('custom-blocks-non-critical-styles', get_template_directory_uri() . '/blocks-non-critical.css#asyncload', array('wp-edit-blocks'), filemtime(get_template_directory() . '/blocks-non-critical.css'));
	wp_enqueue_style('frontend-style-critical', get_template_directory_uri() . '/style.css#asyncload', array('wp-edit-blocks'), filemtime(get_template_directory() . '/style.css'));
	wp_enqueue_style('frontend-style-non-critical', get_template_directory_uri() . '/style-non-critical.css#asyncload', array('wp-edit-blocks'), filemtime(get_template_directory() . '/style-non-critical.css'));
	wp_enqueue_style('splide-style', get_template_directory_uri() . '/inc/libraries/splide/splide.css#asyncload', array('wp-edit-blocks'), filemtime(get_template_directory() . '/inc/libraries/splide/splide.css'));
}
add_action('enqueue_block_editor_assets', 'frontend_admin_styles');

add_action('admin_enqueue_scripts', function () {
	if ('cz' === COUNTRY) {
		wp_enqueue_style('admin-style', get_template_directory_uri() . '/editor.css', array('wp-edit-blocks'), filemtime(get_template_directory() . '/editor.css'));
	}
});

/*
	Load non-critical css async
*/
add_filter('style_loader_tag',  'frontend_preload_filter', 10, 3);
function frontend_preload_filter($html, $handle, $href) {
	if (is_admin()) return $html;

	$style_handles = ['blocks-style'];
	if (in_array($handle, $style_handles)) {
		$html = <<<EOT
				<link rel='preload' as='style' onload="this.onload=null;this.rel='stylesheet'"
				id='$handle' href='$href' type='text/css' media='all' />
				EOT;
	}
	return $html;
}

/**
 * Dequeuing redundant stylesheet that conflicts with the theme's style
 */
function frontend_dequeue_common_css() {
	wp_deregister_style('common');
	wp_dequeue_style('wp-block-library-theme');
}
add_action('wp_enqueue_scripts', 'frontend_dequeue_common_css');
