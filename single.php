<?php

/*
Post Setup
*/
$primary_category = forbes_legacy_frontend_get_primary_tag(get_the_ID());
$category_id = $primary_category?->term_id ?? null;
$category_link = $category_id ? get_term_link($primary_category, CATEGORY_TYPE) : null;
$category_name = $primary_category?->name ?? '';
$primary_image = get_field('primaryImage', get_the_ID()) ?: get_post_thumbnail_id(get_the_ID());

/*
One Page Theme Swticher
 */
$theme_switcher = get_field('theme_switcher', get_the_ID()) ?? false;

/**
 * Get the ID of the parent OnePager article
 * @var int|null
 */
global $parent_one_page_id;
$parent_one_page_id = \OnePage\get_onepage_parent_id(get_the_ID());

/**
 * If the post is of brandvoice tags
 * @var boolean
 */
$is_brandvoice = is_brandvoice(get_the_ID());

/**
 * If the post is of brandvoice tags
 * @var boolean
 */
$is_advoice = is_advoice(get_the_ID());

/*
Life Category
*/

/**
 * The Life categories
 * @var WP_Term
 */
$life_tag = get_field('life', 'option')['tag'] ?? array();
global $road_block_counter;
global $interscroller_block_counter;
global $outstream_counter;
$road_block_counter = 0;
$interscroller_block_counter = 0;
$outstream_counter = 0;

$primary_tag = forbes_legacy_frontend_get_primary_tag(get_the_ID());

$category_id = $primary_tag?->term_id ?? null;
$category_link = $category_id ? get_term_link($primary_tag, CATEGORY_TYPE) : null;
$category_name = $primary_tag?->name ?? '';
$return_term_id = function ($cat) {
	return $cat->term_id;
};

$category_id  = ! empty(get_the_terms(get_the_ID(), CATEGORY_TYPE)) && ! is_wp_error(
	get_the_terms(get_the_ID(), CATEGORY_TYPE)
) ? get_the_terms(get_the_ID(), CATEGORY_TYPE)[0]->term_id : $category_id;
$category_ids = ! empty(get_the_terms(get_the_ID(), CATEGORY_TYPE)) && ! is_wp_error(
	get_the_terms(
		get_the_ID(),
		CATEGORY_TYPE
	)
) ? array_map(
	$return_term_id,
	get_the_terms
	(
		get_the_ID(),
		CATEGORY_TYPE
	)
) : [$category_id];

/**
 * Retrieves the terms associated with the current post.
 *
 * @param int $post_id The ID of the post.
 * @param string $taxonomy The taxonomy to retrieve terms from.
 * @return array|false|WP_Error|null The terms on success, false if there are no terms, WP_Error on failure, or null if the taxonomy does not exist.
 */
$terms = wp_get_post_terms(get_the_ID(), array_filter([
	taxonomy_exists('life_category') ? 'life_category' : false,
	taxonomy_exists('category') ? 'category' : false,
	taxonomy_exists('stitek') ? 'stitek' : false, // TODO: Remove this taxonomy
]));

foreach ($terms as $term) {
	$term->image = get_term_image_url($term->term_id);
	$term->description = get_term_meta($term->term_id, 'newsletter_description', true) ?? '';
}

/**
 * If the post is of tags Life
 * @var boolean
 */
$is_life = ! empty($life_tag) && has_term($life_tag, CATEGORY_TYPE, get_the_ID());

if ($is_life && !$is_brandvoice) {
	$category_id = $life_tag;
}

/**
 * The podcast categories
 * @var array(string)
 */
$podcast_tags = get_field('podcast_tags', 'option') ?? array();

/**
 * If the post is of type podcast
 * @var boolean
 */
$is_podcast = ! empty($podcast_tags) && has_term($podcast_tags, CATEGORY_TYPE, get_the_ID());

/*
Premium Category
*/

/**
 * The Premium custom term
 * @var string
 */
$premium_term = get_field('premium_tag', 'option');

/**
 * If the post contains the block 'remp-paywall/lock'
 * @var boolean
 */
$contains_remp_lock_block = has_block('remp-paywall/lock', get_the_ID());

/**
 * If the post is of premium category
 * @var boolean
 */
$is_premium = ! empty($premium_term) && has_term($premium_term, CATEGORY_TYPE, get_the_ID());
if ($contains_remp_lock_block) {
    $is_premium = true;
}

/*
Breaking News
*/

/**
 * The ID of the breaking news tag
 * @var string
 */
$breaking_news_tag_id = get_field('breaking_news_tag', 'option') ?? null;

/**
 * Whether the post has the breaking news tag
 * @var boolean
 */
$is_breaking_news = $category_id == $breaking_news_tag_id || has_tag($breaking_news_tag_id, get_the_ID());

/*
Sponsored Post
*/

/**
 * whether the category is currently sponsored
 * @var boolean
 * */
$is_cat_sponsored = get_field('sponsored', CATEGORY_TYPE . '_' . $category_id);

if ($is_cat_sponsored) {
	/**
	 * The sponsor's ID
	 * @var int
	 * */
	$sponsor_user = get_field('category_sponsor', CATEGORY_TYPE . '_' . $category_id);

	/**
	 * The sponsor
	 * @var string
	 * */
	$sponsor_text = get_field('sponsored_text', CATEGORY_TYPE . '_' . $category_id);

	/**
	 * The URL to navigate to
	 * @var string
	 * */
	$sponsor_link = get_field('sponsor_link', CATEGORY_TYPE . '_' . $category_id);
}

/**
 * True if the topic is sensitive (no ads shown)
 *
 * @var boolean
 * */
$sensitive_topic = get_field('sensitive_topic');

/*
Advertisements
*/

/**
 * True if ads should be displayed regardless of other rules.
 * @var boolean
 */
$force_ad_display = (get_field('ad_settings'))['force_ad_display'] ?? false;

/**
 * If any settings are on for hiding the ads in the article
 * @var boolean
 */
$hide_ads_triggered = (get_field('hide_ads') || $is_brandvoice || (COUNTRY !== 'cz' && $is_breaking_news) || $sensitive_topic) && $force_ad_display === false;

/*
Post Settings
*/

/**
 * The text content of the sensitive topic section
 *
 * @var string
 * */
$sensitive_topic_text = get_field('sensitive_topic_text', 'option');

/**
 * Whether the first paragraph should be larger than the others
 * @var boolean
 */
$large_first_paragraph = get_field('large_first_paragraph');

/**
 * Whether the first paragraph should be bold
 * @var boolean
 */
$bold_first_paragraph = get_field('bold_first_paragraph');

/**
 * The color value (hex) to use as a background for the post.
 *
 * @var string
 */
$post_background_color = $category_id ? get_field('background_color', CATEGORY_TYPE . '_' . $category_id) : null;

/**
 * Whether to make the titles bold
 * @var boolean
 */
$bold_titles = get_field('bold_titles', 'option');

/**
 * Whether to hide the footer in the article
 * @var boolean
 */
$hide_footer = get_field('hide_footer');

/**
 * Retrieves the redirect URL from the custom field 'redirect_url' if available,
 * otherwise falls back to the permalink of the current post.
 *
 * @return string The URL to redirect to.
 */
$url = get_field('redirect_url') ?? get_permalink();

/**
 * Retrieves all the authors of a specific post.
 *
 * @param int $post_id The ID of the post.
 * @return array An array of authors for the post.
 */
$authors = get_all_the_authors(get_the_ID());

/**
 * Get the label from the badge toggle, or the post category
 * @var string
 */
$label = $category_name;

/**
 * Retrieves the value of the 'exclude_for_free_users' field.
 *
 * This function uses the 'get_field' function from the Advanced Custom Fields plugin
 * to retrieve the value of the 'exclude_for_free_users' field. This field is used to
 * determine whether the content should be excluded for free users.
 *
 * @return mixed The value of the 'exclude_for_free_users' field.
 */
$exlude_for_free_users = get_field('exclude_for_free_users') ?? false;

/**
 * Whether the unlock article feature is enabled
 * @var boolean
 */
$enabled_unlock_article_feature = get_field('enabled_unlock_article_feature', 'option') ?? false;

/**
 * Check if the current post is a print article
 */
$print_category = get_field('print_category', 'option');
$is_print   = ! empty($print_category) && has_term($print_category, CATEGORY_TYPE, get_the_ID());


/**
 * Comment of the day field
 */
$comment_of_the_day_tag_id = get_field('comment_of_the_day', 'option');

/**
 * If the post is of tags Comment of the day
 */
$is_comment_of_the_day = ! empty($comment_of_the_day_tag_id) && has_term($comment_of_the_day_tag_id, CATEGORY_TYPE);

/**
 * Temporary function to check term in any taxonomy
 */
function check_term_in_any_taxonomy($term): bool
{
    if (empty($term)) {
        return false;
    }

    // Get all taxonomies
    $taxonomies = get_taxonomies();

    // Loop through each taxonomy
    foreach ($taxonomies as $taxonomy) {
        // Check if the term exists in the current taxonomy
        if (has_term($term, $taxonomy)) {
            return true; // Term found in this taxonomy
        }
    }

    return false; // Term not found in any taxonomy
}

/**
 * Comment of the day field
 */
$comment_of_the_day_tag_id = get_field('comment_of_the_day', 'option');

/**
 * If the post is of tags Comment of the day
 */
$is_comment_of_the_day = ! empty($comment_of_the_day_tag_id) && check_term_in_any_taxonomy($comment_of_the_day_tag_id);

/**
 * Get Magazine Post Data
*/
$magazines = $is_print ? get_magazine_post_data() : [];

get_header();

\DataExtract\DataExtractor::publish_data(get_the_ID());

$classes = (function () use ($post_background_color, $is_breaking_news, $is_brandvoice, $is_life, $is_print, $is_comment_of_the_day) {
	$additional_classes = '';

	if ($is_breaking_news) {
		$additional_classes .= ' single--breaking-news';
	}

    if ($is_comment_of_the_day) {
        $additional_classes .= ' single--comment-of-the-day';
    }

	if ($is_life && !$is_breaking_news) {
		$additional_classes .= ' single--life';
	}

	if (!$is_breaking_news) {
		$additional_classes .= ' single--cz';
	}

	if ($post_background_color && $is_life) {
		$additional_classes .= ' single--colored';
	}

	if ($is_print) {
		$additional_classes .= ' single--print';
	}

	return $additional_classes;
})();


foreach ($authors as $author) {
    $author->comment_of_the_day = false;

    if ($is_comment_of_the_day) {
        $author->comment_of_the_day = true;
    }
}

foreach ($authors as $author) {
    $author->comment_of_the_day = false;

    if ($is_comment_of_the_day) {
        $author->comment_of_the_day = true;
    }
}

?>

<script>
	var postID = '<?= get_the_ID(); ?>';
	var postCategory = '<?= json_encode($category_ids) ?>';
</script>

<!-- Make the authors and terms available to the React application -->
<script>
	window.ArticleAuthors = <?php echo json_encode($authors); ?>;
	window.ArticleTerms = <?php echo json_encode($terms); ?>;
	window.Magazines = <?php echo json_encode($magazines); ?>;
	window.currentPostId = <?php echo get_the_ID(); ?>;
</script>


<!-- This is used to block free users from accessing an article -->
<script type="text/javascript">
	window.lockedForFreeUsers = <?php echo $exlude_for_free_users ? 'true' : 'false'; ?>;
	window.isFreeArticlesFeatureEnabled = <?php echo $enabled_unlock_article_feature ? 'true' : 'false'; ?>;
	window.isArticleWithoutAds = <?php echo get_field('hide_ads') ? 'true' : 'false'; ?>;
</script>

<!-- This is used by the React application to fetch AI recommendations -->
<script>
	window.Article = <?= wp_json_encode([
		'id'					=> get_the_ID(),
		'authors'				=> get_all_the_authors(get_the_ID()),
		'country'				=> COUNTRY,
		'headline'				=> html_entity_decode(the_title('', '', false)),
		'categoryId'			=> $category_id,
		'categoryName'			=> $primary_tag->slug ?? '',
		'image' 				=> [
			'medium' => (string) wp_get_attachment_image_url($primary_image, 'medium'),
			'thumbnail' => (string) wp_get_attachment_image_url($primary_image),
			'thumbnail-square' => (string) wp_get_attachment_image_url($primary_image, 'thumbnail-square'),
		],
		'newsletter'			=> [
			'title' => trim((string) get_field('newsletter_title')),
			'description' => trim(strip_tags((string) get_field('newsletter_description')))
		],
		'publishDate' 			=> get_the_date(),
		'publishDateTimestamp'	=> get_the_date('U'),
		'readTime'				=> get_post_read_time(get_the_ID()),
		'tags'					=> $terms,
		'tag'					=> $is_premium ? get_term($premium_term)->name : $category_name,
		'huTags'				=> !is_wp_error(get_the_tags(get_the_ID())) && get_the_tags(get_the_ID()) ? get_the_tags(get_the_ID()) : [],
		'url'					=> $url,
		'isLife'				=> defined('CONTENT_TYPE') && CONTENT_TYPE === 'life' ? true : false,
	], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) ?>
</script>

<?php
	$articleData = get_article_data(get_the_ID(), $is_premium);
?>

<main data-article-slug="<?= get_post_field( 'post_name', get_post() ); ?>" class="single<?= $classes; ?>" data-postid="<?= get_the_ID(); ?>">
	<?php if ($theme_switcher) : ?>

		<?php get_template_part('template-parts/onepage-theme-switcher/index'); ?>

	<?php endif; ?>

	<?php if (get_field('customCss')) : ?>

		<style>
			<?= strip_tags(get_field('customCss')); ?>
		</style>

	<?php endif; ?>

	<div class="container">

		<div>

			<div class="row">

                <div class="<?php
                if ($is_breaking_news || $is_comment_of_the_day) : echo 'col-12 col-lg-8';
                elseif ($is_brandvoice || $is_life) : echo 'col-12';
                else : echo 'col-12';
                endif; ?>">

					<div class="header">

                        <?php
                        if ($primary_image) : ?>

							<div>
                                <div class="header__image-wrapper">


                                    <img src="<?= wp_get_attachment_image_url( $primary_image, 'single_featured' ) ?>"
                                         alt="<?= get_the_title(); ?>"
                                         fetchpriority="high"
                                         srcset="<?= wp_get_attachment_image_url( $primary_image, 'single_featured2x' ) ?> 2x"
                                    >

									<?php

										$photo_credit = new PhotoCredit($primary_image);
										$photo_credit->render();
									?>

									<?php
									/**
									 * The author of the cover image
									 * @var string
									 */
									$author = get_post($primary_image)?->post_content;
									?>

								</div>
                                <?php
                                if ($is_breaking_news || $is_comment_of_the_day) : ?>

									<div class="article-meta__wrapper">
										<div class="reactArticleMeta" data-authors="<?php echo htmlspecialchars(json_encode(get_all_the_authors(get_the_ID())), ENT_QUOTES, 'UTF-8'); ?>" data-article="<?php echo htmlspecialchars(json_encode($articleData), ENT_QUOTES, 'UTF-8'); ?>"></div>
									</div>

								<?php endif; ?>
							</div>


						<?php endif; ?>

                        <div class="header__content-wrapper <?= ! $primary_image ? ' header__content-wrapper--no-image' : '' ?> <?=
                        $is_comment_of_the_day ? ' header__content-wrapper--comment-of-the-day' : '' ?>">

							<div class="header__content">

                                <div class="header__category-date">
									<?php
										$tag = new Tag(
											text: isset($articleData['category']['label']) ? $articleData['category']['label'] : $label,
											icon: isset($articleData['category']['icon']) ? $articleData['category']['icon'] : null,
											url: isset($articleData['category']['link']) ? $articleData['category']['link'] : '',
										);
										echo $tag->render();
									?>

                                    <?php
                                    if ($is_breaking_news || $is_comment_of_the_day) : ?>
                                        <span class="header__publish-date"><?= get_the_date('j. n. Y', get_the_ID()) ?></span>
									<?php endif; ?>
								</div>

								<h1 class="h3-noto heading"><?= get_the_title(); ?></h1>


                                <?php
                                if ( ! is_wp_error($authors) && ! empty($authors) && ($is_breaking_news || $is_comment_of_the_day)) : ?>
                                    <?php
                                    if ($is_breaking_news) : ?>
									<div class="header__authors-image">
										<?php
											$count = 0;
											$total_authors = count($authors);

											foreach($authors as $author) {
												$count++;
												$author_image = get_the_author_image_url($author->term_id);
												$vector_logo = get_field('vector', 'coauthor_' . $author->term_id);
											?>

											<div class="author-image__wrapper">
												<?php if($vector_logo) { ?>
													<div class="author-image author-image--vector" style="mask-image: url('<?php echo esc_url(wp_get_attachment_url($vector_logo)); ?>'); -webkit-mask-image: url('<?php echo esc_url(wp_get_attachment_url($vector_logo)); ?>');"></div>
												<?php } else { ?>
													<div class="author-image">
														<img src="<?= $author_image ?>" alt="<?= $author->name; ?>'s Profile Image">
													</div>
												<?php } ?>
											</div>

											<?php if ($count == 3 && $total_authors > 3) { ?>
												<div class="author-image__wrapper">
													<div class="author-image author-image--more">
														+<?php echo $total_authors - 3; ?>
													</div>
												</div>
												<?php break; ?>
											<?php } ?>

										<?php } ?>

									</div>
                                    <?php
                                    endif ?>
									<div class="header__authors-name <?= count($authors) > 1 ? 'multiple-authors' : ''; ?>">
                                        <?php
                                        if (count($authors) > 1) {
                                            foreach ($authors as $key => $author) {
                                                $author_name = $author->name;
                                                ?>
                                                <span><?= $key === 0 ? __('od', 'FORBES') : __('a', 'FORBES') ?></span>
                                                <a class="author-name" rel="noopener" href="<?= get_term_link($author) ?>"><span><?= $author_name ?></span></a>
                                            <?php }
                                        }

                                        if (count($authors) === 1) {
                                            $author = $authors[0];

											$author_position = empty($author->position) ? '' : sprintf('<b>,</b> <span class="author-position">%s</span>', esc_html($author->position));

											printf(
												'<a class="author-name" rel="noopener" href="%s"><span>%s</span></a>%s',
												esc_attr(get_term_link($author)),
												esc_html($author->name),
												$author_position
											);
                                        }
										?>
									</div>
								<?php endif; ?>

							</div>

						</div>

					</div>

				</div>

			</div>

            <?php
            if ( ! $is_breaking_news && ! $is_comment_of_the_day) : ?>
				<div class="row">

                    <div class="col-12 col-lg-7 offset-lg-1">

						<div class="header__author">
							<?php if (!is_wp_error($authors) && !empty($authors) && !$is_breaking_news) :
								foreach ($authors as $author) :
									$authorComponent = new Author(
										authorId: $author->term_id,
										type: get_field('authorRole', 'coauthor_' . $author->term_id),
										name: $author->name,
										profileImage: get_the_author_image_url($author->term_id),
										bio: '',
										position: '',
										followLink: null,
										url: isset($author->url) ? $author->url : '',
										buttonClass: 'author__follow-button--transparent',
										isBrandvoice: $is_brandvoice,
										isAdvoice: $is_advoice,
										categoryId: forbes_legacy_frontend_get_primary_tag(get_the_ID())->term_id ?? null
									);
									$authorComponent->render();
								endforeach;
							endif; ?>
						</div>

						<?php if ($is_cat_sponsored) : ?>

							<?php get_template_part('template-parts/category-sponsor-box/index', null, ['sponsor' => $sponsor_user ?? null, 'sponsor_text' => $sponsor_text ?? null, 'sponsor_link' => $sponsor_link ?? null, 'placement' => 'top']); ?>

						<?php endif; ?>

					</div>

				</div>

			<?php endif; ?>

		</div>

		<!-- Article body starts -->
		<div class="container-fluid single-post__wrapper" style="padding: 0">

			<div class="row">

				<!-- Article left column -->
                <div class="col-12 col-lg-7 <?php
                if ( ! $is_breaking_news) : echo 'offset-lg-1'; endif; ?>">

					<?php if ($is_podcast) : ?>

						<?php get_template_part('template-parts/podcast-block/index'); ?>

					<?php endif; ?>

					<div class="body__content-wrapper">

					<!-- This script clones the meta data of the article between the first and second gutenberg block (except breaking news tag)-->
                        <?php
                        if ( ! $is_breaking_news && ! $is_comment_of_the_day) : ?>
						<script>
							document.addEventListener("DOMContentLoaded", function() {
								// Get the container element and the existing article-meta div
								let container = document.querySelector('.gutenberg-content');
								let existingMeta = document.querySelector('.reactArticleMeta');

								// Check if the container and existingMeta are found
								if (container && existingMeta) {
									// Clone the article-meta div
									let clonedMeta = existingMeta.cloneNode(true);

									// Get the first child block inside the container
									let firstBlock = container.children[0];

									// Insert the cloned div right after the first block
									if (firstBlock) {
										container.insertBefore(clonedMeta, firstBlock);
									}
								}
							});
						</script>
					<?php endif; ?>

                        <?php
                        if ($is_comment_of_the_day) : ?>
                            <script>
	                            document.addEventListener('DOMContentLoaded', function () {
		                            const container = document.querySelector('.gutenberg-content');
		                            const headerContent = document.querySelector('.header__content');
		                            const
			                            firstParagrahpBlock = container.querySelector
			                            ('p');

		                            if (firstParagrahpBlock) {
			                            let clonedParagraph = firstParagrahpBlock.cloneNode(true);

			                            clonedParagraph.classList.add('comment-of-the-day--first-p');

			                            firstParagrahpBlock.style.display = 'none';

			                            // Insert the cloned div right after the headerContent
			                            headerContent.insertBefore(clonedParagraph, headerContent.nextElementSibling);
		                            }
	                            });
                            </script>
                        <?php
                        endif;
                        ?>


                        <?php
                        if (is_plugin_active('wordpress-seo/wp-seo.php') || is_plugin_active('wordpress-seo-premium/wp-seo-premium.php')) : ?>
							<?php $read_time = (string) YoastSEO()->meta->for_current_page()->estimated_reading_time_minutes; ?>
						<?php endif; ?>

                        <div class="gutenberg-content <?= $large_first_paragraph ? ' large-first-p' : '';
                        ?><?=
                        $bold_first_paragraph ? ' bold-first-p' : ''; ?><?=
                        $bold_titles ? ' bold-titles' : ''; ?><?= 'cz' === COUNTRY ? ' postContent' : ''; ?>" data-readtime="<?= $read_time ? $read_time : '' ?>">

							<?php the_content(); ?>

						</div>

						<?php if ($sensitive_topic_text && $sensitive_topic) : ?>

							<div class="gutenberg-content__sensitive-topic">

								<span class="gutenberg-content__sensitive-icon"></span>

								<p><?= $sensitive_topic_text; ?></p>

							</div>

						<?php endif; ?>

						<?php if ($is_cat_sponsored) : ?>

							<?php get_template_part('template-parts/category-sponsor-box/index', null, ['sponsor' => $sponsor_user ?? null, 'sponsor_text' => $sponsor_text ?? null, 'sponsor_link' => $sponsor_link ?? null, 'placement' => 'bottom']); ?>

						<?php endif; ?>

						<?php if (!$hide_footer) : ?>

							<div class="below-article">

								<div class="reactArticleMeta" data-authors="<?php echo htmlspecialchars(json_encode(get_all_the_authors(get_the_ID())), ENT_QUOTES, 'UTF-8'); ?>" data-article="<?php echo htmlspecialchars(json_encode($articleData), ENT_QUOTES, 'UTF-8'); ?>"></div>

								<div class="below-article__terms">
									<div id="reactArticleTermsBox"></div>
								</div>


                                <?php if ($is_brandvoice || $is_advoice) {



								} else {

										echo '<div id="reactAuthorBoxBottom"> </div>';

									}
								?>

							</div>

						<?php endif; ?>

					</div>

				</div>

				<!-- Article right column for ads -->
				<div class="col-hidden col-lg-4">

					<?php if (!$hide_ads_triggered || !$is_print) : ?>

						<div id="article-sticky-ad" class="googlead justDesktop"></div>
						<div id="article-sticky-ad--secondary" class="googlead justDesktop"></div>

					<?php endif; ?>

				</div>

			</div>

		</div>

	</div>

	<div class="related-posts-wrapper">

		<?php if(!$is_print) : ?>
			<div class="container">
				<?php get_template_part('template-parts/recommended-articles/index'); ?>
			</div>
		<?php else : ?>
			<div class="container">
				<?php
				// Get the current post date
				$current_post_date = get_the_date('Y-m-d H:i:s');

				// Get the next post by date
				$next_args = array(
					'post_type' => 'post',
					'posts_per_page' => 1,
					'orderby' => 'date',
					'order' => 'ASC',
					'post__not_in' => array(get_the_ID()),
					'date_query' => array(
						array(
							'after' => $current_post_date,
							'inclusive' => false,
						),
					),
					'tax_query' => array(
						array(
							'taxonomy'  => CATEGORY_TYPE,
							'field'     => 'term_id',
							'terms'     => $print_category,
							'operator'  => 'IN',
						),
					),
				);

				$next_query = new WP_Query($next_args);

				// If there are no more posts, get a random one without the current post
				if (!$next_query->have_posts()) {
					$random_args = array(
						'post_type' => 'post',
						'posts_per_page' => 1,
						'orderby' => 'rand',
						'post__not_in' => array(get_the_ID()),
						'tax_query' => array(
							array(
								'taxonomy'  => CATEGORY_TYPE,
								'field'     => 'term_id',
								'terms'     => $print_category,
								'operator'  => 'IN',
							),
						),
					);

					$related_query = new WP_Query($random_args);
				} else {
					$related_query = $next_query;
				}
				?>

				<?php if ($related_query && $related_query->have_posts()) : ?>

					<?php while ($related_query->have_posts()) : $related_query->the_post(); ?>

						<div class="related-print-article__wrapper">
							<?php get_template_part('template-parts/print-related-article/index', null, array('post_id' => get_the_ID())); ?>
						</div>

					<?php endwhile; ?>

				<?php endif;

				wp_reset_postdata(); ?>
			</div>

			<div class="single--print__header">

				<div class="container">

                    <h4 class="heading"><?php echo __('More articles from the print issue', 'FORBES'); ?></h4>

				</div>

			</div>

			<div class="reactPrintArticles"></div>
		<?php endif; ?>

	</div>
</main>

<div id="react-article-unlocked"></div>

<?php get_footer(); ?>
