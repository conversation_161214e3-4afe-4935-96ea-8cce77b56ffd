<?php

get_template_part('template-parts/header/header');

get_template_part('template-parts/navigation-minimal/navigation-minimal');

/**
 * Get the navigation settings array from the option.
 * @var array $navigation Array containing navigation settings.
 */
$navigation = get_field('navigation', 'option');

/**
 * Extract the URL of the my account page from the navigation settings array.
 * @var string|null $my_account_link URL of the my account page or null if not set.
 */
$my_account_link = $navigation['my_account_page_link'] ?? null;

/**
 * Extract the slug (last segment of the URL path) of the my account page.
 * @var string $my_account_page_slug Slug of the my account page.
 */
global $my_account_page_slug;
$my_account_page_slug = basename(parse_url($my_account_link, PHP_URL_PATH));
?>

<script>
  window.myAccountPageSlug = <?php echo json_encode($my_account_page_slug) ?>;
</script>
