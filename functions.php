<?php
require_once __DIR__ . '/vendor/autoload.php';
include_once(ABSPATH . 'wp-admin/includes/plugin.php');

if (!defined('FEATURE_TOGGLE_MY_ACCOUNT')) {
	// Turned off for Hungary.
	define('FEATURE_TOGGLE_MY_ACCOUNT', (defined('COUNTRY') && COUNTRY === 'hu' && defined('APP_ENV') && APP_ENV === 'production')  ? 0 : 1);
}

// Cron Schedules
include 'functions/cron-schedules.php';
// Styles
include 'functions/styles/styles.php';
// Scripts
include 'functions/scripts/scripts.php';
// React App Hash
include 'react-app/build-hash.php';
// Ecommerce
include 'functions/ecommerce/ecommerce.php';
// ACF
include 'functions/acf/acf.php';
// Navigation
include 'functions/navigation/navigation.php';
// Theme
include 'functions/theme/theme.php';
// Taxonomies
include 'functions/taxonomies/taxonomies.php';
// Queries
include 'functions/queries/queries.php';
// Plugins
include 'functions/plugins/plugins.php';
// SEO
include 'functions/seo/seo.php';
// Utils
include 'functions/utils/utils.php';
// Snippets
include 'functions/snippets/snippets.php';
// Components
include 'functions/components/components.php';
// API Endpoints
include 'functions/api/subscription-form-endpoint.php';

// Atoms
require_once 'atoms/author/author.php';
require_once 'atoms/tag/tag.php';
require_once 'atoms/heading/heading.php';
require_once 'atoms/link/link.php';
require_once 'atoms/tab/tab.php';
require_once 'atoms/premium-share-modal/premium-share-modal.php';
require_once 'atoms/subscribe-bar/subscribe-bar.php';
require_once 'atoms/button/button.php';
require_once 'atoms/toggle/toggle.php';
require_once 'atoms/input/input.php';
require_once 'atoms/checkbox/checkbox.php';
require_once 'atoms/photo-credit/photo-credit.php';
require_once 'atoms/social-login-buttons/social-login-buttons.php';
require_once 'atoms/job-card/job-card.php';
require_once 'atoms/chip/chip.php';
require_once 'atoms/modal/modal.php';
require_once 'atoms/benefit-card/benefit-card.php';
require_once 'atoms/segmented-control/segmented-control.php';

// Audio Article
require_once 'inc/audio-article/text-to-speech/TextToSpeechCore.php';
require_once 'inc/audio-article/text-to-speech/ElevenLabsTextToSpeech.php';
require_once 'inc/audio-article/AudioArticleRules.php';
require_once 'inc/audio-article/AudioArticleQueue.php';
require_once 'inc/audio-article/AudioArticleGenerator.php';
require_once 'inc/audio-article/AudioArticleInject.php';
require_once 'inc/audio-article/AudioArticleAdmin.php';
require_once 'inc/audio-article/AudioArticleStats.php';
require_once 'inc/audio-article/AudioArticleModule.php';

// Recommendation
require_once 'inc/recommendation/Override.php';
require_once 'inc/recommendation/OverrideMap.php';
require_once 'inc/recommendation/Behavior.php';
require_once 'inc/recommendation/Targeting.php';
require_once 'inc/recommendation/Feed.php';
require_once 'inc/recommendation/Strategy.php';
require_once 'inc/recommendation/Manager.php';
require_once 'inc/recommendation/Endpoint.php';

if (COUNTRY === 'hu') {
	// Scripts
	include 'functions/scripts/scripts_hu.php';
	// Theme
	include 'functions/theme/theme_hu.php';
	// Misc
	include 'functions/coauthor-migration.php';
	// Queries
	include 'functions/queries/queries_hu.php';
}

if (COUNTRY === 'sk') {
	// Scripts
	include 'functions/scripts/scripts_sk.php';
	include 'functions/print-articles/print-articles.php';

}

if (COUNTRY === 'cz') {
	// Seznam RSS feed
	include 'functions/seznam-rss.php';
	// Scripts
	include 'functions/scripts/scripts_cz.php';
	// Theme
	include 'functions/theme/theme_cz.php';
	// Queries
	include 'functions/queries/queries_cz.php';
	// Taxonomies
	include 'functions/taxonomies/taxonomies_cz.php';
	// Plugins
	include 'functions/plugins/plugins_cz.php';
	// Snippets
	include 'functions/snippets/snippets_cz.php';

    // AI Sort
    include 'functions/ai-sort-plugin/ai-sort-plugin.php';

	if (defined('WP_DEBUG') && !WP_DEBUG){
		// Life & Default Content Split
		include 'functions/content-split/content-split.php';


	}
}

include 'functions/ai-tool/editorial-ai-tool.php';

if (!function_exists('dd')) {
	function dd($data) {
		echo '<pre style="background: #f5f5f5; color: #333; padding: 10px; border-radius: 5px; font-family: Consolas, Monospace; font-size: 14px; z-index: 9999; text-align: left; width: 100%; max-width: 1000px; overflow: auto;">';

		if (is_array($data) || is_object($data)) {
			print_r($data);
		} else {
			var_dump($data);
		}

		echo '</pre>';
		die();
	}
}

if (!function_exists('wp_get_current_url')) {
	function wp_get_current_url() {
		return home_url(add_query_arg(array(), $GLOBALS['wp']->request));
	}
}

/**
 * React App Enqueuer
 *
 * This function enqueues the React application script for the WordPress theme.
 * It relies on a globally defined $BUILD_HASH variable, which is included
 * from 'react-app/build-hash.php'. The $BUILD_HASH variable contains the
 * unique hash for the current build of the React app, ensuring cache busting.
 */
function load_react_app() {
    global $BUILD_HASH; // Ensure global scope for the build hash

    if (isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] === 'local') {
		wp_enqueue_script(
			'react-app',
			'http://localhost:3000/my-account/static/js/bundle.js',
			array(),
			'1.0',
			true
		);
	} else {
		// Construct the script URL using the build hash
		$script_url = get_template_directory_uri() . '/react-app/static/js/main.js';

		// Enqueue the React app script
		wp_enqueue_script(
			'react-app',
			$script_url,
			array(),
			$BUILD_HASH,
			true
		);
	}
}
add_action('wp_enqueue_scripts', 'load_react_app');

/**
 * Social Login Detection Script Enqueuer
 *
 * This function enqueues the social login detection script specifically for single post pages (articles).
 * The script detects the "socialLoginStatus=failed" URL parameter and automatically opens the login modal.
 * It depends on common.js which contains the window.openReactLoginModal function.
 */
function enqueue_social_login_detection_script() {
    // Only enqueue on single post pages (articles)
    if (is_single()) {
        wp_enqueue_script(
            'social-login-detection',
            get_template_directory_uri() . '/assets/js/social-login-detection.js',
            array('common-script'), // Depend on common-script which contains window.openReactLoginModal
            '1.0.1',
            true // Load in footer
        );
    }
}
add_action('wp_enqueue_scripts', 'enqueue_social_login_detection_script');

if(WP_DEBUG) {
	// Add CORS headers
	function add_cors_http_header(){
		header("Access-Control-Allow-Origin: " . 'localhost:3000');
		header("Access-Control-Allow-Methods: POST, GET, OPTIONS, DELETE, PUT");
		header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, X-Requested-With");
	}

	add_action('init','add_cors_http_header');

	// Handle preflight requests
	function handle_preflight() {
		if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
			if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
				header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
			}

			if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
				header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
			}

			exit(0);
		}
	}
	add_action('init', 'handle_preflight');
}

function add_image_url_to_coauthor_response($response) {
    if (isset($response->data['acf']['profileImage'])) {
        $image_id = $response->data['acf']['profileImage'];
        $image_url = wp_get_attachment_url($image_id);
        // Add a new key 'profileImageUrl' to the response data
        $response->data['acf']['profileImageUrl'] = $image_url;
    }
    return $response;
}

add_filter('rest_prepare_coauthor', 'add_image_url_to_coauthor_response', 10, 3);

function add_image_url_to_stitek_response($response) {
    if (isset($response->data['acf']['newsletter_image'])) {
        $image_id = $response->data['acf']['newsletter_image'];
        $image_url = wp_get_attachment_url($image_id);
        // Add a new key 'profileImageUrl' to the response data
        $response->data['acf']['newsletter_image'] = $image_url;
    }
    return $response;
}

add_filter('rest_prepare_stitek', 'add_image_url_to_stitek_response', 10, 3);

function add_image_url_to_category_response($response) {
    if (isset($response->data['acf']['newsletter_image'])) {
        $image_id = $response->data['acf']['newsletter_image'];
        $image_url = wp_get_attachment_url($image_id);
        // Add a new key 'profileImageUrl' to the response data
        $response->data['acf']['newsletter_image'] = $image_url;
    }
    return $response;
}

add_filter('rest_prepare_category', 'add_image_url_to_category_response', 10, 3);

function add_fields_to_pages_response($response) {
    if (isset($response->data['acf']['premium_modal']['header_background_image'])) {
        $image_id = $response->data['acf']['premium_modal']['header_background_image'];
        $response->data['acf']['premium_modal']['header_background_image_url'] = wp_get_attachment_image_url($image_id, 'medium');

		$button_link_id = $response->data['acf']['premium_modal']['button_link'];
        $response->data['acf']['premium_modal']['button_link_url'] = get_permalink($button_link_id);
    }

    return $response;
}

add_filter('rest_prepare_page', 'add_fields_to_pages_response', 10, 3);

/**
 * Enhances the WordPress REST API response for posts by adding custom fields.
 *
 * @param WP_REST_Response $response The original response object containing data about the post.
 * @return WP_REST_Response Modified response object with additional custom fields.
 */
function add_fields_to_post_response($response) {
	$id = $response->data['id'];

	$print_category = get_field('print_category', 'option');

    $response->data['isPrint'] = ! empty($print_category) && has_term($print_category, CATEGORY_TYPE, $id);
	$response->data['title'] = get_the_title($id);
	$tagTerm = frontend_get_primary_tag($id);
	$response->data['tagId'] = $tagTerm ? $tagTerm->term_id : '';
	$response->data['tagName'] = $tagTerm ? $tagTerm->name : '';
	$response->data['tagLink'] = $tagTerm ? get_term_link($tagTerm, CATEGORY_TYPE) : '';
	$authorTerms = wp_get_post_terms($id, 'coauthor');
	$response->data['authorName'] = !is_wp_error($authorTerms) && isset($authorTerms[0]) ? $authorTerms[0]->name : '';
	$response->data['authorLink'] = !is_wp_error($authorTerms) && isset($authorTerms[0]) ? get_term_link($authorTerms[0]->term_id) : '';
	$response->data['publishDate'] = get_the_date('d.m.Y', $id);
	$response->data['readTime'] = get_post_read_time($id);
    $response->data['shortDescription'] = get_field('shortDescription', $id);
    $response->data['newsletter_description'] = get_field('newsletter_description', $id);
	$response->data['magazine'] = get_field('magazine_id', $id) ? get_field('magazine_id', $id) : '';

	if (isset($response->data['featured_media'])) {
		$response->data['imageLink'] = wp_get_attachment_image_url($response->data['featured_media'], 'medium');
	} else {
		$response->data['imageLink'] = '';
	}


    return $response;
}
add_filter('rest_prepare_post', 'add_fields_to_post_response', 10, 3);

/**
 * Modifies the query arguments for the REST API post queries to filter posts based on 'isPrint', 'magazine', and 'exclude_posts' parameters.
 *
 * @param array $args The original query arguments for retrieving posts via the REST API.
 * @param WP_REST_Request $request The request object containing request parameters.
 * @return array Modified query arguments with added taxonomy and post exclusion if applicable.
 */
function filter_posts_by_custom_params($args, $request) {
    // Handle the 'isPrint' parameter
    $is_print = $request->get_param('isPrint');
    if (isset($is_print) && $is_print === 'true') {
        $print_category = get_field('print_category', 'option');
        if ($print_category) {
            $args['tax_query'] = [
                [
                    'taxonomy' => CATEGORY_TYPE,
                    'field'    => 'term_id',
                    'terms'    => $print_category,
                ]
            ];
        }
    }

    // Handle the 'magazine' parameter
    $magazine_id = $request->get_param('magazine');
    if (!empty($magazine_id)) {
        $args['meta_query'][] = [
            'key'     => 'magazine_id',
            'value'   => $magazine_id,
            'compare' => '='
        ];
    }

    // Handle the 'exclude_posts' parameter
    $exclude_posts = $request->get_param('exclude_posts');
    if (!empty($exclude_posts)) {
        if (is_string($exclude_posts)) {
            $exclude_posts = explode(',', $exclude_posts);
        }
        $exclude_posts = array_map('intval', $exclude_posts);
        $args['post__not_in'] = $exclude_posts;
    }

    return $args;
}
add_filter('rest_post_query', 'filter_posts_by_custom_params', 10, 2);

// I need a function which checks if the user is logged in or not, also checks it with the /user/info MyNewsletter get function
function logged_in_user() {
	$token = $_COOKIE['n_token'] ?? $_GET['token'] ?? '';
	if ($token) {
		try {
			$user = (new MyAccount)->get('/user/info');

			if($user->status == 'ok') {
				return $user;
			}

			return false;
		} catch (\Exception $ex) {
			return false;
		}
	}
	return false;
}

function transfer_data_to_react() {
    wp_enqueue_script('react-data', get_template_directory_uri() . '/assets/js/react-data.js', array(), false, true);

    $script_data = array(
        'isSingle' => is_singular(array('page', 'post', 'special', 'event', 'list', 'magazine')),
        'postId' => get_the_ID(),
        'postType' => get_post_type(),
        'postUrl' => get_the_permalink(),
		'ignoreLastVisited' => get_field('ignore_page_for_last_visited_content'),
    );

    // Custom data for the Forbes sales related pages
    if (is_page_template('template-subscription.php') || is_page_template('template-subscription-activation.php') || is_page_template('template-thank-you.php')) {
        $script_data['forbes_sales_funnel'] = array(
            'funnel_name' => get_field('funnel_name') ?? '',
			'funnel_authentication_required' => get_field('funnel_authentication_required') ?? false,
			'list_of_steps' => get_field('list_of_steps', 'option') ?? [],
			'list_of_features' => get_field('list_of_features', 'option') ?? [],
        );
    }

	$empty_button = get_field('empty_button', 'option') ?: [];
	$cancel_subscription_modal = get_field('cancel_subscription_modal', 'option') ?: [];
	$script_data['forbes_my_account_subscription'] = array(
		'emptyPosts' => get_field('empty_posts', 'option') ?: [],
		'emptyButtonText' => $empty_button['empty_button_text'] ?: '',
		'emptyButtonLink' => $empty_button['empty_button_link'] ?: '',
		'cancelSubscriptionModalContent' => $cancel_subscription_modal['cancel_content'] ?: '',
	);

	$activation_error_page = get_field('error_page', 'option') ?: [];
	$activation_digital_success_page = get_field('digital_success_page', 'option') ?: [];
	$activation_print_success_page = get_field('print_success_page', 'option') ?: [];
	$script_data['forbes_subscription_activation'] = array(
		'errorDescription' => $activation_error_page['description'] ?: '',
		'digitalSuccessDescription' => $activation_digital_success_page['description'] ?: '',
		'printSuccessDescription' => $activation_print_success_page['description'] ?: '',
	);

	$payment_failed = get_field('payment_failed', 'option') ?: [];
	$script_data['forbes_my_account_payments'] = array(
		'unavailableInvoiceDescription' => get_field('unavailable_invoice_description', 'option') ?: '',
		'retryPaymentDescription' => $payment_failed['retry_payment_description'] ?: '',
		'otherHelpDescription' => $payment_failed['other_help_description'] ?: '',
	);

	$script_data['forbes_authentication'] = array(
		'privacyText' => get_field('privacy_text', 'option') ?: '',
	);

    wp_localize_script('react-data', 'wpVars', $script_data);

}
add_action('wp_enqueue_scripts', 'transfer_data_to_react');

function load_custom_translations() {
    $country = defined('COUNTRY') ? COUNTRY : 'cz';
    $lang_file_path = get_template_directory() . '/languages/' . $country . '.json';

    if (file_exists($lang_file_path)) {
        $translations = json_decode(file_get_contents($lang_file_path), true);
        if (!is_array($translations)) {
            error_log('Translation file format error: ' . $lang_file_path);
            return;
        }

        global $custom_translations;
        $custom_translations = $translations;
    } else {
        error_log('Translation file not found: ' . $lang_file_path);
    }
}
add_action('init', 'load_custom_translations');

function translations($keyPath) {
    global $custom_translations;
    $keys = explode('.', $keyPath); // Split the key path into individual keys

    $currentLevel = $custom_translations;
    foreach ($keys as $key) {
        if (isset($currentLevel[$key])) {
            $currentLevel = $currentLevel[$key];
        } else {
            // Fallback to the original key path if the translation is not found
            return esc_html($keyPath);
        }
    }

    return esc_html($currentLevel);
}

/**
 * Enqueues the copy clipboard script for the frontend theme.
 *
 * @return void
 */
function frontend_enqueue_copy_clipboard_script() {
	if (is_admin()) { // Check if we are in the admin area
		wp_enqueue_script('frontend_enqueue_copy_clipboard_script', get_template_directory_uri() . '/assets/js/copy-url-to-clipboard.js', array('jquery'), null, true);
	}
}
add_action('admin_enqueue_scripts', 'frontend_enqueue_copy_clipboard_script');

/**
 * Add photo credit field to media library
 * @param array $form_fields
 */
function add_photo_credit_field_to_media_library($form_fields, $post) {
    $form_fields['photo_credit'] = array(
        'label' => __('Photo Credit', 'FORBES'),
        'input' => 'text',
        'value' => get_post_meta($post->ID, 'photo_credit', true),
        'helps' => '',
		'required' => false,
    );
    return $form_fields;
}
add_filter('attachment_fields_to_edit', 'add_photo_credit_field_to_media_library', 10, 2);

/**
 * Custom admin CSS
 * @return void
 */
function custom_admin_css() {
    echo '<style>
        .media-modal .media-types-required-info { display: none; }
    </style>';
}
add_action('admin_head', 'custom_admin_css');

/**
 * Save photo credit field in media library
 * @param array $post
 */
function save_photo_credit_field_in_media_library($post, $attachment) {
    if (isset($attachment['photo_credit'])) {
        update_post_meta($post['ID'], 'photo_credit', sanitize_text_field($attachment['photo_credit']));
    }
    return $post;
}
add_filter('attachment_fields_to_save', 'save_photo_credit_field_in_media_library', 10, 2);

/**
 * Adds photo credit HTML to core/image blocks before rendering.
 *
 * @param  string  $block_content  The content of the image block.
 * @param  array  $block  The block data.
 *
 * @return string Modified block content with added photo credits.
 */
function add_photo_credit_to_image_block($block_content, $block)
{
    // Ensure this is a core/image block
    if ($block['blockName'] === 'core/image' && isset($block['attrs']['id'])) {
        // Extract the image ID from block attributes
        $img_id = $block['attrs']['id'];

        // Create a new PhotoCredit instance for the image ID
        $photo_credit = new PhotoCredit($img_id);

        // Get the image description from the WordPress database
        $description = get_post($img_id)->post_content;

        // Render the photo credit HTML
        ob_start();
        $photo_credit->render();
        $photo_credit_html = ob_get_clean();

        // Extract the `<figure>` from the block content
        preg_match('/<figure[^>]+>/i', $block_content, $figure_matches);
        $figure_tag       = $figure_matches[0] ?? '';
        $figure_close_tag = '</figure>';

        // Extract the `<figcaption>` from the block content, if any
        $figcaption = '';
        if (preg_match('/<figcaption[^>]*>(.*?)<\/figcaption>/is', $block_content, $caption_matches)) {
            $figcaption = $caption_matches[0];
        } elseif ( ! empty($description)) {
            $figcaption = '<figcaption class="wp-element-caption">' . esc_html($description) . '</figcaption>';
        }

        // Remove the existing `<figcaption>` (to re-add later) and extract the <img>
        $block_content = preg_replace('/<figcaption[^>]*>.*?<\/figcaption>/is', '', $block_content);
        preg_match('/<img[^>]+>/i', $block_content, $img_matches);
        $img_tag = $img_matches[0] ?? '';

        // find <a *> tag and copy it to the new layout
        preg_match('/<a[^>]+>/i', $block_content, $a_matches);
        $a_tag       = $a_matches[0] ?? '';
        $a_close_tag = ! empty($a_matches) ? '</a>' : '';

        // Construct the new layout
        if ( ! empty($img_tag)) {
            $block_content = sprintf(
                '%s%s<div class="photo-credit-wrapper">%s%s%s%s</div>%s%s',
                '', // Optional space for any future additional attributes for <figure>
                $figure_tag, // Insert the <figure> tag first
                $a_tag, // Insert the <a> tag first
                $img_tag,  // Insert the <img> tag first
                $a_close_tag, // Insert the </a> tag first
                $photo_credit_html, // Then add the photo credit HTML
                $figcaption,       // Finally, add the figcaption below the <div>
                $figure_close_tag
            );
        }
    }

    return $block_content;
}

add_filter('render_block_core/image', 'add_photo_credit_to_image_block', 10, 2);

/**
 * REST API validation for Gutenberg editor
 * This ensures error messages are properly displayed in Gutenberg
 */
function validate_featured_image_photo_credit_rest($prepared_post, $request)
{
	// Only validate for posts
	if ($prepared_post->post_type !== 'post') {
		return $prepared_post;
	}

	// Only validate when trying to publish or schedule
	if ( ! in_array($prepared_post->post_status, ['publish', 'future'])) {
		return $prepared_post;
	}

	// Get the featured image ID from the request
	$featured_image_id = null;

	// Check if featured_media is set in the request
	if (isset($request['featured_media']) && ! empty($request['featured_media'])) {
		$featured_image_id = (int)$request['featured_media'];
	}

	// Also check for ACF primaryImage field
	if ( ! $featured_image_id && isset($request['acf']) && isset($request['acf']['primaryImage'])) {
		$featured_image_id = (int)$request['acf']['primaryImage'];
	}

	// For existing posts, get the current featured image if not provided in request
	if ( ! $featured_image_id && $prepared_post->ID) {
		$featured_image_id = get_field('primaryImage', $prepared_post->ID) ?: get_post_thumbnail_id($prepared_post->ID);
	}

	// If there's a featured image, validate photo credit
	if ($featured_image_id) {
		$photo_credit = get_post_meta($featured_image_id, 'photo_credit', true);

		// If photo credit is empty, return error
		if (empty($photo_credit)) {
			return new WP_Error(
				'missing_photo_credit',
				__(
					'The featured article photo requires photo credit attribution. Please add photo credit to the featured image before publishing.',
					'FORBES'
				),
				['status' => 400]
			);
		}
	}

	return $prepared_post;
}

add_filter('rest_pre_insert_post', 'validate_featured_image_photo_credit_rest', 10, 2);




/**
 * Adds lazy loading attribute to images in the content.
 *
 * @param  string  $content  The content to be filtered.
 *
 * @return string The filtered content with lazy-loaded images.
 */
function add_lazy_loading_to_images(string $content): string
{
	// Use a regex to safely add the loading attribute to <img> tags without duplicating the attribute.
	$pattern     = '/<img(?![^>]*(loading=|fetchpriority="high"))[^>]*>/i';
	$replacement = '<img loading="lazy"';

	return preg_replace_callback($pattern, function ($matches) use ($replacement) {
		return str_replace('<img', $replacement, $matches[0]);
	}, $content);
}

add_filter('the_content', 'add_lazy_loading_to_images');

// remove srcset for SVGs
add_filter('wp_calculate_image_srcset', function ($sources, $size_array, $image_src, $image_meta, $attachment_id) {
    // Get the file type of the image
    $file_type = get_post_mime_type($attachment_id);

    // If the file is an SVG, return an empty array to disable srcset
    if ($file_type === 'image/svg+xml') {
        return false;
    }

    return $sources;
}, 10, 5);

/**
 * Enqueue page data as a JavaScript object when on a page
 * @return void
 */
function enqueue_page_data(): void {
	if (is_page()) {
		?>
		<script>
			window.Page = {
			  id: <?php echo esc_js(get_the_ID()); ?>,
			  title: '<?php echo esc_js( get_the_title()); ?>',
			}
        </script>
		<?php
	}
}
add_action('wp_footer', 'enqueue_page_data' );

add_filter('rest_prepare_taxonomy', function ($response, $taxonomy, $request) {
  $context = !empty($request['context']) ? $request['context'] : 'view';
  // Context is edit in the editor
  if ($context === 'edit' && $taxonomy->meta_box_cb === false) {
    $data_response = $response->get_data();
    $data_response['visibility']['show_ui'] = false;
    $response->set_data($data_response);
  }
  return $response;
}, 10, 3);

// Set meta_box_cb to false for post categories
// Only allow administrators to edit categories
add_filter('register_taxonomy_args', function ($args, $taxonomy) {
	if ($taxonomy === 'category' || $taxonomy === 'post_tag') {
		$args['capabilities'] = [
			'manage_terms' => 'administrator',
			'edit_terms' => 'administrator',
			'delete_terms' => 'administrator',
			'assign_terms' => 'assign_categories',
		];

		$args['meta_box_cb'] = false;
		$args['show_in_quick_edit'] = false;
	}

	// Use custom column for the Forbes category.
	if ($taxonomy === 'category') {
		$args['show_admin_column'] = false;
	}

	return $args;
}, 10, 2);

// Add custom column to the post list - Forbes category
add_filter('manage_post_posts_columns', function ($columns) {
	// Insert new column before the 'date' column
	$date_column = $columns['date'];
	unset($columns['date']);
	$columns['forbes_category'] = __('Category', 'FORBES');
	$columns['date'] = $date_column;

	return $columns;
});

// Add content to the custom column - Forbes category
add_action('manage_post_posts_custom_column', function ($column_name, $post_id) {
	if ($column_name === 'forbes_category') {
		$category = frontend_get_primary_category($post_id);

		if ($category && $category->slug !== 'uncategorized') {
			$path = [$category];

			while ($category->parent) {
				$category = get_term($category->parent);
				$path[] = $category;
			}

			$root_label = $category->taxonomy === 'category' ? 'Forbes' : 'Life';
			$root_url = get_admin_url(null, 'edit.php?taxonomy_exists=' . $category->taxonomy);

			if (taxonomy_exists('life_category')) {
				printf('<a href="%s">%s</a>', $root_url, $root_label);
			} else {
				echo $root_label;
			}

			foreach (array_reverse($path) as $item) {
				$filter_name = $item->taxonomy === 'category' ? 'cat' : $item->taxonomy;
				$filter_value = $item->taxonomy === 'category' ? $item->term_id : $item->slug;
				$url = get_admin_url(null, "edit.php?$filter_name=$filter_value");
				printf(' / <a href="%s">%s</a>', $url, $item->name);
			}
		}
	}
}, 10, 2);

// For every post query, add the advoice stitek to tax_query exclusions unless it is explicity
// included in the tax_query
add_action('pre_get_posts', function (\WP_Query $query) {
	return; // TODO: This is disabled for now, because it causes issues with the advoice category

	if ($query->get('post_type') === 'post' && !is_admin()) {
		$tax_query = $query->get('tax_query');
		$advoice_stitek_id = get_field('advoice_category', 'option');
		$exclude_advoice = true;

		$exclude_advoice_query = [
			'taxonomy' => CATEGORY_TYPE,
			'field' => 'term_id',
			'terms' => $advoice_stitek_id,
			'operator' => 'NOT IN',
		];

		if (is_array($tax_query)) {
			foreach ($tax_query as $item) {
				if (is_array($item) && isset($item['taxonomy']) && isset($item['terms'])) {
					$taxonomy = $item['taxonomy'];
					$operator = strtoupper($item['operator'] ?? 'IN');
					$terms = is_array($item['terms']) ? $item['terms'] : [$item['terms']];

					$is_explicit_advoice_query =
						$taxonomy === CATEGORY_TYPE &&
						$operator === 'IN' &&
						in_array($advoice_stitek_id, $terms);

					if ($is_explicit_advoice_query) {
						$exclude_advoice = false;
						break;
					}
				}
			}

			if ($exclude_advoice) {
				$tax_query = [
					'relation' => 'AND',
					$exclude_advoice_query,
					$tax_query,
				];

				$query->set('tax_query', $tax_query);
			}
		} else {
			$query->set('tax_query', [$exclude_advoice_query]);
		}
	}
});

add_filter('wpseo_sitemap_entries_per_page', function() {
    return 200; // The number of posts per sitemap chunk (See Yoast SEO plugin)
});

if (isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] === 'production' && COUNTRY === 'cz') {
    add_action('wp_body_open', static function () {
        echo <<<EOT
            <script type="text/javascript">
                (function(c,l,a,r,i,t,y){
                    c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                    t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                    y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                })(window, document, "clarity", "script", "e4zgyfj262");
            </script>
        EOT;
    });

    add_action('wp_head', function () {
		$template_blacklist = [
			'template-subscription.php',
			'template-pricing.php',
		];

		$show_ads = get_field('show_ads', get_the_ID()) ?? true;
		$remp_paywall_lock = has_block('remp-paywall/lock', get_the_ID());

		if (
			$show_ads &&
			! $remp_paywall_lock &&
			! forbes_user_has_access('minimalads') &&
			! is_page_template($template_blacklist)) {
            echo <<<EOT
                <script async src="https://cdn.performax.cz/yi/adsbypx/px_autoads.js?aab=ulite"></script>
                <link rel="stylesheet" href="https://cdn.performax.cz/yi/adsbypx/px_autoads.css"/>
            EOT;
        }


        echo <<<EOT

            <script type="text/javascript" src="https://c.seznam.cz/js/rc.js"></script>
            <script>
            document.addEventListener('DOMContentLoaded', function() {
              var lastFiredConsent = null;
              var consentProcessed = false; // Flag to track if consent data has been processed

              // Function to fire the retargeting event
              function fireRetargetingEvent(consent) {
                if (consent !== lastFiredConsent) {
                  var retargetingConf = {
                    rtgId: 42743,
                    consent: consent
                  };
                  if (window.rc && window.rc.retargetingHit) {
                    window.rc.retargetingHit(retargetingConf);
                    console.log('Retargeting event fired with consent:', consent);
                    lastFiredConsent = consent; // Update the last fired consent
                  } else {
                    console.error('Retargeting function not available.');
                  }
                } else {
                  console.log('Retargeting event with consent', consent, 'already fired. Skipping.');
                }
              }

              // Function to handle the consent data
              function handleConsentData(tcData) {
                console.log('TC Data:', tcData);

                // Clear the default firing timeout if consent data is processed
                clearTimeout(defaultEventTimeout);
                consentProcessed = true; // Mark consent as processed

                // Determine the consent value
                var consent = (tcData.gdprApplies && tcData.purpose.consents[1]) ? 1 : 0;
                fireRetargetingEvent(consent);
              }

              // Function to check if __tcfapi is available and set up listeners
              function checkTCFAPI(retries = 10, interval = 500) {
                if (typeof __tcfapi !== 'undefined') {
                  // Retrieve the user's consent status
                  __tcfapi('getTCData', 2, function(tcData, success) {
                    if (success) {
                      handleConsentData(tcData);
                    } else {
                      console.error('Failed to retrieve consent data. Handling available tcData as fallback.');
                      handleConsentData(tcData);
                    }
                  });

                  // Listen for changes in the consent status
                  __tcfapi('addEventListener', 2, function(tcData, success) {
                    if (success) {
                      handleConsentData(tcData);
                    } else {
                      console.error('Failed to add event listener for consent changes.');
                    }
                  });
                } else if (retries > 0) {
                  console.warn('__tcfapi not defined yet. Retrying...');
                  setTimeout(function() {
                    checkTCFAPI(retries - 1, interval);
                  }, interval);
                } else {
                  console.error('__tcfapi is not defined. Ensure the TCF API script is correctly loaded.');
                }
              }

              // Set a timeout to fire the default event with consent: 0 after 1500ms if no consent data is processed
              var defaultEventTimeout = setTimeout(function() {
                if (!consentProcessed) {
                  fireRetargetingEvent(0);
                }
              }, 1500);

              // Initial check for TCF API availability with retries
              checkTCFAPI();
            });
            </script>
        EOT;
    });
}

// Add custom filter to edit.php posts - Forbes site (Forbes / Life)
add_action('restrict_manage_posts', function ($post_type) {
	$site = isset($_GET['taxonomy_exists']) ? $_GET['taxonomy_exists'] : '';

	if ($post_type === 'post' && taxonomy_exists('life_category')) { ?>

		<select name="taxonomy_exists">
			<option value=""><?= __('Both sites (Fores + Life)', 'FORBES'); ?></option>
			<option value="category" <?php selected($site, 'category'); ?>>Forbes</option>
			<option value="life_category" <?php selected($site, 'life_category'); ?>>Life</option>
		</select>

	<?php }
});

add_filter('parse_query', function ($query) {
	global $pagenow;

	if (is_admin() && $pagenow === 'edit.php' && isset($_GET['taxonomy_exists']) && $_GET['taxonomy_exists'] !== '') {
		$tax_query = $query->get('tax_query') ?: [];

		$tax_query[] = [
			'taxonomy' => $_GET['taxonomy_exists'],
			'operator' => 'EXISTS',
		];

		$query->set('tax_query', $tax_query);
	}
});

// Add script before the closing </body> tag. Use high priority to ensure it loads last.
add_action('wp_footer', function () { ?>

	<script>
		if (window.crm_user) {
			document.body.classList.add('crm-user-exists', `crm-user-${window.crm_user.user.id}`);
		}
	</script>

<?php }, 1000);

// Add dFlip PDF viewer to the print page template for each magazine.
add_filter('the_content', function($content) {
	if (is_page_template('template-print-archive.php') && forbes_user_has_access('digi')) {
		$content = '';
		$magazines = get_magazine_post_data();

		foreach ($magazines as $magazine) {
			if ($magazine['pdf_id']) {
				$content .= sprintf(
					'[dflip id="%1$s" type="hidden" trigger="open-%1$s"][/dflip]<div id="open-%1$s"></div>',
					esc_attr($magazine['pdf_id'])
				);
			}
		}
	}

	return $content;
}, 1, 1);

// Register and enqueue the script for the HTML tag abbreviation format in Gutenberg editor.
// TODO: Move this out of functions.php to a dedicated Gutenberg block plugin or file.
function format_html_tag_abbr_script_register(): void {
	wp_register_script(
		'format-html-tag-abbr-js',
		get_template_directory_uri() . '/assets/js/gutenberg/format-html-tag-abbr.js',
		['wp-rich-text', 'wp-element', 'wp-editor', 'wp-data', 'wp-i18n'],
		'1.0.0',
	);
}
add_action('init', 'format_html_tag_abbr_script_register' );

function format_html_tag_abbr_enqueue_assets_editor(): void {
	wp_enqueue_script('format-html-tag-abbr-js');
	wp_set_script_translations('format-html-tag-abbr-js', 'FORBES', get_template_directory() . 'languages' );
}
add_action( 'enqueue_block_editor_assets', 'format_html_tag_abbr_enqueue_assets_editor' );

// Override ACF validation messages with custom translations.
add_filter('gettext', function($translated_text, $text, $domain) {
	if ($text === 'Validation failed' && $domain === 'acf') {
		$translated_text = _x('Validation failed', 'ACF validation', 'FORBES');
		if ($translated_text === 'Validation failed') {
			$translated_text = $text;
		}
	}

	if ($text === '1 field requires attention' && $domain === 'acf') {
		$translated_text = _x('1 field requires attention', 'ACF validation', 'FORBES');
		if ($translated_text === '1 field requires attention') {
			$translated_text = $text;
		}
	}

	if ($text === '%d fields require attention' && $domain === 'acf') {
		$translated_text = _x('%d fields require attention', 'ACF validation', 'FORBES');
		if ($translated_text === '%d fields require attention') {
			$translated_text = $text;
		}
	}

	if ($text === '%s value is required' && $domain === 'acf') {
		$translated_text = _x('%s value is required', 'ACF validation', 'FORBES');
		if ($translated_text === '%s value is required') {
			$translated_text = $text;
		}
	}

	return $translated_text;
}, 10, 3);

add_action('wp_footer', function (): void {
	if (class_exists('RempClient') && method_exists('RempClient', 'getRequestsLog')) {
		$requestsLog = RempClient::getRequestsLog();
		echo '<script>window.rempClientRequestsLog = ' . json_encode($requestsLog) . ';</script>';
	}
});
