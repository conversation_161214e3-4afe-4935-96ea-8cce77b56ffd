<?php
$queries = array();
parse_str($_SERVER['QUERY_STRING'], $queries);

$referer_queries = array();
if (isset($_SERVER['HTTP_REFERER'])) {
	parse_str(parse_url($_SERVER['HTTP_REFERER'], PHP_URL_QUERY), $referer_queries);
}

/**
 * Whether the list is via a crossroad page
 * @var boolean
 */
$is_sub_list = $args['is_sub_list'] ?? false;

/**
 * If sublist, the array of values of the list
 * @var array|null
 */
$chosen_list = $is_sub_list ? $args['chosen_list'] : null;

/**
 * The custom back button is on/off
 * @var boolean
 */
$is_custom_back_button = $is_sub_list
	? $chosen_list['crossroad_sections']['custom_back_button']
	:  get_field('sections')['custom_back_button'];

/**
 * The fields for the custom back button
 * @var array
 */
$custom_back_button_fields = ($is_sub_list
	? $chosen_list['back_button']
	: get_field('back_button'))
	?? ['link' => null];

/**
 * The link for the back button
 * @var string
 */
$back_button_link = ($is_custom_back_button && $custom_back_button_fields['link'])
	? $custom_back_button_fields['link']
	: ($args['back_button_link']
		?? get_the_permalink(get_the_ID()));

/**
 * The label of the back button
 * @var string
 */
$back_button_label = ($is_custom_back_button && $custom_back_button_fields['label'])
	? $custom_back_button_fields['label']
	: esc_html__('Back To the List', 'FORBES');

/**
 * The current list item to render
 * @var array
 */
$item = $args['item'] ?? null;

/**
 * The list item count
 * @var int
 */
$list_length = $args['list_length'] ?? null;

/**
 * Reverse numbering
 * @var boolean
 */
$reverse_numbering = ($is_sub_list
	? $chosen_list['crossroad_sections']['reverse_numbering']
	: get_field('sections')['reverse_numbering'])
	?? false;

/**
 * The ranking of the item
 * @var string
 */
$ranking = $reverse_numbering
	? $list_length - ($args['index']
		?? $item['order']['default']) + 1
	: $args['index'] ?? $item['order']['default']
	?? null;

$prev_item_number = $reverse_numbering ? $ranking + 1 : $ranking - 1;
$next_item_number = $reverse_numbering ? $ranking - 1 : $ranking + 1;

/**
 * Custom ranking
 * @var string
 */
$custom_ranking = $item ? ($item['custom']['custom_ranking'] ?? null) : null;

/**
 * Turn off numbering on list
 * @var boolean
 */
$show_ranking = ($is_sub_list
	? $chosen_list['crossroad_sections']['show_ranking']
	: get_field('sections')['show_ranking'])
	?? true;

/**
 * The previous item to link to
 * @var array
 */
$prev_item = $args['previous'] ?? null;

/**
 * The link of the previous item
 * @var array
 */
$prev_item_link = $prev_item
	? ($args['prev_item_link']
		?? get_the_permalink(get_the_ID()) . $prev_item['slug'] . '/')
	: null;

/**
 * The next item to link to
 * @var array
 */
$next_item = $args['next'] ?? null;

/**
 * The link of the next item
 * @var array
 */
$next_item_link = $next_item
	? ($args['next_item_link']
		?? get_the_permalink(get_the_ID()) . $next_item['slug'] . '/')
	: null;

if (is_preview()) {
	$chosen_list_slug = get_field('crossroad_page') ? $args['chosen_list_slug'] . '/' : '';
	$prev_item_link = $prev_item['list_url'] . '/' . $chosen_list_slug . $prev_item['slug'] . '/';
	$next_item_link = $next_item['list_url'] . '/' . $chosen_list_slug . $next_item['slug']  . '/';
}

/**
 * Chaining is on/off
 * @var boolean
 */
$is_chaining_prev = $is_sub_list
	? $chosen_list['crossroad_sections']['custom_chaining_prev']
	: get_field('sections')['custom_chaining_prev'];

/**
 * The fields for the chaining
 * @var array
 */
$chaining_fields_prev = $is_sub_list ? $chosen_list['chaining_prev'] : get_field('chaining_prev');

/**
 * The label for the chaining
 * @var string
 */
$chaining_label_prev = $chaining_fields_prev['label'] ?? null;

/**
 * The link for the chaining
 * @var string
 */
$chaining_link_prev = $chaining_fields_prev['link'] ?? null;
/**
 * Chaining is on/off
 * @var boolean
 */
$is_chaining = $is_sub_list
	? $chosen_list['crossroad_sections']['custom_chaining']
	: get_field('sections')['custom_chaining'];

/**
 * The fields for the chaining
 * @var array
 */
$chaining_fields = $is_sub_list
	? $chosen_list['chaining']
	: get_field('chaining');

/**
 * The label for the chaining
 * @var string
 */
$chaining_label = $chaining_fields['label'] ?? null;

/**
 * The link for the chaining
 * @var string
 */
$chaining_link = $chaining_fields['link'] ?? null;

if (isset($queries['ordering'])) {
	$prev_item_link .= '?ordering=' . $queries['ordering'];
	$next_item_link .= '?ordering=' . $queries['ordering'];
}

if ((!$is_custom_back_button && !$custom_back_button_fields['link']) && isset($referer_queries['slide'])) {
	$back_button_link .= '?slide=' . $referer_queries['slide'];
}

/**
 * Whether to show the social icons
 * @var boolean
 */
$show_social_icons = $args['show_social_icons']
	?? (get_field('sections')['social_icons']
		?? false);

/**
 * Types of list item
 * @var array
 */
$types = array();


/**
 * If the list is full width layout
 * @var boolean
 */
$full_width_layout = $is_sub_list
	? $chosen_list['crossroad_sections']['full_width_layout']
	: get_field('sections')['full_width_layout'];

/**
 * True if we want to hide the recommended ad
 * @var boolean
 */
$hide_recommendation_placeholder = (get_field('ad_settings'))['recommended_articles'] ?? false;
?>

<?php if (have_rows('content')) : ?>

	<?php while (have_rows('content')) : the_row(); ?>
		<?php $types[] = get_sub_field('type'); ?>
	<?php endwhile; ?>

<?php endif; ?>

<main class="page-single-list-item" data-listid="<?= get_the_ID(); ?>">

	<div class="container">

		<div class="row">

			<div class="col-12">

				<div class="page-single-list-item__navigation">

					<a href="<?= $back_button_link; ?>" class="page-single-list-item__back-button cta-link-tag after-icon after-icon--back-arrow"><?= $back_button_label; ?></a>

					<div class="page-single-list-item__single-nav-wrapper">

						<?php if ($prev_item) : ?>

							<a href="<?= $prev_item_link; ?>" class="page-single-list-item__prev-item-button button button--secondary button--medium after-icon after-icon--back-chevron">

								<?php if ($show_ranking) : ?>
									<?= $prev_item_number . '. ' . $prev_item['title']; ?>
								<?php else : ?>
									<?= $prev_item['title']; ?>
								<?php endif; ?>
							</a>

						<?php elseif ($is_chaining_prev && $chaining_label_prev && $chaining_link_prev) : ?>

							<a href="<?= $chaining_link_prev; ?>" class="page-single-list-item__prev-item-button button button--secondary button--medium after-icon after-icon--back-chevron"><?= $chaining_label_prev; ?></a>

						<?php endif; ?>

						<?php if ($next_item) : ?>

							<a href="<?= $next_item_link; ?>" class="page-single-list-item__next-item-button button button--secondary button--medium after-icon after-icon--chevron">
								<?php if ($show_ranking) : ?>
									<?= $next_item_number . '. ' . $next_item['title']; ?>
								<?php else : ?>
									<?= $next_item['title']; ?>
								<?php endif; ?>
							</a>

						<?php elseif ($is_chaining && $chaining_label && $chaining_link) : ?>

							<a href="<?= $chaining_link; ?>" class="page-single-list-item__next-item-button button button--secondary button--medium after-icon after-icon--chevron"><?= $chaining_label; ?></a>

						<?php endif; ?>

					</div>

				</div>

			</div>

		</div>

		<div class="<?= $full_width_layout ? 'full-width-image' : 'row' ?> page-single-list-item__content">

			<div class="<?= $full_width_layout ? 'full-width-image' : 'col-12 col-md-4' ?>">

				<div class="page-single-list-item__image-wrapper">

					<?php if (isset($item['image'])) : ?>

						<img srcset="<?= $item['image'] ?>?r=eyJ3Ijo3MDAsImgiOjcwMCwicSI6OTB9 2x"
                             src="<?= $item['image'] ?>?r=eyJ3IjozNTAsImgiOjM1MCwicSI6OTB9"
                             alt="<?= $item['title'] ?? '' ?>"
                             class="page-single-list-item__image"
                             fetchpriority="high">

						<?php if ($ranking && $show_ranking && !in_array("custom_ranking", $types)) : ?>

							<span class="page-single-list-item__ranking callout"><?= $ranking; ?></span>

						<?php elseif ($show_ranking && $custom_ranking != null) : ?>

							<div class="list-item__ranking-wrapper list-item__ranking-wrapper--custom">
								<span class="list-item__ranking--custom callout"><?= $custom_ranking; ?></span>
							</div>

						<?php endif; ?>

					<?php endif; ?>

				</div>

			</div>

			<div class="col-12 col-md-8 page-single-list-item__content-wrapper">

				<?php if ($show_social_icons) : ?>

					<div class="page-single-list-item__social-icons-wrapper">

						<?php
						$social_media = $item['social_media'];
						get_template_part('template-parts/social-icons/index', null, [
							'list'		=> true,
							'facebook'	=> isset($social_media['facebook']) && filter_var($social_media['facebook'], FILTER_VALIDATE_URL) ? $social_media['facebook'] : null,
							'instagram'	=> isset($social_media['instagram']) && filter_var($social_media['instagram'], FILTER_VALIDATE_URL) ? $social_media['instagram'] : null,
							'linkedin'	=> isset($social_media['linkedin']) && filter_var($social_media['linkedin'], FILTER_VALIDATE_URL) ? $social_media['linkedin'] : null,
							'twitter'	=> isset($social_media['twitter']) && filter_var($social_media['twitter'], FILTER_VALIDATE_URL) ? $social_media['twitter'] : null,
							'tiktok'	=> isset($social_media['tiktok']) && filter_var($social_media['tiktok'], FILTER_VALIDATE_URL) ? $social_media['tiktok'] : null,
							'youtube'	=> isset($social_media['youtube']) && filter_var($social_media['youtube'], FILTER_VALIDATE_URL) ? $social_media['youtube'] : null
						]) ?>

					</div>

				<?php endif; ?>

				<h3 class="page-single-list-item__title"><?= $item['title'] ?? ''; ?></h3>

				<div class="page-single-list-item__table-wrapper">

					<?php if (!empty($item['custom'])) : ?>

						<?php foreach ($item['custom'] as $key => $value) : ?>

							<?php if ($value && $key != 'custom_ranking' && $key != 'frbsurl') : ?>

								<div class="page-single-list-item__table-row">

									<span class="page-single-list-item__table-label footnote"><?= $key; ?></span>

									<span class="page-single-list-item__table-value"><?= $value; ?></span>

								</div>

							<?php elseif ($value && $key == 'frbsurl') : ?>

								<script>
									const customListUrl = '<?= $value ?>';
								</script>

							<?php endif; ?>

						<?php endforeach; ?>

					<?php endif; ?>

				</div>

				<div id="article-roadblock-ad-1" class="googlead googlead--roadblock"></div>

				<div class="page-single-list-item__content-wrapper gutenberg-content">

					<?php if (isset($item['content'])) : ?>

						<?= $item['content']; ?>

					<?php endif; ?>

				</div>

			</div>

		</div>

		<div class="row">

			<div class="col-12">

				<div class="page-single-list-item__navigation">

					<div></div>

					<div class="page-single-list-item__single-nav-wrapper">

						<?php if ($prev_item) : ?>

							<a href="<?= $prev_item_link; ?>" class="page-single-list-item__prev-item-button button button--secondary button--medium after-icon after-icon--back-chevron">

								<?php if ($show_ranking) : ?>
									<?= $prev_item_number . '. ' . $prev_item['title']; ?>
								<?php else : ?>
									<?= $prev_item['title']; ?>
								<?php endif; ?>
							</a>

						<?php elseif ($is_chaining_prev && $chaining_label_prev && $chaining_link_prev) : ?>

							<a href="<?= $chaining_link_prev; ?>" class="page-single-list-item__prev-item-button button button--secondary button--medium after-icon after-icon--back-chevron"><?= $chaining_label_prev; ?></a>

						<?php endif; ?>

						<?php if ($next_item) : ?>

							<a href="<?= $next_item_link; ?>" class="page-single-list-item__next-item-button button button--secondary button--medium after-icon after-icon--chevron">
								<?php if ($show_ranking) : ?>
									<?= $next_item_number . '. ' . $next_item['title']; ?>
								<?php else : ?>
									<?= $next_item['title']; ?>
								<?php endif; ?>
							</a>

						<?php elseif ($is_chaining && $chaining_label && $chaining_link) : ?>

							<a href="<?= $chaining_link; ?>" class="page-single-list-item__next-item-button button button--secondary button--medium"><?= $chaining_label; ?></a>

						<?php endif; ?>

					</div>

				</div>

			</div>

		</div>

		<?php if (!get_field('hide_recommendation_single')) : ?>
			<div id="reactAIRecommendation"></div>
		<?php endif; ?>

	</div>

</main>

<?php
$articleData = wp_json_encode([
	'id'					=> get_the_ID(),
	'categoryId'			=> null,
	'tags'					=> $terms ?? [],
	'huTags'				=> !is_wp_error(get_the_tags(get_the_ID())) && get_the_tags(get_the_ID()) ? get_the_tags(get_the_ID()) : [],
	'country'				=> COUNTRY,
], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

?>

<script type="text/javascript">
	window.Article = <?= $articleData; ?>;
</script>
