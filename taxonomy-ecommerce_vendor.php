<?php

$termSlug = get_query_var('term');
$vendor = ForbesEcommerceSync\Vendor::getBySlug($termSlug);

if (!$vendor) {
	wp_redirect(get_home_url());
	exit;
}

// Získanie page parametra z URL
$currentPage = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$itemsPerPage = 20;

// Výpočet celkového počtu produktov na načítanie (page * itemsPerPage)
$totalItemsToLoad = $currentPage * $itemsPerPage;

$productsQuery = (new ForbesEcommerceSync\ProductQuery())
	->setVendor($vendor)
	->setPerPage($totalItemsToLoad);

if (!empty($_GET['category'])) {
	$category = ForbesEcommerceSync\Category::getBySlug($_GET['category']);
	$productsQuery->setCategory($category);
}

$products = $productsQuery->fetchAll();

if ( ! empty($products)) {
    foreach ($products as $product) {
        $product->content = wp_kses_post(html_entity_decode($product->content, ENT_QUOTES));
    }
}

$categories = array_values(array_filter(
	ForbesEcommerceSync\Category::getTopLevel(),
	fn($category): bool => (new ForbesEcommerceSync\ProductQuery())
		->setCategory($category)
		->setVendor($vendor)
		->exists()
));

$vendorDetailData = [
	'name' => $vendor->name,
    'description' => wp_kses_post(html_entity_decode($vendor->description)),
];

$vendorLogo = [
	'url' => $vendor->logoUrl,
	'alt' => $vendor->name,
];

?>

<?php get_header(); ?>

<main class="vendors-single">
	<div class="container">
		<div class="row">
			<div class="col-12">
				<div
					id="reactEcommerceVendorDetailPage"
                    data-vendor-detail="<?php echo esc_attr(wp_json_encode($vendorDetailData)); ?>"
                    data-products="<?php echo esc_attr(wp_json_encode($products)); ?>"
                    data-vendor-logo="<?php echo esc_attr(wp_json_encode($vendorLogo)); ?>"
                    data-vendor-id="<?php echo esc_attr(wp_json_encode($vendor->id)); ?>"
                    data-categories="<?php echo esc_attr(wp_json_encode($categories)); ?>"
                    data-current-page="<?php echo esc_attr($currentPage); ?>"
				></div>
			</div>
		</div>
	</div>
</main>

<?php get_footer(); ?>
