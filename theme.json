{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 2, "settings": {"layout": {"contentSize": "840px", "wideSize": "1100px"}, "color": {"palette": [{"slug": "text-primary", "color": "var(--color-text-primary)", "name": "Text Primary"}, {"slug": "text-secondary", "color": "var(--color-text-secondary)", "name": "Text Secondary"}, {"slug": "text-brand", "color": "var(--color-text-brand)", "name": "Text Brand"}, {"slug": "text-success", "color": "var(--color-text-success)", "name": "Text Success"}, {"slug": "text-error", "color": "var(--color-text-error)", "name": "Text Error"}, {"slug": "text-primary-invert", "color": "var(--color-text-primary-invert)", "name": "Text Primary Invert"}, {"slug": "text-secondary-invert", "color": "var(--color-text-secondary-invert)", "name": "Text Secondary Invert"}, {"slug": "surface-primary", "color": "var(--color-surface-primary)", "name": "Surface Primary"}, {"slug": "surface-primary-invert", "color": "var(--color-surface-primary-invert)", "name": "Surface Primary Invert"}, {"slug": "surface-secondary", "color": "var(--color-surface-secondary)", "name": "Surface Secondary"}, {"slug": "surface-secondary-invert", "color": "var(--color-surface-secondary-invert)", "name": "Surface Secondary Invert"}, {"slug": "surface-invert", "color": "var(--color-surface-invert)", "name": "Surface Invert"}, {"slug": "surface-icon", "color": "var(--color-surface-icon)", "name": "Surface Icon"}, {"slug": "surface-program-premium", "color": "var(--color-surface-program-premium)", "name": "Surface Program Premium"}, {"slug": "surface-program-standard", "color": "var(--color-surface-program-standard)", "name": "Surface Program Standard"}, {"slug": "surface-brand-light", "color": "var(--color-surface-brand-light)", "name": "Surface Brand Light"}, {"slug": "surface-brand-dark", "color": "var(--color-surface-brand-dark)", "name": "Surface Brand Dark"}, {"slug": "surface-accent-blue", "color": "var(--color-surface-accent-blue)", "name": "Surface Accent Blue"}, {"slug": "surface-accent-emerald", "color": "var(--color-surface-accent-emerald)", "name": "Surface Accent Emerald"}, {"slug": "surface-accent-rose", "color": "var(--color-surface-accent-rose)", "name": "Surface Accent Rose"}, {"slug": "icon-primary", "color": "var(--color-icon-primary)", "name": "Icon Primary"}, {"slug": "icon-primary-invert", "color": "var(--color-icon-primary-invert)", "name": "Icon Primary Invert"}, {"slug": "icon-secondary", "color": "var(--color-icon-secondary)", "name": "Icon Secondary"}, {"slug": "icon-secondary-invert", "color": "var(--color-icon-secondary-invert)", "name": "Icon Secondary Invert"}, {"slug": "icon-onLight", "color": "var(--color-icon-onLight)", "name": "Icon On Light"}, {"slug": "icon-onLight-invert", "color": "var(--color-icon-onLight-invert)", "name": "Icon On Light Invert"}, {"slug": "icon-onDark", "color": "var(--color-icon-onDark)", "name": "Icon On Dark"}, {"slug": "icon-onDark-invert", "color": "var(--color-icon-onDark-invert)", "name": "Icon On Dark Invert"}, {"slug": "button-primary", "color": "var(--color-button-primary)", "name": "Button Primary"}, {"slug": "button-secondary", "color": "var(--color-button-secondary)", "name": "Button Secondary"}, {"slug": "button-primary-pressed", "color": "var(--color-button-primary-pressed)", "name": "Button Primary Pressed"}, {"slug": "button-secondary-pressed", "color": "var(--color-button-secondary-pressed)", "name": "Button Secondary Pressed"}, {"slug": "button-primary-disabled", "color": "var(--color-button-primary-disabled)", "name": "Button Primary Disabled"}, {"slug": "button-secondary-disabled", "color": "var(--color-button-secondary-disabled)", "name": "Button Secondary Disabled"}, {"slug": "button-secondary-disabled-invert", "color": "var(--color-button-secondary-disabled-invert)", "name": "Button Secondary Disabled Invert"}, {"slug": "button-primary-invert", "color": "var(--color-button-primary-invert)", "name": "Button Primary Invert"}, {"slug": "link-default", "color": "var(--color-link-default)", "name": "<PERSON>"}, {"slug": "link-hover", "color": "var(--color-link-hover)", "name": "<PERSON>"}, {"slug": "link-hover-invert", "color": "var(--color-link-hover-invert)", "name": "Link Hover Invert"}, {"slug": "link-visited", "color": "var(--color-link-visited)", "name": "Link Visited"}, {"slug": "link-life-hover", "color": "var(--color-link-life-hover)", "name": "Link Life Hover"}, {"slug": "link-life-visisted", "color": "var(--color-link-life-visisted)", "name": "Link Life Visisted"}, {"slug": "link-status-success-default", "color": "var(--color-link-status-success-default)", "name": "Link Status Success Default"}, {"slug": "link-status-success-hover", "color": "var(--color-link-status-success-hover)", "name": "Link Status Success Hover"}, {"slug": "link-status-alert-default", "color": "var(--color-link-status-alert-default)", "name": "Link Status <PERSON>"}, {"slug": "link-status-alert-hover", "color": "var(--color-link-status-alert-hover)", "name": "Link Status Al<PERSON>"}, {"slug": "tag-default", "color": "var(--color-tag-default)", "name": "Tag Default"}, {"slug": "tag-default-invert", "color": "var(--color-tag-default-invert)", "name": "Tag Default Invert"}, {"slug": "tag-hover", "color": "var(--color-tag-hover)", "name": "Tag Hover"}, {"slug": "tag-life-default", "color": "var(--color-tag-life-default)", "name": "Tag Life Default"}, {"slug": "tag-life-hover", "color": "var(--color-tag-life-hover)", "name": "Tag Life Hover"}, {"slug": "input-border-default", "color": "var(--color-input-border-default)", "name": "Input Border Default"}, {"slug": "input-border-hover", "color": "var(--color-input-border-hover)", "name": "Input Border Hover"}, {"slug": "input-border-active", "color": "var(--color-input-border-active)", "name": "Input Border Active"}, {"slug": "input-border-success", "color": "var(--color-input-border-success)", "name": "Input Border Success"}, {"slug": "input-border-error", "color": "var(--color-input-border-error)", "name": "Input Border Error"}, {"slug": "divider", "color": "var(--color-divider)", "name": "Divider"}, {"slug": "divider-invert", "color": "var(--color-divider-invert)", "name": "Divider <PERSON>"}, {"slug": "white", "color": "var(--color-white)", "name": "White"}, {"slug": "black", "color": "var(--color-black)", "name": "Black"}, {"slug": "other-slate-foreground", "color": "var(--color-other-slate-foreground)", "name": "Other Slate Foreground"}, {"slug": "other-slate-background", "color": "var(--color-other-slate-background)", "name": "Other Slate Background"}, {"slug": "other-stone-foreground", "color": "var(--color-other-stone-foreground)", "name": "Other Stone Foreground"}, {"slug": "other-stone-background", "color": "var(--color-other-stone-background)", "name": "Other Stone Background"}, {"slug": "other-emerald-foreground", "color": "var(--color-other-emerald-foreground)", "name": "Other Emerald Foreground"}, {"slug": "other-emerald-background", "color": "var(--color-other-emerald-background)", "name": "Other Emerald Background"}, {"slug": "other-blue-foreground", "color": "var(--color-other-blue-foreground)", "name": "Other Blue Foreground"}, {"slug": "other-blue-background", "color": "var(--color-other-blue-background)", "name": "Other Blue Background"}, {"slug": "other-pink-foreground", "color": "var(--color-other-pink-foreground)", "name": "Other Pink Foreground"}, {"slug": "other-pink-background", "color": "var(--color-other-pink-background)", "name": "Other Pink Background"}, {"slug": "surface-secondary-opacity", "color": "var(--color-surface-secondary-opacity)", "name": "Surface Secondary Opacity"}, {"slug": "post-background", "color": "var(--color-post-background)", "name": "Post Background"}, {"slug": "product-background", "color": "var(--color-product-background)", "name": "Product Background"}, {"slug": "subscribe-bar-background", "color": "var(--color-subscribe-bar-background)", "name": "Subscribe Bar Background"}, {"slug": "subscribe-bar-text", "color": "var(--color-subscribe-bar-text)", "name": "Subscribe Bar Text"}, {"slug": "subscribe-bar-button", "color": "var(--color-subscribe-bar-button)", "name": "Subscribe <PERSON>"}, {"slug": "subscribe-bar-highlight", "color": "var(--color-subscribe-bar-highlight)", "name": "Subscribe Bar Highlight"}, {"slug": "white-darkfixed", "color": "var(--color-white-darkfixed)", "name": "White Darkfixed"}, {"slug": "background-darkfixed", "color": "var(--color-background-darkfixed)", "name": "Background Darkfixed"}, {"slug": "bodytext-darkfixed", "color": "var(--color-bodytext-darkfixed)", "name": "Bodytext Darkfixed"}, {"slug": "border-darkfixed", "color": "var(--color-border-darkfixed)", "name": "Border Darkfixed"}, {"slug": "tag-default-darkfixed", "color": "var(--color-tag-default-darkfixed)", "name": "Tag Default Darkfixed"}, {"slug": "tag-hover-darkfixed", "color": "var(--color-tag-hover-darkfixed)", "name": "Tag Hover Darkfixed"}, {"slug": "tag-life-default-darkfixed", "color": "var(--color-tag-life-default-darkfixed)", "name": "Tag Life Default Darkfixed"}, {"slug": "tag-life-hover-darkfixed", "color": "var(--color-tag-life-hover-darkfixed)", "name": "Tag Life Hover Darkfixed"}]}, "typography": {"fontFamilies": [{"slug": "noto-serif", "fontFamily": "var(--font-noto-serif)", "name": "<PERSON><PERSON>"}, {"slug": "archivo", "fontFamily": "var(--font-archivo)", "name": "Archivo"}, {"slug": "jetbrains-mono", "fontFamily": "var(--font-jetbrains-mono)", "name": "JetBrains Mono"}], "fontSizes": [{"slug": "h1", "size": "5.2rem", "name": "H1"}, {"slug": "h2", "size": "4.6rem", "name": "H2"}, {"slug": "h3", "size": "3.8rem", "name": "H3"}, {"slug": "h4", "size": "2.8rem", "name": "H4"}, {"slug": "h5", "size": "2.2rem", "name": "H5"}, {"slug": "h6", "size": "1.8rem", "name": "H6"}, {"slug": "body", "size": "1.8rem", "name": "Body"}, {"slug": "lead", "size": "2rem", "name": "Lead"}, {"slug": "basic-16", "size": "1.6rem", "name": "Basic 16"}, {"slug": "basic-14", "size": "1.4rem", "name": "Basic 14"}, {"slug": "tag-text", "size": "1.2rem", "name": "Tag Text"}, {"slug": "small-text", "size": "1rem", "name": "Small Text"}, {"slug": "link-20", "size": "2rem", "name": "Link 20"}, {"slug": "link-16", "size": "1.6rem", "name": "Link 16"}, {"slug": "link-14", "size": "1.4rem", "name": "Link 14"}]}, "shadow": {"presets": [{"slug": "level-1", "shadow": "var(--box-shadow-level-1)", "name": "Level 1"}, {"slug": "level-2", "shadow": "var(--box-shadow-level-2)", "name": "Level 2"}, {"slug": "level-3", "shadow": "var(--box-shadow-level-3)", "name": "Level 3"}, {"slug": "level-4", "shadow": "var(--box-shadow-level-4)", "name": "Level 4"}, {"slug": "level-5", "shadow": "var(--box-shadow-level-5)", "name": "Level 5"}, {"slug": "elevation-light-locker", "shadow": "var(--box-shadow-elevation-light-locker)", "name": "Elevation Light Locker"}, {"slug": "elevation-light-level-2", "shadow": "var(--box-shadow-elevation-light-level-2)", "name": "Elevation Light Level 2"}, {"slug": "elevation-light-level-3", "shadow": "var(--box-shadow-elevation-light-level-3)", "name": "Elevation Light Level 3"}, {"slug": "extra", "shadow": "var(--box-shadow-extra)", "name": "Extra"}]}}}