<?php

/**
 * Template Name: Premium Search (HU)
 */

get_header();

$args = array(
	'posts_per_page' => -1,
	'post_type' 	 => 'post',
	'post_status' 	 => 'publish',
	'tax_query'      => array(
		array(
			'taxonomy' 	=> CATEGORY_TYPE,
			'terms' 	=> array('brand-club', 'brand-voice', 'tamogatoi-tartalom'),
			'field' 	=> 'slug',
			'operator' 	=> 'IN',
		),
	)
);

$query = new WP_Query($args);

?>

<main class="page-premium">
	<div class="container">

	<?php if ($query->have_posts()) : ?>

		<?php while ($query->have_posts()) : $query->the_post(); ?>

		<div class="page-premium__title-wrapper">
			<a href="<?= get_the_permalink() ?>">
				<p><?= get_the_title(); ?></p>

			</a>
			<div class="h-d--flex">
				<p><?= get_the_author(); ?></p>
				<p class="page-premium__date"><?= get_the_date(); ?></p>
			</div>
		</div>

		<?php endwhile; ?>

	<?php endif; ?>
	</div>
</main>

<?php get_footer(); ?>

