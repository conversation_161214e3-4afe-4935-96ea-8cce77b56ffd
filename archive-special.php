<?php 

get_header(); 

?>

<main class="special-archive">

	<?php if ('hu' === COUNTRY) : ?>

		<?php get_template_part('template-parts/leaderboard-ad/index', null, array('page' => 'archive')) ?>

	<?php endif; ?>

	<div class="container">

		<?php $pageTitle = new Heading(esc_html__('Specials & Rankings', 'FORBES') , 'h1', null, '700', 'noto-serif'); echo $pageTitle->render(); ?>		

		<?php if (have_posts()) : ?>

			<div class="row">

				<div class="col-12<?= 'cz' !== COUNTRY ? ' col-md-8' : ''; ?>">

					<div class="special-archive__grid<?= 'cz' === COUNTRY ? ' no-ad' : ''; ?>">

						<?php $index = 0; ?>

						<?php while (have_posts()) : the_post(); ?>

							<?php if (2 === $index && 'hu' === COUNTRY) : ?>

								<?php get_template_part('template-parts/leaderboard-ad/index', null, array('page' => 'archive', 'mobile' => true)); ?>

							<?php endif; ?>

							<?php get_template_part('template-parts/event-and-special-card/index', null, array('special' => true)); ?>

							<?php $index++; ?>

						<?php endwhile; ?>

					</div>


				</div>

				<?php if ('cz' !== COUNTRY) : ?>

					<div class="col-12 col-md-4">

						<div class="special-archive__ad-container special-archive__ad-container--desktop googlead-container">
							<div id="category-ad-desktop" class="googlead"></div>
						</div>

					</div>

				<?php endif; ?>

			</div>

			<div class="row">
				<div class="col-12">
					<?php get_template_part('template-parts/pagination/index', null, array('posts_found' => $wp_query->found_posts, 'paged' => get_query_var('paged') ? get_query_var('paged') : 1, 'max_num_pages' => $wp_query->max_num_pages)); ?>

					<?php wp_reset_query(); ?>
				</div>
			</div>

		<?php endif; ?>

	</div>

</main>

<?php get_footer(); ?>
