@layer theme, base, components, utilities;

@import 'tailwindcss/theme.css' layer(theme) prefix(tw);
@import 'tailwindcss/utilities.css' layer(utilities) prefix(tw);

@custom-variant dark (&:where(.dark-mode, .dark-mode *));

@layer utilities {
  .tw\:\!mb-0 {
    margin-bottom: 0px !important;
  }
}

@theme inline {
  --spacing: 0.4rem;

  /* Font Families */
  --font-noto-serif: var(--font-noto-serif);
  --font-archivo: var(--font-archivo);
  --font-jetbrains-mono: var(--font-jetbrains-mono);

  --tracking-forbes-plus: 0.28;
  --tracking-forbes-minus: -0.28;
  --tracking-forbes-plus-2: 0.36;
  --tracking-forbes-minus-2: -0.36;

  /* Box Shadows */
  --shadow-level-1: var(--box-shadow-level-1);
  --shadow-level-2: var(--box-shadow-level-2);
  --shadow-level-3: var(--box-shadow-level-3);
  --shadow-level-4: var(--box-shadow-level-4);
  --shadow-level-5: var(--box-shadow-level-5);
  --shadow-elevation-light-locker: var(--box-shadow-elevation-light-locker);
  --shadow-product: var(--box-shadow-product);
  --shadow-magazine-cover: var(--box-shadow-magazine-cover);
  --shadow-elevation-light-level-2: var(--box-shadow-elevation-light-level-2);
  --shadow-elevation-light-level-3: var(--box-shadow-elevation-light-level-3);
  --shadow-extra: var(--box-shadow-extra);
  --shadow-product: var(--box-shadow-product);
  --shadow-magazine-cover: var(--box-shadow-magazine-cover);
  --shadow-elevation-light-level-2: var(--box-shadow-elevation-light-level-2);
  --shadow-elevation-light-level-3: var(--box-shadow-elevation-light-level-3);
  --shadow-extra: var(--box-shadow-extra); /* Text Colors */
  --shadow-product: var(--box-shadow-product);
  --shadow-magazine-cover: var(--box-shadow-magazine-cover);
  --shadow-elevation-light-level-2: var(--box-shadow-elevation-light-level-2);
  --shadow-elevation-light-level-3: var(--box-shadow-elevation-light-level-3);
  --shadow-extra: var(--box-shadow-extra); /* Text Colors */
  --shadow-product: var(--box-shadow-product);
  --shadow-magazine-cover: var(--box-shadow-magazine-cover);
  --shadow-elevation-light-level-2: var(--box-shadow-elevation-light-level-2);
  --shadow-elevation-light-level-3: var(--box-shadow-elevation-light-level-3);
  --shadow-extra: var(--box-shadow-extra); /* Text Colors */

  /* Text colors */
  --color-text-primary: var(--color-text-primary);
  --color-text-secondary: var(--color-text-secondary);
  --color-text-brand: var(--color-text-brand);
  --color-text-success: var(--color-text-success);
  --color-text-error: var(--color-text-error);
  --color-text-primary-invert: var(--color-text-primary-invert);
  --color-text-secondary-invert: var(--color-text-secondary-invert);

  /* Surface Colors */
  --color-surface-primary: var(--color-surface-primary);
  --color-surface-primary-invert: var(--color-surface-primary-invert);
  --color-surface-secondary: var(--color-surface-secondary);
  --color-surface-secondary-invert: var(--color-surface-secondary-invert);
  --color-surface-invert: var(--color-surface-invert);
  --color-surface-icon: var(--color-surface-icon);
  --color-surface-program-premium: var(--color-surface-program-premium);
  --color-surface-program-standard: var(--color-surface-program-standard);
  --color-surface-brand-light: var(--color-surface-brand-light);
  --color-surface-brand-dark: var(--color-surface-brand-dark);
  --color-surface-accent-blue: var(--color-surface-accent-blue);
  --color-surface-accent-emerald: var(--color-surface-accent-emerald);
  --color-surface-accent-rose: var(--color-surface-accent-rose);

  /** Icon Colors */
  --color-icon-primary: var(--color-icon-primary);
  --color-icon-primary-invert: var(--color-icon-primary-invert);
  --color-icon-secondary: var(--color-icon-secondary);
  --color-icon-secondary-invert: var(--color-icon-secondary-invert);
  --color-icon-onLight: var(--color-icon-onLight);
  --color-icon-onLight-invert: var(--color-icon-onLight-invert);
  --color-icon-onDark: var(--color-icon-onDark);
  --color-icon-onDark-invert: var(--color-icon-onDark-invert);

  /* Button Colors */
  --color-button-primary: var(--color-button-primary);
  --color-button-secondary: var(--color-button-secondary);
  --color-button-primary-pressed: var(--color-button-primary-pressed);
  --color-button-secondary-pressed: var(--color-button-secondary-pressed);
  --color-button-primary-disabled: var(--color-button-primary-disabled);
  --color-button-secondary-disabled: var(--color-button-secondary-disabled);
  --color-button-secondary-disabled-invert: var(--color-button-secondary-disabled-invert);
  --color-button-primary-invert: var(--color-button-primary-invert);

  /* Link Colors */
  --color-link-default: var(--color-link-default);
  --color-link-hover: var(--color-link-hover);
  --color-link-hover-invert: var(--color-link-hover-invert);
  --color-link-visited: var(--color-link-visited);
  --color-link-life-hover: var(--color-link-life-hover);
  --color-link-life-visisted: var(--color-link-life-visisted);
  --color-link-status-success-default: var(--color-link-status-success-default);
  --color-link-status-success-hover: var(--color-link-status-success-hover);
  --color-link-status-alert-default: var(--color-link-status-alert-default);
  --color-link-status-alert-hover: var(--color-link-status-alert-hover);

  /* Tag Colors */
  --color-tag-default: var(--color-tag-default);
  --color-tag-default-invert: var(--color-tag-default-invert);
  --color-tag-hover: var(--color-tag-hover);
  --color-tag-life-default: var(--color-tag-life-default);
  --color-tag-life-hover: var(--color-tag-life-hover);

  /* Input Colors */
  --color-input-border-default: var(--color-input-border-default);
  --color-input-border-hover: var(--color-input-border-hover);
  --color-input-border-active: var(--color-input-border-active);
  --color-input-border-success: var(--color-input-border-success);
  --color-input-border-error: var(--color-input-border-error);

  /* Other Colors */
  --color-divider: var(--color-divider);
  --color-divider-invert: var(--color-divider-invert);
  --color-white: var(--color-white);
  --color-black: var(--color-black);
  --color-other-slate-foreground: var(--color-other-slate-foreground);
  --color-other-slate-background: var(--color-other-slate-background);
  --color-other-stone-foreground: var(--color-other-stone-foreground);
  --color-other-stone-background: var(--color-other-stone-background);
  --color-other-emerald-foreground: var(--color-other-emerald-foreground);
  --color-other-emerald-background: var(--color-other-emerald-background);
  --color-other-blue-foreground: var(--color-other-blue-foreground);
  --color-other-blue-background: var(--color-other-blue-background);
  --color-other-pink-foreground: var(--color-other-pink-foreground);
  --color-other-pink-background: var(--color-other-pink-background);
  --color-surface-secondary-opacity: var(--color-surface-secondary-opacity);
  --color-post-background: var(--color-post-background);
  --color-product-background: var(--color-product-background);

  /* Subscribe bar */
  --color-subscribe-bar-background: var(--color-subscribe-bar-background);
  --color-subscribe-bar-text: var(--color-subscribe-bar-text);
  --color-subscribe-bar-button: var(--color-subscribe-bar-button);
  --color-subscribe-bar-highlight: var(--color-subscribe-bar-highlight);

  /* Non-changing colors */
  --color-white-darkfixed: var(--color-white-darkfixed);
  --color-background-darkfixed: var(--color-background-darkfixed);
  --color-bodytext-darkfixed: var(--color-bodytext-darkfixed);
  --color-border-darkfixed: var(--color-border-darkfixed);
  --color-tag-default-darkfixed: var(--color-tag-default-darkfixed);
  --color-tag-hover-darkfixed: var(--color-tag-hover-darkfixed);
  --color-tag-life-default-darkfixed: var(--color-tag-life-default-darkfixed);
  --color-tag-life-hover-darkfixed: var(--color-tag-life-hover-darkfixed);
}
