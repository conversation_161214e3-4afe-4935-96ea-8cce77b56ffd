<svg width="96" height="95" viewBox="0 0 96 95" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M46 0L0.745167 45.2548L46 90.5097L91.2548 45.2548L46 0Z" fill="white"/>
</g>
<rect x="30.2451" y="36.5" width="27" height="22" stroke="black" stroke-width="3"/>
<path d="M36.7451 30H63.7451V52" stroke="black" stroke-width="3"/>
<defs>
<filter id="filter0_d" x="0.745117" y="0" width="94.5097" height="94.5097" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.921569 0 0 0 0 0.654902 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
