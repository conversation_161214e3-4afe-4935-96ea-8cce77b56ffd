<?php

global $wp_query;

$found_posts = $wp_query->found_posts ?? 0;
$max_num_pages = $wp_query->max_num_pages ?? 1;
$paged = $wp_query->query_vars['paged'] ?? 1;

$author_id = get_queried_object_id();
$is_brandvoice_author = !empty(get_field('isBrandvoice', 'coauthor_' . $author_id));
$author = get_author_datas($author_id); ?>

<?php get_header(); ?>

<script>
    window.singleAuthor = <?php echo json_encode($author); ?>;
</script>

<main class="author-single <?= $is_brandvoice_author ? 'author-single--brandvoice' : ''; ?>">

	<div class="author-single__header">
		<div class="container">
			<?php
				$authorComponent = new Author(
					$author_id,
					$author['role'],
					$author['name'],
					$author['image'],
					$author['description'],
					$author['position'],
				);
				$authorComponent->render();
			?>
		</div>
	</div>

	<?php if ( have_posts() ) : ?>
		<div class="author-single__content">
			<div class="container">
                <?php $pageTitle = new Heading(__('Latest articles', 'FORBES'), 'h3', null, '600', 'archivo');
                echo $pageTitle->render(); ?>
			</div>

			<div class="container">
				<div class="author-single__content-wrapper">

					<div class="row author-single__posts-wrapper">
						<?php while (have_posts()) : the_post(); ?>
							<div class="author-single__post-wrapper col-lg-4 col-md-6 col-12">
								<?php get_template_part('template-parts/article-card/index', null, ['post_id' => get_the_ID(), 'article_card_type' => 'archive']); ?>
							</div>
					   <?php endwhile; ?>
					</div>

				   <div class="row">
						<div class="col-12">
							<?php get_template_part('template-parts/pagination/index', null, ['posts_found' => $found_posts, 'paged' => $paged, 'max_num_pages' => $max_num_pages]); ?>
						</div>
					</div>
				</div>
			</div>
		</div>
	<?php endif; ?>

</main>

<?php get_footer(); ?>
