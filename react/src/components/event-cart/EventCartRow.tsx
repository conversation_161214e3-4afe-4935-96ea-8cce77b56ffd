import React from 'react';
import { EventItem } from 'components/event-cart/EventCart';

export default function EventCartRow(props: {
  item: EventItem;
  s: string;
  onClick: () => void;
  localUnitLabel: string;
  onClick1: () => void;
  s1: string;
  index: number;
  length: number;
}) {
  return (
    <React.Fragment>
      {/* Event Row - Mobile: vertical stack, Tablet+: horizontal table row */}
      <div className="tw:flex tw:flex-col tw:md:flex-row tw:md:justify-between tw:md:items-center tw:gap-[16px] tw:md:gap-[10px] tw:max-w-[730px] tw:w-full">
        {/* Product Info */}
        <div className="tw:flex tw:flex-row tw:items-stretch tw:gap-[8px] tw:w-full tw:md:max-w-[370px]">
          <div className="tw:flex tw:flex-col tw:flex-1">
            <div className="tw:flex tw:flex-col tw:flex-1">
              <div className="tw:flex tw:flex-col tw:flex-1">
                <span className="basic-16-new tw:text-text-secondary tw:w-full">{props.item.eventName}</span>
                <span className="h5-condensed tw:text-text-primary tw:w-full">{props.item.ticketType}</span>
              </div>
              <span className="basic-16-new tw:text-text-primary tw:w-full">{props.s}</span>
            </div>
          </div>
        </div>

        {/* Mobile: Quantity Row */}
        <div className="tw:flex tw:flex-row tw:justify-between tw:items-center tw:w-full tw:md:hidden">
          <span className="basic-16-new tw:text-text-primary">Množství</span>
          <div className="tw:flex tw:flex-row tw:justify-center tw:items-center tw:gap-[16px]">
            {/* Minus Button */}
            <div className="tw:w-[40px] tw:h-[40px] tw:relative tw:flex-shrink-0">
              <button
                onClick={props.onClick}
                className="tw:w-full tw:h-full tw:cursor-pointer"
                disabled={props.item.quantity === 0}
              >
                <div className="tw:absolute tw:left-[12px] tw:top-[12px] tw:w-[16px] tw:h-[16px]">
                  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g opacity="0.64">
                      <path d="M3 8.5H13" stroke="#475569" strokeWidth="1.5" />
                    </g>
                  </svg>
                </div>
              </button>
            </div>

            {/* Quantity Display */}
            <div className="tw:flex tw:flex-row tw:justify-center tw:gap-[4px]">
              <span className="basic-16-new tw:text-text-primary">{props.item.quantity}</span>
              <span className="basic-16-new tw:text-text-primary">{props.localUnitLabel}</span>
            </div>

            {/* Plus Button */}
            <div className="tw:w-[40px] tw:h-[40px] tw:relative tw:flex-shrink-0">
              <button onClick={props.onClick1} className="tw:w-full tw:h-full tw:cursor-pointer">
                <div className="tw:absolute tw:left-[12px] tw:top-[12px] tw:w-[16px] tw:h-[16px]">
                  <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M3 8.5H8M13 8.5H8M8 8.5V3.5M8 8.5V13.5"
                      stroke="#475569"
                      strokeWidth="1.5"
                      strokeLinecap="square"
                    />
                  </svg>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* Tablet+: Quantity Controls (original horizontal layout) */}
        <div className="tw:hidden tw:md:flex tw:flex-row tw:justify-between tw:items-center tw:max-w-[140px] tw:w-full">
          {/* Minus Button */}
          <div className="tw:w-[40px] tw:h-[40px] tw:relative tw:flex-shrink-0">
            <button
              onClick={props.onClick}
              className="tw:w-full tw:h-full tw:cursor-pointer"
              disabled={props.item.quantity === 0}
            >
              <div className="tw:absolute tw:left-[12px] tw:top-[12px] tw:w-[16px] tw:h-[16px]">
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g opacity="0.64">
                    <path d="M3 8.5H13" stroke="#475569" strokeWidth="1.5" />
                  </g>
                </svg>
              </div>
            </button>
          </div>

          {/* Quantity Display */}
          <div className="tw:flex tw:flex-row tw:justify-center tw:gap-[4px] tw:flex-1">
            <span className="basic-16-new tw:text-text-primary">{props.item.quantity}</span>
            <span className="basic-16-new tw:text-text-primary">{props.localUnitLabel}</span>
          </div>

          {/* Plus Button */}
          <div className="tw:w-[40px] tw:h-[40px] tw:relative tw:flex-shrink-0">
            <button onClick={props.onClick1} className="tw:w-full tw:h-full tw:cursor-pointer">
              <div className="tw:absolute tw:left-[12px] tw:top-[12px] tw:w-[16px] tw:h-[16px]">
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M3 8.5H8M13 8.5H8M8 8.5V3.5M8 8.5V13.5"
                    stroke="#475569"
                    strokeWidth="1.5"
                    strokeLinecap="square"
                  />
                </svg>
              </div>
            </button>
          </div>
        </div>

        {/* Mobile: Total Row */}
        <div className="tw:flex tw:flex-row tw:justify-between tw:items-center tw:w-full tw:md:hidden">
          <span className="basic-16-new tw:text-text-primary">Spolu</span>
          <span className="basic-16-new tw:text-text-secondary">{props.s1}</span>
        </div>

        {/* Tablet+: Item Total (original layout) */}
        <div className="tw:hidden tw:md:flex tw:max-w-[100px] tw:w-full tw:justify-end">
          <span className="basic-16-new tw:text-text-secondary tw:text-right">{props.s1}</span>
        </div>
      </div>

      {/* Spacer after each item */}
      <div className="tw:w-full tw:h-[24px] tw:bg-white" />

      {/* Divider after each item except the last one */}
      {props.index < props.length - 1 && (
        <>
          <div className="tw:w-full tw:h-[1px] tw:bg-[#E2E8F0]" />
          <div className="tw:w-full tw:h-[24px] tw:bg-white" />
        </>
      )}
    </React.Fragment>
  );
}
