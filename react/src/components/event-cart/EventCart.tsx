import React, { useState } from 'react';
import { useTranslations } from '../../localization/useTranslations';
import EventCartRow from './EventCartRow';
import Button from '../buttons/SimpleButton/Button';

export interface EventItem {
  id: string;
  eventName: string;
  ticketType: string;
  price: number;
  currency: string;
  quantity: number;
}

export interface EventCartProps {
  items: EventItem[];
  onQuantityChange: (itemId: string, newQuantity: number) => void;
  onBackClick: () => void;
  onCheckoutClick: () => void;
  isCheckoutDisabled?: boolean;
}

export default function EventCart({
  items: initialItems,
  onQuantityChange,
  onBackClick,
  onCheckoutClick,
  isCheckoutDisabled = false,
}: EventCartProps) {
  const [items, setItems] = useState<EventItem[]>(initialItems);
  const translations = useTranslations();

  // Use translations with fallbacks
  const localBackLinkText = translations?.eventCart?.backLinkText || 'Back to Events';
  const localCheckoutButtonText = translations?.eventCart?.checkoutButtonText || 'Checkout';
  const localQuantityLabel = translations?.eventCart?.quantityLabel || 'Quantity';
  const localTotalLabel = translations?.eventCart?.totalLabel || 'Total';
  const localTotalToPayLabel = translations?.eventCart?.totalToPayLabel || 'Total to pay';
  const localUnitLabel = translations?.eventCart?.unitLabel || 'pcs';
  const calculateItemTotal = (item: EventItem) => {
    return item.price * item.quantity;
  };

  const calculateGrandTotal = () => {
    return items.reduce((total, item) => total + calculateItemTotal(item), 0);
  };

  const formatPrice = (amount: number, currency: string) => {
    return `${amount.toFixed(2)} ${currency}`;
  };

  const handleQuantityDecrease = (itemId: string, currentQuantity: number) => {
    if (currentQuantity > 0) {
      const newQuantity = currentQuantity - 1;
      setItems((prevItems) =>
        prevItems.map((item) => (item.id === itemId ? { ...item, quantity: newQuantity } : item))
      );
      onQuantityChange(itemId, newQuantity);
    }
  };

  const handleQuantityIncrease = (itemId: string, currentQuantity: number) => {
    const newQuantity = currentQuantity + 1;
    setItems((prevItems) => prevItems.map((item) => (item.id === itemId ? { ...item, quantity: newQuantity } : item)));
    onQuantityChange(itemId, newQuantity);
  };

  return (
    <div className="tw:flex tw:flex-col tw:items-center tw:gap-[24px] tw:px-[16px] tw:md:px-0 tw:py-[40px_0px_80px]">
      {/* Main Table Container */}
      <div className="tw:flex tw:flex-col tw:w-full tw:max-w-[730px]">
        {/* Table Header - Hidden on mobile, visible from tablet up */}
        <div className="tw:hidden tw:md:flex tw:w-full tw:max-w-[730px] tw:justify-between">
          <span className="tw:max-w-[370px] tw:w-full"></span>
          <span className="tw:max-w-[140px] tw:w-full basic-14-new tw:text-text-primary tw:text-center">
            {localQuantityLabel}
          </span>
          <span className="tw:max-w-[100px] tw:w-full basic-14-new tw:text-text-primary tw:text-right">
            {localTotalLabel}
          </span>
        </div>

        {/* Spacer */}
        <div className="tw:w-full tw:h-[8px] tw:bg-white" />

        {/* Divider */}
        <div className="tw:w-full tw:h-[1px] tw:bg-[#E2E8F0]" />

        {/* Spacer */}
        <div className="tw:w-full tw:h-[24px] tw:bg-white" />

        {/* Event Rows */}
        {items.map((item, index) => (
          <EventCartRow
            key={item.id}
            item={item}
            s={formatPrice(item.price, item.currency)}
            onClick={() => handleQuantityDecrease(item.id, item.quantity)}
            localUnitLabel={localUnitLabel}
            onClick1={() => handleQuantityIncrease(item.id, item.quantity)}
            s1={formatPrice(calculateItemTotal(item), item.currency)}
            index={index}
            length={items.length}
          />
        ))}

        {/* Divider before total */}
        <div className="tw:w-full tw:h-[1px] tw:bg-[#E2E8F0]" />

        {/* Spacer */}
        <div className="tw:w-full tw:h-[24px] tw:bg-white" />

        {/* Total Row */}
        <div className="tw:relative tw:w-full tw:max-w-[730px] tw:h-[22px] tw:flex tw:justify-between tw:md:justify-end">
          <div className="tw:w-auto tw:h-[22px] tw:flex tw:justify-center">
            <span className="basic-16-new tw:text-text-secondary">{localTotalToPayLabel}</span>
          </div>
          <div className="tw:min-w-[75px] tw:ml-[74px] tw:h-[22px]">
            <span className="link-16-new tw:text-text-primary tw:text-right tw:w-full tw:block">
              {formatPrice(calculateGrandTotal(), items[0]?.currency ?? '')}
            </span>
          </div>
        </div>

        {/* Spacer */}
        <div className="tw:w-full tw:h-[24px] tw:bg-white" />

        {/* Final Divider */}
        <div className="tw:w-full tw:h-[1px] tw:bg-[#E2E8F0]" />
      </div>

      {/* Call to Action */}
      <div className="tw:flex tw:flex-col tw:md:flex-row tw:justify-between tw:items-center tw:gap-[16px] tw:w-full tw:max-w-[730px]">
        {/* Back Link */}
        <button
          onClick={onBackClick}
          className="tw:order-2 tw:md:order-1 tw:flex tw:flex-row tw:justify-center tw:items-center tw:gap-[4px] tw:cursor-pointer tw:group"
        >
          <div className="tw:w-[16px] tw:h-[16px]">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clipPath="url(#clip0_7468_4715)">
                <path d="M15 8L1 8M1 8L6.25 14M1 8L6.25 2" stroke="#020617" strokeWidth="1.5" strokeLinecap="square" />
              </g>
              <defs>
                <clipPath id="clip0_7468_4715">
                  <rect width="16" height="16" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </div>
          <span className="link-14-new tw:underline">{localBackLinkText}</span>
        </button>

        {/* Checkout Button */}
        <Button
          onClick={onCheckoutClick}
          isDisabled={isCheckoutDisabled || calculateGrandTotal() === 0}
          type="primary"
          size="large"
          className="tw:max-w-[350px] tw:!w-full tw:md:order-2"
          label={localCheckoutButtonText}
        />
      </div>
    </div>
  );
}
