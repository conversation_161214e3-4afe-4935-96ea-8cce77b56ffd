import React from 'react';

export interface IconLockOpenedProps {
  className?: string;
  size?: number | string;
  color?: string;
  fill?: string;
  stroke?: string;
}

const IconLockOpened: React.FC<IconLockOpenedProps> = ({
  className = '',
  size = 24,
  color = 'currentColor',
  fill,
  stroke,
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      className={className}
      color={color}
      fill={fill || 'none'}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_8996_7009)">
        <path
          d="M12.6667 13.5V14H3.33333V8.00004H7.00002M6.66666 2.28872C7.01136 2.10898 7.43129 2.00004 7.94032 2.00004C10.1214 2.00004 10.6667 4.00004 10.6667 5.00004V8.00004H12.6667V8.66671M5.33333 6.25004V8.00004M1.33333 1.33337L14.6667 14.6667"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="square"
        />
      </g>
      <defs>
        <clipPath id="clip0_8996_7009">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconLockOpened;
