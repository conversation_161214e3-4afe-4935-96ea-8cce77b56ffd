{"name": "frontend", "version": "1.0.0", "description": "Splendex frontend wordpress theme", "scripts": {"build:style.css": "node-sass -q --output-style compressed assets/scss/style.scss style.css", "build:blocks.css": "node-sass -q --output-style compressed blocks/style.scss blocks.css", "build:editor-blocks.css": "node-sass -q --output-style compressed blocks/editor.scss editor-blocks.css", "build:blocks-non-critical.css": "node-sass -q --output-style compressed blocks/style-non-critical.scss blocks-non-critical.css", "build:editor.css": "node-sass -q --output-style compressed assets/scss/gutenberg/editor.scss editor.css", "build:lists.css": "node-sass -q --output-style compressed assets/scss/lists.scss lists.css", "build:single.css": "node-sass -q --output-style compressed assets/scss/single.scss single.css", "build:style-non-critical.css": "node-sass -q --output-style compressed assets/scss/style-non-critical.scss style-non-critical.css", "build:my-account.css": "node-sass -q --output-style compressed assets/scss/my-account.scss my-account.css", "build:tailwind.css": "tailwindcss -i index.css -o tailwind.css --minify", "build:css": "run-p build:style.css build:blocks.css build:editor-blocks.css build:blocks-non-critical.css build:editor.css build:lists.css build:single.css build:style-non-critical.css build:my-account.css build:tailwind.css", "build": "run-p build:css", "dev": "nodemon -w . -e 'php js jsx ts tsx scss' --exec \"npm run build\"", "fix:prettier": "prettier --write .", "fix": "npm run fix:prettier", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "author": "Splendex Kft.", "license": "ISC", "devDependencies": {"@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/blocks": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@tailwindcss/cli": "^4.1.4", "browser-sync": "^2.29.3", "eslint-plugin-storybook": "^0.12.0", "gulp": "^4.0.2", "gulp-rename": "^2.0.0", "gulp-uglify": "^3.0.2", "node-sass": "^9.0.0", "nodemon": "^3.1.10", "npm-run-all": "^4.1.5", "prettier": "^3.5.2", "prop-types": "^15.8.1", "storybook": "^8.6.12", "storybook-addon-deep-controls": "^0.9.2", "storybook-dark-mode": "^4.0.2", "tailwindcss": "^4.1.4"}}