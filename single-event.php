<?php

if (isset($_GET['cart'])) {
  get_header();
  
  get_template_part('template-parts/event-cart/event-cart');
  
  get_footer();
  
  return;
}

/**
 * The category of the event
 * @var string
 */
$category = !empty(get_categories(array('taxonomy' => 'event-category'))) ? get_categories(array('taxonomy' => 'event-category'))[0]->name : null;

/**
 * The link to the category archive
 * @var string
 */
$category_link = !empty(get_categories(array('taxonomy' => 'event-category'))) ? get_term_link( get_categories(array('taxonomy' => 'event-category'))[0]->term_id ) : null;

$related_events = get_field('related_events') ?? [];

get_header();
?>

<main class="single-event">

	<div class="container">

		<div class="header">

			<div class="row">

				<div class="col-12">

					<div class="header__image-wrapper">

						<?php if ( has_post_thumbnail() ):?>

							<?= get_the_post_thumbnail(
								get_the_ID(),
								'large',
								array('class' => 'header__image', 'alt' => get_the_title(), 'fetchpriority' => 'high')
							) ?>

						<?php endif;?>

					</div>

				</div>

			</div>

			<div class="row">

				<div class="col-hidden col-xl-1"></div>

				<div class="col-12 col-xl-10">

					<div class="header__content-wrapper">

						<?php if( $category && $category_link ):?>

							<a href="<?= $category_link;?>" class="header__category cta-link-tag"><?= $category;?></a>

						<?php endif;?>

						<?php $pageTitle = new Heading(get_the_title(), 'h1', null, '700', 'noto-serif'); echo $pageTitle->render(); ?>

						<?php if( has_excerpt() ):?>

							<div class="header__excerpt">

								<?php the_excerpt();?>

							</div>

						<?php endif;?>

					</div>

				</div>

			</div>

		</div>

		<div class="body">

			<div class="row">

				<div class="col-hidden col-xl-1"></div>

                <div class="gutenberg-content col-12 col-xl-10">

					<?php the_content();?>

				</div>

				<div class="col-hidden col-xl-1"></div>

			</div>

			<?php if($related_events && count($related_events)) : ?>
				<div id="events-slider" class="home-events-carousel__carousel single-slider" data-numberSlides="<?= count($related_events); ?>">
					<?php foreach($related_events as $event) : ?>
						<?php get_template_part('template-parts/event-card/index', null,  ['id' => $event['event']]) ?>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>

		</div>

	</div>

</main>

<?php get_footer();?>
