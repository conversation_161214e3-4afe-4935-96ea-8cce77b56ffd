<?php

$termSlug = get_query_var('term');
$category = ForbesEcommerceSync\Category::getBySlug($termSlug);

if (!$category) {
	// TODO: Redirect to brands page?
	wp_redirect(get_home_url());
	exit;
}

// Získanie page parametra z URL
$currentPage = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$itemsPerPage = 20;

// Výpočet celkového počtu produktov na načítanie (page * itemsPerPage)
$totalItemsToLoad = $currentPage * $itemsPerPage;

$products = (new ForbesEcommerceSync\ProductQuery())
	->setCategory($category)
	->setPerPage($totalItemsToLoad)
	->setSortBy($_GET['sortBy'] ?? null)
	->setSortOrder($_GET['sortOrder'] ?? null)
	->fetchAll();

if ( ! empty($products)) {
    foreach ($products as $product) {
        $product->content = wp_kses_post(html_entity_decode($product->content, ENT_QUOTES));
    }
}

// $genders = array_values(array_filter(
// 	ForbesEcommerceSync\Gender::getTopLevel(),
// 	fn($gender): bool => (new ForbesEcommerceSync\ProductQuery())
// 		->setGender($gender)
// 		->setCategory($category)
// 		->exists()
// ));

// $tabs = [
// 	[
// 		'label' => __('All products', 'FORBES'),
// 		'link' => 'category=',
// 	],
// ];

// $tabs += array_map(
// 	fn($gender): array => [
// 		'label' => $gender->name,
// 		'link' => "gender={$gender->slug}",
// 	],
// 	$genders
// );

$tabs = [];
$filters = [];

?>

<?php get_header(); ?>

<main class="product-category-single">
	<div class="container">
		<div class="row">
			<div class="col-12">
				<div
					class="reactEcommerceCategoryPage"
                    data-products="<?php echo esc_attr(wp_json_encode($products)); ?> "
                    data-category-details="<?php echo esc_attr(wp_json_encode($category)); ?>"
                    data-category-filters="<?php echo esc_attr(wp_json_encode($filters)); ?>"
                    data-tabs="<?php echo esc_attr(wp_json_encode($tabs)); ?>"
                    data-current-page="<?php echo esc_attr($currentPage); ?>"
				></div>
			</div>
		</div>
	</div>
</main>

<?php get_footer(); ?>
